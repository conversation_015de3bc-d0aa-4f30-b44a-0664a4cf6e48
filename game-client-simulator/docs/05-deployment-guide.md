# 游戏客户端模拟器 - 部署和使用指南

## 🚀 快速开始

### 环境要求
- **Node.js** >= 16.0.0
- **npm** >= 8.0.0
- **游戏网关** 运行在可访问的地址

### 安装步骤
```bash
# 1. 克隆或下载项目
git clone <repository-url>
cd game-client-simulator

# 2. 安装依赖
npm install

# 3. 配置环境
cp config/example.config.js config/local.config.js
# 编辑 config/local.config.js 设置网关地址等

# 4. 验证安装
npm test

# 5. 运行示例
npm run example
```

## ⚙️ 配置说明

### 基础配置文件
```javascript
// config/local.config.js
module.exports = {
  // 网关配置
  gateway: {
    url: 'ws://***************:3000',  // 网关WebSocket地址
    timeout: 30000,                    // 请求超时时间
    reconnect: true,                   // 是否自动重连
    maxRetries: 5                      // 最大重连次数
  },
  
  // 日志配置
  logging: {
    level: 'info',                     // 日志级别: debug, info, warn, error
    console: true,                     // 是否输出到控制台
    file: 'logs/simulator.log',        // 日志文件路径
    maxSize: '10m',                    // 日志文件最大大小
    maxFiles: 5                        // 保留的日志文件数量
  },
  
  // 性能配置
  performance: {
    concurrent: 10,                    // 并发虚拟玩家数量
    delay: 1000,                       // 操作间延迟（毫秒）
    timeout: 60000                     // 场景执行超时时间
  },
  
  // 测试配置
  test: {
    environment: 'test',               // 测试环境标识
    cleanup: true,                     // 是否自动清理测试数据
    dataPath: 'test-data/',           // 测试数据存储路径
    reportPath: 'reports/'            // 测试报告存储路径
  }
};
```

### 环境变量配置
```bash
# .env 文件
GATEWAY_URL=ws://***************:3000
LOG_LEVEL=info
TEST_ENVIRONMENT=test
CONCURRENT_PLAYERS=5
```

## 🎮 使用方式

### 1. 命令行使用
```bash
# 执行单个场景
npm run scenario newbie-flow

# 执行多个场景
npm run scenario newbie-flow guild-creation daily-routine

# 指定配置文件
npm run scenario newbie-flow --config config/production.config.js

# 并发执行
npm run scenario newbie-flow --concurrent 5

# 生成详细报告
npm run scenario newbie-flow --report --output reports/
```

### 2. 编程方式使用
```javascript
// examples/basic-usage.js
const { GameClient, VirtualPlayer } = require('../src');

async function basicExample() {
  // 创建游戏客户端
  const client = new GameClient({
    gateway: 'ws://***************:3000'
  });
  
  try {
    // 连接到游戏服务器
    await client.connect();
    console.log('✅ 已连接到游戏服务器');
    
    // 创建虚拟玩家
    const player = new VirtualPlayer(client, {
      username: `test_${Date.now()}`,
      password: 'Test123456!'
    });
    
    // 执行游戏操作
    await player.perform('auth.register');
    await player.perform('auth.login');
    await player.perform('character.create', {
      name: '测试角色',
      serverId: 'server_001'
    });
    
    console.log('✅ 基础操作完成');
    console.log('角色信息:', player.getGameState('character'));
    
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
  } finally {
    await client.disconnect();
  }
}

basicExample();
```

### 3. 场景执行
```javascript
// examples/scenario-execution.js
const { GameClient } = require('../src');

async function runScenario() {
  const client = new GameClient();
  await client.connect();
  
  try {
    // 执行新手流程场景
    const result = await client.playScenario('newbie-complete-flow', {
      username: 'newbie_test',
      characterName: '新手测试角色'
    });
    
    if (result.success) {
      console.log('✅ 新手流程完成');
      console.log('执行时长:', result.duration, 'ms');
      console.log('角色等级:', result.player.finalState.level);
    } else {
      console.error('❌ 新手流程失败:', result.error);
    }
    
  } finally {
    await client.disconnect();
  }
}

runScenario();
```

## 🔧 高级用法

### 1. 自定义操作
```javascript
// src/actions/custom/my-action.js
const BaseAction = require('../../core/base-action');

class MyCustomAction extends BaseAction {
  static metadata = {
    name: '自定义操作',
    description: '这是一个自定义的游戏操作',
    category: 'custom',
    params: {
      customParam: { type: 'string', required: true }
    }
  };

  async perform(client, params) {
    const { customParam } = params;
    
    // 执行自定义逻辑
    const result = await client.callAPI('custom.api', {
      param: customParam
    });
    
    return {
      success: true,
      data: result.data
    };
  }
}

module.exports = MyCustomAction;
```

### 2. 批量测试
```javascript
// examples/batch-testing.js
const { GameClient } = require('../src');

async function batchTest() {
  const scenarios = [
    'newbie-complete-flow',
    'guild-master-flow',
    'daily-player-flow'
  ];
  
  const results = [];
  
  for (const scenario of scenarios) {
    const client = new GameClient();
    await client.connect();
    
    try {
      const result = await client.playScenario(scenario);
      results.push({
        scenario,
        success: result.success,
        duration: result.duration
      });
    } catch (error) {
      results.push({
        scenario,
        success: false,
        error: error.message
      });
    } finally {
      await client.disconnect();
    }
  }
  
  // 生成测试报告
  console.table(results);
}

batchTest();
```

### 3. 并发测试
```javascript
// examples/concurrent-testing.js
const { GameClient } = require('../src');

async function concurrentTest() {
  const playerCount = 10;
  const promises = [];
  
  for (let i = 0; i < playerCount; i++) {
    const promise = (async () => {
      const client = new GameClient();
      await client.connect();
      
      try {
        return await client.playScenario('newbie-complete-flow', {
          username: `concurrent_player_${i}`,
          characterName: `并发玩家${i}`
        });
      } finally {
        await client.disconnect();
      }
    })();
    
    promises.push(promise);
  }
  
  const results = await Promise.allSettled(promises);
  
  const successCount = results.filter(r => 
    r.status === 'fulfilled' && r.value.success
  ).length;
  
  console.log(`✅ 并发测试完成: ${successCount}/${playerCount} 成功`);
}

concurrentTest();
```

## 📊 监控和调试

### 1. 日志查看
```bash
# 实时查看日志
tail -f logs/simulator.log

# 查看错误日志
grep "ERROR" logs/simulator.log

# 查看特定操作的日志
grep "auth.login" logs/simulator.log
```

### 2. 性能监控
```javascript
// 启用性能监控
const client = new GameClient({
  monitoring: {
    enabled: true,
    metrics: ['duration', 'memory', 'network'],
    interval: 5000  // 每5秒输出一次指标
  }
});
```

### 3. 调试模式
```bash
# 启用调试模式
DEBUG=simulator:* npm run scenario newbie-flow

# 只显示特定模块的调试信息
DEBUG=simulator:websocket npm run scenario newbie-flow
```

## 🚨 故障排除

### 常见问题

#### 1. 连接失败
```
错误: WebSocket连接失败
解决: 检查网关地址和端口是否正确，确保网关服务正在运行
```

#### 2. 认证失败
```
错误: 登录失败，用户名或密码错误
解决: 检查用户名密码格式，确保符合游戏要求
```

#### 3. 操作超时
```
错误: 操作执行超时
解决: 增加timeout配置，检查网络连接稳定性
```

#### 4. 内存不足
```
错误: JavaScript heap out of memory
解决: 减少并发数量，增加Node.js内存限制
```

### 调试技巧

#### 1. 启用详细日志
```javascript
const client = new GameClient({
  logging: {
    level: 'debug',
    verbose: true
  }
});
```

#### 2. 单步执行
```javascript
// 在场景中添加断点
steps:
  - name: "调试点"
    action: "debug.breakpoint"
    params:
      message: "检查当前状态"
      waitForInput: true
```

#### 3. 状态检查
```javascript
// 检查玩家状态
console.log('当前状态:', player.getGameState());
console.log('会话信息:', client.session.export());
```

## 📈 性能优化

### 1. 连接池优化
```javascript
const client = new GameClient({
  connectionPool: {
    size: 10,           // 连接池大小
    keepAlive: true,    // 保持连接
    timeout: 30000      // 连接超时
  }
});
```

### 2. 内存管理
```javascript
// 定期清理内存
setInterval(() => {
  if (global.gc) {
    global.gc();
  }
}, 60000);
```

### 3. 批量操作
```javascript
// 批量执行操作减少网络开销
await client.batchPerform([
  { action: 'hero.train', params: { heroId: 'hero1' } },
  { action: 'hero.train', params: { heroId: 'hero2' } },
  { action: 'hero.train', params: { heroId: 'hero3' } }
]);
```

---

**文档完成**：游戏客户端模拟器的完整文档已编写完成，包含项目概述、开发指南、API参考、场景编写和部署使用等全部内容。
