/**
 * 获取球迷信息 对应old项目: footballGround.getBallFans() 和 matchService.getFansRank() 用于商业赛系统获取对手的球迷数量和排名信息
 * 
 * 微服务: hero
 * 模块: ground
 * Controller: ground
 * Pattern: ground.getFansInfo
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.447Z
 */

const BaseAction = require('../../../core/base-action');

class GroundgetFansInfoAction extends BaseAction {
  static metadata = {
    name: '获取球迷信息 对应old项目: footballGround.getBallFans() 和 matchService.getFansRank() 用于商业赛系统获取对手的球迷数量和排名信息',
    description: '获取球迷信息 对应old项目: footballGround.getBallFans() 和 matchService.getFansRank() 用于商业赛系统获取对手的球迷数量和排名信息',
    category: 'hero',
    serviceName: 'hero',
    module: 'ground',
    actionName: 'ground.getFansInfo',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取球迷信息 对应old项目: footballGround.getBallFans() 和 matchService.getFansRank() 用于商业赛系统获取对手的球迷数量和排名信息成功'
      };
    } else {
      throw new Error(`获取球迷信息 对应old项目: footballGround.getBallFans() 和 matchService.getFansRank() 用于商业赛系统获取对手的球迷数量和排名信息失败: ${response.message}`);
    }
  }
}

module.exports = GroundgetFansInfoAction;