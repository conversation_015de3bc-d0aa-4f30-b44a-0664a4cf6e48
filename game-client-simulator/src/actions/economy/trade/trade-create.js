/**
 * 创建交易
 * 
 * 微服务: economy
 * 模块: trade
 * Controller: trade
 * Pattern: trade.create
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.416Z
 */

const BaseAction = require('../../../core/base-action');

class TradecreateAction extends BaseAction {
  static metadata = {
    name: '创建交易',
    description: '创建交易',
    category: 'economy',
    serviceName: 'economy',
    module: 'trade',
    actionName: 'trade.create',
    prerequisites: ["login"],
    params: {
      "data": {
            "type": "any",
            "required": true,
            "description": "any类型参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { data } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      data
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '创建交易成功'
      };
    } else {
      throw new Error(`创建交易失败: ${response.message}`);
    }
  }
}

module.exports = TradecreateAction;