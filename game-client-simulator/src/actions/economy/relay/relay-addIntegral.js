/**
 * 添加积分（内部接口）
 * 
 * 微服务: economy
 * 模块: relay
 * Controller: relay
 * Pattern: relay.addIntegral
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.399Z
 */

const BaseAction = require('../../../core/base-action');

class RelayaddIntegralAction extends BaseAction {
  static metadata = {
    name: '添加积分（内部接口）',
    description: '添加积分（内部接口）',
    category: 'economy',
    serviceName: 'economy',
    module: 'relay',
    actionName: 'relay.addIntegral',
    prerequisites: ["login","character"],
    params: {
      "uid": {
            "type": "string",
            "required": true,
            "description": "uid参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "amount": {
            "type": "number",
            "required": true,
            "description": "amount参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { uid, serverId, amount } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      uid,
      serverId,
      amount
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '添加积分（内部接口）成功'
      };
    } else {
      throw new Error(`添加积分（内部接口）失败: ${response.message}`);
    }
  }
}

module.exports = RelayaddIntegralAction;