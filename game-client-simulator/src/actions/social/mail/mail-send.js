/**
 * 发送邮件
 * 
 * 微服务: social
 * 模块: mail
 * Controller: mail
 * Pattern: mail.send
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.600Z
 */

const BaseAction = require('../../../core/base-action');

class MailsendAction extends BaseAction {
  static metadata = {
    name: '发送邮件',
    description: '发送邮件',
    category: 'social',
    serviceName: 'social',
    module: 'mail',
    actionName: 'mail.send',
    prerequisites: ["login","character"],
    params: {
      "receiverUid": {
            "type": "string",
            "required": true,
            "description": "receiverUid参数"
      },
      "senderUid": {
            "type": "string",
            "required": true,
            "description": "senderUid参数"
      },
      "mailId": {
            "type": "number",
            "required": true,
            "description": "mailId参数"
      },
      "mailType": {
            "type": "number",
            "required": true,
            "description": "mailType参数"
      },
      "attachList": {
            "type": "array",
            "required": true,
            "description": "attachList参数"
      },
      "specialAttachInfo": {
            "type": "any",
            "required": false,
            "description": "specialAttachInfo参数"
      },
      "param1": {
            "type": "any",
            "required": false,
            "description": "param1参数"
      },
      "param2": {
            "type": "any",
            "required": false,
            "description": "param2参数"
      },
      "param3": {
            "type": "any",
            "required": false,
            "description": "param3参数"
      },
      "param4": {
            "type": "any",
            "required": false,
            "description": "param4参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { receiverUid, senderUid, mailId, mailType, attachList, specialAttachInfo, param1, param2, param3, param4 } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      receiverUid,
      senderUid,
      mailId,
      mailType,
      attachList,
      specialAttachInfo,
      param1,
      param2,
      param3,
      param4
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '发送邮件成功'
      };
    } else {
      throw new Error(`发送邮件失败: ${response.message}`);
    }
  }
}

module.exports = MailsendAction;