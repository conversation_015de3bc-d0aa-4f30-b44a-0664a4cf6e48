/**
 * 获取邮件列表
 * 
 * 微服务: social
 * 模块: mail
 * Controller: mail
 * Pattern: mail.getList
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.601Z
 */

const BaseAction = require('../../../core/base-action');

class MailgetListAction extends BaseAction {
  static metadata = {
    name: '获取邮件列表',
    description: '获取邮件列表',
    category: 'social',
    serviceName: 'social',
    module: 'mail',
    actionName: 'mail.getList',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "page": {
            "type": "number",
            "required": false,
            "description": "page参数"
      },
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, page, limit } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      page,
      limit
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取邮件列表成功'
      };
    } else {
      throw new Error(`获取邮件列表失败: ${response.message}`);
    }
  }
}

module.exports = MailgetListAction;