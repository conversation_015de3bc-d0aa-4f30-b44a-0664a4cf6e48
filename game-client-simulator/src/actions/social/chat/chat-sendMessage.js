/**
 * 发送聊天消息
 * 
 * 微服务: social
 * 模块: chat
 * Controller: chat
 * Pattern: chat.sendMessage
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.562Z
 */

const BaseAction = require('../../../core/base-action');

class ChatsendMessageAction extends BaseAction {
  static metadata = {
    name: '发送聊天消息',
    description: '发送聊天消息',
    category: 'social',
    serviceName: 'social',
    module: 'chat',
    actionName: 'chat.sendMessage',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "messageData": {
            "type": "object",
            "required": true,
            "description": "messageData参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, serverId, messageData } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      serverId,
      messageData
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '发送聊天消息成功'
      };
    } else {
      throw new Error(`发送聊天消息失败: ${response.message}`);
    }
  }
}

module.exports = ChatsendMessageAction;