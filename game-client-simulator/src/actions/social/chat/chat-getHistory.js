/**
 * 获取聊天历史
 * 
 * 微服务: social
 * 模块: chat
 * Controller: chat
 * Pattern: chat.getHistory
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.563Z
 */

const BaseAction = require('../../../core/base-action');

class ChatgetHistoryAction extends BaseAction {
  static metadata = {
    name: '获取聊天历史',
    description: '获取聊天历史',
    category: 'social',
    serviceName: 'social',
    module: 'chat',
    actionName: 'chat.getHistory',
    prerequisites: ["login","character"],
    params: {
      "channelId": {
            "type": "string",
            "required": true,
            "description": "channelId参数"
      },
      "limit": {
            "type": "number",
            "required": false,
            "description": "limit参数"
      },
      "before": {
            "type": "number",
            "required": false,
            "description": "before参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { channelId, limit, before } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      channelId,
      limit,
      before
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '获取聊天历史成功'
      };
    } else {
      throw new Error(`获取聊天历史失败: ${response.message}`);
    }
  }
}

module.exports = ChatgetHistoryAction;