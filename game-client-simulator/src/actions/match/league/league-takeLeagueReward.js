/**
 * 领取联赛奖励 基于old项目的联赛奖励发放逻辑
 * 
 * 微服务: match
 * 模块: league
 * Controller: league
 * Pattern: league.takeLeagueReward
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.532Z
 */

const BaseAction = require('../../../core/base-action');

class LeaguetakeLeagueRewardAction extends BaseAction {
  static metadata = {
    name: '领取联赛奖励 基于old项目的联赛奖励发放逻辑',
    description: '领取联赛奖励 基于old项目的联赛奖励发放逻辑',
    category: 'match',
    serviceName: 'match',
    module: 'league',
    actionName: 'league.takeLeagueReward',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "leagueId": {
            "type": "number",
            "required": true,
            "description": "leagueId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, leagueId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      leagueId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '领取联赛奖励 基于old项目的联赛奖励发放逻辑成功'
      };
    } else {
      throw new Error(`领取联赛奖励 基于old项目的联赛奖励发放逻辑失败: ${response.message}`);
    }
  }
}

module.exports = LeaguetakeLeagueRewardAction;