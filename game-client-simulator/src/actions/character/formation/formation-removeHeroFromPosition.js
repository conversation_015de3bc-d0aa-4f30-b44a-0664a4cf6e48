/**
 * 从阵容位置移除球员 对应old项目: deleteHeroFromTeam方法，优化API命名
 * 
 * 微服务: character
 * 模块: formation
 * Controller: formation
 * Pattern: formation.removeHeroFromPosition
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.334Z
 */

const BaseAction = require('../../../core/base-action');

class FormationremoveHeroFromPositionAction extends BaseAction {
  static metadata = {
    name: '从阵容位置移除球员 对应old项目: deleteHeroFromTeam方法，优化API命名',
    description: '从阵容位置移除球员 对应old项目: deleteHeroFromTeam方法，优化API命名',
    category: 'character',
    serviceName: 'character',
    module: 'formation',
    actionName: 'formation.removeHeroFromPosition',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "formationId": {
            "type": "string",
            "required": true,
            "description": "formationId参数"
      },
      "position": {
            "type": "string",
            "required": true,
            "description": "position参数"
      },
      "heroId": {
            "type": "string",
            "required": true,
            "description": "heroId参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, formationId, position, heroId, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      formationId,
      position,
      heroId,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '从阵容位置移除球员 对应old项目: deleteHeroFromTeam方法，优化API命名成功'
      };
    } else {
      throw new Error(`从阵容位置移除球员 对应old项目: deleteHeroFromTeam方法，优化API命名失败: ${response.message}`);
    }
  }
}

module.exports = FormationremoveHeroFromPositionAction;