/**
 * 添加资源 对应old项目: game.player.addResource
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.addResource
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.324Z
 */

const BaseAction = require('../../../core/base-action');

class CharacteraddResourceAction extends BaseAction {
  static metadata = {
    name: '添加资源 对应old项目: game.player.addResource',
    description: '添加资源 对应old项目: game.player.addResource',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.addResource',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "resourceType": {
            "type": "string",
            "required": true,
            "description": "resourceType参数"
      },
      "amount": {
            "type": "number",
            "required": true,
            "description": "amount参数"
      },
      "reason": {
            "type": "string",
            "required": false,
            "description": "reason参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, resourceType, amount, reason, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      resourceType,
      amount,
      reason,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '添加资源 对应old项目: game.player.addResource成功'
      };
    } else {
      throw new Error(`添加资源 对应old项目: game.player.addResource失败: ${response.message}`);
    }
  }
}

module.exports = CharacteraddResourceAction;