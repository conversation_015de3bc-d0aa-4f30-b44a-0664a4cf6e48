/**
 * 创建新角色
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.create
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.305Z
 */

const BaseAction = require('../../../core/base-action');

class CharactercreateAction extends BaseAction {
  static metadata = {
    name: '创建新角色',
    description: '创建新角色',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.create',
    prerequisites: ["login"],
    params: {
      "userId": {
            "type": "string",
            "required": true,
            "description": "userId参数"
      },
      "serverId": {
            "type": "string",
            "required": true,
            "description": "serverId参数"
      },
      "openId": {
            "type": "string",
            "required": true,
            "description": "openId参数"
      },
      "name": {
            "type": "string",
            "required": true,
            "description": "name参数"
      },
      "avatar": {
            "type": "string",
            "required": false,
            "description": "avatar参数"
      },
      "faceIcon": {
            "type": "number",
            "required": false,
            "description": "faceIcon参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { userId, serverId, openId, name, avatar, faceIcon } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      userId,
      serverId,
      openId,
      name,
      avatar,
      faceIcon
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '创建新角色成功'
      };
    } else {
      throw new Error(`创建新角色失败: ${response.message}`);
    }
  }
}

module.exports = CharactercreateAction;