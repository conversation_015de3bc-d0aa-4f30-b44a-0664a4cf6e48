/**
 * 购买体力
 * 
 * 微服务: character
 * 模块: character
 * Controller: character
 * Pattern: character.energy.buy
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.312Z
 */

const BaseAction = require('../../../core/base-action');

class CharacterenergybuyAction extends BaseAction {
  static metadata = {
    name: '购买体力',
    description: '购买体力',
    category: 'character',
    serviceName: 'character',
    module: 'character',
    actionName: 'character.energy.buy',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "buyDto": {
            "type": "object",
            "required": true,
            "description": "buyDto参数"
      },
      "serverId": {
            "type": "string",
            "required": false,
            "description": "serverId参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, buyDto, serverId } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      buyDto,
      serverId
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '购买体力成功'
      };
    } else {
      throw new Error(`购买体力失败: ${response.message}`);
    }
  }
}

module.exports = CharacterenergybuyAction;