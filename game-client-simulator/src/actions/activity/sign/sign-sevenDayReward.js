/**
 * 七日签到奖励
 * 
 * 微服务: activity
 * 模块: sign
 * Controller: sign
 * Pattern: sign.sevenDayReward
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.279Z
 */

const BaseAction = require('../../../core/base-action');

class SignsevenDayRewardAction extends BaseAction {
  static metadata = {
    name: '七日签到奖励',
    description: '七日签到奖励',
    category: 'activity',
    serviceName: 'activity',
    module: 'sign',
    actionName: 'sign.sevenDayReward',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "day": {
            "type": "number",
            "required": true,
            "description": "day参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, day } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      day
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '七日签到奖励成功'
      };
    } else {
      throw new Error(`七日签到奖励失败: ${response.message}`);
    }
  }
}

module.exports = SignsevenDayRewardAction;