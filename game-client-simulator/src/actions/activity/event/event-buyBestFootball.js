/**
 * 最佳11人抽奖 基于old项目: Act.prototype.buyBestFootball
 * 
 * 微服务: activity
 * 模块: event
 * Controller: event
 * Pattern: event.buyBestFootball
 * 
 * 使用AST自动生成于 2025-07-26T12:41:29.261Z
 */

const BaseAction = require('../../../core/base-action');

class EventbuyBestFootballAction extends BaseAction {
  static metadata = {
    name: '最佳11人抽奖 基于old项目: Act.prototype.buyBestFootball',
    description: '最佳11人抽奖 基于old项目: Act.prototype.buyBestFootball',
    category: 'activity',
    serviceName: 'activity',
    module: 'event',
    actionName: 'event.buyBestFootball',
    prerequisites: ["login","character"],
    params: {
      "characterId": {
            "type": "string",
            "required": true,
            "description": "characterId参数"
      },
      "index": {
            "type": "number",
            "required": true,
            "description": "index参数"
      }
},
    timeout: 10000
  };

  async perform(client, params) {
    const { characterId, index } = params;
    
    // 底层调用时拼接 serviceName.actionName
    const fullActionName = `${this.constructor.metadata.serviceName}.${this.constructor.metadata.actionName}`;
    const response = await client.callAPI(fullActionName, {
      characterId,
      index
    });
    
    if (response.code === 0) {
      return {
        success: true,
        ...response.data,
        message: '最佳11人抽奖 基于old项目: Act.prototype.buyBestFootball成功'
      };
    } else {
      throw new Error(`最佳11人抽奖 基于old项目: Act.prototype.buyBestFootball失败: ${response.message}`);
    }
  }
}

module.exports = EventbuyBestFootballAction;