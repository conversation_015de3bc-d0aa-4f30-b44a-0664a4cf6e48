/**
 * 配置管理器
 * 
 * 功能：
 * 1. 配置文件加载和合并
 * 2. 环境变量处理
 * 3. 配置验证
 * 4. 动态配置更新
 */

const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');

class ConfigManager {
  constructor() {
    this.config = {};
    this.configPath = path.join(__dirname);
    this.loadConfig();
  }

  /**
   * 加载配置
   */
  loadConfig() {
    try {
      // 加载默认配置
      const defaultConfig = require('./default.config.js');
      this.config = { ...defaultConfig };
      
      // 尝试加载本地配置
      const localConfigPath = path.join(this.configPath, 'local.config.js');
      if (fs.existsSync(localConfigPath)) {
        const localConfig = require(localConfigPath);
        this.config = this.mergeConfig(this.config, localConfig);
        logger.debug('📁 已加载本地配置文件');
      }
      
      // 尝试加载环境特定配置
      const env = process.env.NODE_ENV || 'development';
      const envConfigPath = path.join(this.configPath, `${env}.config.js`);
      if (fs.existsSync(envConfigPath)) {
        const envConfig = require(envConfigPath);
        this.config = this.mergeConfig(this.config, envConfig);
        logger.debug(`📁 已加载${env}环境配置文件`);
      }
      
      // 验证配置
      this.validateConfig();
      
      logger.info('✅ 配置加载完成');
      
    } catch (error) {
      logger.error('❌ 配置加载失败:', error);
      throw error;
    }
  }

  /**
   * 深度合并配置对象
   */
  mergeConfig(target, source) {
    const result = { ...target };
    
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (
          typeof source[key] === 'object' && 
          source[key] !== null && 
          !Array.isArray(source[key]) &&
          typeof target[key] === 'object' && 
          target[key] !== null && 
          !Array.isArray(target[key])
        ) {
          result[key] = this.mergeConfig(target[key], source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }
    
    return result;
  }

  /**
   * 验证配置
   */
  validateConfig() {
    const errors = [];
    
    // 验证网关配置
    if (!this.config.gateway.url) {
      errors.push('网关URL不能为空');
    }
    
    if (this.config.gateway.timeout < 1000) {
      errors.push('网关超时时间不能小于1000ms');
    }
    
    // 验证性能配置
    if (this.config.performance.concurrent < 1) {
      errors.push('并发数量不能小于1');
    }
    
    if (this.config.performance.concurrent > 100) {
      errors.push('并发数量不能大于100');
    }
    
    // 验证日志配置
    const validLogLevels = ['error', 'warn', 'info', 'debug'];
    if (!validLogLevels.includes(this.config.logging.level)) {
      errors.push(`无效的日志级别: ${this.config.logging.level}`);
    }
    
    if (errors.length > 0) {
      throw new Error(`配置验证失败:\n${errors.join('\n')}`);
    }
  }

  /**
   * 获取配置值
   */
  get(path, defaultValue = undefined) {
    const keys = path.split('.');
    let current = this.config;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }
    
    return current;
  }

  /**
   * 设置配置值
   */
  set(path, value) {
    const keys = path.split('.');
    let current = this.config;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current) || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
    logger.debug(`⚙️ 配置已更新: ${path} = ${value}`);
  }

  /**
   * 获取完整配置
   */
  getAll() {
    return { ...this.config };
  }

  /**
   * 重新加载配置
   */
  reload() {
    // 清除require缓存
    const configFiles = [
      './default.config.js',
      './local.config.js',
      `./${process.env.NODE_ENV || 'development'}.config.js`
    ];
    
    configFiles.forEach(file => {
      const fullPath = path.resolve(this.configPath, file);
      delete require.cache[fullPath];
    });
    
    this.loadConfig();
    logger.info('🔄 配置已重新加载');
  }

  /**
   * 导出配置到文件
   */
  exportConfig(filePath) {
    try {
      const configJson = JSON.stringify(this.config, null, 2);
      fs.writeFileSync(filePath, configJson);
      logger.info(`📤 配置已导出到: ${filePath}`);
    } catch (error) {
      logger.error('❌ 配置导出失败:', error);
      throw error;
    }
  }

  /**
   * 获取环境信息
   */
  getEnvironmentInfo() {
    return {
      nodeEnv: process.env.NODE_ENV || 'development',
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      pid: process.pid,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      configLoaded: true,
      gatewayUrl: this.config.gateway.url,
      logLevel: this.config.logging.level
    };
  }
}

// 创建单例实例
const configManager = new ConfigManager();

module.exports = configManager;
