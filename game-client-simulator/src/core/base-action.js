/**
 * 操作基类
 * 
 * 功能：
 * 1. 统一的操作执行流程
 * 2. 参数验证和前置检查
 * 3. 错误处理和重试机制
 * 4. 执行结果标准化
 */

const logger = require('../utils/logger');

class BaseAction {
  constructor() {
    this.metadata = this.constructor.metadata || {};
  }

  /**
   * 执行操作
   * @param {GameClient} client - 游戏客户端
   * @param {Object} params - 操作参数
   * @returns {Promise<Object>} 操作结果
   */
  async execute(client, params = {}) {
    const actionName = this.metadata.name || this.constructor.name;
    const startTime = Date.now();
    
    logger.action(actionName, '开始执行', { params });
    
    try {
      // 前置检查
      await this.preCheck(client, params);
      
      // 执行主要逻辑
      const result = await this.perform(client, params);
      
      // 后置处理
      await this.postProcess(client, params, result);
      
      const duration = Date.now() - startTime;
      logger.performance(actionName, duration);
      logger.success(`操作完成: ${actionName}`);
      
      return {
        success: true,
        data: result,
        duration,
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.failure(`操作失败: ${actionName}`, error);
      
      return {
        success: false,
        error: {
          message: error.message,
          code: error.code || 'UNKNOWN_ERROR',
          stack: error.stack
        },
        duration,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 前置检查（子类可重写）
   */
  async preCheck(client, params) {
    // 检查客户端连接
    if (!client.isConnected) {
      throw new Error('客户端未连接到服务器');
    }
    
    // 检查必需参数
    if (this.metadata.params) {
      for (const [key, config] of Object.entries(this.metadata.params)) {
        if (config.required && (params[key] === undefined || params[key] === null)) {
          throw new Error(`缺少必需参数: ${key}`);
        }
        
        // 类型检查
        if (params[key] !== undefined && config.type) {
          if (!this.validateParamType(params[key], config.type)) {
            throw new Error(`参数类型错误: ${key} 应该是 ${config.type}`);
          }
        }
        
        // 值验证
        if (params[key] !== undefined && config.validate) {
          if (!config.validate(params[key])) {
            throw new Error(`参数值无效: ${key}`);
          }
        }
      }
    }
    
    // 检查前置条件
    if (this.metadata.prerequisites) {
      for (const prerequisite of this.metadata.prerequisites) {
        if (!this.checkPrerequisite(client, prerequisite)) {
          throw new Error(`前置条件不满足: ${prerequisite}`);
        }
      }
    }
  }

  /**
   * 执行主要逻辑（子类必须实现）
   */
  async perform(client, params) {
    throw new Error('子类必须实现 perform 方法');
  }

  /**
   * 后置处理（子类可重写）
   */
  async postProcess(client, params, result) {
    // 自动更新会话状态
    if (result && typeof result === 'object') {
      if (result.token) {
        client.session.setToken(result.token);
      }
      if (result.userId) {
        client.session.setUserId(result.userId);
      }
      if (result.characterId) {
        client.session.setCharacterId(result.characterId);
      }
      if (result.character) {
        client.session.updateCharacter(result.character);
      }
      if (result.heroes) {
        client.session.updateHeroes(result.heroes);
      }
      if (result.guild) {
        client.session.updateGuild(result.guild);
      }
    }
  }

  /**
   * 验证参数类型
   */
  validateParamType(value, expectedType) {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'email':
        return typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case 'uuid':
        return typeof value === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value);
      default:
        return true;
    }
  }

  /**
   * 检查前置条件
   */
  checkPrerequisite(client, prerequisite) {
    switch (prerequisite) {
      case 'login':
        return client.session.isValid();
      case 'character':
        return client.session.hasCharacter();
      case 'guild':
        return !!client.session.getGameState('guild');
      case 'heroes':
        const heroes = client.session.getGameState('heroes');
        return Array.isArray(heroes) && heroes.length > 0;
      default:
        // 自定义前置条件检查
        return !!client.session.getGameState(prerequisite);
    }
  }

  /**
   * 获取操作元数据
   */
  getMetadata() {
    return {
      name: this.metadata.name || this.constructor.name,
      description: this.metadata.description || '',
      category: this.metadata.category || 'general',
      params: this.metadata.params || {},
      prerequisites: this.metadata.prerequisites || [],
      timeout: this.metadata.timeout || 30000,
      retries: this.metadata.retries || 3,
      ...this.metadata
    };
  }

  /**
   * 验证操作是否可执行
   */
  async canExecute(client, params = {}) {
    try {
      await this.preCheck(client, params);
      return { canExecute: true };
    } catch (error) {
      return { 
        canExecute: false, 
        reason: error.message 
      };
    }
  }

  /**
   * 获取操作帮助信息
   */
  getHelp() {
    const metadata = this.getMetadata();
    
    let help = `操作: ${metadata.name}\n`;
    help += `描述: ${metadata.description}\n`;
    help += `分类: ${metadata.category}\n`;
    
    if (metadata.prerequisites.length > 0) {
      help += `前置条件: ${metadata.prerequisites.join(', ')}\n`;
    }
    
    if (Object.keys(metadata.params).length > 0) {
      help += `参数:\n`;
      for (const [key, config] of Object.entries(metadata.params)) {
        const required = config.required ? '(必需)' : '(可选)';
        const type = config.type ? `[${config.type}]` : '';
        const desc = config.description || '';
        help += `  ${key} ${type} ${required} - ${desc}\n`;
      }
    }
    
    return help;
  }
}

module.exports = BaseAction;
