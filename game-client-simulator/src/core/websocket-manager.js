/**
 * WebSocket管理器
 *
 * 功能：
 * 1. WebSocket连接管理（使用socket.io-client）
 * 2. 消息发送和接收
 * 3. 自动重连机制
 * 4. 错误处理和超时控制
 * 5. 认证令牌管理
 */

const io = require('socket.io-client');
const EventEmitter = require('events');
const logger = require('../utils/logger');

class WebSocketManager extends EventEmitter {
  constructor(url, options = {}) {
    super();
    this.url = url;
    this.options = {
      reconnectInterval: 5000,
      maxReconnectAttempts: 5,
      timeout: 30000,
      transports: ['websocket', 'polling'],
      forceNew: true,
      ...options
    };

    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.messageQueue = [];
    this.pendingRequests = new Map();
    this.requestId = 0;
    this.token = null;
  }

  /**
   * 设置认证令牌
   */
  setToken(token) {
    this.token = token;
    logger.debug('🔑 设置认证令牌');
  }

  /**
   * 连接到WebSocket服务器
   */
  async connect() {
    return new Promise((resolve, reject) => {
      try {
        logger.info(`🔌 连接到WebSocket服务器: ${this.url}`);

        // 使用socket.io-client连接
        const socketOptions = {
          timeout: this.options.timeout,
          transports: this.options.transports,
          forceNew: this.options.forceNew
        };

        // 如果有token，在连接时传递
        if (this.token) {
          socketOptions.auth = { token: this.token };
        }

        this.socket = io(this.url, socketOptions);

        this.socket.on('connect', () => {
          this.isConnected = true;
          this.reconnectAttempts = 0;
          logger.info('✅ WebSocket连接和认证成功');

          // 发送队列中的消息
          this.flushMessageQueue();

          this.emit('connected');
          resolve();
        });

        this.socket.on('message', (data) => {
          this.handleMessage(data);
        });

        this.socket.on('disconnect', (reason, details) => {
          this.isConnected = false;
          logger.warn(`⚠️ WebSocket连接断开: ${reason}`);
          if (details) {
            logger.debug('断开详情:', details);
          }
          this.emit('disconnected');

          // 自动重连
          this.attemptReconnect();
        });

        this.socket.on('connect_error', (error) => {
          logger.error(`❌ WebSocket连接失败: ${error.message}`);
          this.emit('error', error);
          reject(error);
        });

        this.socket.on('error', (error) => {
          logger.error(`❌ Socket错误: ${error.message || error}`);
          this.emit('error', error);
        });

        // 连接超时处理
        setTimeout(() => {
          if (!this.isConnected) {
            reject(new Error('WebSocket连接超时'));
          }
        }, this.options.timeout);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 发送消息
   */
  async sendMessage(command, data = {}) {
    return new Promise((resolve, reject) => {
      const requestId = ++this.requestId;
      const message = {
        id: requestId,
        command,
        data,
        timestamp: Date.now()
      };

      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error(`请求超时: ${command}`));
      }, this.options.timeout);

      // 保存请求回调
      this.pendingRequests.set(requestId, {
        resolve,
        reject,
        timeout,
        command
      });

      if (this.isConnected) {
        // 使用socket.io的emit方法发送消息
        this.socket.emit('message', message);
        logger.debug(`📤 发送消息: ${command}`, { requestId, data });
      } else {
        // 连接断开时加入队列
        this.messageQueue.push(message);
        logger.warn(`📋 消息加入队列: ${command}`);
      }
    });
  }

  /**
   * 调用微服务API
   */
  async callAPI(action, params = {}) {
    try {
      logger.debug(`🔗 调用API: ${action}`, params);

      const response = await this.sendMessage(action, params);

      if (response.code === 0) {
        logger.debug(`✅ API调用成功: ${action}`);
        return response;
      } else {
        const error = new Error(response.message || 'API调用失败');
        error.code = response.code;
        error.action = action;
        throw error;
      }
    } catch (error) {
      logger.error(`❌ API调用失败: ${action}`, error.message);
      throw error;
    }
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(rawData) {
    try {
      const message = JSON.parse(rawData.toString());
      logger.debug(`📥 接收消息:`, message);
      
      if (message.id && this.pendingRequests.has(message.id)) {
        const request = this.pendingRequests.get(message.id);
        clearTimeout(request.timeout);
        this.pendingRequests.delete(message.id);
        
        if (message.error) {
          request.reject(new Error(message.error));
        } else {
          request.resolve(message);
        }
      } else {
        // 主动推送的消息
        this.emit('message', message);
      }
    } catch (error) {
      logger.error('❌ 消息解析失败:', error);
    }
  }

  /**
   * 刷新消息队列
   */
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.socket.emit('message', message);
      logger.debug(`📤 发送队列消息: ${message.command}`);
    }
  }

  /**
   * 尝试重连
   */
  attemptReconnect() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      logger.error('❌ 达到最大重连次数，停止重连');
      return;
    }
    
    this.reconnectAttempts++;
    logger.info(`🔄 尝试重连 (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        logger.error('❌ 重连失败:', error);
      });
    }, this.options.reconnectInterval);
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;

    // 清理待处理的请求
    for (const [id, request] of this.pendingRequests) {
      clearTimeout(request.timeout);
      request.reject(new Error('连接已断开'));
    }
    this.pendingRequests.clear();

    logger.info('🔌 WebSocket连接已断开');
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      connected: this.isConnected,
      url: this.url,
      reconnectAttempts: this.reconnectAttempts,
      pendingRequests: this.pendingRequests.size,
      queuedMessages: this.messageQueue.length
    };
  }
}

module.exports = WebSocketManager;
