/**
 * 游戏客户端模拟器 - 主入口
 * 
 * 导出所有核心组件和工具
 */

// 核心组件
const GameClient = require('./core/game-client');
const VirtualPlayer = require('./core/virtual-player');
const WebSocketManager = require('./core/websocket-manager');
const SessionManager = require('./core/session-manager');
const ActionRegistry = require('./core/action-registry');
const BaseAction = require('./core/base-action');

// 工具组件
const ASTAPIScanner = require('./utils/ast-api-scanner');
const ScenarioLoader = require('./utils/scenario-loader');
const logger = require('./utils/logger');

// 配置管理
const configManager = require('./config/config-manager');

// 主要导出
module.exports = {
  // 核心类
  GameClient,
  VirtualPlayer,
  WebSocketManager,
  SessionManager,
  ActionRegistry,
  BaseAction,
  
  // 工具类
  ASTAPIScanner,
  <PERSON><PERSON><PERSON><PERSON>oa<PERSON>,
  logger,
  configManager,
  
  // 便捷方法
  createClient: (options = {}) => new GameClient(options),
  createPlayer: (client, playerData = {}) => new VirtualPlayer(client, playerData),
  
  // 版本信息
  version: require('../package.json').version,
  name: require('../package.json').name
};
