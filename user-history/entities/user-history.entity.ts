import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 全局用户统计接口
 */
export interface GlobalUserStats {
  totalPlayTime: number;                  // 总游戏时长(秒)
  totalLoginDays: number;                 // 总登录天数
  totalCharacters: number;                // 总角色数
  totalServersPlayed: number;             // 游戏过的区服数
  firstGameTime: Date;                    // 首次游戏时间
  totalRecharge: number;                  // 总充值金额
  vipLevel: number;                       // VIP等级
  lastLoginTime: Date;                    // 最后登录时间
}

/**
 * 区服用户统计接口
 */
export interface ServerUserStats {
  playTime: number;                       // 该区服游戏时长(秒)
  loginDays: number;                      // 该区服登录天数
  characterCount: number;                 // 该区服角色数
  maxLevel: number;                       // 最高等级
  maxPower: number;                       // 最高战力
  rechargeAmount: number;                 // 该区服充值金额
  lastRankingPosition?: number;           // 最后排名位置
  totalBattles: number;                   // 总战斗次数
  winRate: number;                        // 胜率
}

/**
 * 角色摘要信息 (轻量级，用于历史记录)
 */
@Schema({ _id: false })
export class CharacterSummary {
  @ApiProperty({ description: '角色ID' })
  @Prop({ required: true })
  characterId: string;

  @ApiProperty({ description: '角色名' })
  @Prop({ required: true })
  name: string;

  @ApiProperty({ description: '等级' })
  @Prop({ default: 1 })
  level: number;

  @ApiProperty({ description: '战力' })
  @Prop({ default: 0 })
  power: number;

  @ApiProperty({ description: '头像' })
  @Prop({ required: false })
  avatar?: string;

  @ApiProperty({ description: '职业/位置' })
  @Prop({ required: false })
  profession?: string;

  @ApiProperty({ description: '创建时间' })
  @Prop({ default: Date.now })
  createdAt: Date;

  @ApiProperty({ description: '最后活跃时间' })
  @Prop({ default: Date.now })
  lastActiveAt: Date;

  @ApiProperty({ description: '是否为主角色' })
  @Prop({ default: false })
  isMain: boolean;

  @ApiProperty({ description: '状态' })
  @Prop({ default: 'active', enum: ['active', 'deleted'] })
  status: 'active' | 'deleted';

  @ApiProperty({ description: '总游戏时长(秒)' })
  @Prop({ default: 0 })
  totalPlayTime: number;

  @ApiProperty({ description: '最后登录时间' })
  @Prop({ required: false })
  lastLoginTime?: Date;
}

export const CharacterSummarySchema = SchemaFactory.createForClass(CharacterSummary);

/**
 * 区服历史记录项
 */
@Schema({ _id: false })
export class ServerHistoryItem {
  @ApiProperty({ description: '区服ID' })
  @Prop({ required: true, index: true })
  serverId: string;

  @ApiProperty({ description: '区服名称' })
  @Prop({ required: true })
  serverName: string;

  @ApiProperty({ description: '首次登录时间' })
  @Prop({ required: true })
  firstLoginTime: Date;

  @ApiProperty({ description: '最后登录时间' })
  @Prop({ required: true })
  lastLoginTime: Date;

  @ApiProperty({ description: '总游戏时长(秒)' })
  @Prop({ default: 0 })
  totalPlayTime: number;

  @ApiProperty({ description: '登录次数' })
  @Prop({ default: 0 })
  loginCount: number;

  @ApiProperty({ description: '该区服的角色摘要' })
  @Prop({ type: [CharacterSummarySchema], default: [] })
  characters: CharacterSummary[];

  @ApiProperty({ description: '区服内统计' })
  @Prop({ type: Object, default: {} })
  serverStats: ServerUserStats;

  @ApiProperty({ description: '区服内成就' })
  @Prop({ type: [String], default: [] })
  serverAchievements: string[];

  @ApiProperty({ description: '状态' })
  @Prop({ default: 'active', enum: ['active', 'migrated', 'deleted', 'merged'] })
  status: 'active' | 'migrated' | 'deleted' | 'merged';

  @ApiProperty({ description: '推荐分数' })
  @Prop({ default: 0 })
  recommendationScore: number;

  @ApiProperty({ description: '合服历史记录' })
  @Prop({ type: [Object], default: [] })
  mergeHistory: Array<{
    fromServerId: string;
    toServerId: string;
    mergeTime: Date;
    dataTransferred: boolean;
  }>;

  @ApiProperty({ description: '数据迁移记录' })
  @Prop({ type: [Object], default: [] })
  migrationHistory: Array<{
    fromServerId: string;
    toServerId: string;
    migrationType: 'manual' | 'auto' | 'merge';
    migrationTime: Date;
    status: 'pending' | 'completed' | 'failed';
    reason?: string;
  }>;
}

export const ServerHistoryItemSchema = SchemaFactory.createForClass(ServerHistoryItem);

export interface UserHistoryDocument extends Omit<UserHistory, 'id'>, Document {
  getServerHistory(serverId: string): ServerHistoryItem | undefined;
  updateServerHistory(serverId: string, updates: Partial<ServerHistoryItem>): void;
  updateCharacterSummary(serverId: string, characterId: string, updates: Partial<CharacterSummary>): void;
}

/**
 * 用户全局历史记录表
 * 跨区服的用户基础信息和历史记录
 */
@Schema({
  timestamps: true,
  collection: 'user_histories',
})
export class UserHistory {
  @ApiProperty({ description: '用户ID' })
  id?: string;

  @ApiProperty({ description: '用户ID (主键)' })
  @Prop({ 
    required: true, 
    unique: true, 
    index: true,
    comment: '用户唯一标识'
  })
  userId: string;

  @ApiProperty({ description: '用户名' })
  @Prop({ 
    required: true,
    comment: '用户名'
  })
  username: string;

  @ApiProperty({ description: '邮箱' })
  @Prop({ 
    required: true,
    comment: '用户邮箱'
  })
  email: string;

  @ApiProperty({ description: '最后游戏的区服ID' })
  @Prop({ 
    required: false,
    index: true,
    comment: '最后游戏的区服ID'
  })
  lastServerId?: string;

  @ApiProperty({ description: '最后使用的角色ID' })
  @Prop({ 
    required: false,
    comment: '最后使用的角色ID'
  })
  lastCharacterId?: string;

  @ApiProperty({ description: '区服历史记录' })
  @Prop({ 
    type: [ServerHistoryItemSchema], 
    default: [],
    comment: '用户在各个区服的历史记录'
  })
  serverHistory: ServerHistoryItem[];

  @ApiProperty({ description: '全局统计数据' })
  @Prop({ 
    type: Object, 
    default: {},
    comment: '用户的全局统计数据'
  })
  globalStats: GlobalUserStats;

  @ApiProperty({ description: '跨服成就' })
  @Prop({ 
    type: [String], 
    default: [],
    comment: '用户获得的跨服成就'
  })
  globalAchievements: string[];

  @ApiProperty({ description: '用户偏好设置' })
  @Prop({ 
    type: Object, 
    default: {},
    comment: '用户的游戏偏好设置'
  })
  preferences: Record<string, any>;

  @ApiProperty({ description: '创建时间' })
  createdAt?: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt?: Date;
}

export const UserHistorySchema = SchemaFactory.createForClass(UserHistory);

// 创建索引
UserHistorySchema.index({ userId: 1 }, { unique: true });
UserHistorySchema.index({ lastServerId: 1 });
UserHistorySchema.index({ 'serverHistory.serverId': 1 });
UserHistorySchema.index({ 'globalStats.lastLoginTime': -1 });
UserHistorySchema.index({ updatedAt: -1 });

// 虚拟字段
UserHistorySchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// 确保虚拟字段被序列化
UserHistorySchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

// 静态方法：查找用户历史
UserHistorySchema.statics.findByUserId = function(userId: string) {
  return this.findOne({ userId }).exec();
};

// 静态方法：查找活跃用户
UserHistorySchema.statics.findActiveUsers = function(days: number = 7) {
  const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
  return this.find({
    'globalStats.lastLoginTime': { $gte: cutoffDate }
  }).sort({ 'globalStats.lastLoginTime': -1 }).exec();
};

// 实例方法：获取区服历史
UserHistorySchema.methods.getServerHistory = function(serverId: string) {
  return this.serverHistory.find(item => item.serverId === serverId);
};

// 实例方法：添加或更新区服历史
UserHistorySchema.methods.updateServerHistory = function(serverId: string, updates: Partial<ServerHistoryItem>) {
  const existingIndex = this.serverHistory.findIndex(item => item.serverId === serverId);
  
  if (existingIndex >= 0) {
    // 更新现有记录
    Object.assign(this.serverHistory[existingIndex], updates);
  } else {
    // 添加新记录
    this.serverHistory.push({
      serverId,
      serverName: updates.serverName || serverId,
      firstLoginTime: new Date(),
      lastLoginTime: new Date(),
      totalPlayTime: 0,
      loginCount: 0,
      characters: [],
      serverStats: {
        playTime: 0,
        loginDays: 0,
        characterCount: 0,
        maxLevel: 0,
        maxPower: 0,
        rechargeAmount: 0,
        totalBattles: 0,
        winRate: 0,
      },
      serverAchievements: [],
      status: 'active',
      recommendationScore: 0,
      ...updates,
    });
  }
  
  // 按最后登录时间排序
  this.serverHistory.sort((a, b) => b.lastLoginTime.getTime() - a.lastLoginTime.getTime());
  
  // 只保留最近20个区服记录
  if (this.serverHistory.length > 20) {
    this.serverHistory = this.serverHistory.slice(0, 20);
  }
};

// 实例方法：更新角色摘要
UserHistorySchema.methods.updateCharacterSummary = function(
  serverId: string, 
  characterId: string, 
  updates: Partial<CharacterSummary>
) {
  const serverHistory = this.getServerHistory(serverId);
  if (!serverHistory) return;
  
  const existingIndex = serverHistory.characters.findIndex(char => char.characterId === characterId);
  
  if (existingIndex >= 0) {
    // 更新现有角色
    Object.assign(serverHistory.characters[existingIndex], updates);
  } else {
    // 添加新角色
    serverHistory.characters.push({
      characterId,
      name: updates.name || 'Unknown',
      level: updates.level || 1,
      power: updates.power || 0,
      avatar: updates.avatar,
      profession: updates.profession,
      createdAt: new Date(),
      lastActiveAt: new Date(),
      isMain: updates.isMain || false,
      status: 'active',
      totalPlayTime: 0,
      ...updates,
    });
  }
  
  // 按最后活跃时间排序
  serverHistory.characters.sort((a, b) => b.lastActiveAt.getTime() - a.lastActiveAt.getTime());
};
