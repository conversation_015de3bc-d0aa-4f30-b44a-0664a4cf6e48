module.exports = {
	GM_COMMAND: {
		MODE: 1,
	},
	CREATE_ROLE_STEP: {
		WATCH_RECORD:  1,       //观看比赛
		MATURE_TYPE: 2,         //选择新手和老手
		GET_NEW_BALLER: 3,      //获得新球员
		FINISHED_STEP: 0,       //完成状态
	},
	CREATE_ROLE_QUALIFIED: {
		NEWER: 1,               //新手
		OLDER: 2,               //老手
	},
	CREATE_INIT_BALLER_MODE: {
		NONE: 0,                //配置表填写错误
		INIT_BALLER: 1,         //初始球员
		NEWER_GIVE: 2,          //新手给与的球员
		OLDER_GIVE: 3,          //老手给与的球员
	},
	ONE_LEVEL_ATTR_NAMES : {
		Speed: 1,                   //速度
		Jumping: 2,                 //弹跳
		Strength: 3,                //力量
		Stamina: 4,                 //耐力
		Finishing: 5,               //射门
		Dribbling: 6,               //盘带
		Passing: 7,                 //传球
		Heading: 8,                 //头球
		StandingTackle: 9,          //抢断
		SlidingTackle: 10,          //铲球
		LongPassing: 11,            //长传
		LongShots: 12,              //远射
		Penalties: 13,              //点球
		CornerKick: 14,             //角球
		FreeKick: 15,               //任意球
		ExplosiveForce: 16,         //爆发力
		Attack: 17,                 //出击
		Volleys: 18,                //制空
		Save: 19,                   //扑救
		ResistanceDamage: 20,       //抗伤性
	},
	ONE_LEVEL_ATTR_INDEX_TO_NAMES : [
		"Default",
		"Speed",
		"Jumping",
		"Strength",
		"Stamina",
		"Finishing",
		"Dribbling",
		"Passing",
		"Heading",
		"StandingTackle",
		"SlidingTackle",
		"LongPassing",
		"LongShots",
		"Penalties",
		"CornerKick",
		"FreeKick",
		"ExplosiveForce",
		"Attack",
		"Volleys",
		"Save",
		"ResistanceDamage",
		"AllAttr"					//所有属性
	],
	TWO_LEVEL_ATTR_NAMES : {
		BallerRating: 1,        	//球员实力
	},
	TEAM_FORMATION_CONFIG_POSITION_TO_ID : {
		"GK": 1,//Position => [PositionID]
		"DC": 2,
		"DL": 3,
		"DR": 4,
		"DM": 5,
		"MC": 6,
		"ML": 7,
		"MR": 8,
		"AM": 9,
		"ST": 10,
		"WL": 11,
		"WR": 12
	},
	BATTLE_ATTACK_MODE : {
		"Default": 0,
		"Heading" : 1,		//头球
		"LongShots" : 2,	//远射
		"PushShots" : 3,	//推射
		"RushPosition" : 4,	//抢点
		"LobShots" : 5,		//吊射
		"OneOnOne" : 6,		//单刀
		"FreeKick" : 7,		//任意球
		"CornerKick" : 8,	//角球
		"PenaltyKick" : 9,	//点球
		"AllMode": 10		//所有方式
	},
	BATTLE_TYPE : {
		PveLeagueCopy : 1,		//推图PVE
		PveTrophy : 2,			//杯赛PVE
		PvpMatch : 3,			//商业赛PVP
		PvpLeague : 4,			//联赛PVP
		PveWorldCup : 5,		//世界杯PVE
		FirstBattle : 6,		//第一场战斗
		PvpDqCupMatch: 8,		//懂球杯
		MiddleEastCup: 9,		//中东杯PVE
		PveWorldBoss: 10,       //世界boss
		GulfCup: 11,			//海湾杯
		MLS: 12,					//美职联
		PvpDqdRankActivity: 13,		//懂球帝外站排名活动战斗PVP
		chairman: 14,			//主席争夺战PVP
		PvpWarOfFaith: 15,        //信仰之战
		peak: 16,				//信仰巅峰赛
		PvpGroundMatch: 17				//个人球场争夺战
    },
	BATTLE_TEAM_TYPE : {
		Player : 1,		//玩家
		Enemy : 2,		//敌人
		Robot : 3,		//机器人
		Test : 4,		//用于测试
		NoneAccount: 5	//白板账号
	},
	BATTLE_SKILL_INTER_TYPE : {
		InstantSkill : 1,
		DurativeSkill : 2,
		NextAtkSkill : 3
	},
	BATTLE_SKILL_TYPE :{
		Active : 0,			//激活
		Passive : 1,		//被动
		Trigger : 2,		//几率触发
	},
	BATTLE_SKILL_CALC_STATE : {
		Default : 1,	//初始状态
		Trigger : 2		//触发
	},
	BATTLE_SKILL_EFFECT_TYPE: {
		SuccessPer: 1,	//增加突破/射门成功率
		AttrPer: 2,		//增加属性百分比
		NextAtkPenaltyKick: 3,	//下次攻击点球成功率
		NextAtkEnterAtk: 4		//下次进攻参与进攻百分比
	},
	LEAGUE_COPY: {
		LeagueIsTakeLeagueReward: 1,   //联赛奖励
		LeagueIsAllCopyPassed: 2,      //全部副本通过
		LeagueCopyProcess: 3,          //副本进度
		LeagueCopyTakeCopyRewardProcess: 4, //是否领取副本奖励
		LeagueCopyIsFinshed: 5,        //是否完成三星挑战
	},
	TABLE_SYSTEM_PARAM: {
		maxMoraleSlot : 1,
		maxBattleTime : 2,
		battlePerFactor : 3,
		MaxBattlePer : 4,
		MinBattlePer : 5,
		LeagueSeason : 115,					//赛季天数
		DELAY_DAY: 119,                     //生涯延长天数
		PEAK_INITIAL: 144,					//巅峰战各队伍系统初始下注额
		PEAK_RATIO: 145,					//巅峰战中奖玩家分配比
		PEAK_CHAIRMAN_RATIO: 147,			//巅峰赛冠军分配比
		maxMailCount: 1000,
		maxExprideMinute : 1001,
		ContinuedRecoverTime: 1105,    //口香糖多久恢复精力
		ContinuedRecoverEnergyCount: 1106, //恢复几点精力
		ContinuedRecoverDuration: 1107,    //持续时长
		matchActualStrengthDiffValueOne: 1111,
		matchActualStrengthDiffValueTwo: 1112,
		matchActualStrengthDiffValueThrid: 1113,
		matchActualStrengthDiffValueFour: 1114,
		matchActualStrengthDiffValueFive: 1115,
		matchActualStrengthDiffValueSix: 1116,
		matchEveryDayFightTimes: 1117,	//商业赛每日被动挑战
		matchSearchCost: 1118,
		matchRewardRatio: 1119, //商业赛欧元奖励系数(百分比)
		matchWinRatio: 1120,    //商业赛胜利百分比
		matchLoseRatio: 1121,   //商业赛失败百分比
		matchDrawRatio: 1122,   //商业赛平局百分比
		matchFreeFightTimes: 1123,//商业赛每天免费挑战次数
		matchBuyFightTimes: 1124,//商业赛每天可购买挑战次数
		matchBuyGold: 1125,//商业赛每次购买花费懂币数
		matchExpendEnery: 1126,//商业赛每次挑战消耗精力值
		matchFlushcash: 1127,//商业赛每次重新匹配花费欧元数
		RELAY_GOLD: 1165, //转播花费球币
		BATTLE_SKIP_GOLD: 1303,			//比赛跳过卡耗费的懂币
		DQ_MATCH_ENROLL_COST: 1306,		//懂球杯报名消耗精力
		middleEastCup_everyday_countMax: 1308,	//中东杯每天挑战次数上限
		middleEastCup_round2_Gold: 1309,	//中东杯第二轮花费球币
		middleEastCup_more_Gold: 1310,	//中东杯每天第3~第10次挑战所需球币
		middleEastCup_Battle_energy: 1311,	//中东杯比赛消耗精力
		middleEastCup_round1_bonus: 1312,	//中东杯第一轮奖励欧元数
		middleEastCup_round1_added: 1313,	//中东杯第一轮额外奖励欧元数
		middleEastCup_round2_bonus: 1314,	//中东杯第二轮奖励欧元数
		middleEastCup_round2_added: 1315,	//中东杯第二轮额外奖励欧元数
		middleEastCup_more_bonus: 1316,		//中东杯3-10奖励欧元数
		middleEastCup_more_added: 1317,		//中东杯3-10额外奖励欧元数
		middleEastCup_lucky_max: 1318,		//中东杯幸运值上限
		middleEastCup_lucky_add: 1319,		//中东杯每场比赛增加的幸运值
		middleEastCup_lucky_empty: 1320,	//中东杯幸运没满是掉落概率
		middleEastCup_lucky_full: 1321,		//中东杯幸运值满掉落概率
		gulfCup_everyday_countMax: 1400,	//海湾杯每天挑战次数上限
		gulfCup_round2_Gold: 1401,	//海湾杯第二轮花费球币
		gulfCup_more_Gold: 1402,	//海湾杯每天第3~第10次挑战所需球币
		gulfCup_Battle_energy: 1403,//海湾杯比赛消耗精力
		gulfCup_round1_bonus: 1404,	//海湾杯第一轮奖励物品id
		gulfCup_round1_added: 1405,	//海湾杯第一轮额外奖励物品id
		gulfCup_round2_bonus: 1406,	//海湾杯第二轮奖励物品id
		gulfCup_round2_added: 1407,	//海湾杯第二轮额外奖励物品id
		gulfCup_more_bonus: 1408,	//海湾杯3-10奖励物品id
		gulfCup_more_added: 1409,	//海湾杯3-10额外奖励物品id
		gulfCup_lucky_max: 1410,	//海湾杯幸运值上限
		gulfCup_lucky_add: 1411,	//海湾杯每场比赛增加的幸运值
		gulfCup_lucky_empty: 1412,	//海湾杯幸运没满是掉落概率
		gulfCup_lucky_full: 1413,	//海湾杯幸运值满掉落概率
		MLS_round1_bonus: 1414,	//美职联第一轮奖励物品id
		MLS_round1_added: 1415,	//美职联第一轮额外奖励物品id
		MLS_round2_bonus: 1416,	//美职联第二轮奖励物品id
		MLS_round2_added: 1417,	//美职联第二轮额外奖励物品id
		MLS_more_bonus: 1418,	//美职联3-5奖励物品id
		MLS_more_added: 1419,	//美职联3-5额外奖励物品id
		MLS_everyday_countMax: 1420,	//美职联每天挑战次数上限
		MLS_round2_Gold: 1421,	//美职联第二轮花费球币
		MLS_more_Gold: 1422,	//美职联每天第3~第10次挑战所需球币
		MLS_Battle_energy: 1423,//美职联比赛消耗精力
		EVERY_DAY_TAKE_ENERGY_VALUE: 3022, 		   //免费领取的精力值
		EVERY_DAY_TAKE_ENERGY_LAKE_COST: 3023, 	   //补领的金币
		EVERY_DAY_TAKE_ENERGY_START_MIDDAY: 3025,  //第1次免费领取精力时段（开始）
		EVERY_DAY_TAKE_ENERGY_END_MIDDY: 3026, 	   //第1次免费领取精力时段（结束）
		EVERY_DAY_TAKE_ENERGY_START_EVENING: 3027, //第2次免费领取精力时段（开始）
		EVERY_DAY_TAKE_ENERGY_END_EVENING: 3028,   //第2次免费领取精力时段（结束)
		TrophyLimitType: 4000,  //杯赛次数限制类型
		WorldChatItem: 5001,	//世界聊天消耗物品ID
		FollowMaxCount: 5002,   //关注上限
		FansMaxCount: 5003,     //粉丝上限
		FollowMaxResult: 5004,  //关注分页大小
		HornChatItem: 5005,		//广播喇叭消耗物品ID
		FreeChatTimes: 5006,	//每日免费聊天次数
		UNLOCK_GOLD_COACH_LEVEL_PRICE: 9008, //金牌教练升级所需金币
		BATTLE_SKIP_ITEM: 9009,			//战斗跳过物品(战斗跳过卡)
		SLOTS_ONE_MONEY: 9100,			//拉霸一次消耗数量
		SLOTS_MUCH_MONEY: 9101,			//拉霸十一次消耗数量
		SLOTS_FREE_NUM: 9102,			//拉霸每日免费次数
		SLOTS_GUARANTEE_NUM: 9103,		//拉霸获得保底次数
		SLOTS_GUARANTEE_MONEY: 9104,	//拉霸获得保底金额
		Turntable_king: 9013,			//老虎机大王权重
		Turntable_queen:	9014,		//老虎机小王权重
		GroundMatchCostEnergy: 10000,		//每次占领花费精力
		GroundMatchCostCash: 10001,		//抢夺手续费（欧元）
		GroundMatchReportInverval: 10002,	//每次举报间隔时长（秒）
		GroundMatchLeftInverval: 10003,		//撤离所需最短占领时长（秒）
		GroundMatchRobMaxTime: 10004,		//每次最长占领时长
		GroundMatchMaxRecordNum: 10005,		//争夺记录的最大记录条数
		GroundMatchMaxReportNum: 10006,		//每天举报次数上限
		GroundMatchRefreshHour: 10007,		//每天排行榜刷新时间
		GroundMatchMaxReward: 10008,		//单次满8小时占领奖励欧元上限
		GroundMatchFansChangeTime: 10009,	//粉丝变化所需占领时长（秒）
		GroundMatchFansChangeNum: 10010,	//每次粉丝变化数
		beliefShopAlterMax: 10100,			//信仰商店修改定价次数上限3
		GroundMatchSearchCash: 10011,	      //每次搜索费用（欧元）
		GroundMatchSystemProtectTime: 10012,  //系统保护时间（秒）
		GroundMatchReportReward: 10013,		  //每次举报获得的欧元
		GroundMatchBeliefNumInverval: 10014,		  //信仰积分所需占领时长（秒）
		ResetDoubleRechargeDate: 20000			//双倍充值每月充值日期
	},
	BagBookMarkUnlockState: {
		LOCK: 0,
		UNLOCK: 1,
	},
	BagUseItemTypeEnum: {
		USE_ITEM_MAIN_PROPERTY_EFFECT_CURRENCY: 0,  //货币
		USE_ITEM_MAIN_PROPERTY_EFFECT_CONSUME: 1,   //消耗品
		USE_ITEM_MAIN_PROPERTY_EFFECT_GIFT_BAG : 2, //礼包
		USE_ITEM_MAIN_PROPERTY_EFFECT_HERO_CARD : 3, //球员卡
		USE_ITEM_MAIN_PROPERTY_EFFECT_HERO_CONTRACT : 4, //球员合约
	},
	BagUseItemSubTypeEnum: {
		USE_ITEM_SUB_PROPERTY_EFFECT_NONE: 0, 			//除了货币类型外，这个子类型都不处理
		USE_ITEM_SUB_PROPERTY_EFFECT_HERO_CARD: 1,  	//球员碎片
		USE_ITEM_SUB_PROPERTY_EFFECT_FAME: 2,  	//声望
		USE_ITEM_SUB_PROPERTY_EFFECT_ENERGY: 3,	//精力
		USE_ITEM_SUB_PROPERTY_EFFECT_CASH: 4,  	//欧元
		USE_ITEM_SUB_PROPERTY_EFFECT_TRAIN_COUNT: 5,  //训练点
		USE_ITEM_SUB_PROPERTY_EFFECT_CONTINUED_BUFF: 6, //持续buff
		USE_ITEM_SUB_PROPERTY_EFFECT_SCOUT_ENERGY: 7, //球探体力
		USE_ITEM_SUB_PROPERTY_EFFECT_YONGER: 9,			//青春永驻药剂
		USE_ITEM_SUB_PROPERTY_EFFECT_WORLD_BOSS_COIN: 10,			//世界boss币
		USE_ITEM_SUB_PROPERTY_EFFECT_INTEGRAL_COIN: 11,			//转播积分
		USE_ITEM_SUB_PROPERTY_EFFECT_HONOR: 12,			//教练荣誉
		USE_ITEM_SUB_PROPERTY_BELIEF_INTEGRAL: 13			//教练荣誉
	},
	BagUseItemGiftPackageType: {
		FIXED: 1,   //固定礼包
		WEIGHTS: 2, //权重
		LOOT: 1,    //掉落礼包
		BELIEF: 2   //信仰礼包
	},
	BagNotifyMail: {
		NONE: 0,
		BAG_FULL: 1,
	},
	BAG_BOOKMARK_TYPE: {
		CAN_USE_ITEM: 1, //消耗品
		GIFT_PACK: 2,    //礼包
		HERO_CARD: 3,    //球员卡
		ALL_ITEM: 4,     //所有东西
	},
	GiftPackageRewardType: { //礼包赠送类型 
		ITEM: 1,   //物品
		HERO:2	   //球员
	},
	CommonFunctionCmdId: {
		INIT_MAIL_LIST: 1,
		ADD_ROBOT: 2,
	},
	MailType: {
		SYSMAIL: 1, 			//系统邮件
		ACTIVITY_REWARD: 2,  	//奖励邮件
		PLAY_RECORD: 3,          //录像回放邮件
	},
	 MailExpiredDelType: {
		DIRECT: 0,          //默认规则 只领取，不删除
		TAKE_ATTACH: 1,     //领取后删除
		PARAM1: 2,	        //参数1
		PARAM2: 3           //参数2
	},
	BagPageMarkType: {      //修改这里的值，请和策划确认页签类型
		NORMAL_ITEM: 1, 	//消耗品 
		GIFT_ITEM: 2,       //礼包
		HERO: 3,            //球员页签
	},
	MAIL_TRANSLATE_CONTENT: {
		LEAGUE_COPY_REWARD: 1,
		LEAGUE_ALL_PASSED_REWARD: 2,
		BAG_IS_FULL: 3,
		EXCHANGE_REWARD: 4,   //兑换奖励
		TASK_REWARD:5,        //任务奖励
		ENROLL_SUCCESS: 7,
		ENROLL_FAILED: 8,
		CommunityNotifyWin: 9,
		CommunityNotifyLost: 10,
		CommunityPromotion: 11,
		NormalNotifyWin: 12,
		NormalNotifyLost: 13,
		NormalPromotion: 14,
		KnockOutNotifyWin: 15,
		KnockOutNotifyLost: 16,
		ProfessionPromotion: 17,
		ProfessionFailed: 18,
		ProfessionNotifyWin: 19,
		ProfessionNotifyLost: 20,
		CommunityReward: 21,
		NormalReward: 22,
		PROFESSION_REWARD: 23,
		Result_Is_Null: 24,    //轮空
		RESULT_DRAW: 25,       //平局
		PROFESSION_ENROLL: 26, //专业联赛报名通知
		STORE_REWARD:27,       //商城奖励
		SEASON_STORE: 28,      //赛季商城
		MONTH_CARD: 29,        //月卡
		LIMIT_STORE: 30,       //限时商城
		ACT_AWARD_MAIL: 31,    //活动奖励邮件
		WORLD_CUP: 32,     //世界杯
		PROFESSION_ENROLL_START: 33, //懂联赛报名
		PROFESSION_DIRECT_START: 34, //懂联赛开始
		National_Day: 35,//国庆活动
		BUSSINESS_MATCH_PASSIVE_WIN: 36,		//被动商业赛-胜
		BUSSINESS_MATCH_PASSIVE_LOST: 37,		//被动商业赛-负
		BUSSINESS_MATCH_PASSIVE_DRAW: 38,		//被动商业赛-平
		BUSSINESS_RANK_AWARD: 39,		//商业赛排行奖励
		DQ_CUP_REWARD: 40,				//懂球杯奖励
		WORLD_BOSS_JOIN:41,           //世界boss参与奖励
		WORLD_BOSS_KILL:42,           //世界boss击杀奖励
		WORLD_BOSS_RANK:43,           //世界boss排名奖励
		BUSSINESS_MATCH_WIN: 44,		//主动挑战商业赛-胜
		BUSSINESS_MATCH_LOST: 45,		//主动挑战商业赛-负
		BUSSINESS_MATCH_DRAW: 46,		//主动挑战商业赛-平
		RELAY_TASKS: 47,				//赛事转播成就奖励
		RELAY_SCHEDULE: 48,				//赛事转播福利奖励
		BEST_FOOTBALL: 49,              //最佳11人
		BEST_FOOTBALL_REWARD: 50,        //最佳11人奖励已送达
		ENERGY_FEEDBACK: 51,              //精力回馈
		REGRESSION: 52,                    //赛季返场
		TURNTABLE: 53,					//老虎机
		GULFCUP: 54,					//海湾杯
		MLS: 55,						//美职联
		MIDDLECUP: 56,					//中东杯
		GULFCUP_FAIL: 58,					//海湾杯失败
		MLS_FAIL: 59,						//美职联失败
		CHIEF_BATTLE_ELECTION: 61,			//首席争夺战当选奖励
		CHIEF_BATTLE_REWARD: 62, 			//首席争夺战奖励
		CAMPAIGN_RETURN: 63, 			//竞选返还
		GROUND_MATCH_REPORT_OWNER: 64,			//球场举报信提醒
		GROUND_MATCH_REPORT_OCCUPIER: 65,		//球场被举报提醒
		GROUND_MATCH_REPORT_REPORTER: 66,		//球场举报奖励
		PEAK_MATCH_OPEN: 67,			//巅峰赛开赛
		PEAK_MATCH_ROUND_WIN: 68,		//巅峰战晋级
		PEAK_MATCH_ROUND_LOSE: 69,		//巅峰战晋级失败
		PEAK_MATCH_CHAMPION: 70,		//巅峰战夺得冠军
		PEAK_MATCH_SECOND: 71,			//巅峰战决赛失败
		PEAK_MATCH_RANK_GUESS_AWARD: 72,	//巅峰战冠军竞猜奖池分成
		PEAK_MATCH_RANK_GUESS_WIN: 75,		//巅峰战竞猜成功
		PEAK_MATCH_RANK_GUESS_LOSE: 76,		//巅峰战竞猜失败
		GROUND_MATCH_OCCUPY_REWARD: 77,		//个人球场占领奖励
		GROUND_MATCH_BE_OCCUPIED: 78,		//个人球场被占领损失
		GROUND_MATCH_ROB_REWARD: 79,		//球场抢夺成功奖励
		GROUND_MATCH_BE_ROBBED: 80,			//球场占领被抢夺提醒
		GROUND_MATCH_FIELD_CHANGE: 81,		//球场占领变更提醒
		GROUND_MATCH_DRIVE_AWAY: 82,		//占领被驱赶提醒
		GROUND_MATCH_DRIVE_SUCCESS: 83,		//驱赶成功提醒
		SLOTS_REWARD: 84,					//拉霸奖励邮件
		PEAK_MATCH_ONE_ROUND_LOSE: 85,		//巅峰战首轮晋级失败
	},
	LoginState : {
		INIT : 1,     //初始状态
		AUTH : 2,     //通过账号验证 (此状态暂时不需要同步)
		LOGIN : 3     //登录成功
	},
	BattleState : {
		INIT : 1,				//初始状态
		LOGIN_BATTLE : 2,		//登录战斗服
		BATTLE : 3				//战斗
	}, 
	MAIL_TAKE_STATE: {         //邮件阅读状态
		NOT_OPEN: 0,           //未阅
		ALREADY_OPEN: 1        //已阅
	},
	MAIL_ITEM_TYPE: {          //邮件附件类型
		ITEM: 0,			   //物品
		HERO: 1,			   //球员
		CURRENCY:2              //货币 (只用于玩家经验，其他走物品)
	},
	FILTER_ROUTE_LOG:[
		"getHeroList",
		"updateHeros",
		"PushFuncSwitch",
		"createRole",
		//"testBattle",
		"teamFormationExchangeHero",
		"CSGetMailList",
		"getAllTaskList",
		"getLeagueCopyData",
		//"getTeamFormationList",
		"getChatMsgList",
		"getActList",
		"updateActList",
	],
	LEAGUE_ENROLL_STATUS: {
		ENROLL_BEGIN: 0,         //报名未开始
		ENROLL_RUNNING: 1,       //正在进行
		ENROLL_OVER: 2           //结束 
	},
	LEAGUE_TYPE_ID: {
		// COMMUNITY: 1,          //社区赛(海选)
		// NORMAL: 2,             //常规赛
		// KNOCKOUT: 3,    	   //淘汰赛
		// AMATEUR: 4, 		   //业余
		// LEAGUE_SECOND: 5,      //乙级
		// LEAGUE_FIRST: 6,       //甲级
		// CHAMPIONS: 7,          //冠军
		// SUPER: 8,              //超级
		SUPER: 1,              //超级
		CHAMPIONS: 2,          //冠军
		LEAGUE_FIRST: 3,       //甲级
		LEAGUE_SECOND: 4,      //乙级
		AMATEUR: 5, 		   //业余
		// KNOCKOUT: 6,    	   //淘汰赛
		PREPARE: 6,			//预备联赛
		// NORMAL: 7,             //常规赛
		//因为前面还有16信仰预备联赛联赛 6 - 21
		COMMUNITY: 8,          //社区赛(海选)
		PROFESSION: 9
	},
	LEAGUE_RUN_TYPE_ID: {
		COMMUNITY: 8,          //预选赛(海选)
		NORMAL: 7,             //常规赛
		KNOCKOUT: 6,    	   //淘汰赛
		PROFESSION: 5          //专业联赛
	},
	LEAGUE_NAME: {
		COMMUNITY: "预选",
		// NORMAL:    "常规",
		// KNOCKOUT:  "淘汰赛",
		PREPARE: "预备",
		AMATEUR:   "业余", 		   //业余
		LEAGUE_SECOND: "乙级",      //乙级
		LEAGUE_FIRST:  "甲级",       //甲级
		CHAMPIONS: "冠军",          //冠军
		SUPER: "超级",              //超级
	},
	TEAM_TYPE_NAME:[
		"一",
		"二",
		"三",
		"四",
		"五",
		"六",
		"七",
		"八",
		"九",
		"十",
		"十一",
		"十二",
		"十三",
		"十四",
		"十五",
	],
	TEAM_TACTICS_LIST:{
		"1":101,
		"2":201,
		"3":301,
		"4":401,
		"5":501,
		"6":601,
		"7":701,
		"8":801,
		"9":901
	},
	TEAM_DEF_TACTICS_LIST:{
		"11":1101,
		"12":1201,
		"13":1301,
		"14":1401,
		"15":1501,
		"16":1601,
		"17":1701,
		"18":1801
	},
	LEAGUE_RUN_ROUND: {
		CommunityAudition: 0,
		CommunityGroup64to32: 1,
		CommunityGroup32to16: 2,
		CommunityGroup16to8: 3,
		CommunityGroup8to4: 4,

		NormalGroupSplitPrepare: 0,
		NormalGroup64to32: 1,
		NormalGroup32to16: 2,
		NormalGroup16to8: 3,
		NormalGroup8to4: 4,
		NormalGroup4to2: 5,
		NormalKKT: 6,

		KnockoutGroupSplitPrepare: 0,
		KnockoutGroup32to16: 1,
		ProfessionGroupSplitPrepare: 0,
		ProfessionGroup1: 1,
		ProfessionGroup2: 2,
		ProfessionGroup3: 3,
		ProfessionGroup4: 4,
		ProfessionGroup5: 5,
		ProfessionGroup6: 6,
		ProfessionGroup7: 7,
		ProfessionGroup8: 8,
		ProfessionGroup9: 9,
		ProfessionGroup10: 10,

		ProfessionGroup11: 11,
		ProfessionGroup12: 12,
		ProfessionGroup13: 13,
		ProfessionGroup14: 14,
		ProfessionGroup15: 15,
		ProfessionGroup16: 16,
		ProfessionGroup17: 17,
		ProfessionGroup18: 18,
		ProfessionGroup19: 19,
		ProfessionGroup20: 20,

		ProfessionGroup21: 21,
		ProfessionGroup22: 22,
		ProfessionGroup23: 23,
		ProfessionGroup24: 24,
		ProfessionGroup25: 25,
		ProfessionGroup26: 26,
		ProfessionGroup27: 27,
		ProfessionGroup28: 28,
		ProfessionGroup29: 29,
		ProfessionGroup30: 30,

		ProfessionGroup31: 31,
		ProfessionGroup32: 32,
		ProfessionGroup33: 33,
		ProfessionGroup34: 34,
		ProfessionGroup35: 35,
		ProfessionGroup36: 36,
		ProfessionGroup37: 37,
		ProfessionGroup38: 38,
	},
	LEAGUE_SCHEDULE_ID: {
		ENROLL: 1,
		COMMUNITY: 2,
		RELEASE_NORMAL_LIST: 3,
		NORMAL: 4,
		RELEASE_KNOCKOUT_LIST: 5,
		KNOCKOUT: 6,
		RELEASE_PROFESSION_LIST: 7,
		PROFESSION: 8,
		SEND_FINAL_AWARD: 9,
		SEND_NEXT_GAME: 10,
	},
	LEAGUE_TIME_GROUP_ID:
	{
		ENROLL_START: 1,
		ENROLL_END: 2,
		NOON: 3,
		AFTERNOON: 4,
		EVENING: 5,
		TEN_CLOCK: 6,
		FIVE_CLOCK: 7,
		TWENTY_ONE: 8,
	},
	LEAGUE_STATUS: {
		CLOSE: 0,
		OPEN: 1,
	},
	LEAGUE_WAIT_CALL_BACK_STATUS: {
		NOT_START: 0,
		RUNNING: 1,
		END: 2,
	},
	SCOUT_PARAM: {
		primaryRp:3001,
		seniorRp:3002,
		topRp:3003,
		scoutEnergyMax:3004,
		scoutEnergyInit:3005,
		primaryEnergyInit:3006,
		seniorEnergyInit:3007,
		topEnergyInit:3008,
		scoutMaxRp:3009,
		needEnergy:3010,
		primaryRe:3011,
		seniorRe:3012,
		topRe:3013,
	},
	PLAY_INFO:{
		level:1,
		exp:2,
		faceId:3,
		energy:4,
		gold:5,
		cash:6,
		vip:7,
		trophy:8,
		fame: 9,
		trainCount: 10,
		scoutEnergy: 11,
		isFirstRecharge:12,
		vipExp: 13,
		worldCoin: 14,
		chip: 15,
		league: 16,
		integral: 17,
		honor: 18,
		beliefNum: 19,
		beliefIntegral: 20,
		beliefLiveness: 21,
		honorWallIntegral: 22
	},
	ENERGY_INFO:{
		consumeNum:3020,
		energyInit:3021,
		freeEnergy:3022,
		recoverTime:3029,
		recoverNum:3024,
		bFirstReTime:3025,
		eFirstReTime:3026,
		bSecondReTime:3027,
		eSecondReTime:3028
	},
	BATTLE_RECORD_TYPE:{
		PVPLeagueBattle: 1
	},
	TASK_INFO:{
		taskType:"task",
		reTaskNum:2001,
		resetType:3,
		goldCoachType: 5,
		nationalSign: 6,
		RelayType: 7,
		BELIEF_TASK_TYPE: 8,
		VIP_REWARD:1229
	},
	TARGET_TYPE:{
		ONE:1,						// 1：X队伍X星通关X次
		TWO:2,						// 2：X星通关X联赛
		THREE:3,					// 3：获得X数量的球员
		FOUR:4,						// 4：推图X次
		FIVE:5,						// 5：球队达到X级
		SIX:6,						// 6：拥有X个新球员
		SEVEN:7,					// 7：X个球员达到X级
		EIGHT:8,					// 8：单个球员达到X级
		NINE:9,						// 9：购买X个新道具
		TEN:10,						// 10：提升X次球员等级获得指定一个球员
		ELEVEN:11,					// 11：获得一个指定球员
		TWELVE: 12,     			// 12：升级球场X次
		THIRTEEN: 13,   			// 13: 分享阵容
		FOURTEEN: 14,				// 14：参与X次联赛
		FIFTEEN: 15,				// 15：球员特训X次
		SIXTEEN: 16,				// 16：球员突破X次
		SEVENTEEN: 17,				// 17：拥有X个完美突破球员
		EIGHTEEN: 18,				// 18：拥有X个9星的球员
		NINETEEN: 19,				// 19：拥有1个X星的球员
		TWENTY: 20,					// 20：俱乐部身价达到X
		TWENTY_ONE: 21,				// 21：累计拥有X欧元
		TWENTY_TWO: 22,				// 22：累计拥有X球币
		TWENTY_THREE: 23,			// 23：球员升星X次
		TWENTY_FOUR: 24,			// 24：获得X欧元
		TWENTY_FIVE: 25,			// 25：消耗X球币
		TWENTY_SIX: 26,				// 26：消耗X欧元
		TWENTY_SEVEN: 27,			// 27：提升X次阵型等级
		TWENTY_EIGHT: 28,			// 28：提升X次战术等级
		TWENTY_NINE: 29,			// 29：世界频道发言X次
		THIRTY: 30,					// 30：关注X位玩家
		THIRTY_ONE: 31,				// 31：参与X次世界杯
		THIRTY_SIGN: 32,			// 32：发言国庆快乐
		THIRTY_THREE: 33,           // 33: 球场收集
		THIRTY_FOUR:34,             // 34: 球员泡澡
		THIRTY_FIVE:35,				// 35: 参加商业赛
		THIRTY_SIX:36,				// 36: 球探搜索球员
		THIRTY_SEVEN:37,			// 37：购买世界杯次数
		THIRTY_EIGHT:38,			// 38: 球员状态调整
		THIRTY_NINE:39,             // 39: 金牌教练巡回赛
		JOIN_MIDDLE_EAST_CUP:40,    // 40: 参加X次中东杯
		JOIN_WORLD_BOSS:41,         // 41: 参加X次世界BOSS
		KILL_WORLD_BOSS:42,         // 42: 击杀X次世界BOSS
		JOIN_DQ_CUP:43,             // 43: 参与X五大次联赛
		WIN_MIDDLE_EAST_CUP:44,     // 44: 获胜X次中东杯
		JOIN_GULF_CUP: 45,          // 45：参与海湾杯x次
		WIN_GULF_CUP: 46,           // 46：战胜海湾杯x次
		WIN_LEAGIE: 47,				// 47：本赛季联赛胜利x次
		GOAL_LEAGUE: 48,			// 48: 本赛季进球x个
		PROMOTED_LEAGUE: 49,		// 49: 当前赛季完成所有比赛
		CHAMPION_LEAGUE: 50,		// 50: 本赛季获得X级联赛冠军
		SECOND_LEAGUE: 51,			// 51: 本赛季获得X级联赛亚军
		PLACE_ONE_LEAGUE: 52,		// 52: 本赛季获得X级联赛3-5名
		PLACE_TWO_LEAGUE: 53,		// 53: 本赛季获得X级联赛6-11名
		GIVE_ENERGY: 54,			// 54: 赠送精力
		BELIEF_CASH: 55,            // 55：信仰捐赠100万欧元
		BELIEF_CASH_1: 56,          // 56: 信仰捐赠500万欧元
		BELIEF_CASH_2: 57,          // 56: 信仰捐赠1000万欧元
		BELIEF_CASH_3: 58,          // 56: 信仰捐赠5000万欧元
		JOIN_GAME_HUB:99            // 99：进入游戏圈子
	},
	FUNC_SWITCH_TYPE: {
		LEAGUE: 1,
	},
	LEAGUE_BATTLE_STATUS: {
		NOT_BEGIN: 0,
		RUNNING: 1,
		END: 2,
	},
	HERO_SYSTEM:{
		singleCultivate: 3050,
		repeatedCultivate: 3051,
		singleLow: 3052,
		singleUp: 3053,
		repeatedUp: 3054,
		repeatedLow: 3055,
		breakoutCost: 3056,
		rebreakoutCost: 3057,
		elementaryCost: 3058,
		mediumCost: 3059,
		seniorCost: 3060,
		directionalCost: 3061,
		upStarCost: 3062,
		ITEMID_1: 29991,	//铜百搭卡
		ITEMID_2: 29992,	//银百搭卡
		ITEMID_3: 29993,	//金百搭卡
		ITEMID_4: 29994,	//黑百搭卡
		ITEMID_5: 29995,	//青训百搭卡
		ITEMID_6: 29996,	//闪回百搭卡
		ITEMID_7: 29997,	//印象百搭卡
		ITEMID_8: 29998,	//未来之星百搭卡
		ITEMID_9: 30000,	//万能百搭卡
		ITEMID_10: 29999,	//红百搭卡
		RETIREMENT: 116,           //退役球员衰减系数
		COLOR_1: "铜",
		COLOR_2: "青训端午",
		COLOR_3: "青训",
		COLOR_5: "青训儿童",
		COLOR_6: "银",
		COLOR_7: "儿童节",
		COLOR_8: "端午限定",
		COLOR_9: "金",
		COLOR_12: "闪回",
		COLOR_15: "印象",
		COLOR_18: "未来之星",
		COLOR_21: "欧联之星",
		COLOR_24: "欧冠之星",
		COLOR_27: "黑",
		COLOR_30: "年度最佳阵容",
		COLOR_31: "红",
		COLOR_28: "经典黑",
		COLOR_29: "MVP",
		COLOR_36: "传奇",
		NORMAL_TERMINATION: 6,   //普通解约返还资源（训练卡）百分比
		GOLD_TERMINATION: 7,     //球币解约返还资源（欧元）百分比
		NORMAL_STAR_TERMINATION: 8,   //普通解约返对应星级球探体力百分比
		GOLD_STAR_TERMINATION: 9,     //球币解约返对应星级球探体力百分比
		GOLD_NUM: 10,                //解约所需球币
		LEFT_DAY_GOLD: 120,          //增加生涯消耗金币数
	},
	CHAT_CHANNEL: {
		SYSTEM_CHANNEL: 0,
		WORLD_CHANNEL: 1,
		GUILD_CHANNEL: 2,
		FRIEND_CHANNEL: 3,
		HORN_CHANNEL: 4		//喇叭 (移除不用了,直接用System)
	},
	CHAT_TYPE: {
		SYSTEM: -1,		//系统
		NORMAL: 0,		//世界
		ITEM: 1,		//物品超链接
		HERO: 2,		//球员超链接
		HORN: 3,		//喇叭
		HONOR_WALL: 4	//荣誉墙
	},
	KNOCOUT_LEVEL: {
		DOWNGRADE: 0,  //降级
		REMAIN: 1,     //保级
		PROMOTION: 2,  //晋级
	},
	FOOTBALLGROUND: {
		mainPrestige:1101,
		mainFans:1102,
		openLevel: 1104,
		firstPos:1200,
		secondPos:1201,
		thirdPos:1202,
		fourthPos:1203,
		fifthPos:1204,
		sixthPos:1205,
		transferPos_1: 1206,
		transferPos_2: 1207,
		transferPos_3: 1208,
		transferPos_4: 1209,
		transferPos_5: 1210,
		transferPos_6: 1166,  //第6个助理教练位置解锁所需等级
		ADMIN_REWAED: 1221,
		TRAIN_REWARD: 1223,
		TRANSFER_REWARD: 1225,
		HOSPITAL_REWARD: 1227,
		HOSPITAL_POS:1211,
		SLIGHT_INJURY_TIME: 1212,        //轻微治疗时间
		MINOR_INJURY_TIME: 1213,         //轻伤治疗时间
		SERIOUS_INJURY_TIME: 1214,       //重伤治疗时间
		NORMAL_MEDKIT:1215,      //普通医疗箱缩减时间
		SENIOR_MEDKIT:1216,      //高级医疗箱缩减时间
		PERFECT_MEDKIT:1217,     //完美医疗箱缩减时间
		BALL_FANS_RATIO: 1228,   //球迷奖励系数
		FIRST_DOWN_STAGE: 1151,  //第一阶段下限
		FIRST_UP_STAGE: 1152,    //第一阶段上限
		SECOND_DOWN_STAGE: 1153,  //第2阶段下限
		SECOND_UP_STAGE: 1154,    //第2阶段上限
		THREE_DOWN_STAGE: 1155,  //第3阶段下限
		FIRST_STAGE_GOLD: 1156,  //第1阶段所需球币
		SECOND_STAGE_GOLD: 1157,  //第2阶段所需球币
		THREE_STAGE_GOLD: 1158,  //第3阶段所需球币
		NOTABLE_REWARD: 1302,
		SECOND_POS_VIP: 1218,
		THIRD_POS_VIP:1219,
		HOSPITAL_VIP: 1305,
	},
	ENROLL_NOTIFY: {
		FAILED: 0,
		SUCCESS: 1,
	},
	FINAL_MAIL_NOTIFY_TYPE: {
		ELIMINATE: 0,
		PROMOTION: 1,
	},
	CLUSTER_2_GAME_FUNC_TYPE: {
		PVP_LEAGUE_NOTIFY_ENROLL_RESULT_MAIL: 1, //邮件通知报名结果
		PVP_LEAGUE_PREPARE: 2, 				     //等待比赛开始
		PVP_LEAGUE_START: 3,   			         //比赛立即开始
		PVP_LEAGUE_FINAL_REWARD: 4, 			 //邮件发送最终奖励
		PVP_LEAGUE_NOTIFY_WIN_AND_LOSE_MAIL: 5,  //邮件通知玩家的输赢
		PVP_LEAGUE_NOTIFY_FINAL_WIN_MAIL:6,      //邮件通知最终赢得玩家
		PVP_LEAGUE_GET_BATTLE_DATA: 101,         //拉取战斗服数据
		PVP_MATCH_UPDATE_BATTLE_RECORD: 102,     //更新匹配战斗记录
		PVP_MATCH_GET_ALL_PLAYER_FIGHT_TIMES: 103, //拉取所有玩家的挑战次数
		PVP_LEAGUE_PUBLIC_NOTIFY_PLAYER: 104,      //公共通知玩家邮件
	},
	CURRENCY_TYPE: {
		CASH: 1,
		GOLD: 2,
		ENERGY: 3,
		TRAIN: 4,
		FAME: 5,
	},
	TROPHY_LIMIT_TYPE:
	{
		NO_LIMIT: 0,
		TOTAL_LIMIT: 1,
		TEAM_LIMIT: 2,
	},
	TROPHY_UPDATE_TYPE: {
		TOTAL_NUM: 1,
		TOTAL_ALREADY_PURCHASE_NUM: 2,
		TEAM_NUM: 3,
		TEAM_ALREADY_PURCHASE_NUM: 4
	},
	CONSUME_TYPE: {
		CASH: 1,
		GOLD: 2,
		ENERGY: 3,
	},
	PVE_RESULT: {
		LOSE: 0,
		WIN: 1,
	},
	ONLINE_GLOBAL_DATA_TYPE: {
		SHOP: 1,
		SEASONSTORE:2,
		GOLD_COACH: 3,   //金牌教练
	},
	ONLINE_PLAYER_ONLINE_STATE:{
		ONLINE: 1,
		OFFLINE: 2
	},
	FOLLOW_DETAIL_TYPE: {
		FOLLOW: 1,
		FANS: 2,
	},
	FOOTBALL_GROUND_STATUS: {
		CLOSE:0,          //关闭
		OPEN:1            //开放
	},
	ITEM_RESID: {
		CASH: 1,  		  //欧元
		GOLD: 2,   		  //球币
		ENERGY: 3, 		  //精力
		TRAIN_COUNT: 4,   //训练点数
		FAME: 5,   		  //声望
		SCOUT_ENERGY: 6,  //体力
		WORLD_COIN:9,     //世界boss币
		RELAY_INTEGRAL:14,	  //转播积分
		HONOR: 15,        //教练荣誉
		BELIEF_INTEGRAL: 16,		//信仰积分
		BELIEF_LIVENESS: 17,     //信仰贡献
		HONORWALL_INTEGRAL: 19		//荣誉墙积分
	},
	SIGN_PRICE:{
		ONE:9001,
		TWO:9002,
		THREE:9003,
		FOUR: 9004,
		FIVE: 9005,
		SIX: 9006
	},
	VIP_SHOP_GIFT_TYPE: {
		LEVEL: 1,   //等级
		SPECIAL: 2, //特权
		HANDSEL: 3, //特殊赠送
	},
	VIP_SHOP_BUYIN_CURRENCY_TYPE: {
		CASH: 1,  //欧元
		GOLD: 2,  //懂币
	},
	DROP_CONFIG_TYPE: {
		WEIGHTS: 1,
		FIXED: 2
	},
	DROP_ITEM_TYPE: {
		ITEM: 1,
		HERO: 2,
	},
	VIP_SHOP_ITEM_TYPE: {
		ITEM: 1,
		HERO: 2,
	},
	SEASONSTORE: {
		COMMONREFRESH:1,         //普通刷新
		SUPERREFRESH:2,          //超级刷新
		GUARANTEEDREFRESH:3,     //保底刷新
		COMMONREFRESHNUM: 6001,  //普通刷新消耗
		SUPERREFRESHNUM:6002,    //超级刷新
		MINIMUM: 6003            //赛季商城保底刷新次数

	},
	ACT_BTN_STATUS: {
		NOT_TAKE: 0,          //不可领取
		CAN_TAKE: 1,		  //可领取
		GO_CHARGE: 2,         //前往充值
		BUY_GOODS: 3,         //购买
		ALREADY_TAKE: 4,      //已领取
		BEST_SELLER: 5,       //售罄
		EXCHANGE_ITEM: 6,     //兑换
		ALREADY_SEND_EMAIL: 7,//已发邮邮件
		LATE_TAKE: 8 ,        //迟到领取
		NOT_SIGN: 9,			//未签到
		LEAK_SIGN: 10,			//漏签
		REPAIR_SIGN: 11,		//补签
	},
	ACT_CONTROL_SPECIAL_TYPE: {
		NONE: 0,
		FIRST_CHARGE: 1,
		MONTH_CONSUME: 2
	},
	ACT_PARAM_PUSH_WAY: {
		NONE: 0,
		MAIN_UI: 1,         //主界面
		ACTIVITY_UI: 2,     //活动界面
		MAIN_ACTIVITY_UI: 3,//主界面和活动界面 
	},
	ACT_COMMON_TYPE: {                         //活动类型
		ACT_TYPE_DAILY_ON_CHARGE:      1,      //每日充值
		ACT_TYPE_ACCUMULATION_CHARGE:  2,      //累计充值
		ACT_TYPE_ACCUMULATION_CONSUME: 3,      //累计消耗
		ACT_TYPE_FIRST_CHARGE:         4,      //首充
		ACT_TYPE_MONTH_CARD:		   5,      //月卡
		ACT_TYPE_GOLD_COACH:           6,      //金牌教练 此活动只能开启一个
		ACT_TYPE_GET_HERO:             7,      //球员获取途径
		ACT_TYPE_CONTINUE_CHARGE:      8,      //5日连续储值
		ACT_TYPE_SEVENDAY_SIGN:		   9,	   //国庆签到
		ACT_TYPE_PUSH_GIFT:            20,     //推送礼包
		ACT_TYPE_SPECIAL_CHARGE:       21,     //名人堂活动
		ACT_TYPE_ACCUMULATION_CHARGE_1:  22,    //累计充值1
		ACT_TYPE_ACCUMULATION_CONSUME_1: 23,    //累计消耗1
		ACT_TYPE_SPECIAL_CHARGE_1:       24,    //998充值活动1
		ACT_TYPE_TURNTABLE:       		 25,	//老虎机
		ACT_TYPE_OPEN_GIFT_BAG:			 29,	//开服大礼包
		ACT_TYPE_SPECIAL_CHARGE_2:       30,    //98充值活动
		ACT_TYPE_CONTINUE_CHARGE_1:      31,    //4日连续储值
		ACT_TYPE_CONTINUE_CHARGE_2:      32,    //3日连续储值
		ACT_TYPE_BIG_GIFT:      35,    			//超值大礼包
		ACT_TYPE_SPECIAL_CHARGE_3:       36,    //38充值活动
		ACT_TYPE_SPECIAL_CHARGE_4:       37,    //68充值活动
		ACT_TYPE_SLOTS:       		 	 39,	//拉霸
	},
	ACT_TIME_TYPE: 
	{
		PERSISTENT: 1,           //持久化活动,不需要删除
		CYCLE: 2,                //周期性活动
	},
	ACT_REFRESH_TYPE: 
	{
		NONE: 0,                 //不处理刷新
		CROSS_DAY: 1,            //跨天刷新
		CROSS_WEEK: 2,           //跨周
		CROSS_MONTH: 3,          //跨月
	},
	ACT_RECHARGE_ID: {
		ONE_RMB_GIFT: 8,
		TWO_RMB_GIFT: 9,
		THREE_RMB_GIFT: 10,
		MONTH_CARD: 11,
		GOLD_COACH: 12,
	},
	ACT_GOLD_COACH_AWARD_TYPE: {
		FREE: 1,
		CHARGE: 2,
	},
	ACT_GOLD_COACH_BUY_TYPE: {
		NONE: 0,
		BUY: 1,
	},
	OFFLINE_EVENT_TYPE: {
		ACT_MAIL: 1,            //活动邮件
		ADD_CASH: 2,            //离线添加欧元
		ADD_GOLD: 3,            //离线添加懂币
		ADD_BUSINESS: 4,        //被打添加商业赛次数
		RELAY_FINAL: 5,		    //赛事转播赛季结束奖励结算
		RELAY_UPDATA: 6,	    //赛事转播赛季更新赛季
        RELAY_FULFIL:7,         //赛事转播完成所有比赛任务
		FOLLOW_GIVE_ENERGY: 8	//赠送精力
	},
	MONTH_CARD_STATUS: { //状态  0未领取   1已领取  2已发邮件
		NOT_TAKE: 0,
		ALREADY_TAKE: 1,
		ALREADY_EMAIL: 2,
	},
	DB_SAVE_FIRST_CHARGE_STATUS: {
		NONE: 0,  //未激活
		ACTIVE: 1,//已激活
	},
	NEWER_GUIDE_TYPE: {
		HARD: 1,
		SOFT: 2,
		OTHER: 3,
	},
	NEWER_GUIDE_STATUS: {
		NOT_TRIGGER: 0,
		PROCESS: 1,
		FINISH: 2,
	},
	NEWER_GUIDE_TRIGGER_CONDITION: {
		NONE: 0,
		TASK: 1,
		ROLE_LEVEL: 2,
		TASK_JUMP: 3, //任务跳转
	},
	NEWER_GUIDE_DEAL_ID: {       //一下的id都要处理为自动完成
		WatchRecordId:  1,       //观看比赛
		MatureTypeId: 2,         //选择新手和老手
		GetNewBallerId: 3,       //获得新球员
	},
	NEWER_GUIDE_TRIGGER_TYPE: {
		NORMAL: 1,               //一般性引导
		TRIGGER: 2,              //触发引导
		FINISHED_EVERY_DAY: 3,   //每日完成任务
	},
	NEWER_GUIDE_USER_GROUP_TYPE: {
		NEWER: 1,               //只限新手
		ALL: 2,                 //全部
	},
	NEWER_GUIDE_CAN_COMPLETE: {
		COMMON: 1,              //一般性引导
		SIGN_HERO: 2,           //签约英雄
	},
	WORLD_CUP: {
		EVERY_DAY_JOIN: 1161,
		COST_MONEY: 1162,
		BUY_MAX: 1163,
		CSOT_ENERGY: 1164,
		FIRST: 1,        //冠军
		SECOND: 2,       //亚军
		THREE: 3,        //4强
		FOUR: 4,         //8强
		FIVE: 5,         //16强
		SIX: 6,          //32强
	},
	FOOTBALL_GROUND_ADD_ALL_PROPERTY: {
		HANDBOOK: 1,           //图鉴加成
		TRAINER: 2,			   //教练加成
	},
	FOOTBALL_GROUND_CALC_TYPE: {  //球员属性线性加成类型
		ADD: 1,                   //增加
		SUB: 2,                   //削弱
	},
	EVERY_DAY_TAKE_ENERGY_TYPE: {
		MIDDAY: 1,                 //中午
		EVENING: 2,                //晚间
	},
	HERO_STATUS: {              //球员状态
		PEAKEDNESS: 1,          //巅峰
		FINE: 2,                //良好
		COMMONLY: 3,            //一般
		DIFFERENCE: 4,          //差
		DOWNTURN: 5,            //低迷
		BATTLE_NUM: 100,        //战斗场数
	},
	HERO_TREATY: {                //球员状态
		TREATY_DAY: 101,          //合约天数
		DECAY_RATIO: 102,         //属性衰减系数
		SEASON_NUM: 103,          //赛季        		 
	},
	COMMON_DB_ID: {
		CHAT_MSG_CACHE: 1,			//Common表ID: 1. 记录全局聊天信息
		ASSOCIATION_MSG_CACHE: 2,  	//记录协会聊天
		RANK_ROBOT_CACHE: 3,		//记录主站排名活动机器人映射表
		ALL_SERVICE_GIFT: 4,        //全服开礼包
		BELIEF_CLEAR_TIME: 5,       //信仰活跃度清空时间
		CHMATCH_DATA: 6,			//首席争夺战数据统计
		PKMATCH_DATA: 7,			//信仰巅峰战统计
		WAR_OF_FAITH_RECORD: 8,      //信仰争夺战信息记录
        SLOTS_DATA: 9               //拉霸总参与人数与活动金额
	},
	SHARE_EVENET_TYPE: {
		BATTLE_SHARE: 1
	},

	HERO_HEALTH_STATUS: {
		HEALTH: 1,         //健康
		SLIGHT_INJURY: 2,   //轻微伤
		MINOR_INJURY: 3,    //轻伤
		SERIOUS_INJURY: 4,   //重伤
		TEAM_MAX_FATIGUE: 20, //伤病球队疲劳临界值
		WIN: 21,     //比赛胜利当前阵容球员疲劳系数变化
		DRAW:22,
		LOSE:23,
		OTHER_WIN:24,  //比赛胜利其他球员疲劳系数变化
		OTHER_DRAW:25,
		OTHER_LOSE:26,
		ATTENUATION_NUM:27,   //伤病触发后当前阵容球员疲劳系数变化
		OTHER_ATTENUATION_NUM: 28,
		RETIME_NUM: 29,  //每小时空闲时间全队球员疲劳系数变化
		RETIME: 109,   //每隔X小时降低PL值
		SLIGHT_INJURY_NUM: 110,        //轻微衰减系数
		MINOR_INJURY_NUM: 111,         //轻伤衰减系数
		SERIOUS_INJURY_NUM: 112,       //重伤衰减系数
		REDUCE_NUM: 113,               //降低值
		REDUCE_NUM_MAX: 114,           //最大降低值
		SERIOUS_INJURY_TIME: 117,      //重伤时间
	},
	
	TASK_GOLD_MEDAL_COACH_TYPE: {  //金牌教练任务类型
		FREE: 0,                   //免费任务
		CHARGE: 1,                 //金牌任务
		BOTHER: 2,                 //两者都
	},
	STATIS_LOG_TYPE: {
		LOGIN: 1,		//用户登录
		LOGOUT: 2,		//用户登出
		GUIDE_STEP: 3,	//新手引导         //1 id 引导Id
		LEVEL_UP: 4,	//升级
		PVE_LEAGUE: 5,	//PVE推图结果           // teamCopyId finshedStar 副本 星数
		PVP_LEAGUE_SIGNUP: 6,	//PVP懂联赛报名  now报名时间 
		PVP_LEAGUE_RANK: 7,		//PVP懂联赛排名  rank
		PVP_BUSINESS: 8,		//PVP商业赛结果 (打和被打的都记录: 2条记录) result 0平 1输 2赢
		BUY_ENERGY: 9,			//球探体力购买
		RANDOM_FOOTBALLER: 10,	//球探抽取
		SIGNUP_FOOTBALLER: 11,	//签约
		GROUND_LEVEL_UP: 12,	//球场升级
		GROUND_GOLD_LEVEL_UP: 13,	//用懂币秒CD
		SHOP_BUY: 14,				//商城购买
		FIRST_RECHARGE: 15,			//首冲         value
		MONTH_VIP: 16,				//月卡         actId, costMoney
		EVERYDAY_RECHARGE_GIFT: 17,	//每日充值礼包 actId, tagIndex(从0开始计数), value  
		GET_RECHARGE_TOTAL: 18,		//累计充值     actId, totalCharge 总充值
		COST_TOTAL: 19,				//累计消耗     actId, totalGold 总金币
		PVE_WORLDCUP: 20,			//世界杯结果
		PVE_WORLDCUP_BUY: 21,		//世界杯购买
		CREATE_ROLE_QUALIFIED: 22,  //新老手
        CLIENT_LOAD_RES: 23,         //客户端加载资源统计
		WORLD_BOSS_JOIN_NUM: 24,       //世界boss人数统计
		ADD_ITEM: 25,                  //物品记录
		UPGRADE_STAR: 26,               //升星
		USE_ITEM: 27,                    //使用物品
		HERO_RENEWAL: 28,                 //球员续约
		OPEM_GIFT_BAG: 29,                 //开服大礼包
		ADD_HERO: 30,                //新增球员
		DEL_HERO:31,                  //解雇球员
		CULTIVATE_HERO: 32,            //培养球员
		MEDICAL_COST: 33,               //医疗中心加速球员
		ENERGY_COST: 34,               //精力消耗
		ADD_ENERGY: 35,               //精力增加
		HOF_ACT: 36,                  //名人堂活动
		CUMULATIVE_CONSUMPTION: 37,    //累计消耗
		SEND_MAIL: 38,                  //发邮件
		ADD_GOLD: 39,                  //新增金币
		USE_GOLD: 40,                   //消耗金币
		ADD_CASH: 41,                  //新增欧元
		USE_CASH: 42,                   //消耗欧元
		ADD_BELIEF_NUM: 43,            //新增信仰积分
		ADD_BELIEFLIVENESS:44,         //新增信仰贡献
		USE_BELIEF_NUM: 45,            //使用信仰积分
		USE_BELIEFLIVENESS:46,         //使用信仰贡献
	},
	PLAYER: {
		CASH_REWRAD: 1300,          //开局一个亿
		COACH: 1304,                //朴教练活动
	},
	HTTP_SEND_MAIL_TYPE: {
		SINGLE_PLAYER: 1,			//单人
		ALL_SERVER: 2,				//全服
		MULTI_PLAYER: 3				//多人
	},
	REDDOT_HINT:{					//红点提示类型 state：未领取 1 领取 0
		EMAIL: 1,					//邮件
		TASKS: 2,					//任务
		ITEM:  3,					//物品
		SCOUT: 4,					//球探
		VIP:   5,					//vip
		ENERGY: 6,					//免费领取精力
		HOSPITAL: 7,				//医疗
		TRAIN: 8,					//训练
		RELAY: 9,					//赛事转播
		FOLLOW: 10					//好友赠送体力
	},
	RECHARGE_STATE: {
		CREATE_ORDER: 0,		//创建订单
		GET_CALLBACK: 1,		//支付回调
		SEND_GOODS: 2			//发货
	},
	SKIP_BATTLE_TYPE: {
		USE_ITEM: 1,		//使用跳过卡
		USE_GOLD: 2			//使用懂币
	},

	NEWER_TASK:{
		SEARCH_HERO: 1,
		SIGN_HERO: 2,
		AUTOMATIC_DISPOSAL:3,
		LEAGUE_COPY: 4,
		HERO_TRAIN: 5,
		USER_ITEM: 6,
		GET_MONEY: 7,
		STRENGTH_UP: 8,
		JOIN_WORLD_CUP: 9,
		PLAYER_LEVEL: 10
	},

	SYSTEM_OPEN_LEVEL:{
		WORLD_CUP: 7,
	},

	DQ_CUP_MATCH_SCHEDULE: {
		MATCH_INIT: 0,		//比赛初始状态 (未开赛, 休止期)
		ENROLL_START: 1,	//比赛报名开始
		ENROLL_END: 2,		//比赛报名结束，预选赛开始
		PRE_MATCH_END: 3,	//预选赛结束, 64强赛开始
		MATCH_64_END: 4,	//64强赛结束, 32强赛开始
		MATCH_32_END: 5,	//32强赛结束, 16强赛开始
		MATCH_16_END: 6,	//16强赛结束, 8强赛开始
		MATCH_8_END: 7,		//8强赛结束, 半决赛开始
		MATCH_4_END: 8,		//半决赛结束, 决赛开始
		MATCH_2_END: 9,		//决赛结束, 等待奖励发放
		SEND_REWARD_START: 10,	//开始发放奖励
		SEND_REWARD_END: 11		//结束发放奖励
	},

	DQ_CUP_MATCH_PERIOD:{
		ENROLL: 1,			//报名期
		PRE_MATCH: 2,		//预选赛
		MATCH_64: 3,		//64强
		MATCH_32: 4,		//32强
		MATCH_16: 5,		//16强
		MATCH_8: 6,			//8强
		MATCH_4: 7,			//4强
		MATCH_2: 8,			//2强
		WAIT_REWARD: 9,		//奖励发放等待期
		SEND_REWARD: 10,	//发奖励
		SEND_FINISHED: 11	//奖励发放完成
	},

	CHAIRMAN_MATCH_SCHEDULE: {
		MATCH_INIT: 0,		//比赛初始状态 (未开赛, 休止期)
		ENROLL_START: 1,	//比赛报名开始
		ENROLL_END: 2,		//比赛报名结束
		PRE_MATCH_START: 3,	//预选赛开始
		PRE_MATCH_END: 4,	//预选赛结束, 64强赛开始
		MATCH_64_END: 5,	//64强赛结束, 32强赛开始
		MATCH_32_END: 6,	//32强赛结束, 16强赛开始
		MATCH_16_END: 7,	//16强赛结束, 8强赛开始
		MATCH_8_END: 8,		//8强赛结束, 半决赛开始
		MATCH_4_END: 9,		//半决赛结束, 决赛开始
		MATCH_2_END: 10,	//决赛结束，竞选准备阶段
		CAMPAIGN_START: 11,	//竞选开始
		CAMPAIGN_END: 12,	//竞选结束
		SEND_REWARD: 13,		//发奖励
		SEND_FINISHED: 14	//发奖励结束
	},

	CHAIRMAN_MATCH_PERIOD:{
		MATCH_INIT: 0,		//比赛初始状态 (未开赛, 休止期)
		ENROLL_START: 1,	//报名期
		ENROLL_END: 2,		//报名结束
		PRE_MATCH: 3,		//预选赛
		MATCH_64: 4,		//64强
		MATCH_32: 5,		//32强
		MATCH_16: 6,		//16强
		MATCH_8: 7,			//8强
		MATCH_4: 8,			//4强
		MATCH_2: 9,			//2强
		WAIT_REWARD: 10,	//决赛结束，竞选准备阶段
		CAMPAIGN_START: 11,	//竞选开始
		CAMPAIGN_END: 12,	//竞选结束
		SEND_REWARD: 13,	//发奖励
		SEND_FINISHED: 14	//发奖励结束
	},

	PEAK_MATCH_SCHEDULE:{
		MATCH_INIT: 0,		//比赛初始状态 (未开赛, 休止期)
		BET_ON: 1,				//押注阶段
		PUBLICATION_LIST: 2,	//公布名单
		MATCH_32: 3,			//16强
		MATCH_16: 4,			//8强
		MATCH_8: 5,				//4强
		MATCH_4: 6,				//半决赛
		MATCH_2: 7,				//决赛
		SEND_REWARD: 8,			//发	奖励
	},

	PEAK_MATCH_PERIOD:{
		MATCH_INIT: 0,		//比赛初始状态 (未开赛, 休止期)
		BET_ON: 1,				//押注阶段
		PUBLICATION_LIST: 2,	//公布名单
		MATCH_32: 3,			//16强
		MATCH_16: 4,			//8强
		MATCH_8: 5,				//4强
		MATCH_4: 6,				//半决赛
		MATCH_2: 7,				//决赛
		SEND_REWARD: 8,			//发	奖励
	},

	WORLD_BOSS:{
		CD_TIME: 200,      //世界BOSS挑战冷却时间（秒）
		KILL_GOLD: 201,    //世界BOSS清除冷却时间每次花费懂币数
		BEGIN_TIME: 13,    //距离世界BOSS活动开启多少秒弹窗提示
	},

	BATTLE_TACTICS_STATE: {
		NO_RESTRAIN: 0,		//无克制关系
		RESTRAIN: 1,		//克制关系
		BE_RESTRAINED: 2	//被克制关系
	},

	SET_FORMATION_MODE: {
		FreeKick: 1,               //任意球
		Penalties: 2,              //点球
		CornerKick: 3              //角球
	},

	DB_NAME: {
		player: "player",
		heros: "heros",
		email: "email",
		item: "item",
		bag: "bag",
		teamFormation: "teamFormation",
		leagueCopy: "leagueCopy",
		scout: "scout",
		tasks: "tasks",
		footballGround: "footballGround",
		businessMatch: "businessMatch",
		trophyCopy: "trophyCopy",
		trainer: "trainer",
		follow: "follow",
		store: "store",
		newPlayerSign: "newPlayerSign",
		sevenDaySign: "sevenDaySign",
		everyDaySign: "everyDaySign",
		vipShop: "vipShop",
		seasonStore: "seasonStore",
		act: "act",
		limitStore: "limitStore",
		offlineEvent: "offlineEvent",
		newerGuide: "newerGuide",
		worldCup: "worldCup",
		everyDayEnergy: "everyDayEnergy",
		newerTask: "newerTask",
		middleEastCup: "middleEastCup",
		gulfCup: "gulfCup",
		relay: "relay",
		MLS: "MLS",
		sign: "sign",
		commonActivity: "commonActivity",
		beliefSkill: "beliefSkill",
		honorWall: "honorWall"
	},

	HERO_BATTLE_TYPE: {
		PveLeagueCopy : 1,		//推图PVE
		PveWorldCup : 2,		//世界杯PVE
		MiddleEastCup: 3,		//中东杯PVE
		PveWorldBoss: 4,        //世界boss
		GulfCup: 5,			    //海湾杯
		MLS: 6,					//美职联
		PvpMatch : 21			//商业赛PVP
	},
	CLIENT_CARD_TYPE: {
		"铜" : 1,
		"银" : 3,
		"女银" : 4,
		"青训": 6,
		"金": 9,
		"闪回": 12,
		"印象": 15,
		"未来之星": 18,
		"欧联之星": 21,
		"欧冠之星": 24,
		"黑": 27,
		"经典黑": 28,
		"MVP": 29,
		"年度最佳": 30,
		"经典": 33,
		"传奇": 36
	},
	BELIEF: {
		COST_CASH_NUM: 131,      //每次捐献消耗欧元数量
		CASH_BELIEF_NUM: 132,      //每次捐献消耗欧元数量获得信仰值
		CASH_BELIEF_GOLD_NUM: 133,      //每次捐献消耗欧元数量获得信仰资金
		COST_GOLD_NUM: 141,        //每次捐献消耗球币数量
		GOLD_BELIEF_NUM: 142,      //每次捐献消耗球币数量获得信仰值
		GOLD_BELIEF_GOLD_NUM: 143,      //每次捐献消耗球币数量获得信仰资金
	},
	FORMATION_TYPE: {
		COMMON: 1,		//通用阵型
		GROUND_ATK: 2,		//球场争夺战-进攻阵型
		GROUND_DEF: 3,		//球场争夺战-防守阵型
		GROUND: 4,			//球场争夺战-进攻+防守阵型
		ALL_TYPE: 100
	},
	WAR_OF_FAITH: {
		MATCH_INIT: 0,		       //比赛初始状态 (未开赛, 休止期)
		ENROLL_START: 1,	       //报名期
		ENROLL_END: 2,			   //报名结束
		ONE_LOCK_TEAM: 3,          //第一阶段锁定阵容实力
		ONE_BATTLE_START: 4,       //第一比赛阶段
		ONE_UNLOCK_TEAM: 5,        //第一阶段阵容解除时间
		TWO_LOCK_TEAM: 6,          //第二阶段锁定阵容实力
		TWO_BATTLE_START: 7,       //第二比赛阶段
		TWO_UNLOCK_TEAM: 8,        //第二阶段解除阵容
		THREE_LOCK_TEAM: 9,        //第三阶段锁定阵容实力
		THREE_BATTLE_START: 10,    //第三比赛阶段
		SEND_REWARD: 11,           //发奖 公布战况
		CLEAR_DATA_TIME: 12        //清数据时间
	},
	GROUND_MATCH_RECORD_TYPE: {
		ROB: 1,					//抢夺
		BE_ROBBED: 2,			//被抢夺
		ROB_FAIL: 3,			//抢夺失败
		BE_ROBBED_FAIL: 4,		//被抢夺失败
		DRIVE_AWAY: 5,			//驱赶
		BE_DRIVED: 6,			//被驱赶
		DRIVE_AWAY_FAIL: 7,		//驱赶失败
		BE_DRIVED_FAIL: 8,		//被驱赶失败
		LEAVE: 9,				//撤离
		BE_LEFT: 10,			//被撤离
		LEAVE_FAIL: 11,			//撤离失败
		BE_LEFT_FAIL: 12,		//被撤离失败
		OCCUPY: 13,				//占领
		BE_OCCUPIED: 14,		//被占领
		OCCUPY_FAIL: 15,		//占领失败
		BE_OCCUPIED_FAIL: 16	//被占领失败
	},
	GROUND_MATCH_FIELD_DETAIL_NAME: {
		"1": "初级训练场",
		"2": "普通训练场",
		"3": "青训训练场",
		"4": "高级训练场",
		"5": "巨星训练场"
	},
	GROUND_MATCH_TOPLIST_TYPE: {
		ASSETS_RANK: 1,					//资产排行榜
		WOLRD_RANK: 2,					//世界级排行榜
		INTERCONTINENTAL_RANK: 3,		//洲际级排行榜
		COUNTRY_RANK: 4,				//国家级排行榜
		AREA_RANK: 5,					//地区级排行榜
		COMMUNITY_RANK: 6				//社区级排行榜
	},
	//个人球场数据变更,同步类型
	GROUND_MATCH_SYNC_TYPE: {
		OCCUPY_TIME_OVER: 1,		//占领到期
		ROB_EVENT: 2,				//抢占事件
		USE_PROTECT: 3,				//使用保护文书
		LEAVE_AWAY: 4,				//撤离事件
		DRIVE_AWAY: 5,				//驱赶事件
		REPORT: 6,					//举报事件
		SET_FORMATION: 7,			//设置阵容队伍
		UPGRADE_MAIN_GROUND: 8		//主球场升级
	},

	WAR_OF_FATITH_SYSTEM_PARAM: {
		READY_TIME: 212,				//每场比赛准备时间
		INSPIRE_1: 215,					//第一次鼓舞加成
		INSPIRE_2: 216,					//第二次鼓舞加成
		INSPIRE_GOLDNUM_1: 213,			//第一次鼓舞所需球币
		INSPIRE_GOLDNUM_2: 214,			//第二次鼓舞所需球币
		HOLDER_REWARD:210,              //占领奖励
		OTHER_HOLDER_REWARD:211         //占领其他球场奖励
	},
	//荣誉墙
	HONOR_WALL_TYPE: {
		PEAK: 1,		//信仰巅峰赛
		CHAIRMAN: 2,	//首席争夺赛
		BELIEF: 3,		//信仰争夺赛
		LEAGUE: 4,		//懂联赛
		DQCUP: 5		//五大联赛杯
	},
	HONOR_DISPOSE_TYPE: {
		JOIN_NUM: 1,	//总参与次数
		DATA: 2			//数据
	},
	DQCUP_TYPE: {
		Italy: 1,		//意甲
		Germany: 2,		//德甲
		England: 3,		//英超
		Spain: 4,		//西甲
		France: 5		//法甲
	},
	HONOR_TASK_TYPE: {
		JOIN: 1,//参加
		TOP: 2, //名次
		LEVEL_TOP: 3,//级别and名次
		BELIEF_DEFEND: 4,//信仰争夺战防守
		BELIEF_ATTACK: 5,//信仰争夺战占领
	},
	MATCH_TYPE: {
		PvpLeague : 1,			//联赛PVP
		PvpWarOfFaith: 2,       //信仰之战
	}
};