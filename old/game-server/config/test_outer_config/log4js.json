{"appenders": [{"type": "file", "filename": "./logs/node-log-${opts:serverId}.log", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "node-log"}, {"type": "console"}, {"type": "file", "filename": "${opts:base}/logs/con-log-${opts:serverId}.log", "pattern": "connector", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "con-log"}, {"type": "file", "filename": "${opts:base}/logs/rpc-log-${opts:serverId}.log", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "rpc-log"}, {"type": "file", "filename": "${opts:base}/logs/forward-log-${opts:serverId}.log", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "forward-log"}, {"type": "file", "filename": "${opts:base}/logs/rpc-debug-${opts:serverId}.log", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "rpc-debug"}, {"type": "file", "filename": "${opts:base}/logs/crash.log", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "crash-log"}, {"type": "file", "filename": "${opts:base}/logs/admin.log", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "admin-log"}, {"type": "file", "filename": "${opts:base}/logs/pomelo-${opts:serverId}.log", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "pomelo"}, {"type": "file", "filename": "${opts:base}/logs/pomelo-admin.log", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "pomelo-admin"}, {"type": "file", "filename": "${opts:base}/logs/pomelo-rpc-${opts:serverId}.log", "maxLogSize": 10485760, "layout": {"type": "basic"}, "backups": 5, "category": "pomelo-rpc"}], "levels": {"node-log": "DEBUG", "con-log": "DEBUG", "rpc-log": "ERROR", "forward-log": "ERROR", "rpc-debug": "ERROR", "crash-log": "DEBUG", "pomelo": "DEBUG", "pomelo-admin": "DEBUG", "pomelo-rpc": "DEBUG"}, "replaceConsole": true, "lineDebug": true}