//发送邮件的脚本
let async = require('async');
let mongoClient = require('mongodb').MongoClient;
let clusterConfig = require('../../../config/cluster.json');
let commonEnum = require('../../../../shared/enum');
let http = require('http');
let querystring = require('querystring');
let routeUtil = require('../../util/routeUtil');
let request = require('request');
let logger = require('pomelo-logger').getLogger(__filename);

let msg = "马儿马儿快快跑，过来过来吃青草...";

let sendMsg = {channel: commonEnum.CHAT_CHANNEL.SYSTEM_CHANNEL, type: commonEnum.CHAT_TYPE.HORN, msg: msg,
    senderUid: "系统", senderName: "系统", rollingTime: 3};

let sendRollingMsg = function () {
    //console.log('sendSystemMail sendNum & data: ', uidArray.length, mailInfo);
    let postData = {
            sendMsg: sendMsg
        };
    let opt = {
        body: postData, // 需要post的数据
        json: true, //数据的格式
        url: 'http://'+routeUtil.getClusterHost()+':'+clusterConfig.authHttpPort+'/broadcast/rollingMsg',
        //url: 'http://*************'+':'+clusterConfig.authHttpPort+'/broadcast/rollingMsg',
    };
    //console.log('post opt:', opt);
    request.post(opt, function (err, httpResponse, body) {
        if (!!err) {
            console.log('Error :', err);
        }
        else {
            console.log(' Body :', body);
        }
        console.log('send rolling msg ok.');
    });
};

sendRollingMsg();
/*
let sendAllMail = function(uidArr, cb) {
    let allSendNum = uidArr.length;
    if(allSendNum <= 0) {
        console.log('allSendNum <= 0');
        return;
    }
    //分割批量发送
    let sendTimes = Math.floor(allSendNum/onceSendMaxNum);
    sendRound = 0;
    for(let n=0; n<sendTimes; n++) {
        let toSendUidArr = uidArr.slice(n*onceSendMaxNum, (n+1)*onceSendMaxNum);
        setTimeout(sendSystemMail, (n+1)*sendDurationTime, toSendUidArr, sendMailConfig, sendTimes, cb);
    }
    sendSystemMail(uidArr.slice(sendTimes*onceSendMaxNum, allSendNum-sendTimes*onceSendMaxNum), sendMailConfig, sendTimes, cb);
};

//主流程:
async.waterfall([
    //连接数据库
    function(callback){
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let dbUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(dbUrl, { useNewUrlParser: true },function(error, dbclient){
            if(!!error){
                console.log("sendMail connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            client = dbclient;
            dbClient = dbclient.db(clusterConfig.clusterDBName);
            console.log("sendMail connect clusterDB ok.");
            callback();
        });
    },
    //读取发送邮件的文件信息
    function(callback){
        console.log("toSendMail Info: ", sendMailConfig.title+'\n', sendMailConfig.content+'\n', sendMailConfig.attachList);
        callback();
    },
    //查询需要发送玩家uidArray
    function(callback){
        let uidArray = [];
        if(sendMailConfig.type === commonEnum.HTTP_SEND_MAIL_TYPE.SINGLE_PLAYER && (!!sendMailConfig.playerName)) {
            let collection = dbClient.collection("account");
            collection.findOne({name: sendMailConfig.playerName}, function (err, doc) {
                if(!!err){
                    console.log("one player: get account error. PlayerName:", sendMailConfig.playerName, err);
                    return callback(err);
                }
                if(!doc){
                    console.log("one player: get account null. PlayerName", sendMailConfig.playerName);
                    return callback("account null");
                }
                uidArray.push({uid: doc.uid, gid: doc.gid});
                callback(null, uidArray);
            });
        } else if(sendMailConfig.type === commonEnum.HTTP_SEND_MAIL_TYPE.ALL_SERVER) {
            let collection = dbClient.collection("account");
            collection.find({}).toArray(function (err, docs) {
                //console.log('findAll docs:', docs);
                for(let i=0,lens=docs.length;i<lens;i++) {
                    //if(docs[i].gid === "game-june-1" || docs[i].gid === "game-june-2") {
                        uidArray.push({uid: docs[i].uid, gid: docs[i].gid});
                    //}
                }
                callback(null, uidArray);
            });
        } else if(sendMailConfig.type === commonEnum.HTTP_SEND_MAIL_TYPE.MULTI_PLAYER) {
            let collection = dbClient.collection("account");
            if(!sendMailConfig.playerName || !sendMailConfig.playerName.length) {
                logger.error("type 3. playerName is not array");
                return callback(null, uidArray);
            }
            async.eachSeries(sendMailConfig.playerName, function (name, callback1) {
                collection.findOne({name: name}, function (err, doc) {
                    if(!!err){
                        console.log("multi player: get account error. PlayerName:", name, err);
                        return callback1(err);
                    }
                    if(!doc){
                        console.log("multi player: get account null. PlayerName", name);
                        return callback1("account null");
                    }
                    uidArray.push({uid: doc.uid, gid: doc.gid});
                    callback1(null);
                });
            }, function (err) {
                if(!!err) {
                    logger.debug("multi player err : ", err);
                }
                logger.debug("type 3 uidArray: ", uidArray.length);
                callback(null, uidArray);
            });
        }
    },
    //发送邮件http
    function(uidArray, callback){
        console.log("uidArray: ", uidArray);
        if(!uidArray || !uidArray.length) {
            return callback('uidArray error');
        }
        sendAllMail(uidArray, callback);
    },
    ], function(error, res){
    if(!!error){
        client.close();
        console.log("sendMail fail. error: ", error);
        return;
    }
    client.close();
    console.log("sendMail success!");
    process.exit();
});
*/
