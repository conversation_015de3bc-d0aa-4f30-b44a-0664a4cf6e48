let mongodb = require("mongodb");
let mongoClient = require("mongodb").MongoClient;
let mongoAdmin = require("mongodb").Admin;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');

let clusterConfig = require("../../../config/cluster.json");
let gameConfig = require("../../../config/game.json");
let playerName = "xx01";
let playerGid = "";
let playerUid = "";

let findItemResId = 12005;
let haveNum = 0;

/********************提取玩家oid，uid写入文件openId2Uid.json********************/
var clusterDbUser = clusterConfig.clusterDBName + '-admin';
var clusterDBUrl = "mongodb://" + clusterDbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;

async.waterfall([
    function (callback) {
        mongoClient.connect(clusterDBUrl, { useNewUrlParser: true },function(error, dbclient){
            if(error){
                logger.error("connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            let db = dbclient.db(clusterConfig.clusterDBName);
            db.collection("account", function (err1, col) {
                col.findOne({name: playerName}, function (err2, doc) {
                    if(!!err2) {
                        logger.error("find player name fail in account. playerName: ", playerName);
                        return callback(err2);
                    }
                    if(!doc) {
                        logger.debug("find player name doc is null", playerName);
                        return callback("doc is null");
                    }
                    playerUid = doc.uid;
                    playerGid = doc.gid;
                    logger.debug("player uid, gid: ", playerName, playerUid, playerGid);
                    dbclient.close();
                    callback(null);
                })
            })
        });
    }, function (callback) {
        let dbUser = playerGid + '-admin';
        let gameDbUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + playerGid;
        mongoClient.connect(gameDbUrl, { useNewUrlParser: true },function(error, dbclient){
            if(error){
                logger.error("connect gameDbUrl failed! err: " + error);
                return callback(error);
            }
            let db = dbclient.db(playerGid);
            db.collection("item", function (err1, col1) {
                col1.findOne({uid: playerUid}, function (err2, doc) {
                    for(let i=0, lens=doc.item.length; i<lens; i++) {
                        if(doc.item[i].ResID === findItemResId) {
                            haveNum += doc.item[i].Num;
                        }
                    }
                    dbclient.close();
                    callback(null);
                })
            });
        });
    }], function (err) {
        logger.debug("find result: the item have num: ", haveNum);
        process.exit();
});
