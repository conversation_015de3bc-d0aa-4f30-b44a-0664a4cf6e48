let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let clusterConfig = require("../../../config/cluster.json");

//唯一会变得  玩家名字
let playerName = "";

/********************提取玩家oid，uid写入文件openId2Uid.json********************/
var clusterDbUser = clusterConfig.clusterDBName + '-admin';
var clusterDBUrl = "mongodb://" + clusterDbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;

async.waterfall([
    function (callback) {
        mongoClient.connect(clusterDBUrl, { useNewUrlParser: true },function(error, dbclient){
            if(error){
                logger.error("connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            let db = dbclient.db(clusterConfig.clusterDBName);
            db.collection("account", function (err1, col) {
                col.findOne({name: playerName}, function (err2, doc) {
                    if(!!err2) {
                        logger.error("find player name fail in account. playerName: ", playerName);
                        return callback(err2);
                    }
                    if(!doc) {
                        logger.error("find player name doc is null", playerName);
                        return callback("doc is null");
                    }
                    dbclient.close();
                    callback(null, doc);
                })
            })
        });
    }, function (doc, callback) {
        logger.error("userName-->",doc.name, "  userOpenId-->", doc.oid, "  userUid-->", doc.uid);
        callback(null);
    }], function (err) {
        process.exit();
});
