let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
var fs = require("fs");

let clusterConfig = require('../../../config/cluster.json');
let brecordConfig = require('../../../config/brecord.json');
let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');

//统计联赛数据
let seasonId = 3;
let isForTest = false;

let dbClusterClient = {};
let dbBrecordClient = {};

let collectionNameList = [
    "community", 		//社区赛
    "normal", 			//常规赛
    "knockout", 		//淘汰赛
    "profession" 		//专业赛
];

let allBattleIdList = [];
let professionBattleMap = new Map();

//战斗数据缓存
let allBattleResultList = [];
let professionBattleResultList = [];

//最受欢迎阵型(阵型resId, 出现次数)
let formationMap = new Map();
let bestFormationId = 0;
let bestFormationPlayerList = new Map();    //{name, times}

//最受欢迎战术(战术resId, 出现次数)
let tacticMap = new Map();
let bestTacticId = 0;
let bestTacticPlayerList = new Map();

//各个位置最受欢迎球员
let positionUseList = {
    "GK": new Map(),
    "DL": new Map(),
    "DC": new Map(),
    "DR": new Map(),
    "DM": new Map(),
    "ML": new Map(),
    "MC": new Map(),
    "MR": new Map(),
    "AM": new Map(),
    "WL": new Map(),
    "ST": new Map(),
    "WR": new Map()
};
let bestPositionUse = {
    "GK": 0,
    "DL": 0,
    "DC": 0,
    "DR": 0,
    "DM": 0,
    "ML": 0,
    "MC": 0,
    "MR": 0,
    "AM": 0,
    "WL": 0,
    "ST": 0,
    "WR": 0
};
let bestPositionRatingList = {
    "GK": new Map(),    //{player: rating}
    "DL": new Map(),
    "DC": new Map(),
    "DR": new Map(),
    "DM": new Map(),
    "ML": new Map(),
    "MC": new Map(),
    "MR": new Map(),
    "AM": new Map(),
    "WL": new Map(),
    "ST": new Map(),
    "WR": new Map()
};

//各个位置战力最高球员
let positionRatingList = {
    "GK": new Map(),
    "DL": new Map(),
    "DC": new Map(),
    "DR": new Map(),
    "DM": new Map(),
    "ML": new Map(),
    "MC": new Map(),
    "MR": new Map(),
    "AM": new Map(),
    "WL": new Map(),
    "ST": new Map(),
    "WR": new Map()
};
let bestPositionRating = {
    "GK": 0,
    "DL": 0,
    "DC": 0,
    "DR": 0,
    "DM": 0,
    "ML": 0,
    "MC": 0,
    "MR": 0,
    "AM": 0,
    "WL": 0,
    "ST": 0,
    "WR": 0
};

//懂超冠军信息
let champion = {
    "uid": "",
    "name": "",
    "formationMap": new Map(),
    "tacticMap": new Map()
};

//最具进攻力玩家
let bestAttackerName = "";
let maxAttackerNum = 0;
let bestAttacker = {
    "formationMap": new Map(),
    "tacticMap": new Map()
};

//最佳防守玩家
let bestDefenderName = "";
let maxDefenderNum = 0;
let bestDefender = {
    "formationMap": new Map(),
    "tacticMap": new Map()
};

//进球数据/失球数据
let attackScoreMap = new Map();
let lostScoreMap = new Map();

//定位球进球最多球员(任意球+点球: attackMode 7,9)
let placeKickBaller = new Map();

let resultText = "";

async.waterfall([
    function (callback) {
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let clusterDBUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(clusterDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
            if(!!error) {
                logger.error("connect cluster db fail.", error);
                return callback(error);
            }
            dbClusterClient = dbclient;
            let db = dbClusterClient.db(clusterConfig.clusterDBName);
            let index = 0;
            let length = collectionNameList.length;
            async.whilst(
                function() {
                    //logger.debug("index , length: ", index, length);
                    return index < length;
                },
                function(cb){
                    let name = collectionNameList[index++];
                    let collection = db.collection(name);
                    collection.findOne({uid: seasonId}, function (err, doc) {
                        if(!!err){
                            logger.debug("find collection fail: ", name);
                            return cb(err);
                        }
                        logger.debug("find data: ", doc.battleMap.length);
                        for(let i=0,lens1=doc.battleMap.length; i<lens1; i++) {
                            allBattleIdList.push(doc.battleMap[i].battleObj.roomUid);
                            if(name === "profession") {
                                professionBattleMap.set(doc.battleMap[i].battleObj.roomUid, 1);
                            }
                        }
                        if(name === "profession") {
                            //logger.debug("rank: ", doc.finalRank[doc.finalRank.length-1]);
                            champion.uid = doc.finalRank[doc.finalRank.length-1].rankObjList[0].playerUid;
                        }
                        //logger.debug("allBattleIdList num:", allBattleIdList.length, allBattleIdList[0], doc.battleMap);
                        cb(null);
                    });
                },
                function(err){
                    if(!!err){
                        return callback(err);
                    }
                    logger.debug("get allBattleIdList finish! allBattleNum: ", allBattleIdList.length);
                    callback(null);
                }
            );
        });
    },
    function (callback) {
        let db = dbClusterClient.db(clusterConfig.clusterDBName);
        let collection = db.collection("account");
        collection.findOne({uid: champion.uid}, function (err, doc) {
            champion.name = doc.name;
            logger.debug("champion name: ", champion.name);
            callback(null);
        });
    },
    function (callback) {
        let dbUser = brecordConfig.brecordDBName + '-admin';
        let brecordDBUrl = "mongodb://" + dbUser + ':' + brecordConfig.dbPasswd + '@' + brecordConfig.brecordDBUrl + '/' + brecordConfig.brecordDBName;
        mongoClient.connect(brecordDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
            if(!!error) {
                logger.error("connect brecord db fail.", error);
                return callback(error);
            }
            dbBrecordClient = dbclient;
            let db = dbBrecordClient.db(brecordConfig.brecordDBName);
            let collection = db.collection("brecord");

            let professionBattleResultList = [];

            //for test
            if(isForTest) {
                allBattleIdList = [];
                allBattleIdList.push('X8_8zMHCihK6fClQXGEFZhqA');
            }

            logger.debug("start calc ...");

            async.eachSeries(allBattleIdList, function (roomUid, cb1) {
                collection.findOne({uid: roomUid},function (err, doc) {
                    if(!!err){
                        logger.debug("find brecord fail. roomUid: ", roomUid);
                        return cb1(err);
                    }
                    if(!doc) {
                        //logger.error("doc.result is null", roomUid);
                        return cb1(null);
                    }
                    if(professionBattleMap.has(roomUid) || isForTest) {
                        professionBattleResultList.push(doc.result);
                    }
                    allBattleResultList.push(doc.result);
                    for(let m=0; m<2; m++) {
                        //logger.debug("teamName: ", doc.result.battleEndInfo.stInfos);
                        //logger.debug("formationID: ", doc.result.preBattleInfo[m].formationID);
                        //logger.debug("teamRating: ", doc.result.preBattleInfo[m].rating);
                        //logger.debug("attackTacticID: ", doc.result.preBattleInfo[m].attackTacticID);
                        //logger.debug("memberInfo: ", doc.result.preBattleInfo[m].memberInfo);
                        let n = formationMap.get(doc.result.preBattleInfo[m].formationID) || 0;
                        formationMap.set(doc.result.preBattleInfo[m].formationID, ++n);
                        n = tacticMap.get(doc.result.preBattleInfo[m].attackTacticID) || 0;
                        tacticMap.set(doc.result.preBattleInfo[m].attackTacticID, ++n);
                        for(let i=0, lens1=doc.result.preBattleInfo[m].memberInfo.length; i<lens1; i++) {
                            let baller = doc.result.preBattleInfo[m].memberInfo[i];
                            let times = positionUseList[baller.position].get(baller.name) || 0;
                            positionUseList[baller.position].set(baller.name, ++times);
                            let obj = positionRatingList[baller.position].get(baller.name);
                            let rating = 0;
                            if(!!obj) {
                                rating = obj.rating;
                            }
                            if(rating < baller.rating) {
                                positionRatingList[baller.position].set(baller.name, {player: doc.result.preBattleInfo[m].teamName, rating: baller.rating});
                            }
                        }
                        //玩家进球记录
                        if(professionBattleMap.has(roomUid)) {
                            let atkTeamName = doc.result.preBattleInfo[m].teamName;
                            let oppIndex = 0;
                            if(m === 0) oppIndex = 1;
                            //let defTeamName = doc.result.preBattleInfo[oppIndex].teamName;
                            let atkScore = attackScoreMap.get(atkTeamName) || 0;
                            let addScore = doc.result.battleEndInfo.stInfos[m].shotNum;
                            attackScoreMap.set(atkTeamName, atkScore+addScore);
                            let defScore = lostScoreMap.get(atkTeamName) || 0;
                            let lostScore = doc.result.battleEndInfo.stInfos[oppIndex].shotNum;
                            lostScoreMap.set(atkTeamName, defScore+lostScore);
                        }
                        //logger.debug("doc.result", doc.result.battleRoundInfo);
                    }
                    //logger.debug("battle info: ", doc.result.preBattleInfo, doc.result.battleRoundInfo, doc.result.battleEndInfo);
                    cb1(null);
                });
            }, function (err) {
                //logger.debug("result : ", formationMap, tacticMap, positionUseList, positionRatingList);
                //1. 最受欢迎阵型
                let tmpNum = 0;
                for(let [k,v] of formationMap) {
                    if(bestFormationId === 0) {
                        bestFormationId = k;
                        tmpNum = v;
                    }else {
                        if(tmpNum < v) {
                            bestFormationId = k;
                            tmpNum = v;
                        }
                    }
                }
                resultText += "最受欢迎阵型id: " + bestFormationId + " , 出战次数: " + tmpNum + '\n';
                //2. 最受欢迎战术
                tmpNum = 0;
                for(let [k,v] of tacticMap) {
                    if(bestTacticId === 0) {
                        bestTacticId = k;
                        tmpNum = v;
                    }else {
                        if(tmpNum < v) {
                            bestTacticId = k;
                            tmpNum = v;
                        }
                    }
                }
                resultText += "最受欢迎战术id: " + bestTacticId + " , 出战次数: " + tmpNum + '\n';
                //3. 各个位置最受欢迎球员
                resultText += '\n' + "各个位置最受欢迎球员: " + '\n';
                for(let position in positionUseList) {
                    tmpNum = 0;
                    for(let [k,v] of positionUseList[position]) {
                        if(bestPositionUse[position] === 0) {
                            bestPositionUse[position] = k;
                            tmpNum = v;
                        }else {
                            if(tmpNum < v) {
                                bestPositionUse[position] = k;
                                tmpNum = v;
                            }
                        }
                    }
                    resultText += "位置[" + position + "]: 最受欢迎球员: " + bestPositionUse[position] + ", 出战次数: " + tmpNum + '\n';
                }
                //4. 各个位置培养最好球员
                resultText += '\n' +"各个位置战力最高球员: " + '\n';
                for(let position in positionRatingList) {
                    let tmpObj = 0;
                    for(let [k,v] of positionRatingList[position]) {
                        if(bestPositionRating[position] === 0) {
                            bestPositionRating[position] = k;
                            tmpObj = v;
                        }else {
                            if(tmpObj.rating < v.rating) {
                                bestPositionRating[position] = k;
                                tmpObj = v;
                            }
                        }
                    }
                    resultText += "位置[" + position + "]: 培养战力最高球员: " + bestPositionRating[position] + ", 战力: "
                        + tmpObj.rating + ', 所属玩家: ' + tmpObj.player +'\n';
                }
                //5. 最具进攻力玩家 + 最具防守玩家
                for(let [k, v] of attackScoreMap) {
                    if(bestAttackerName === "") {
                        bestAttackerName = k;
                        maxAttackerNum = v;
                    }else {
                        if(maxAttackerNum < v) {
                            bestAttackerName = k;
                            maxAttackerNum = v;
                        }
                    }
                }
                for(let [k, v] of lostScoreMap) {
                    if(bestDefenderName === "") {
                        bestDefenderName = k;
                        maxDefenderNum = v;
                    }else {
                        if(maxDefenderNum > v) {
                            bestDefenderName = k;
                            maxDefenderNum = v;
                        }
                    }
                }
                resultText += '\n' +"最具进攻力玩家: " + bestAttackerName + ", 进球数: " + maxAttackerNum + '\n';
                resultText += "最具防守玩家: " + bestDefenderName + ", 失球数: " + maxDefenderNum + '\n';

                //冠军,最具进攻力玩家,最具防守玩家数据统计:
                for(let i=0, lens=professionBattleResultList.length;i<lens;i++) {
                    let battleResult = professionBattleResultList[i];
                    for(let m=0; m<2; m++) {
                        if(champion.name === battleResult.preBattleInfo[m].teamName) {
                            let v = champion.formationMap.get(battleResult.preBattleInfo[m].formationID) || 0;
                            champion.formationMap.set(battleResult.preBattleInfo[m].formationID, ++v);
                            v = champion.tacticMap.get(battleResult.preBattleInfo[m].attackTacticID) || 0;
                            champion.tacticMap.set(battleResult.preBattleInfo[m].attackTacticID, ++v);
                        }
                        if(bestAttackerName === battleResult.preBattleInfo[m].teamName) {
                            let v = bestAttacker.formationMap.get(battleResult.preBattleInfo[m].formationID) || 0;
                            bestAttacker.formationMap.set(battleResult.preBattleInfo[m].formationID, ++v);
                            v = bestAttacker.tacticMap.get(battleResult.preBattleInfo[m].attackTacticID) || 0;
                            bestAttacker.tacticMap.set(battleResult.preBattleInfo[m].attackTacticID, ++v);
                        }
                        if(bestDefenderName === battleResult.preBattleInfo[m].teamName) {
                            let v = bestDefender.formationMap.get(battleResult.preBattleInfo[m].formationID) || 0;
                            bestDefender.formationMap.set(battleResult.preBattleInfo[m].formationID, ++v);
                            v = bestDefender.tacticMap.get(battleResult.preBattleInfo[m].attackTacticID) || 0;
                            bestDefender.tacticMap.set(battleResult.preBattleInfo[m].attackTacticID, ++v);
                        }
                        //最佳阵容胜场最多玩家
                        let oppIndex = 0;
                        if(m === 0) oppIndex = 1;
                        //最受欢迎阵型获胜最多玩家
                        if(battleResult.preBattleInfo[m].formationID === bestFormationId) {
                            if(battleResult.battleEndInfo.stInfos[m].shotNum >= battleResult.battleEndInfo.stInfos[oppIndex].shotNum) {
                                let times = bestFormationPlayerList.get(battleResult.preBattleInfo[m].teamName) || 0;
                                bestFormationPlayerList.set(battleResult.preBattleInfo[m].teamName, ++times);
                            }
                        }

                        //最受欢迎战术
                        if(battleResult.preBattleInfo[m].attackTacticID === bestTacticId) {
                            //battleResult.battleEndInfo.stInfos
                            if(battleResult.battleEndInfo.stInfos[m].shotNum >= battleResult.battleEndInfo.stInfos[oppIndex].shotNum) {
                                let times = bestTacticPlayerList.get(battleResult.preBattleInfo[m].teamName) || 0;
                                bestTacticPlayerList.set(battleResult.preBattleInfo[m].teamName, ++times);
                            }
                        }
                        //最受欢迎各个位置球员战力数据
                        for(let k in bestPositionUse) {
                            for(let index=0,lens0=battleResult.preBattleInfo[m].memberInfo.length;index<lens0;index++) {
                                let member = battleResult.preBattleInfo[m].memberInfo[index];
                                //logger.debug("1111", member, bestPositionUse[k]);
                                if(member.name === bestPositionUse[k]) {
                                    let r = bestPositionRatingList[k].get(battleResult.preBattleInfo[m].teamName) || 0;
                                    if(r < member.rating) {
                                        bestPositionRatingList[k].set(battleResult.preBattleInfo[m].teamName, member.rating);
                                    }
                                }
                            }
                        }
                    }
                    //球员定位球
                    for(let i=0,lens=battleResult.battleRoundInfo.length;i<lens;i++) {
                        if(battleResult.battleRoundInfo[i].attackMode === 7 || battleResult.battleRoundInfo[i].attackMode === 9) {
                            let l = battleResult.battleRoundInfo[i].periodInfo.length;
                            let resId = battleResult.battleRoundInfo[i].periodInfo[l-1].A1Info.resId;
                            let n = placeKickBaller.get(resId) || 0;
                            placeKickBaller.set(resId, ++n);
                        }
                    }
                }

                let bestFormationPlayer = getBestData(bestFormationPlayerList, true).id;
                let bestTacticPlayer = getBestData(bestTacticPlayerList, true).id;

                resultText += "最受欢迎阵型: " + bestFormationId + " , 胜场最多玩家名: " + bestFormationPlayer + '\n';
                resultText += "最受欢迎战术id: " + bestTacticId + " , 胜场最多玩家名: " + bestTacticPlayer + '\n';

                //各个最受欢迎位置战力最高
                resultText += "各个最受欢迎位置战力最高玩家数据: \n";
                for(let k in bestPositionRatingList) {
                    //logger.debug("bestPositionRatingList[k]: ", bestPositionRatingList[k]);
                    let bestData = getBestData(bestPositionRatingList[k], true);
                    resultText += "位置[" + k + "], 最受欢迎球员中战力最强的玩家: " + bestData.id + ", 战力: " + bestData.v + "\n";
                    // logger.debug("位置[" + k + "], 最受欢迎球员中战力最强的玩家: " + bestData.id + ", 战力: " + bestData.v);
                }

                let championBestFormation = getBestData(champion.formationMap, true);
                let championBestTactic = getBestData(champion.tacticMap, true);
                let attackerBestFormation = getBestData(bestAttacker.formationMap, true);
                let attackerBestTactic = getBestData(bestAttacker.tacticMap, true);
                let defenderBestFormation = getBestData(bestDefender.formationMap, false);
                let defenderBestTactic = getBestData(bestDefender.tacticMap, false);
                // logger.debug("best: ", championBestFormation, championBestTactic, attackerBestFormation,
                //     attackerBestTactic, defenderBestFormation, defenderBestTactic);
                let championBestBattle = {};
                let attackerBestBattle = {};
                let defenderBestBattle = {};

                for(let i=0, lens=professionBattleResultList.length;i<lens;i++) {
                    let battleResult = professionBattleResultList[i];
                    for(let m=0; m<2; m++) {
                        if(champion.name === battleResult.preBattleInfo[m].teamName && championBestFormation.id === battleResult.preBattleInfo[m].formationID) {
                            championBestBattle = battleResult.preBattleInfo[m].memberInfo;
                        }
                        if(bestAttackerName === battleResult.preBattleInfo[m].teamName && attackerBestFormation.id === battleResult.preBattleInfo[m].formationID) {
                            attackerBestBattle = battleResult.preBattleInfo[m].memberInfo;
                        }
                        if(bestDefenderName === battleResult.preBattleInfo[m].teamName && defenderBestFormation.id === battleResult.preBattleInfo[m].formationID) {
                            defenderBestBattle = battleResult.preBattleInfo[m].memberInfo;
                        }
                    }
                }
                //冠军实力最高的球员
                let championBestRating = {name: "", rating: 0, position: ""};
                for(let i=0,lens=championBestBattle.length;i<lens;i++) {
                    if(championBestRating.name === "") {
                        championBestFormation.name = championBestBattle[i].name;
                        championBestFormation.rating = championBestBattle[i].rating;
                        championBestFormation.position = championBestBattle[i].position;
                    }else if(championBestFormation.rating < championBestBattle[i].rating) {
                        championBestFormation.name = championBestBattle[i].name;
                        championBestFormation.rating = championBestBattle[i].rating;
                        championBestFormation.position = championBestBattle[i].position;
                    }
                }

                resultText += "冠军使用频率最高的阵容: " + championBestFormation.id + ", 战术: " + championBestTactic.id + ", 实力最高的球员: "
                    + championBestFormation.name + ", 位置: " + championBestFormation.position + ", 实力: " + championBestFormation.rating + "\n";
                resultText += "最具进攻力玩家 使用频率最高的阵容: " + attackerBestFormation.id + ", 战术: " + attackerBestTactic.id + "\n";
                // logger.debug("冠军使用频率最高的阵容: " + championBestFormation.id + ", 战术: " + championBestTactic.id +
                //     ", 实力最高的球员: " + championBestFormation.name + ", 位置: " + championBestFormation.position + ", 实力: " + championBestFormation.rating);
                // logger.debug("最具进攻力玩家 使用频率最高的阵容: " + attackerBestFormation.id + ", 战术: " + attackerBestTactic.id);

                resultText += "最具进攻力玩家 进攻组合数据: \n";
                for(let i=0,lens=attackerBestBattle.length;i<lens;i++) {
                    if(attackerBestBattle[i].position === "WL" || attackerBestBattle[i].position === "ST" || attackerBestBattle[i].position === "WR") {
                        // logger.debug("最具进攻力玩家 进攻组合: [" + attackerBestBattle[i].position + "]: " + attackerBestBattle[i].name);
                        resultText += "最具进攻力玩家 进攻组合: [" + attackerBestBattle[i].position + "]: " + attackerBestBattle[i].name + "\n";
                    }
                }
                // logger.debug("最佳防守玩家 使用频率最高的阵容: " + defenderBestFormation.id + ", 战术: " + defenderBestTactic.id);
                resultText += "最佳防守玩家 使用频率最高的阵容: " + defenderBestFormation.id + ", 战术: " + defenderBestTactic.id + "\n";
                resultText += "最佳防守玩家 防守组合数据: \n";
                for(let i=0,lens=defenderBestBattle.length;i<lens;i++) {
                    if(defenderBestBattle[i].position === "DL" || defenderBestBattle[i].position === "DC" || defenderBestBattle[i].position === "DR"
                    || defenderBestBattle[i].position === "DM" || defenderBestBattle[i].position === "GK") {
                        resultText += "最佳防守玩家 防守组合: [" + defenderBestBattle[i].position + "]: " + defenderBestBattle[i].name + "\n";
                        //logger.debug("最佳防守玩家 防守组合: [" + defenderBestBattle[i].position + "]: " + defenderBestBattle[i].name);
                    }
                }

                //定位球前5球员
                let arrayObj=Array.from(placeKickBaller);
                arrayObj.sort(function(a,b){return b[1]-a[1]});
                resultText += "定位球前5数据: \n";
                for(let i=0,lens=arrayObj.length;i<lens&&i<5;i++) {
                    resultText += "第" + (i+1) + "名: 球员resId: " + arrayObj[i][0] + ", 次数: " + arrayObj[i][1] + "\n";
                }
                //logger.debug(resultText);
                callback(null);
            });
        });
    }
], function (err) {
    dbClusterClient.close();
    dbBrecordClient.close();
    logger.debug("find league static data finished. \n", resultText);
    process.exit();
});


function getBestData(map, isMore) {
    let ret = {id: 0, v: 0};
    for(let [k,v] of map) {
        if(ret.id === 0) {
            ret.id = k;
            ret.v = v;
        }else {
            if(isMore) {
                if(ret.v < v) {
                    ret.id = k;
                    ret.v = v;
                }
            }else {
                if(ret.v > v) {
                    ret.id = k;
                    ret.v = v;
                }
            }
        }
    }
    return ret;
}

