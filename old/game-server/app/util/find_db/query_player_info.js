let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
let fs = require("fs");
let os = require('os')

let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');
let dbMap = new Map();

//格式化时间
let timeFormat = function (t) {
    let d = new Date();
    let timeString =  d.getFullYear() + "-" + (d.getMonth()+1) + "-" + d.getDate() + " "
        + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();
    return timeString;
};

//是否同一天
let isSameDay = function (t) {
    let d = new Date(t);
    let today = new Date();
    return d.getYear() === today.getYear() &&
        d.getMonth() === today.getMonth() &&
        d.getDate() === today.getDate();
};

let playerInfo = {
    loginNum:0,
    createNum:0,
    noCreateNum:0,
    queryTime: timeFormat()
};

async.eachSeries(serversConfig.development.game, function (gameInfo, cb1) {
    let serverId = gameInfo.id;
    let dbUser = serverId + '-admin';
    let gameDBUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
    mongoClient.connect(gameDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
        if(!!error) {
            logger.error("connect game db fail.", error, serverId);
            return cb1(error);
        }
        dbMap.set(serverId, dbclient);
        let db = dbclient.db(serverId);
        db.collection("player", function (err, col) {
            col.find().toArray(function (err, list) {
                async.eachSeries(list, function (obj, cb2) {
                    logger.error("obj.loginTime", obj.loginTime, "obj.createTime", obj.createTime, "obj.regTime", obj.regTime)
                    if(obj.createTime !== 0 && isSameDay(obj.loginTime)){
                        playerInfo.loginNum++;
                    }

                    if(obj.createTime !== 0 && isSameDay(obj.createTime)){
                        playerInfo.createNum++;
                    }

                    if(obj.createTime === 0 && isSameDay(obj.regTime)){
                        playerInfo.noCreateNum++;
                    }

                    cb2(null)
                }, function (err) {
                    cb1(null);
                });
            });
        })
    })
}, function (err) {
    //关闭数据库连接
    for(let [k,v] of dbMap) {
        v.close();
    }
    logger.error("登录人数:", playerInfo.loginNum, "创角人数:",playerInfo.createNum, "未创角:", playerInfo.noCreateNum)
    let filePath = __dirname + "/recordPlayerInfo.txt";
    fs.writeFileSync(filePath,JSON.stringify(playerInfo) + os.EOL, {flag: "a"});
    process.exit();
});



