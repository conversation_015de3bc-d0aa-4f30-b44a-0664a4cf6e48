//发送邮件的脚本
let async = require('async');
let mongoClient = require('mongodb').MongoClient;
let clusterConfig = require('../../../config/cluster.json');
let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');
let sendMailConfig = require('./needSend/toSend.json');
let commonEnum = require('../../../../shared/enum');
let http = require('http');
let querystring = require('querystring');
let routeUtil = require('../../util/routeUtil');
let timeUtil = require('../../util/timeUtils');
let request = require('request');
let logger = require('pomelo-logger').getLogger(__filename);

let alreadySendPath = "./alreadySend/";
let onceSendMaxNum = 1000;
let sendDurationTime = 60 * 1000;
let sendRound = 0;
let client = "";
let dbClient = "";
let gameClientMap = new Map();

let expireDay = 15;
let expireTime = timeUtil.now() - 15 * 24 * 3600 * 1000;
let isJustFind = false;

let sendSystemMail = function (uidArray, mailInfo, sendTimes, cb) {
    console.log('sendSystemMail sendNum & data: ', uidArray.length, mailInfo);
    let postData = {
            uidArray: uidArray,
            mail: mailInfo
        };
    let opt = {
        body: postData, // 需要post的数据
        json: true, //数据的格式
        url: 'http://'+routeUtil.getClusterHost()+':'+clusterConfig.authHttpPort+'/mail/sendSystemMail',
        //url: 'http://*************'+':'+clusterConfig.authHttpPort+'/mail/sendSystemMail',
    };
    //console.log('post opt:', opt);
    request.post(opt, function (err, httpResponse, body) {
        if (!!err) {
            console.log('Error :', err);
        }
        else {
            console.log(' Body :', body);
        }
        sendRound++;
        if(sendRound > sendTimes) {
            console.log('send All mails.');
            cb();
        }
    });
};

let sendAllMail = function(uidArr, cb) {
    let allSendNum = uidArr.length;
    if(allSendNum <= 0) {
        console.log('allSendNum <= 0');
        return;
    }
    //分割批量发送
    let sendTimes = Math.floor(allSendNum/onceSendMaxNum);
    sendRound = 0;
    for(let n=0; n<sendTimes; n++) {
        let toSendUidArr = uidArr.slice(n*onceSendMaxNum, (n+1)*onceSendMaxNum);
        setTimeout(sendSystemMail, (n+1)*sendDurationTime, toSendUidArr, sendMailConfig, sendTimes, cb);
    }
    sendSystemMail(uidArr.slice(sendTimes*onceSendMaxNum, allSendNum-sendTimes*onceSendMaxNum), sendMailConfig, sendTimes, cb);
};

//主流程:
async.waterfall([
    //连接数据库
    function(callback){
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let dbUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(dbUrl, { useNewUrlParser: true },function(error, dbclient){
            if(!!error){
                console.log("sendMail connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            client = dbclient;
            dbClient = dbclient.db(clusterConfig.clusterDBName);
            console.log("sendMail connect clusterDB ok.");
            callback();
        });
    },
    //读取发送邮件的文件信息
    function(callback){
        console.log("toSendMail Info: ", sendMailConfig.title+'\n', sendMailConfig.content+'\n', sendMailConfig.attachList);
        callback();
    },
    //查询需要发送玩家uidArray
    function(callback){
        let uidArray = [];
        if(sendMailConfig.type === commonEnum.HTTP_SEND_MAIL_TYPE.SINGLE_PLAYER && (!!sendMailConfig.playerName)) {
            let collection = dbClient.collection("account");
            collection.findOne({name: sendMailConfig.playerName}, function (err, doc) {
                if(!!err){
                    console.log("one player: get account error. PlayerName:", sendMailConfig.playerName, err);
                    return callback(err);
                }
                if(!doc){
                    console.log("one player: get account null. PlayerName", sendMailConfig.playerName);
                    return callback("account null");
                }
                uidArray.push({uid: doc.uid, gid: doc.gid});
                callback(null, uidArray);
            });
        } else if(sendMailConfig.type === commonEnum.HTTP_SEND_MAIL_TYPE.ALL_SERVER) {
            //连接游戏服
            let games = serversConfig.development.game;
            async.eachSeries(games, function (server, cb) {
                let dbUser = server.id + '-admin';
                let dbUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + server.id;
                mongoClient.connect(dbUrl, { useNewUrlParser: true },function(error, dbclient){
                    if(!!error){
                        console.log("mongoClient connect game failed! err: " + error, server.id);
                        return cb(error);
                    }
                    gameClientMap.set(server.id, dbclient);
                    //dbClient = dbclient.db(clusterConfig.clusterDBName);
                    console.log("sendMail connect game ok. gid: ", server.id);
                    //查询每个服的离线时间不超过15天的玩家，发邮件
                    let db = dbclient.db(server.id);
                    let collection = db.collection("player");
                    let findCondition = {$or: [{loginTime: {$gt: expireTime}}, {leaveTime: {$gt: expireTime}}]};
                    collection.find(findCondition).toArray(function (err, docs) {
                        for(let i=0,lens=docs.length;i<lens;i++) {
                            uidArray.push({uid: docs[i].uid, gid: server.id});
                        }
                        cb(null);
                    });
                });
            }, function (err) {
                logger.debug("err : ", err);
                if(!!err) {
                    return callback(err);
                }
                callback(null, uidArray);
            })
            /*
            async.waterfall([
                function (cb1) {

                },
                let collection = dbClient.collection("account");
                //查询全服账号
                function (cb1) {
                    collection.find({}).toArray(function (err, docs) {
                        for(let i=0,lens=docs.length;i<lens;i++) {
                            //uidArray.push({uid: docs[i].uid, gid: docs[i].gid});
                            allUidList.push({uid: docs[i].uid, gid: docs[i].gid});
                        }
                        cb1(null, allUidList);
                    });
                },
                ], function (err) {
                callback(null, uidArray);
            });
            */
        } else if(sendMailConfig.type === commonEnum.HTTP_SEND_MAIL_TYPE.MULTI_PLAYER) {
            let collection = dbClient.collection("account");
            if(!sendMailConfig.playerName || !sendMailConfig.playerName.length) {
                logger.error("type 3. playerName is not array");
                return callback(null, uidArray);
            }
            async.eachSeries(sendMailConfig.playerName, function (name, callback1) {
                collection.findOne({name: name}, function (err, doc) {
                    if(!!err){
                        console.log("multi player: get account error. PlayerName:", name, err);
                        return callback1(err);
                    }
                    if(!doc){
                        console.log("multi player: get account null. PlayerName", name);
                        return callback1("account null");
                    }
                    uidArray.push({uid: doc.uid, gid: doc.gid});
                    callback1(null);
                });
            }, function (err) {
                if(!!err) {
                    logger.debug("multi player err : ", err);
                }
                logger.debug("type 3 uidArray: ", uidArray.length);
                callback(null, uidArray);
            });
        }
    },
    //发送邮件http
    function(uidArray, callback){
        console.log("uidArray send num: ", uidArray.length);
        if(!uidArray || !uidArray.length) {
            return callback('uidArray error');
        }
        if(isJustFind) {
            console.log("just find ...");
            callback(null);
        }else {
            console.log("send all mail now ...");
            sendAllMail(uidArray, callback);
        }
    },
    ], function(error){
    if(!!error){
        client.close();
        for([k,v] of gameClientMap) {
            v.close();
        }
        console.log("sendMail fail. error: ", error);
        return;
    }
    client.close();
    for([k,v] of gameClientMap) {
        v.close();
    }
    console.log("sendMail success!");
    process.exit();
});
