let mongodb = require("mongodb");
let mongoClient = require("mongodb").MongoClient;
let mongoAdmin = require("mongodb").Admin;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');

let clusterConfig = require("../../../config/cluster.json");
//let clusterConfig = require("./cluster.json");
let brecordConfig = require("../../../config/brecord.json");

//获取cluster mongodb url
let testMongodbUrl = "mongodb://" + clusterConfig.clusterDBUrl + "/test";
let adminMongodbUrl = "mongodb://admin:Dslcfjz2019@" + clusterConfig.clusterDBUrl + "/?";
logger.debug("MongodbUrl: ", testMongodbUrl, adminMongodbUrl);

//根据需要修改
let isNeedAdmin = false;

async.waterfall([
    function(callback) {
        //1.连接test库, 添加admin用户
        if(!isNeedAdmin) {
            return callback(null);
        }
        mongoClient.connect(testMongodbUrl, { useNewUrlParser: true }, function(err1, client) {
            let db = client.db("test");
            const adminDb = new mongoAdmin(db);
            adminDb.addUser("admin","Dslcfjz2019",function(err2, result) {
                if (err2) {
                    //logger.error('addUser admin User fail. err: ', err2);
                }
                else {
                    logger.debug("addUser admin User Success. result: ", result);
                }
                client.close();
                callback(null);
            });
        });
    },
    function(callback) {
        //2.连接admin库, 添加clusterAdmin
        mongoClient.connect(adminMongodbUrl, { useNewUrlParser: true }, function(err1, client) {
            let dbName = clusterConfig.clusterDBName;
            let db = client.db(dbName);
            db.addUser(dbName+"-admin","Dslcfjz2019",function(err2, result) {
                if (err2) {
                    logger.error('addUser cluster User fail. err: ', err2);
                }
                else {
                    logger.debug("addUser cluster User Success. result: ", result);
                }
                client.close();
                callback(null);
            });
        });
    },
    function(callback) {
        //3.连接admin库, 添加brecordAdmin
        mongoClient.connect(adminMongodbUrl, { useNewUrlParser: true }, function(err1, client) {
            let dbName = brecordConfig.brecordDBName;
            let db = client.db(dbName);
            db.addUser(dbName+"-admin","Dslcfjz2019",function(err2, result) {
                if (err2) {
                    logger.error('addUser brecord User fail. err: ', err2);
                }
                else {
                    logger.debug("addUser brecord User Success. result: ", result);
                }
                client.close();
                callback(null);
            });
        });
    },
    ], function(error, res) {
        logger.debug("add cluster & brecord admin finished !");
    }
);

