var utils = module.exports;
var uidGenerator = require('uid-safe');
var crypto = require('crypto');
var os = require('os');

// control variable of func "myPrint"
var isPrintFlag = false;
// var isPrintFlag = true;

/**
 * Check and invoke callback function
 */
utils.invokeCallback = function(cb) {
  if(!!cb && typeof cb === 'function') {
    cb.apply(null, Array.prototype.slice.call(arguments, 1));
  }
};

/**
 * clone an object
 */
utils.clone = function(origin) {
  if(!origin) {
    return;
  }
  var obj = {};
  for(var f in origin) {
    if(origin.hasOwnProperty(f)) {
      obj[f] = origin[f];
    }
  }
  return obj;
};

/**
 * deepCopy(obj)
 * ps:递归调用浅拷贝
 */
utils.deepCopy = function(obj, t) {
  var t = t || {};
  for (var i in obj) {
    if (typeof obj[i] === 'object') {
      t[i] = (obj[i].constructor === Array) ? [] : {};
      this.deepCopy(obj[i], t[i]);
    } else {
      t[i] = obj[i];
    }
　}
  return t;
}

utils.cloneArray = function (originArr) {
  if(!originArr) {
    return [];
  }
  var arr = [];
  for(var f in originArr) {
    if(originArr.hasOwnProperty(f)) {
      arr.push(originArr[f]);
    }
  }
  return arr;
};

utils.size = function(obj) {
  if(!obj) {
    return 0;
  }

  var size = 0;
  for(var f in obj) {
    if(obj.hasOwnProperty(f)) {
      size++;
    }
  }

  return size;
};

// print the file name and the line number ~ begin
function getStack(){
  var orig = Error.prepareStackTrace;
  Error.prepareStackTrace = function(_, stack) {
    return stack;
  };
  var err = new Error();
  Error.captureStackTrace(err, arguments.callee);
  var stack = err.stack;
  Error.prepareStackTrace = orig;
  return stack;
}

function getFileName(stack) {
  return stack[1].getFileName();
}

function getLineNumber(stack){
  return stack[1].getLineNumber();
}

utils.myPrint = function(isPrintFlag) {
  if(isPrintFlag) {
    var len = arguments.length;
    if(len <= 0) {
      return;
    }
    var stack = getStack();
    var aimStr = '\'' + getFileName(stack) + '\' @' + getLineNumber(stack) + ' :\n';
    for(var i = 0; i < len; ++i) {
      aimStr += arguments[i] + ' ';
    }
    console.log('\n' + aimStr);
  }
};
// print the file name and the line number ~ end

//取n1,n2之间的随机数
utils.random = function(n1, n2) {
  if(n1 === n2) {
    return n1;
  }
  else if(n1 < n2) {
    let range = n2 - n1 + 1;
    return Math.floor((Math.random() * range) + n1);
  }
  else {
    let range = n1 - n2 + 1;
    return Math.floor((Math.random() * range) + n2);
  }
};

utils.getModelType = function(resId) {
  return Math.floor(resId/1000);
};

utils.arrayRemove = function (array, index) {
  for(var n=index;n<array.length;n++) {
    if(!array[n+1]) {
      break;
    }
    array[n] = array[n+1];
  }
  array[array.length-1] = null;
  array.length -= 1;
  return array;
};

//unicode 转码
utils.decodeToHex = function(str) {
  var res=[];
  for(var i=0;i < str.length;i++){
    res[i]=("00"+str.charCodeAt(i).toString(16)).slice(-4);
  }
  return "\\u"+res.join("\\u");
};

utils.hexToDecode = function(str) {
  str=str.replace(/\\/g,"%");
  return unescape(str);
};

//生成Uid

//1. 同步方式
var byteLength = 18;
utils.syncCreateUid = function() {
  return uidGenerator.sync(byteLength);
};

//2. 异步方式
utils.asyncCreateUid = function(cb) {
  uidGenerator(byteLength, function (err, uid) {
    cb(err, uid);
  })
};

utils.syncCreateShortUid = function() {
    return uidGenerator.sync(10);
};

//Map转ObjectArray
//example: var tArray = MapToObjectArray("uid", "hero", testMap);
utils.MapToObjectArray = function (key, value, map) {
  var array = [];
  if(!map) {
    return array;
  }
  for(var [k, v] of map) {
    var tmpObj = {};
    tmpObj[key] = k;
    tmpObj[value] = v;
    array.push(tmpObj);
  }
  return array;
};

//ObjectArray转Map
//example: var tMap = ObjectArrayToMap("uid", "hero", tArray);
utils.ObjectArrayToMap = function(key, value, array) {
  var map = new Map();
  for(var index in array) {
    map.set(array[index][key], array[index][value]);
  }
  return map;
};

//保留两位小数
utils.MathRound = function(value) {
  return Math.round( value * 100) / 100;
};

//保留五位小数
utils.Math5Round = function(value) {
  return Math.floor( value * 10000) / 10000;
};

utils.toMap = function(arr) {
  var map =  new Map();
  if (!arr)
  {
      return map;
  }

  for (var i in arr)
  {
     const object =  arr[i];
     var uid = object["Uid"];
     map.set(uid, object);
  }

  return map;
};

utils.toArray =  function(map) {
  var arr = [];
  if (!map)
  {
      return arr;
  }

  map.forEach( function(value, uid) {
      arr.push(value);
  });

  return arr;
};

//将数组索引随机,返回新的数组
utils.arrayIndexRandom = function(srcArr){
  let cloneSrcArr = utils.cloneArray(srcArr);
  let randList = [];
  let len = cloneSrcArr.length;
  for (var i = 0; i < len; i++) 
  {
    var index = Math.floor(Math.random()*(len - i));
    randList.push(cloneSrcArr[index]);
    cloneSrcArr.splice(index,1);
  }
  return randList;
};

//从一个数组中抽出指定原始
utils.getRandomArrayElements = function(arr, count) 
{
  let shuffled = arr.slice(0), i = arr.length, min = i - count, temp, index;
  while (i-- > min) 
  {
      index = Math.floor((i + 1) * Math.random());
      temp = shuffled[index];
      shuffled[index] = shuffled[i];
      shuffled[i] = temp;
  }
  return shuffled.slice(min);
}

//检查某个元素是否存在于数组中
utils.hasUidInList = function(uidList, desUid)
{
  let has = false;
  if (!uidList)
  {
    return has;
  }
  for (let i in uidList) {
    const uid = uidList[i];
    if (uid === desUid)
    {
        has = true;
        break;
    }
  }
  return has;
};

//移动元素 n 大于0向右移动 小于0向左移动
utils.moveElement = function(arr, n) 
{
    if(Math.abs(n)>arr.length) n = n%arr.length
    return arr.slice(-n).concat(arr.slice(0,-n))
};

//删除元素
utils.removeElement = function(arr, val) 
 {
    var index = arr.indexOf(val);
    if (index > -1) 
    {
        arr.splice(index, 1);
    }
};

//以A数组为基准,计算与B的差集
//Note:网上的代码，已经修复了bug 千万不要弄把参数弄反了!!!
utils.arraySubSet = function(array1, array2) {
    var len1 = array1.length;
    var len2 = array2.length;
    var arr1 = array1;
    let arr2 = array2;
    let len = 0;
    if (len1 >= len2)
        len = len1;
    else
    {
        len = len2;
        arr1 = array2;
        arr2 = array1;
    }
    var arr = [];
    while (len--) {
        if (arr2.indexOf(arr1[len]) < 0) {
            arr.push(arr1[len]);
        }
    }
    return arr;
};

//md5签名验证方法
utils.getCommonMd5Sign = function (signObj, secretKey) {
    let sortStr = sortAndLinkStr(signObj);
    //console.log('getCommonMd5Sign signStr: before', sortStr);
    let signStr = sortStr + "&key=" + secretKey;
    //console.log('getCommonMd5Sign signStr: now', signStr);
    const md5 = crypto.createHash('md5');
    md5.update(signStr);
    return md5.digest('hex');
};

var sortAndLinkStr = function (obj){
    let arr = [];
    let num = 0;
    for (let i in obj) {
        if(i === "sign") continue;
        arr[num] = i;
        num++;
    }
    var sortArr = arr.sort();
    let linkStr = "";
    for(let i=0, lens=sortArr.length; i<lens; i++) {
        if(i !== 0) {
            linkStr += '&';
        }
        linkStr += sortArr[i] + '=' + obj[sortArr[i]];
    }
    return linkStr;
};

//置某一位为1
utils.setBit = function(number, value)
{
    number |= (1 << value);
    //console.log("utils.setBit value, number", value, number);
    return number;
}

//检查某一位是否被置位
utils.isBit = function(number, value)
{
    let bit = 1 << value ;
    if (number & bit)
    {
        return true;
    }

    return false;
};

//重置某一位 
utils.resetBit = function(number, value)
{
    number &=~(1 << value);
    //console.log("setBit value", value, number);
    return number;
};

utils.getIPAddress = function () {
    var interfaces = os.networkInterfaces();
    for (var devName in interfaces) {
        var iface = interfaces[devName];
        for (var i = 0; i < iface.length; i++) {
            var alias = iface[i];
            if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
                return alias.address;
            }
        }
    }
};


utils.isNil = function(value) {
    return value === undefined || value === null;
}

/**
 * deepClone(obj)  深拷贝优化版
 * 可拷贝对象和数组
 */
utils.deepClone = function(obj) {
    if (obj === null) return null;
    let clone = Object.assign({}, obj);
    Object.keys(clone).forEach(
        key => (clone[key] = typeof obj[key] === 'object' ? this.deepClone(obj[key]) : obj[key])
    );
    return Array.isArray(obj) && obj.length ? (clone.length = obj.length)
        && Array.from(clone) : Array.isArray(obj) ? Array.from(obj) : clone;
}

//可判判断对象、数组、字符串是否为空
utils.isEmpty = function(val){
    return val == null || !(Object.keys(val) || val).length;
}

/**异步保存通用接口
 * @param app
 * @param isflush   是否立即保存  true：立即  false：定时保存
 * @param key       方法映射的关键词, 使用时需要唯一
 * @param id        实体对象主键  唯一
 * @param val       需要同步对象, 添加后会克隆此对象.
 * @param cb        同步完成后的异步回调, 可以为空
 * utils.funcSave(this.app, true, "dataNodeSync.saveChatMsgToDb", 1, saveObj, function (err) {})
 */
utils.funcSave = function(app, isflush, key, id, val, cb){
    if(isflush){
        app.get("sync").flush(key, id, val, cb);
    }else{
        app.get("sync").exec(key, id, val, cb);
    }
}