let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum.js');

let brecordConfig = require('../../../config/brecord.json');

/******************** 查询过期的战报数据，并处理删除 ********************/

let donnotDelete = true;

let dbUser = brecordConfig.brecordDBName + '-admin';
let drecordDBUrl = "mongodb://" + dbUser + ':' + brecordConfig.dbPasswd + '@' + brecordConfig.brecordDBUrl + '/' + brecordConfig.brecordDBName;

let dbClient;

let oneDayTime = 1 * 24 * 60 * 60 * 1000;  //1天
let nowTime = new Date().getTime();
//每30秒一次循环
let intervalTime = 30 * 1000;

//初始化: 记录删除的数量
let delNumList = {};
for(let index in commonEnum.BATTLE_TYPE) {
    delNumList[commonEnum.BATTLE_TYPE[index]] = 0;
}
logger.debug("delNumList: ", delNumList);

let maxDeleteNum = 10000000;
let oneLoopDeleteNum = 5000;
let loopArr = [];
for(let n=1,lens=Math.floor(maxDeleteNum/oneLoopDeleteNum);n<=lens;n++) {
    loopArr.push(n);
}

let loopFunction = function(col, cb) {
    let toBeDelList = [];
    async.waterfall([
        function (callback) {
            col.find({beginTime: {$lte: (nowTime - 2*oneDayTime)}}).limit(oneLoopDeleteNum).toArray( function (err, doc) {
                if(!doc || !doc.length) {
                    logger.error("get record db fail.");
                    return callback("get record db fail.");
                }
                for(let i=0, lens=doc.length;i<lens;i++) {
                    let data = doc[i];
                    let battleType = data.result.preBattleInfo[0].battleType;
                    if(battleType === commonEnum.BATTLE_TYPE.PvpMatch || battleType === commonEnum.BATTLE_TYPE.PvpDqCupMatch) {
                        //商业赛删除10天前的战报
                        if(data.beginTime < (nowTime - 10*oneDayTime)) {
                            toBeDelList.push({uid: data.uid, type: battleType});
                        }
                    }else if(battleType === commonEnum.BATTLE_TYPE.PvpLeague || battleType === commonEnum.BATTLE_TYPE.chairman ||
                        battleType === commonEnum.BATTLE_TYPE.PvpWarOfFaith || battleType === commonEnum.BATTLE_TYPE.peak ||
                        battleType === commonEnum.BATTLE_TYPE.PvpGroundMatch) {
                        if(data.beginTime < (nowTime - 35*oneDayTime)) {
                            toBeDelList.push({uid: data.uid, type: battleType});
                        }
                    }
                    else {
                        toBeDelList.push({uid: data.uid, type: battleType});
                    }
                }
                callback();
            });
        },
        function (callback) {
            if(donnotDelete) {
                logger.debug("toBeDelList: ", toBeDelList.length);
                return callback();
            }
            let i = 0;
            let length = toBeDelList.length;
            async.whilst(
                function () { return i<length; },
                function (cb1) {
                    let obj = toBeDelList[i++];
                    let uid = obj.uid;
                    let type = obj.type;
                    col.deleteOne({uid: uid}, function (err) {
                        if(err) {
                            logger.error("delete one record err: ", err, uid);
                        } else {
                            delNumList[type]++;
                        }
                        cb1();
                    })
                },
                function (err) {
                    callback();
                }
            )
        },
        function (callback) {
            /*
            col.countDocuments(function (err, num) {
                logger.debug("all record num in db after delete:", num);
                callback();
            });
            */
            callback();
        }
    ], function (err) {
        logger.debug("after loop delNumList: ", delNumList);
        cb();
    })
};

mongoClient.connect(drecordDBUrl, { useNewUrlParser: true },function(error, dbclient){
    if(error){
        logger.error("connect drecordDBUrl failed! err: " + error);
        return callback(error);
    }
    dbClient = dbclient;
    let db = dbclient.db(brecordConfig.brecordDBName);
    db.collection("brecord", function (err1, col) {
        let cursor = col.find({beginTime: {$lte: (nowTime - 2*oneDayTime)}});
        logger.debug("start get the data, start...");
        async.eachSeries(loopArr, function (index, cb) {
            logger.debug("now in delete loop index: ", index);
            if(index === 1) {
                loopFunction(col, cb);
            } else {
                setTimeout(loopFunction, 60*1000, col, cb);
            }
        }, function (err) {
            dbClient.close();
            process.exit();
        });
    })
});
