let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
let fs = require("fs");

let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');
let dataApi = require('../dataApi');

let dbMap = new Map();
let resultMap = new Map();

let fieldConfig = {};

let filePath = __dirname + "/../find_db/ground_fans_data.json";
let nullNum = 0;
let fixNum = 0;

let isFixBallFan = false;
let fixNameList = [];

async.waterfall([
    function (callback) {
        //初始化配置表
        setTimeout(()=>{
            let config = dataApi.allData.data["Field"];
            for(let k in config) {
                let key = config[k].Type + '_' + config[k].Level;
                fieldConfig[key] = config[k];
            }
            //logger.debug("fieldConfig: ", fieldConfig, config);
            callback();
        },2000)
    },
    function (callback) {
        fs.readFile(filePath, 'utf8', (err, data) => {
            if(err) {
                logger.debug("read file err: ", filePath, err);
                return callback(err);
            }
            let obj = JSON.parse(data);
            for(let k in obj) {
                let v = resultMap.get(obj[k].gid);
                if(!v) {
                    resultMap.set(obj[k].gid, new Map());
                    v = resultMap.get(obj[k].gid);
                }
                v.set(obj[k].uid, obj[k]);
            }
            callback();
        })
    },
    function (callback) {
        let calcBallFan = function (groundDoc) {
            let calcBallFan = 0;
            let keyArr = ["adminGround", "mainGround", "trainGround",
                "transferGround", "hospitalGround", "notableGround"];
            for (let i = 0, len = keyArr.length; i < len; i++) {
                let ground = groundDoc[keyArr[i]][0];
                let addFans = 0;
                if (ground.Type === 2 && ground.Level === 1) {
                    addFans = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.mainFans].Param;
                } else if (ground.Level > 1 && fieldConfig[ground.Type + '_' + ground.Level]) {
                    //logger.debug("ground.Type + ground.Level: ", ground.Type + '_' + ground.Level);
                    addFans = fieldConfig[ground.Type + '_' + (ground.Level - 1)].FansShow;
                }
                if (addFans > 0) {
                    //logger.debug("BallFan key, type, level: ", keyArr[i], ground.Type, ground.Level, addFans);
                    calcBallFan += addFans;
                }
            }
            return calcBallFan;
        };
        async.eachSeries(serversConfig.development.game, function (gameInfo, cb1) {
            let serverId = gameInfo.id;
            let dbUser = serverId + '-admin';
            let gameDBUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
            mongoClient.connect(gameDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
                if(!!error) {
                    logger.error("connect game db fail.", error, serverId);
                    return cb1(error);
                }
                dbMap.set(serverId, dbclient);
                let db = dbclient.db(serverId);
                let num = 0;
                //let list = [];
                db.collection("footballGround", function (err, col) {
                    col.find({$and: [{ballFan: {$gt: 0}}, {ballFan: {$lt: 1500}}]}).toArray(function (err, list) {
                        async.eachSeries(list, function (obj, cb2) {
                            col.findOne({uid: obj.uid}, function (err, groundDoc) {
                                if(err) {
                                    logger.debug("find footballGround err: ", obj);
                                    return cb2();
                                }
                                let calcFan = calcBallFan(groundDoc);
                                logger.debug("findOne groundDoc ballFan: ", groundDoc.ballFan, obj.uid, calcFan);
                                num++;
                                if(isFixBallFan) {
                                    if(!!groundDoc.ballFan) {
                                        nullNum++;
                                        logger.debug("newBallFan fix.", calcFan, groundDoc.ballFan);
                                        col.updateOne({uid: obj.uid}, {$set: {ballFan: calcFan}}, function (err) {
                                            if(!err) {
                                                fixNameList.push({uid: obj.uid, gid: serverId, afterFixedFan: calcFan})
                                                fixNum++;
                                                //logger.debug("fix ball fans: ", obj);
                                            }else {
                                                logger.debug("fix err: ", err);
                                            }
                                            return cb2();
                                        });
                                    }
                                    //return cb2();
                                }else {
                                    cb2();
                                }
                            })
                        }, function (err) {
                            logger.debug("fix num: ", num, serverId);
                            cb1();
                        });
                    });
                });
            });
        }, function (err) {
            logger.debug('connect game servers mongodb finish.');
            callback(null);
        })
    }], function (err) {
        //关闭数据库连接
        for(let [k,v] of dbMap) {
            v.close();
        }
        logger.debug("fix ground fans finish. updateNum: ", nullNum, fixNum, fixNameList.length);
        let filePath = __dirname + "/fix_fans_list2.json";
        fs.writeFileSync(filePath, JSON.stringify(fixNameList));
        process.exit();
});



