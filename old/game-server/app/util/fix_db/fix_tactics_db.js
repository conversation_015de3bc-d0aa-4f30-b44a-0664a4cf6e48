let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');
var fs = require("fs");
var dataApi = require("../dataApi");

let clusterConfig = require('../../../config/cluster.json');
let gameConfig = require('../../../config/game.json');
let serversConfig = require('../../../config/servers.json');

//修复战术等级数据
let dbClient = {};

let fixList = [];

async.waterfall([
    function (callback) {
        //初始化要修复数据
        initData();
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let clusterDBUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(clusterDBUrl, {useNewUrlParser: true}, function (error, dbclient) {
            if(!!error) {
                logger.error("connect cluster db fail.", error);
                return callback(error);
            }
            dbClient = dbclient;
            let db = dbclient.db(clusterConfig.clusterDBName);
            let collection = db.collection("account");
            callback(null, collection);
        });
    },
    function (collection, callback) {
        async.eachSeries(fixList, function (data, cb) {
            collection.findOne({
                name: data.Name
            }, function (err, doc) {
                if(!!err)
                {
                    return cb(null);
                }
                data.uid = doc.uid;
                data.gid = doc.gid;
                cb(null);
            })
        }, function (err) {
            callback(null);
        });
    },
    function (callback) {
        //连接游戏服
        let games = serversConfig.development.game;
        async.eachSeries(fixList, function (data, cb) {
            let dbUser = data.gid + "-admin";
            let dbUrl = "mongodb://" + dbUser + ':' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + data.gid;
            mongoClient.connect(dbUrl, { useNewUrlParser: true },function(error, dbclient){
                if(!!error){
                    console.log("mongoClient connect game failed! err: " + error, data.gid);
                    return cb(error);
                }
                console.log("sendMail connect game ok. gid: ", data.gid);
                let db = dbclient.db(data.gid);
                let collection = db.collection("teamFormation");
                collection.findOne({uid:data.uid},function (err, doc) {
                    if (!!err) {
                        cb(null);
                    }
                    //修改前
                    logger.error("teamFormation allTactics and allDefTactics 1", doc.allTactics, doc.allDefTactics);

                    for (let i = 1; i < 9; i++)
                    {
                        doc.allTactics[0][i.toString()] = data["Tactics" + i];
                        doc.allDefTactics[(i+10).toString()] = data["DefTactics" + i];
                    }
                    //修改当前只用战术等级
                    for(let i in doc.teamFormations)
                    {
                        let n = parseInt(doc.teamFormations[i].UseTactics / 100);
                        // logger.error("当前使用阵容进攻战术", i, n, doc.teamFormations[i].UseTactics);
                        doc.teamFormations[i].UseTactics = data["Tactics" + n];
                        // logger.error("修改后使用阵容进攻战术", i, doc.teamFormations[i].UseTactics);

                        let p = parseInt(doc.teamFormations[i].UseDefTactics / 100) - 10;
                        // logger.error("当前使用阵容防守战术", i, p, doc.teamFormations[i].UseDefTactics);
                        doc.teamFormations[i].UseDefTactics = data["DefTactics" + p];
                        // logger.error("修改后使用阵容防守战术", i, doc.teamFormations[i].UseDefTactics);
                    }
                    //修改后
                    logger.error("teamFormation allTactics and allDefTactics 2", doc.allTactics, doc.allDefTactics);

                    collection.updateOne({
                        uid:data.uid
                    },{
                        $set: doc
                    }, function (err) {
                        if(!!err){
                            logger.warn('update err',err);
                            return cb(err);
                        }
                        cb(null);
                    });
                });
            });
        }, function (err) {
            callback(null);
        });
    }
], function (err) {
    dbClient.close();
    logger.debug("fix finished.");
    process.exit();
});

function initData() {
    // 同步读取
    let fileName = __dirname + "/fix_tactics.json";
    fixList = JSON.parse(fs.readFileSync(fileName).toString());
    for(let i in fixList)
    {
        fixList[i].uid = "";
        fixList[i].gid = "";
    }
};