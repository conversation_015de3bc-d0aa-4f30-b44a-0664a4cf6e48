let mongoClient = require("mongodb").MongoClient;
let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let commonEnum = require('../../../../shared/enum');

let clusterConfig = require('../../../config/cluster.json');
let serversConfig = require('../../../config/servers.json');
let gameConfig = require('../../../config/game.json');

/******************** 查询过期的[玩家]数据，并处理删除 ********************/


//to do list: 1. 连接cluster, 连接game, 2. 查询统计过期的用户(level=0, gold=0, vip=0)  3.删除对应表的用户数据
let clusterDb = {};
let gameDbList = {};

//过期时间
let expireTime = 10 * 24 * 60 * 60 * 1000;  //10天过期时间
let nowTime = new Date().getTime();

//仅查询开关
let justFind = true;

//要被删除的uid列表
let deleteUidList = {};
let allPlayerUidList = [];

let gameCollections = [
    "player",
    "heros",
    "email",
    "item",
    "bag",
    "teamFormation",
    "leagueCopy",
    "scout",
    "tasks",
    "footballGround",
    "businessMatch",
    "trophyCopy",
    "trainer",
    "follow",
    "store",
    "newPlayerSign",
    "sevenDaySign",
    "everyDaySign",
    "vipShop",
    "seasonStore",
    "act",
    "seasonStore",
    "limitStore",
    "offlineEvent",
    "newerGuide",
    "worldCup",
    "everyDayEnergy",
    "newerTask",
    "middleEastCup",
    "gulfCup",
    "relay",
    "MLS",
    "sign",
    "commonActivity",
    "beliefSkill"
];

let clusterCollections = [
    "account",      //uid
    "matchRank",    //uid
    //"renameCard"   //playerId
];

async.waterfall([
    function (callback) {
        let dbUser = clusterConfig.clusterDBName + '-admin';
        let clusterUrl = "mongodb://" + dbUser + ':' + clusterConfig.dbPasswd + '@' + clusterConfig.clusterDBUrl + '/' + clusterConfig.clusterDBName;
        mongoClient.connect(clusterUrl, { useNewUrlParser: true }, function (error, dbclient) {
            if(error){
                logger.error("connect clusterDBUrl failed! err: " + error);
                return callback(error);
            }
            clusterDb = dbclient;
            callback(null);
        });
    },
    function (callback) {
        async.eachSeries(serversConfig.production.game, function (gameServer, cb) {
            let serverId = gameServer.id;
            let gameDbUrl = "mongodb://" + serverId+ '-admin:' + gameConfig.dbPasswd + '@' + gameConfig.gameDBUrl + '/' + serverId;
            mongoClient.connect(gameDbUrl, { useNewUrlParser: true }, function (error, dbclient) {
                if (error) {
                    logger.error("connect clusterDBUrl failed! err: " + error);
                    return callback(error);
                }
                //gameDbList.push(dbclient);
                gameDbList[serverId] = dbclient;
                let db = dbclient.db(serverId);
                db.collection("player", function (err, col) {
                    var cursor = col.find({level: {$eq: 1}, gold: {$eq: 0}, vip: {$eq: 0}, createTime: {$lte: (nowTime - expireTime)}});
                    cursor.forEach(function (player) {
                        if(!deleteUidList[serverId]) {
                            deleteUidList[serverId] = [];
                        }
                        deleteUidList[serverId].push(player.uid);
                        allPlayerUidList.push(player.uid);
                    }, function () {
                        cb();
                    });
                });
            })
        }, function (err) {
            callback(null)
        });
    },
    function (callback) {
        if(justFind) {
            return callback(null);
        }
        //删除game数据
        async.eachSeries(serversConfig.production.game, function (gameServer, cb1) {
            let serverId = gameServer.id;
            let db = gameDbList[serverId].db(serverId);
            let playerList = deleteUidList[serverId];
            if(!playerList) {
                return cb1();
            }
            async.eachSeries(playerList, function (playerUid, cb2) {
                deletePlayerData(db, playerUid, gameCollections, cb2);
            }, function (err) {
                if(!!err) {
                    logger.error("deletePlayerData fail.", err);
                    return cb1(err);
                }
                cb1();
            })
        }, function (err) {
            if(!!err) {
                logger.debug("delete player game data fail.", err);
                return callback(err);
            }
            callback(null);
        });
    },
    function (callback) {
        if(justFind) {
            return callback(null);
        }
        //删除cluster数据
        let db = clusterDb.db(clusterConfig.clusterDBName);
        async.eachSeries(allPlayerUidList, function (playerUid, cb) {
            deletePlayerData(db, playerUid, clusterCollections, cb);
        }, function (err) {
            if(!!err) {
                logger.error("delete player cluster data fail.", err);
                return callback(err);
            }
            callback(null);
        });
    }
], function (err) {
    logger.debug("clear player db finished ! clear player num: ", allPlayerUidList.length);
    clusterDb.close();
    for(let key in gameDbList) {
        gameDbList[key].close();
    }
    process.exit();
});

function deletePlayerData(db, playerUid, collectionList, callback) {
    let index = 0;
    //let db = dbclient.db(clusterConfig.clusterDBName);
    async.whilst(
        function () {
            return index < collectionList.length
        },
        function (cb) {
            let name = collectionList[index++];
            db.collection(name, function (error, collection) {
                if (!!error) {
                    logger.debug('delete data err, collectionName:', name, error);
                    return cb(error);
                }
                let deleteInfo = {};
                if(name === "renameCard") {
                    deleteInfo = {playerId: playerUid};
                }else {
                    deleteInfo = {uid: playerUid};
                }
                collection.deleteOne(deleteInfo, function (err, result) {
                    if (!!err) {
                        return cb(err);
                    }
                    logger.info("collection: %s, delete result: %j", name, result);
                    cb()
                })
            })
        },
        function (err) {
            if (!!err) {
                logger.warn("delete game db err:", err);
                return callback(err);
            }
            callback(null);
        });
}
