let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let http = require('http');
let qs = require('querystring');
let clusterConfig = require('../../../config/cluster');

//进程启动前的处理函数
module.exports.beforeStartup = function(app, cb) {
    // do some operations before application start up
    logger.debug("authServer beforeStartup now!");
    cb();
};

//进程启动后的处理函数
module.exports.afterStartup = function(app, cb) {
    // do some operations after application start up
    logger.debug("authServer afterStartup now!");
    cb();
};

//进程关闭前的处理函数
module.exports.beforeShutdown = function(app, cb) {
    // do some operations before application shutdown down
    logger.debug("authServer beforeShutdown now!");
    cb();
};

//所有服务器启动后的处理函数
module.exports.afterStartAll = function(app) {
    // do some operations after all applications start up
    logger.debug("authServer afterStartAll now!");
};
