/**
 * Idea and Persist
 * Created by June on 2016/11/16.
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');

//var playerConfig = require("../../../config/playerConfig.json");
var Code = require('../../../../../shared/code');
var utils = require('../../../util/utils');

module.exports = function(app){
    return new Handler(app);
};

var Handler = function(app){
    this.app = app;
};

