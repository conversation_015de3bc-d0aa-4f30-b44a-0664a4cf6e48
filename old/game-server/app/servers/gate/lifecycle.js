let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');

//进程启动前的处理函数
module.exports.beforeStartup = function(app, cb) {
    // do some operations before application start up
    logger.debug("gateServer beforeStartup now!");
    cb();
};

//进程启动后的处理函数
module.exports.afterStartup = function(app, cb) {
    // do some operations after application start up
    logger.debug("gateServer afterStartup now!");
    cb();
};

//进程关闭前的处理函数
module.exports.beforeShutdown = function(app, cb) {
    // do some operations before application shutdown down
    logger.debug("gateServer beforeShutdown now!");
    cb();
};

//所有服务器启动后的处理函数
module.exports.afterStartAll = function(app) {
    // do some operations after all applications start up
    logger.debug("gateServer afterStartAll now!");
    let games = app.getServersByType("game");
    let gateService = app.get("gateService");
    async.eachSeries(games, function (server, callback) {
        gateService.getAccountNumByGameServerId(server.id, function (err, num) {
            logger.debug("gateServer afterAllServerStartUp ", server.id, err, num);
            if(!gateService.gameServerInfo[server.id]) {
                gateService.gameServerInfo[server.id] = {};
            }
            gateService.gameServerInfo[server.id].accountNum = num;
            callback(err);
        });
    }, function (err) {
        if(!!err) {
            logger.warn("gateServer getAccountNumByGameServerId err : ", err);
        }
    })
};
