let logger = require('pomelo-logger').getLogger(__filename);
let async = require('async');
let clusterConfig = require('../../../config/cluster');
let dataApi = require('../../util/dataApi');

//进程启动前的处理函数
module.exports.beforeStartup = function(app, cb) {
    // do some operations before application start up
    logger.debug("matchServer beforeStartup now!");
    dataApi.allData.readWholeConfig(function (err) {
        cb();
    });
};

//进程启动后的处理函数
module.exports.afterStartup = function(app, cb) {
    // do some operations after application start up
    logger.debug("matchServer afterStartup now!");
    cb();
};

//进程关闭前的处理函数
module.exports.beforeShutdown = function(app, cb) {
    // do some operations before application shutdown down
    logger.debug("matchServer beforeShutdown now!");
    async.waterfall([
        function (callback) {
            logger.debug("matchServer beforeShutdown intervalGetAccount!");
            app.get("matchService").saveMatchRank(callback);
        }, 
        function (callback) {
            app.get("dqCupMatchService").updateDB(callback);
        },
        function (callback) {
            app.get("chairmanMatchService").updateDB();
            callback(null);
        },function (callback) {
                app.get("peakMatchService").updateDB();
                callback(null);
            }],
    function (err) {
        cb();
    });
};

//所有服务器启动后的处理函数
module.exports.afterStartAll = function(app) {
    logger.debug("matchServer afterStartAll now!");
    //初始化懂球杯数据
    app.get("matchService").init();
};
