var Code = require('../../../../../shared/code');
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var qs = require('querystring');

module.exports = function(app) {
	return new Remote(app);
};

var Remote = function(app) {
	this.app = app;
};

Remote.prototype.requestAllAccount = function(msg, next) {
	this.app.get("matchService").requestAllAccount(msg, function(code){
		if(code != Code.OK){
			next(null, {code: Code.FAIL});
			return;
		}
		//logger.error("requestAllAccount:", code);
		next(null, Code.OK);
	});
};
//得到当前玩家：球迷数量，排名
Remote.prototype.getFansRank = function(playerId, msg, next) {
	this.app.get("matchService").getFansRank(playerId, msg, function(code, ballFanCount, fanRank){
		next(null, {code: code, ballFanCount: ballFanCount, fanRank: fanRank});
	});
};
//得到当前玩家周最高排名
Remote.prototype.getWeekFansRank = function(playerId, next) {
	this.app.get("matchService").getWeekFansRank(playerId, function(code, weekFansRank){
		next(null, {code: code, weekFansRank: weekFansRank});
	});
};
//刷新当前玩家周最高排名
Remote.prototype.flashWeekFansRank = function(playerId, next) {
	this.app.get("matchService").flashWeekFansRank(playerId, function(code){
		next(null, {code: code});
	});
};
//得到排名前100的玩家球迷数量和排名发送给前端
Remote.prototype.getGlobalFansRank = function(playerId, next) {
	this.app.get("matchService").getGlobalFansRank(playerId, function(code, rankList){
		next(null, {code: code, rankList: rankList});
	});
};

Remote.prototype.matchBattleReq = function(msg, next) {
	this.app.get("matchService").matchBattleReq(msg, function(code, roomUid){
		next(null, {code: code, roomUid: roomUid});
	});
};

Remote.prototype.getPlayerFightTimeInfo = function(playerId, msg, next) {
	this.app.get("matchService").getPlayerFightTimeInfo(playerId, msg, function(code, fightTimesInfo){
		next(null, {code: code, fightTimesInfo: fightTimesInfo});
	});
};

Remote.prototype.buyPlayerFightTimes = function(playerId, next) {
	if(this.app.get("matchService").getAllTimes(playerId).buyTimes <= 0)
	{
		next(this.app.get("matchService").getAllTimes(playerId));
	}
	else if(this.app.get("matchService").buyPlayerFightTimes(playerId))
	{
		next(this.app.get("matchService").getAllTimes(playerId));
	}
	else
	{
		next(0, 0, 0);
	}
};

Remote.prototype.checkBuyPlayerFightTimes = function(playerId, next) {
	this.app.get("matchService").checkBuyPlayerFightTimes(playerId, function(result){
		next(result);
	});
};

Remote.prototype.getMatchUid = function(playerId, msg, next)
{
	this.app.get("matchService").getMatchUid(playerId, msg, function(code, matchUid){
		next(null, {code: code, matchUid: matchUid});
	});
};

Remote.prototype.updateNowAccountList = function(msg, next)
{
	this.app.get("matchService").updateNowAccountList(msg.accountList, function(code){
		next(null, {code: code});
	});
};

//懂球杯
Remote.prototype.dqMatchEnroll = function(msg, next)
{
	this.app.get("dqCupMatchService").playerEnroll(msg.playerId, msg.gid, msg.name, msg.faceUrl, function(code){
		next(code);
	});
};

Remote.prototype.getDqMatchInfo = function(msg, next)
{
	this.app.get("dqCupMatchService").getDqMatchInfo(msg.playerId, function(code, result){
		next(code, result);
	});
};

Remote.prototype.getDqMatchTopList = function(msg, next)
{
	this.app.get("dqCupMatchService").getDqMatchTopList(msg.playerId, function(code, result){
		next(code, result);
	});
};
//主席争夺战
Remote.prototype.chairmanMatchEnroll = function(msg, next)
{
	this.app.get("chairmanMatchService").playerEnroll(msg.beliefId, msg.playerId, msg.gid, msg.name, msg.faceUrl, function(code){
		next(code);
	});
};
Remote.prototype.getChairmanMatchInfo = function(msg, next)
{
	this.app.get("chairmanMatchService").getChairmanMatchInfo(msg.beliefId, msg.playerId, function(code, result){
		next(code, result);
	});
};

Remote.prototype.getChairmanMatchTopList = function(msg, next)
{
	this.app.get("chairmanMatchService").getChairmanMatchTopList(msg.beliefId, msg.playerId, function(code, result){
		next(code, result);
	});
};

Remote.prototype.coChairmanCampaign = function(msg, next)
{
	this.app.get("chairmanMatchService").coChairmanCampaign(msg, function(code, campaignList){
		next(code, campaignList);
	});
};
//巅峰战
Remote.prototype.getPeakMatchInfo = function(msg, next)
{
	this.app.get("peakMatchService").getPeakMatchInfo(msg.playerId, function(code, result){
		next(code, result);
	});
};

Remote.prototype.getPeakMatchTopList = function(msg, next)
{
	this.app.get("peakMatchService").getPeakMatchTopList(msg.playerId, function(code, result){
		next(code, result);
	});
};

Remote.prototype.StraightBet = function(msg, next)
{
	this.app.get("peakMatchService").betOnProcess(msg, function(code, total){
		next(code, total);
	});
};

Remote.prototype.getBetOnList = function(playerId, gold, beliefNum, next)
{
	this.app.get("peakMatchService").getBetOnForClient(playerId, gold, beliefNum, function(code, teamsInfo){
		next(code, teamsInfo);
	});
};
Remote.prototype.getNowPeriod = function(playerId, next)
{
	this.app.get("peakMatchService").getNowPeriod(playerId, function(matchPeriod, inform){
		next(matchPeriod, inform);
	});
};
//世界boss
Remote.prototype.getWorldBossInfo = function(msg, next)
{
	this.app.get("worldBossService").getWorldBossInfo(msg.playerId, msg.bossId, function(code, result){
		next(code, result);
	});
};

Remote.prototype.clockGetWorldBossInfo = function(msg, next)
{
	this.app.get("worldBossService").clockGetWorldBossInfo(msg.playerId, function(code, result){
		next(code, result);
	});
};

Remote.prototype.getWorldBossRank = function(next)
{
	this.app.get("worldBossService").getWorldBossRank(function(code, result){
		next(code, result);
	});
};

Remote.prototype.killWorldBossTime = function(msg, next)
{
	this.app.get("worldBossService").killWorldBossTime(msg.playerId, function(code){
		next(code);
	});
}

Remote.prototype.worldBossBattle = function(msg, next)
{
	this.app.get("worldBossService").worldBossBattle(msg.playerId, msg.gid, msg.teamAct, msg.name, msg.faceUrl, function(code, roomUid){
		next(code, roomUid);
	});
}

Remote.prototype.updateWorldBossResult = function(msg, next)
{
	this.app.get("worldBossService").updateWorldBossResult(msg.playerId, msg.teamCopyId, msg.selfScore, msg.addRatio, function(code, data, isKiller){
		next(code, data, isKiller);
	});
}

/************************************************************信仰之战*********************************************************************/
Remote.prototype.getAllWarOfFaith = function(msg, next)
{
	this.app.get("beliefMatchService").getAllWarOfFaith(function(code, allWarOfFaith){
		next(code, allWarOfFaith);
	});
};

Remote.prototype.beliefMatchEnroll = function(msg, next)
{
	this.app.get("beliefMatchService").beliefPlayerEnroll(msg.beliefId, msg.playerId, msg.gid, msg.name, msg.faceUrl, msg.strength, msg.type, function(code){
		next(code);
	});
};

Remote.prototype.challengeBelief = function(msg, next)
{
	this.app.get("beliefMatchService").challengeBelief(msg.beliefId, msg.otherBeliefId, msg.playerId, msg.gid, msg.name, msg.faceUrl, msg.strength, function(code){
		next(code);
	});
};

Remote.prototype.getWarOfFaithNextBattleInfo = function(msg, next)
{
	this.app.get("beliefMatchService").getWarOfFaithNextBattleInfo(msg.beliefId, msg.group, function(ret){
		next(ret);
	});
};

Remote.prototype.getWarOfFaithBattleInfo = function(msg, next)
{
	this.app.get("beliefMatchService").getWarOfFaithBattleInfo(msg.beliefId, msg.playerId, function(ret){
		next(ret);
	});
};

Remote.prototype.getWarOfFaithBattleResultInfo = function(msg, next)
{
	this.app.get("beliefMatchService").getWarOfFaithBattleResultInfo(msg.beliefId, msg.group, function(ret){
		next(ret);
	});
};

Remote.prototype.clearAllWarOfFaithData = function(msg, next)
{
	this.app.get("beliefMatchService").clearAllWarOfFaithData(msg, function(err){
		next(Code.OK);
	});
};

Remote.prototype.checkPlayerIsJoinMatch = function(msg, next)
{
	this.app.get("beliefMatchService").checkPlayerIsJoinMatch(msg.beliefId, msg.playerId, function(code){
		next(code);
	});
};

Remote.prototype.checkPlayerIsJoinMatchForDel = function(msg, next)
{
	this.app.get("beliefMatchService").checkPlayerIsJoinMatchForDel(msg.beliefId, msg.playerId, function(code){
		next(code);
	});
};

Remote.prototype.checkIsSendNotify = function(msg, next)
{
	this.app.get("beliefMatchService").checkIsSendNotify(msg.beliefId, msg.playerId, function(code, challengeList){
		next(code, challengeList);
	});
};

Remote.prototype.setFixFlag = function(msg, next)
{
	this.app.get("beliefMatchService").setFixFlag(msg, function(code){
		next(code);
	});
};


Remote.prototype.updateActConfig = function (msg, next) {
	this.app.get("matchService").updateActConfig(msg, function (err) {
		next(Code.OK);
	});
};


Remote.prototype.inspirePlayer = function(msg, next)
{
	this.app.get("beliefMatchService").inspirePlayer(msg, function(code, nextBattleTeam){
		next(code, nextBattleTeam);
	});
};

Remote.prototype.getInspireNum = function(msg, next)
{
	this.app.get("beliefMatchService").getInspireNum(msg, function(code, inspireNum){
		next(code, inspireNum);
	});
};

Remote.prototype.getBeliefReward = function(msg, next)
{
	this.app.get("beliefMatchService").getBeliefReward(msg, function(code, beliefNum, rewardList, beliefLivenessNum, cashNum){
		next(code, beliefNum, rewardList, beliefLivenessNum, cashNum);
	});
};

Remote.prototype.checkIsGetBeliefReward = function(msg, next)
{
	this.app.get("beliefMatchService").checkIsGetBeliefReward(msg, function(code){
		next(code);
	});
};

/************************************************************信仰之战*********************************************************************/










