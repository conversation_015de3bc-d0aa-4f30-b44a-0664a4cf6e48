/**
 * Created by sea on 2020/3/26.
 */
let logger = require('pomelo-logger').getLogger("pomelo", __filename);;
let async = require('async');
let Code = require('../../../shared/code');

module.exports.create = function(db){
	return new beliefDao(db);
};

let beliefDao = function(db){
	this.db = db;
};

//相关数据库Table
let CollectionNames = [
	"belief",
	"warOfFaith",
	"beliefMatch",
    "beliefActiveRank"
];

beliefDao.prototype.readWholeBelief = function(callback){
	let collection = this.db.collection("belief");
	collection.find().toArray().then(allBelief => callback(allBelief));
};

//信仰之战数据
beliefDao.prototype.readWholeWarOfFaith = function(callback){
	let collection = this.db.collection("warOfFaith");
	collection.find().toArray().then(allBelief => callback(allBelief));
};

//信仰之战状态
beliefDao.prototype.readWholeWarOfFaithMatch = function(callback){
	let collection = this.db.collection("beliefMatch");
	collection.find().toArray().then(allBelief => callback(allBelief));
};

beliefDao.prototype.readWholeAllBeliefActiveRank = function(callback){
	let collection = this.db.collection("beliefActiveRank");
	collection.find().toArray().then(rank => callback(rank));
};

beliefDao.prototype.findBeliefShopInfo = function (phase, cb) {
	let self = this;
	self.db.collection("beliefShopInfo", function (error, collection) {
		let newPhase = 0;
		collection.find().toArray(function (err, docs) {
			if(!!err) {
				logger.error("findBeliefShopInfo find error");
				return cb(err);
			}
			for(let i in docs)
			{
				if(docs[i].uid > newPhase)
				{
					newPhase = docs[i].uid;
				}
			}
			collection.findOne({uid: newPhase}, function (err, doc) {
				if(!!err) {
					logger.error("findBeliefShopInfo findOne err:", err, newPhase);
					return cb(err);
				}
				if(doc) {
					return cb(null, doc);
				}
				cb(null);
			});
		});
	});
};
beliefDao.prototype.updateBeliefShopInfo = function (record, cb) {
	let self = this;
	self.db.collection("beliefShopInfo", function (error, collection) {
		collection.findOne({uid: record.phase}, function (err, doc) {
			if(!!err) {
				logger.error("updateBeliefShopInfo findOne err:", err);
				return cb(err);
			}
			if(doc) {
				collection.updateOne({
					uid: record.phase
				},{
					$set: {record: record}
				}, function (err) {
					if(!!err){
						logger.error('updateBeliefShopInfo updateOne err', err);
						return cb(err);
					}
					logger.debug('updateBeliefShopInfo update success.');
					return cb(null);
				});
			}else {
				doc = {};
				doc.uid = record.phase;
				doc.record = {};
				doc.record = record;
				collection.insertOne(doc, {w: 1}, function (err, result) {
					if(!!err){
						logger.error('updateBeliefShopInfo insertOne err', err);
						return cb(err);
					}
					return cb(null);
				});
			}
		});
	});
};