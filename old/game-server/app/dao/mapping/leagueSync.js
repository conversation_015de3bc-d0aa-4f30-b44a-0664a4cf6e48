/**
 * Created by shine on 2015/4/13.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;

module.exports = {
	saveLeagueFsm: function(dbclient, val, callback){
		updateDB("leagueFsm", dbclient, val, callback);
	},
	saveCommunity: function(dbclient, val, callback){
		updateDB("community", dbclient, val, callback);
	},
	saveNormal: function(dbclient, val, callback){
		updateDB("normal", dbclient, val, callback);
	},
	saveKnockout: function(dbclient, val, callback){
		updateDB("knockout", dbclient, val, callback);
	},
	saveProfession: function(dbclient, val, callback){
		updateDB("profession", dbclient, val, callback);
	},
	saveEnrollTime: function(dbclient, val, callback){
		updateDB("enrollTime", dbclient, val, callback);
	},
};

var updateDB = function(collectionName, dbclient, val, callback){
	dbclient.collection(collectionName, function(error, collection) {
		if (!!error) {
			logger.debug('save data err, collectionName:',collectionName, error);
			callback(error);
			return;
		}

		collection.updateOne({
			uid: val.uid
		}, {
			$set: val.e.toJSONforDB()
		}, {w: 1}, function(err, result){
			if(!!err){
				callback(err);
				return;
			}

			//logger.info("collection: %s, save result: %j", collectionName, result);
			callback(null);
		});
	});
};