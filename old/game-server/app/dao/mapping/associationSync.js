/**
 * Created by shine on 2015/4/13.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
let async = require("async");

module.exports = {
	saveAssociation: function(dbclient, val, callback){
		updateDB("association", dbclient, val, callback);
	},

	removeAssociation: function(dbclient, val, callback){
		removeDB("association", dbclient, val, callback);
	},
};
var removeDB = function(collectionName, dbclient, val, callback) {
	dbclient.collection(collectionName, function (error, collection) {
		if (!!error) {
			callback(error);
			return;
		}
		collection.findOne({
			associationId: val.associationId
		}, function(err, doc) {
			if (!!err) {
				return callback(err);
			}

			if (doc) {
				collection.deleteOne({
					associationId: val.associationId
				}, function (err) {
					if (!!err) {
						callback(err);
						return;
					}
					logger.error("removeAssociationDB ok-------------------------", val.associationId);
					callback(null);
				});
			} else {
				callback(null);
			}
		})
	})
}

var updateDB = function(collectionName, dbclient, val, callback){
	dbclient.collection(collectionName, function(error, collection) {
		if (!!error) {
			logger.debug('save data err, collectionName:',collectionName, error);
			callback(error);
			return;
		}

		collection.findOne({
			associationId: val.associationId
		}, function(err, doc) {
			if(!!err){
				return callback(err);
			}

			if(!!doc){
				collection.updateOne({
					associationId: val.associationId
				}, {
					$set: {associationData: val.toJSONforDB()}
				}, {w: 1}, function(err, result){
					if(!!err){
						callback(err);
						return;
					}

					logger.error("saveAssociationDB-------------------", val.associationId);
					callback(null);
				});
			}else {
				collection.insertOne({
					associationId: val.associationId,
					associationData: val.toJSONforDB()
				}, {w: 1}, function(err){
					if(!!err){
						logger.debug('insert new association fail:', val.associationId);
						return callback(err);
					}

					logger.error('新建DB协会:--------------', val.associationId);
					callback(null);
				});
			}
		});
	});
};