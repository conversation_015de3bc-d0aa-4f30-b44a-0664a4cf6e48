/**
 * Created by sea on 2019/12/11.
 */
let logger = require('pomelo-logger').getLogger(__filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let utils = require('../../util/utils');
let timeUtils = require("../../util/timeUtils");
let dataApi = require('../../util/dataApi');
let commonEnum = require('../../../../shared/enum');

let gulfCup = function (player) {
    this.player = player;
    this.uid = player.playerId;
    this.teamList = [];     //队伍列表      这个就不要存DB了
    this.contestNum = 0;    //挑战次数
    this.isBegin = 0;       //是否已经在挑战 0 没有 1 有
    this.CombatList = [];   //战斗列表
    this.conditionID = 0;   //奖励条件ID
    this.conditionMax = 0;  //奖励条件数量
    this.goal = 0;          //总进球数
    this.fumble = 0;        //总失球数
    this.diff = 0;          //总净胜球数
    this.win = 0;           //胜利次数
    this.LuckyValue = 0;            //幸运值
    this.flashTime = 0;
    this.LuckyMax = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_lucky_max].Param;//幸运值上限
    this.contestNumMax = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_everyday_countMax].Param;//挑战次数上限
    this.rewardList = [];
};

util.inherits(gulfCup, EventEmitter);
module.exports = gulfCup;

gulfCup.prototype.initByDB = function (doc) {
    this.uid = doc.uid || "";
    this.contestNum = doc.contestNum || 0;
    this.isBegin = doc.isBegin ||  0;
    this.CombatList = doc.CombatList || [];
    this.conditionID = doc.conditionID || 0;
    this.goal = doc.goal || 0;
    this.fumble = doc.fumble || 0;
    this.diff = doc.diff || 0;
    this.win = doc.win || 0;
    this.LuckyValue = doc.LuckyValue || 0;
    this.flashTime = doc.flashTime || this.initFlashTime();
    this.rewardList = doc.rewardList || [];
};

gulfCup.prototype.toJSONforDB = function () {
    let gulfCupInfo = {
        uid: this.uid,
        contestNum: this.contestNum,
        isBegin: this.isBegin,
        CombatList: this.CombatList,
        conditionID: this.conditionID,
        goal: this.goal,
        fumble: this.fumble,
        diff: this.diff,
        win: this.win,
        LuckyValue: this.LuckyValue,
        flashTime: this.flashTime,
        rewardList: this.rewardList
    };
    return gulfCupInfo;
};
gulfCup.prototype.initFlashTime = function ()
{
    let date = new Date();
    date.setHours(7, 0, 0, 0);
    if(timeUtils.now() > date.getTime())
    {
        this.flashTime = date.getTime();//当天7点
    }
    else
    {
        this.flashTime = date.getTime() - 1000 * 60 * 60 * 24//昨天七点
    }
    return this.flashTime;
};
//是否第二天七点之后
gulfCup.prototype.isToDaySevenHours = function (time)
{
    if(time === 0)
    {
        let date = new Date();
        date.setHours(7, 0, 0, 0);
        time = date.getTime();
    }
    //超过一天直接刷新
    if(timeUtils.now() > time + 1000 * 60 * 60 * 24)
    {
        return true;
    }
    return false;
};
//隔天初始化数据
gulfCup.prototype.isToDayInit = function()
{
    let date = new Date();
    date.setHours(7, 0, 0, 0);
    if(this.isToDaySevenHours(this.flashTime)) {    //过了一天刷新
        //this.sendRewardMail();
        this.contestNum = 0;    //挑战次数
        this.isBegin = 0;       //是否已经在挑战 0 没有 1 有
        this.CombatList = [];   //战斗列表
        this.conditionID = 0;   //奖励条件ID
        this.goal = 0;          //总进球数
        this.fumble = 0;        //总失球数
        this.diff = 0;          //总净胜球数
        this.win = 0;           //胜利次数
        if(timeUtils.now() > date.getTime())
        {
            this.flashTime = date.getTime();//当天7点
        }
        else
        {
            this.flashTime = date.getTime() - 1000 * 60 * 60 * 24//昨天七点
        }
    }
};
//一轮结束初始化数据
gulfCup.prototype.roundEndInit = function()
{
    this.sendRewardMail();
    this.isBegin = 0;       //是否已经在挑战 0 没有 1 有
    this.CombatList = [];   //战斗列表
    this.conditionID = 0;   //奖励条件ID
    this.goal = 0;          //总进球数
    this.fumble = 0;        //总失球数
    this.diff = 0;          //总净胜球数
    this.win = 0;           //胜利次数
    this.getConfigTeam();
};
//发奖励邮件
gulfCup.prototype.sendRewardMail = function()
{
    if(this.isBegin === 0)
        return;
    if(this.rewardList.length > 0)
    {
        let condition = dataApi.allData.data["GulfCup"][this.conditionID].text;
        //发邮件
        let specialAttachInfo = {
            roomUid: ""
        };
        this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.GULFCUP, commonEnum.MailType.SYSMAIL, this.rewardList, specialAttachInfo, condition, this.win, this.goal, this.fumble);
        this.rewardList = [];
        return;
    }
    // else
    // {
        //发邮件
        let specialAttachInfo = {
                roomUid: ""
        };
        this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.GULFCUP_FAIL, commonEnum.MailType.SYSMAIL, [], specialAttachInfo,"","","","");
    // }
};
//修复数据
gulfCup.prototype.repairData = function()
{
    if(!!this.CombatList)
    {
        for(let i in this.CombatList)
        {
            this.CombatList[i].TeamA.IconID = this.CombatList[i].TeamA.IconID.toString();
            this.CombatList[i].TeamB.IconID = this.CombatList[i].TeamB.IconID.toString();
        }
    }
};
//得到配置的八支队伍
gulfCup.prototype.getConfigTeam = function ()
{
    let teamList = [];     //初始化八支队伍
    let cfg = {};

    let config = dataApi.allData.data["GulfCupTeam"];
    if (!config) {
        return cfg;
    }

    let idList = [];
    for(let i in config)
    {
        idList.push(config[i].ID);
    }
    let num = idList.length;
    let count = 0;
    for(let i = 0; i < idList.length; i)
    {
        let team = {};
        let k = Math.floor(Math.random() * idList.length);
        let cfgId = idList[k];
        let cfg = config[cfgId];
        idList.splice(k, 1);
        team.id = cfg.ID.toString();
        team.Name = cfg.Name;
        team.IconID = cfg.IconID.toString();
        team.Formation = cfg.Formation;
        team.OffensiveID = cfg.OffensiveID;
        teamList.push(utils.deepCopy(team));
        count++;
        if(count > num)//保护避免死循环
            break;
    }
    this.teamList = utils.cloneArray(teamList);
    return teamList;
};
//初始化对战列表
gulfCup.prototype.initCombatList = function (flag)
{
    if(!this.teamList)
    {
        this.teamList = this.getConfigTeam();
    }
    let rand = Math.round(Math.random() * this.teamList.length);

    let teamList = utils.cloneArray(this.teamList);
    teamList.splice(rand-1, 1);//八支队伍随机删除一支换成玩家队伍
    let playerTeam = {};
    let player = this.player;
    if(!player) {
        logger.debug('gulfCupService.initCombatList: player not exist !');
        cb(Code.FAIL, "");
        return;
    }
    playerTeam.id = this.uid;
    playerTeam.Name = player.name;
    playerTeam.IconID = player.faceUrl;
    playerTeam.Formation = 0;//formation;
    playerTeam.OffensiveID = 0;//formation.UseTactics;
    teamList.push(playerTeam);//把玩家加入队伍列表
    teamList = this.randomArr(teamList);
    this.CombatList = [];
    //通过队伍列表，初始化对战列表
    for(let idx = 0; idx < teamList.length; idx += 2)
    {
        let Combat = {TeamA: teamList[idx], TeamB: teamList[idx + 1]};
        this.CombatList.push(Combat);
    }
    //logger.error("初始化对战列表：：：：：：：：：：：：：", this.CombatList.length, this.CombatList);
    if(flag)
    {
        return;
    }
    let money = 0;
    switch (this.contestNum) {
        case 0:
            //第一次免费
            break;
        case 1:
            //扣除球币
            money = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_round2_Gold].Param;
            break;
        default :
            //之后每次球币
            money = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_more_Gold].Param;
    }
    this.isBegin = 1;//改为参加状态
    this.contestNum += 1;//挑战次数加1
    player.deductMoney(commonEnum.PLAY_INFO.gold, money);//扣除球币
};
//随机打乱一个数组
gulfCup.prototype.randomArr = function (arr) {
    let count = arr.length;
    let temp = [];
    for(let i = 0; i < count; i++) {
        let k = Math.floor(Math.random() * arr.length);
        temp.push(arr[k]);
        arr.splice(k, 1);
    }
    return temp;
};
//每轮战斗结束战斗列表改变,赢了才会执行
gulfCup.prototype.changeCombatList = function ()
{
    let tmpTeam = {};
    let tmpTeamList = [];
    for(let idx = this.CombatList.length - 1; idx > -1; idx--)
    {
        //logger.error("idx----------------------------", idx, this.CombatList.length);
        //取出玩家的队伍
        if (this.CombatList[idx].TeamA.id === this.uid)
        {
            tmpTeam = this.CombatList[idx].TeamA;//取出玩家队伍
            this.CombatList.splice(idx, 1);
            tmpTeamList.push(tmpTeam);
            continue;
        }
        else if (this.CombatList[idx].TeamB.id === this.uid)
        {
            tmpTeam = this.CombatList[idx].TeamB;//取出玩家队伍
            this.CombatList.splice(idx, 1);
            tmpTeamList.push(tmpTeam);
            continue;
        }
        //取出其他的队伍
        let rand = Math.fround(Math.random());
        if(rand === 1)
        {
            tmpTeam = this.CombatList[idx].TeamA;
            this.CombatList.splice(idx, 1);
            tmpTeamList.push(tmpTeam);
        }
        else
        {
            tmpTeam = this.CombatList[idx].TeamB;
            this.CombatList.splice(idx, 1);
            tmpTeamList.push(tmpTeam);
        }
    }
    this.CombatList = [];
    tmpTeamList = this.randomArr(tmpTeamList);//打乱
    //通过队伍列表，生成新的对战列表
    for(let idx = 0; idx < tmpTeamList.length; idx += 2)
    {
        let Combat = {TeamA: tmpTeamList[idx], TeamB: tmpTeamList[idx + 1]};
        this.CombatList.push(Combat);
    }
};
//得到奖励条件id
gulfCup.prototype.getConditionID = function ()
{
    let config = dataApi.allData.data["GulfCup"];
    let count = 0;
    for(let idx in config)
    {
        count++;
    }
    this.conditionMax = count;//得到所有条件的数量
    if(this.conditionID === 0)
    {
        this.conditionID = Math.floor(Math.random()*this.conditionMax) + 1;//随机获取一个
    }
    return  this.conditionID;
};
//判断是否满足条件领取奖励
gulfCup.prototype.checkAward = function ()
{
    //logger.error("判断是否满足条件领取奖励： 条件ID: %d, 进球：%d, 失球：%d, 净胜球：%d, 胜利场次：%d", this.conditionID, this.goal, this.fumble, this.diff, this.win);
    let flag = false;
    switch(this.conditionID)
    {
        case 1:
            if(this.goal > 10 && this.win === 3)//赢三场并且总进球数大于10
                flag = true;
            break;
        case 2:
            if(this.fumble < 5 && this.win === 3)//赢三场并且总失球数小于5
                flag = true;
            break;
        case 3:
            if(this.diff > 6 && this.win === 3)//赢三场并且总净胜球大于6
                flag = true;
            break;
    }
    return flag;
};
//处理战斗结果        自己得分和对手得分
gulfCup.prototype.pveGulfCupBattleResult = function (reamId, selfScore, otherScore) {
    let ret = {
        code: Code.OK,
        award: [],
        awardNum: [],
    };

    let awardType = 0;
    let addedType = 0;
    let awardNum = 1;//普通奖励物品数量
    let addedNum = 1;//额外奖励物品数量

    this.goal += selfScore;//进球
    this.fumble += otherScore;//失球
    if((selfScore - otherScore) > 0)//净胜
    {
        this.diff += selfScore - otherScore;
    }
    if(selfScore > otherScore)//赢了
    {
        this.win += 1;
        if(this.win < 3)//还没赢三场
        {
            this.changeCombatList();//改变战斗列表
        }
    }

    switch(this.contestNum)//根据挑战次数发奖励
    {
        case 1:
            awardType = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_round1_bonus].Param;
            addedType = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_round1_added].Param;
            break;
        case 2:
            awardType = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_round2_bonus].Param;
            addedType = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_round2_added].Param;
            break;
        default :
            awardType = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_more_bonus].Param;
            addedType = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_more_added].Param;
            break;
    }
   if(selfScore > otherScore && this.CombatList.length === 2)//第一轮的奖励
    {
        //发普通奖励
        let reward = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
            ResId: awardType,
            Num: awardNum,
        }
        this.rewardList.push(reward);
        ret.award.push(awardType);
        ret.awardNum.push(awardNum);
    }
   else
   {
       ret.award.push(0);
       ret.awardNum.push(0);
   }

    let flag = this.checkAward();
    if(flag)//判断是否完成特殊奖励
    {
        let reward = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
            ResId: addedType,
            Num: addedNum,
        }
        this.rewardList.push(reward);
        ret.award.push(addedType);
        ret.awardNum.push(addedNum);
    }
    else
    {
        ret.award.push(0);
        ret.awardNum.push(0);
    }

    //幸运奖
    ret = this.LuckyAward(reamId, selfScore, otherScore, ret);
    //logger.error("发完奖励：：：：：：：：：：：：", this.win, ret, selfScore, otherScore);

    if(this.win >= 3 || selfScore <= otherScore)//打完或输了
    {
        this.roundEndInit();//发完奖励初始化
    }
    //logger.error("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", ret);
    return ret;
};
//幸运奖
gulfCup.prototype.LuckyAward = function (reamId, selfScore, otherScore, ret)
{
    let drop = 0;
    let prob = Math.floor(Math.random()*10000 + 1);

    let config = dataApi.allData.data["GulfCupTeam"];
    let cfg = {};
    for(let i in config)
    {
        if(config[i].ID === reamId)
        {
            cfg = config[i];//得到对战的球队
        }
    }

    if(this.LuckyValue === this.LuckyMax)//幸运值满还是没满两种概率
    {
        drop =  dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_lucky_full].Param;//幸运值满了
    }
    else
    {
        drop =  dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_lucky_empty].Param;//幸运值没满
    }
    let itemId = 0;
    let num = 0;
    //logger.error("随机值：%d, 概率值：%d, 当前幸运值：%d", prob, drop, this.LuckyValue);
    if(prob < drop && selfScore > otherScore)//胜利并中奖
    //if(1)//胜利并中奖
    {
        this.LuckyValue = 0;//中奖重置幸运值
        let luckAward = [];
        let Probability = 0;
        for(let j = 1; j <= 3; j++)
        {
            let tmp = {
                Reward: 0,
                Num: 0,
                Probability: 0
            };
            if(cfg["Reward"+j])
            {
                tmp.Reward = cfg["Reward"+j];
                tmp.Num = cfg["Num"+j];
                tmp.Probability = cfg["Probability"+j];
                Probability += cfg["Probability"+j];
            }
            luckAward.push(utils.clone(tmp));
        }
        //logger.error("看看奖励都有啥：：：：：：：：：：：", luckAward);
        prob = Math.floor(Math.random()*Probability + 1);

        luckAward.sort(__lucky_Award_compare_func);

        //logger.error("看看排序后的都有啥：：：：：：：：：：：", luckAward, prob);
        for(let k = 0; k < luckAward.length; k++)
        {
            if(prob < luckAward[k].Probability)
            {
                itemId = luckAward[k].Reward;
                num = luckAward[k].Num;
                break;
            }
            if(k + 1 === luckAward.length)
            {
                itemId = luckAward[k].Reward;
                num = luckAward[k].Num;
            }
        }
        if(num > 0)
        {
            let reward = {
                ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
                ResId: itemId,
                Num: num,
            }
            this.rewardList.push(reward);
        }
        ret.award.push(itemId);
        ret.awardNum.push(num);
    }
    else//没获得额外奖励，增加幸运值
    {
        this.LuckyValue += dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_lucky_add].Param;
        if(this.LuckyValue > this.LuckyMax)
            this.LuckyValue = this.LuckyMax;
        ret.award.push(0);
        ret.awardNum.push(0);
    }

    return ret;
};

function __lucky_Award_compare_func(Award1, Award2)
{
    let Probability1= Award1.Probability;

    let Probability2 = Award2.Probability;


    if (Probability1 !== Probability2 ) {
        //降序
        if (Probability1 < Probability2) {
            return -1;
        }else if (Probability1 > Probability2) {
            return 1;
        }
    }

    let prob = Math.floor(Math.random());
    if(Probability1 === Probability2)
    {
        if(prob === 0)
        {
            return 1;
        }
        else
        {
            return -1;
        }
    }

    return 0;
};