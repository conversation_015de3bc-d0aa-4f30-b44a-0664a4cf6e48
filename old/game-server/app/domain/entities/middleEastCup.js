/**
 * Created by sea on 2019/11/18.
 */
let logger = require('pomelo-logger').getLogger(__filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let utils = require('../../util/utils');
let timeUtils = require("../../util/timeUtils");
let dataApi = require('../../util/dataApi');
let commonEnum = require('../../../../shared/enum');

let middleEastCup = function (player) {
    this.player = player;
    this.uid = player.playerId;
    this.contestNum = 0;        //挑战次数
    this.SponsorID = 0;         //赞助商ID
    this.isBegin = 0;       //是否已经在挑战 0 没有 1 有
    this.rivalTeamList = []; //对手列表
    this.teamList = []; //选择的三只队伍
    this.awardList = [];    //奖励列表 {bonus, added};
    this.flashTime = 0;
    this.contestNumMax = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_everyday_countMax].Param;      //挑战次数上限
    this.SponsorMax = 4;            //赞助商数量
    this.LuckyValue = 0;            //幸运值
    this.LuckyMax = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_lucky_max].Param;   //幸运值上限
};


util.inherits(middleEastCup, EventEmitter);
module.exports = middleEastCup;

middleEastCup.prototype.initByDB = function (doc) {
    this.uid = doc.uid || "";
    this.contestNum = doc.contestNum || 0;
    this.SponsorID = doc.SponsorID || 0;
    this.isBegin = doc.isBegin ||  0;
    this.rivalTeamList = doc.rivalTeamList || [];
    this.teamList = doc.teamList || [];
    this.awardList = doc.awardList || [];
    this.flashTime = doc.flashTime || this.initFlashTime();
    this.LuckyValue = doc.LuckyValue || 0;
};

middleEastCup.prototype.toJSONforDB = function () {
    let middleEastCupInfo = {
        uid: this.uid,
        contestNum: this.contestNum,
        SponsorID: this.SponsorID,
        isBegin: this.isBegin,
        rivalTeamList: this.rivalTeamList,
        teamList: this.teamList,
        awardList: this.awardList,
        flashTime: this.flashTime,
        LuckyValue: this.LuckyValue,
    };
    return middleEastCupInfo;
};
//修复数据
middleEastCup.prototype.repairData = function()
{
  if(!!this.teamList)
  {
      for(let i in this.teamList)
      {
          this.teamList[i].id = this.teamList[i].id.toString();
          this.teamList[i].IconID = this.teamList[i].IconID.toString();
      }
  }
};
//得到配置的三只队伍
middleEastCup.prototype.getConfigTeam = function ()
{
    let teamList = [];     //初始化三只队伍
    let cfg = {};

    let config = dataApi.allData.data["MiddleEastTeam"];
    if (!config) {
        return cfg;
    }
    let count = 0;
    let idList = [];
    for(let i in config)
    {
        count += 1;
        idList.push(config[i].ID);
    }

    for(let i = 0; i < 3; i++) {
        let team = {};
        let k = Math.floor(Math.random() * idList.length);
        let cfgId = idList[k];
        let cfg = config[cfgId];
        idList.splice(k, 1);
        team.id = cfg.ID.toString();
        team.Name = cfg.Name;
        team.IconID = cfg.IconID.toString();
        team.Formation = cfg.Formation;
        team.OffensiveID = cfg.OffensiveID;
        teamList.push(team);
    }

    return teamList;
};
//得到赞助商id
middleEastCup.prototype.getSponsorID = function ()
{
    this.SponsorID = Math.floor(Math.random()*this.SponsorMax) + 1;//随机获取一个
    return  this.SponsorID;
};
//检查额外条件是否达成
middleEastCup.prototype.checkIsAdded = function (selfScore, otherScore)
{
    let config = dataApi.allData.data["MiddleEastCup"];
    let cfg = config[this.SponsorID];
    switch (cfg.ID) {
        case 1:
            //胜利并且双方进球数大于4
            if((selfScore > otherScore) && (selfScore + otherScore > 4))
                return true;
            break;
        case 2:
            //胜利并且失球少于2
            if((selfScore > otherScore) && (otherScore < 2))
                return true;
            break;
        case 3:
            //胜利并且净胜球大于2
            if((selfScore > otherScore) && (selfScore - otherScore > 2))
                return true;
            break;
        case 4:
            //非平局
            if(selfScore !== otherScore)
                return true;
            break;
    }
    return false;
};
middleEastCup.prototype.initFlashTime = function ()
{
    let date = new Date();
    date.setHours(7, 0, 0, 0);
    if(timeUtils.now() > date.getTime())
    {
        this.flashTime = date.getTime();//当天7点
    }
    else
    {
        this.flashTime = date.getTime() - 1000 * 60 * 60 * 24//昨天七点
    }
    return this.flashTime;
};
//是否第二天七点之后
middleEastCup.prototype.isToDaySevenHours = function (time)
{
    if(time === 0)
    {
        let date = new Date();
        date.setHours(7, 0, 0, 0);
        time = date.getTime();
    }
    //超过一天直接刷新
    if(timeUtils.now() > time + 1000 * 60 * 60 * 24)
    {
        return true;
    }
    return false;
};
//得到选择球队界面  返回是否已经参加，没参加：赞助商id，可以选择的三只队伍的id，玩家挑战的次数
middleEastCup.prototype.getSelectInterface = function ()
{
    let date = new Date();
    date.setHours(7, 0, 0, 0);
    this.repairData();
    if(this.isToDaySevenHours(this.flashTime)) {    //过了一天刷新
        this.contestNum = 0;
        this.getSponsorID();         //赞助商ID
        this.isBegin = 0;       //是否已经在挑战 0 没有 1 有
        this.rivalTeamList = []; //对手列表
        this.teamList =  this.getConfigTeam();
        this.awardList = [];    //奖励列表 {bonus, added};
        if(timeUtils.now() > date.getTime())
        {
            this.flashTime = date.getTime();//当天7点
        }
        else
        {
            this.flashTime = date.getTime() - 1000 * 60 * 60 * 24//昨天七点
        }
    }
    let flag = false;
    //判断本轮是否结束
    for(let i = 0; i < this.awardList.length; i++)
    {
        if(this.awardList[i].bonus !== 0 && i === this.awardList.length - 1)
        {
            flag = true;
        }
    }
    if(this.teamList.length <= 0 && this.isBegin !== 1)
        flag = true;
    if(flag || this.awardList.length > this.rivalTeamList.length)//结束重置
    {
        this.getSponsorID();         //赞助商ID
        this.isBegin = 0;       //是否已经在挑战 0 没有 1 有
        this.rivalTeamList = []; //对手列表
        this.teamList =  this.getConfigTeam();
        this.awardList = [];    //奖励列表 {bonus, added};
    }

    //logger.error("getSelectInterface=========", this.isBegin, this.SponsorID, this.teamList, this.contestNum, this.rivalTeamList, this.awardList);
    let obj = {
        isBegin: this.isBegin,
        SponsorID: this.SponsorID,
        teamList: this.teamList,
        contestNum: this.contestNum,
        rivalTeamList: this.rivalTeamList,
        awardList: this.awardList
    };
    return obj;
};

//点击参加  返回对阵信息和奖励领取信息   玩家选两个队伍作为对手
middleEastCup.prototype.joinMatch = function (playerID, teamIDList)
{
    let ret = {};
    let code = Code.OK;
    if(this.contestNum >= this.contestNumMax)
    {
        //大于参加次数今天不能参加了
        code = Code.COUNT_FALL;
        ret.Code = code;
        return ret;
    }

    let money = 0;
    //判断第几次参加是否要消耗物品
    switch (this.contestNum) {
        case 0:
            //第一次免费
            break;
        case 1:
           //扣除球币
            money = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_round2_Gold].Param;
            break;
        default :
            //之后每次球币
            money = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_more_Gold].Param;
    }
    if(!this.player.checkMoneyIsEnough(commonEnum.PLAY_INFO.gold, money))//检查钱是否足够
    {
        logger.error('middleEastCup.joinMatch: money not enough !', this.uid);
        code = Code.GOLD_FALL;
        ret.Code = code;
        return ret;
    }
    if(!teamIDList || this.isBegin === 1 || teamIDList.length !== 2)
    {
        logger.error('middleEastCup.joinMatch: rivalTeamList err!', this.uid, teamIDList, this.isBegin);
        code = Code.PARAM_FAIL;
        ret.Code = code;
        return ret;
    }

    // //开始时清空下数据
    // this.rivalTeamList = []; //对手列表
    // this.awardList = [];    //奖励列表 {bonus, added};
    // this.flashTime = timeUtils.now();

    //安排赛程
    this.rivalTeamList = [];
    this.rivalTeamList =  this.randomArr(teamIDList);
    this.awardList = [];
    for(let i = 0; i < this.rivalTeamList.length; i++)
    {
        let award = {
            bonus: 0,   //奖励    0 不可领取 1 可以领取 4 以领取 参见 commonEnum.ACT_BTN_STATUS
            added: 0    //额外奖励  0 不可领取 1 可以领取 4 以领取
        };
        this.awardList.push(utils.deepCopy(award));
    }
    this.isBegin = 1;
    this.contestNum =  this.contestNum + 1;
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, money);//扣除球币

    ret.Code = code;
    ret.SponsorID = this.SponsorID;
    ret.contestNum =  this.contestNum;
    ret.rivalTeamList = this.rivalTeamList;
    ret.awardList = this.awardList;
    return ret;
};

//随机打乱一个数组
middleEastCup.prototype.randomArr = function (arr) {
    let count = arr.length;
    let temp = [];
    for(let i = 0; i < count; i++) {
        let k = Math.floor(Math.random() * arr.length);
        temp.push(arr[k]);
        arr.splice(k, 1);
    }
     return temp;
};

//处理战斗结果        自己得分和对手得分
middleEastCup.prototype.pveMiddleEastCupBattleResult = function (reamId, selfScore, otherScore) {
    let ret = {
        code: Code.OK,
        award: [],
        awardNum: [],
    }
    let rewardList = [];
    let awardNum = 0;
    let addedNum = 0;
    let count = 0;
    for(let i = 0; i < this.awardList.length; i++)
    {
        if(this.awardList[i].bonus !== 0)
        {
            count++;
        }
    }

    switch(this.contestNum)
    {
        case 1:
            awardNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_round1_bonus].Param;
            addedNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_round1_added].Param;
            break;
        case 2:
            awardNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_round2_bonus].Param;
            addedNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_round2_added].Param;
            break;
        default :
            awardNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_more_bonus].Param;
            addedNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_more_added].Param;
            break;
    }
    let awardType = dataApi.allData.data["Item"][1].ID;
    //发普通奖励
    //this.player.addResource(commonEnum.PLAY_INFO.cash, awardNum); //这里不发了改成发邮件
    let reward = {
        ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
        ResId: awardType,
        Num: awardNum,
    }
    rewardList.push(reward);
    ret.award.push(awardType);
    ret.awardNum.push(awardNum);
    if(count >= this.awardList.length)
    {
        count = this.awardList.length - 1;
    }
    if(!this.awardList[count].bonus)
    {
        logger.error("middleEastCup awardList[count].bonus err", count, this.awardList);
    }
    this.awardList[count].bonus = 4;//改为以领取
    let flag = this.checkIsAdded(selfScore, otherScore);
    if(flag)//判断是否完成特殊奖励
    {
         //发特殊奖励
        //this.player.addResource(commonEnum.PLAY_INFO.cash, addedNum); //改成发邮件
        let reward = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
            ResId: awardType,
            Num: addedNum,
        }
        rewardList.push(reward);
        ret.award.push(awardType);
        ret.awardNum.push(addedNum);
        this.awardList[count].added = 4;//改为以领取
    }

    //幸运奖
    this.LuckyAward(reamId, selfScore, otherScore, rewardList);
    //有奖励发邮件
    if(rewardList.length > 0)
    {
        let condition = dataApi.allData.data["MiddleEastCup"][this.SponsorID].text2;
        //发邮件
        let specialAttachInfo = {
            roomUid: ""
        };
        this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.MIDDLECUP, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, condition, selfScore, otherScore);
    }
    count++;//发完奖励加1次；
    if(count >= this.awardList.length || otherScore >= selfScore)//本轮挑战结束
    {
        this.getSponsorID();         //赞助商ID
        this.isBegin = 0;       //是否已经在挑战 0 没有 1 有
        this.rivalTeamList = []; //对手列表
        this.teamList =  this.getConfigTeam();
        this.awardList = [];    //奖励列表 {bonus, added};
    }
    //this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: this.player.cash}]); //欧元变更通知
    ret.isBegin = this.isBegin;
    return ret;
};
//幸运奖
middleEastCup.prototype.LuckyAward = function (reamId, selfScore, otherScore, rewardList)
{
    let drop = 0;
    let prob = Math.floor(Math.random()*10000 + 1);

    let config = dataApi.allData.data["MiddleEastTeam"];
    let cfg = {};
    for(let i in config)
    {
        if(config[i].ID === reamId)
        {
            cfg = config[i];//得到对战的球队
        }
    }

    if(this.LuckyValue === this.LuckyMax)//幸运值满还是没满两种概率
    {
        drop =  dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_lucky_full].Param;//幸运值满了
    }
    else
    {
        drop =  dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_lucky_empty].Param;//幸运值没满
    }
    let itemId = 0;
    let num = 0;
    //logger.error("随机值：%d, 概率值：%d, 当前幸运值：%d", prob, drop, this.LuckyValue);
    if(prob < drop && selfScore > otherScore)//胜利并中奖
    {
        this.LuckyValue = 0;//中奖重置幸运值
        let luckAward = [];
        let Probability = 0;
        for(let j = 1; j <= 3; j++)
        {
            let tmp = {
                Reward: 0,
                Num: 0,
                Probability: 0
            };
            if(cfg["Reward"+j])
            {
                tmp.Reward = cfg["Reward"+j];
                tmp.Num = cfg["Num"+j];
                tmp.Probability = cfg["Probability"+j];
               Probability += cfg["Probability"+j];
            }
            luckAward.push(utils.clone(tmp));
        }
        //logger.error("看看奖励都有啥：：：：：：：：：：：", luckAward);
        prob = Math.floor(Math.random()*Probability + 1);

        luckAward.sort(__lucky_Award_compare_func);

        //logger.error("看看排序后的都有啥：：：：：：：：：：：", luckAward, prob);
        for(let k = 0; k < luckAward.length; k++)
        {
            if(prob < luckAward[k].Probability)
            {
                itemId = luckAward[k].Reward;
                num = luckAward[k].Num;
                break;
            }
            if(k + 1 === luckAward.length)
            {
                itemId = luckAward[k].Reward;
                num = luckAward[k].Num;
            }
        }
        let reward = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
            ResId: itemId,
            Num: num,
        }
        rewardList.push(reward);
        this.player.updateBag();
        // ret.award.push(itemId);
        // ret.awardNum.push(num);
    }
    else//没获得额外奖励，增加幸运值
    {
        this.LuckyValue += dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.middleEastCup_lucky_add].Param;
        if(this.LuckyValue > this.LuckyMax)
            this.LuckyValue = this.LuckyMax;
    }
};

function __lucky_Award_compare_func(Award1, Award2)
{
    let Probability1= Award1.Probability;

    let Probability2 = Award2.Probability;


    if (Probability1 !== Probability2 ) {
        //降序
        if (Probability1 < Probability2) {
            return -1;
        }else if (Probability1 > Probability2) {
            return 1;
        }
    }

    let prob = Math.floor(Math.random());
    if(Probability1 === Probability2)
    {
        if(prob === 0)
        {
            return 1;
        }
        else
        {
            return -1;
        }
    }

    return 0;
};


