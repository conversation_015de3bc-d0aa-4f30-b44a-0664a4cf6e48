/**
 * Created by sea on 2019/5/28.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var timeUtils = require("../../util/timeUtils");
var dataApi = require('../../util/dataApi');
var commonEnum = require('../../../../shared/enum');
//任务结构
// let taskInfo = {
//     id:0,               //任务ID
//     type:0,             //任务类型
//     Num:0,              //数量
//     status:0            //0未完成， 1已完成
// }
var Tasks = function (player) {
    EventEmitter.call(this);
    this.player = player;
    this.uid = player.playerId;
    this.allTaskList = initTaskList(); //任务列表
    this.reTaskCount = 0; //刷新次数
    this.resetTime = Date.now(); //重置时间
    this.finishTask = []; //已完成的任务
    this.signResetTime = Date.now();//国庆签到重置时间

    //白嫖相关
    this.giftReTime = 0;           //礼包刷新时间
    this.everyDayTaskNum = 0;      //完成日常任务数量  每日会刷新
    this.everyDayGift = [];        //每日礼包     每日刷新
    this.everyDayRechargeNum = 0;  //充值金额     每日刷新
    this.firstGearTime = 0;        //6元激活时间
    this.secondGearTime = 0;       //30元激活时间

    this.beliefResetTime = 0;      //信仰任务刷新时间

    this.fixId = 0;
};

util.inherits(Tasks, EventEmitter);

module.exports = Tasks;

Tasks.prototype.initByDB = function (doc) {
    this.uid = doc.uid;
    this.allTaskList = doc.allTaskList || initTaskList();
    this.reTaskCount = doc.reTaskCount || 0;
    this.resetTime = doc.resetTime || Date.now();
    this.finishTask = doc.finishTask || [];
    this.signResetTime = doc.signResetTime || Date.now();
    this.giftReTime = doc.giftReTime || 0;
    this.everyDayTaskNum = doc.everyDayTaskNum || 0;
    this.firstGearTime = doc.firstGearTime || 0;
    this.secondGearTime = doc.secondGearTime || 0;
    this.everyDayRechargeNum = doc.everyDayRechargeNum || 0;
    this.everyDayGift = doc.everyDayGift || [];
    this.fixId = doc.fixId || 0;
    this.beliefResetTime = doc.beliefResetTime || 0;
    if (this.allTaskList.length < 5)  //[ {task4: []}]
    {
        let task = {task4 : []};
        this.allTaskList.push(task);
    }
    if (this.allTaskList.length < 6)  //[ {task5: []}]
    {
        let task = {task5 : []};
        this.allTaskList.push(task);
    }
    if (this.allTaskList.length < 7)  //[ {task6: []}]
    {
        let task = {task6 : []};
        this.allTaskList.push(task);
    }
    if (this.allTaskList.length < 8)  //[ {task7: []}]  信仰任务
    {
        let task = {task7 : []};
        this.allTaskList.push(task);
    }
    return this;
};

Tasks.prototype.toJSONforClient = function () {
    let allTaskList = {
        uid: this.uid,
        allTaskList: this.allTaskList,
        reTaskCount: this.reTaskCount,
        resetTime: this.resetTime,
        finishTask: this.finishTask,
        signResetTime: this.signResetTime,
    };
    return allTaskList;
};

Tasks.prototype.toJSONforDB = function () {
    let allTaskList = {
        uid: this.uid,
        allTaskList: this.allTaskList,
        reTaskCount: this.reTaskCount,
        resetTime: this.resetTime,
        finishTask: this.finishTask,
        signResetTime: this.signResetTime,
        giftReTime: this.giftReTime,
        everyDayTaskNum: this.everyDayTaskNum,
        firstGearTime: this.firstGearTime,
        secondGearTime: this.secondGearTime,
        everyDayRechargeNum: this.everyDayRechargeNum,
        everyDayGift: this.everyDayGift,
        fixId: this.fixId,
        beliefResetTime: this.beliefResetTime
    };
    return allTaskList;
};

//修复任务数据or新增个别任务
Tasks.prototype.fixTaskData = function () {
    //新增游戏圈子任务
    // this.addTask(21264);
    for (let i in this.allTaskList) {
        for(let j in this.allTaskList[i][commonEnum.TASK_INFO.taskType + i]) {
            if (this.allTaskList[i][commonEnum.TASK_INFO.taskType + i][j].Num !== 0 && !this.allTaskList[i][commonEnum.TASK_INFO.taskType + i][j].Num) {
                //已经完成得任务
                if(this.allTaskList[i][commonEnum.TASK_INFO.taskType + i][j].status === 1 || this.allTaskList[i][commonEnum.TASK_INFO.taskType + i][j].status === 2) {
                    let resId = this.allTaskList[i][commonEnum.TASK_INFO.taskType + i][j].resId;
                    let config = dataApi.allData.data["Task"][resId];
                    this.allTaskList[i][commonEnum.TASK_INFO.taskType + i][j].Num = config.Num;
                }else {
                    this.allTaskList[i][commonEnum.TASK_INFO.taskType + i][j].Num = 0;
                }
            }

            if(this.allTaskList[i][commonEnum.TASK_INFO.taskType + i][j].resId === 21264) {
                this.allTaskList[i][commonEnum.TASK_INFO.taskType + i].splice(j, 1);
            }
        }
    }
}

/**
 *初始化任务列表
 *表结构 [ { task0: [] }, { task1: [] }, { task2: [] }, { task3: [] } ]
 *分别为主线、支线、日常、成就、金牌教练、国庆签到、转播成就任务、信仰任务
 */
function initTaskList() {
    let tasklist = [];
    for (let i = 0; i < 8; ++i) {
        tasklist[i] = {};
        tasklist[i][commonEnum.TASK_INFO.taskType + i] = [];
    }
    return tasklist;
};


/**
 * 添加任务  任务id
 */
Tasks.prototype.addTask = function (resId) {
    let config = dataApi.allData.data["Task"][resId];
    if (!config) {
        return;
    }
    //检查要添加的任务是否已经完成
    if (!this._checkAddTaskIsFinish(resId)) {
        // logger.error("task is finish addTask", resId);
        return;
    }
    //检查任务表中是否已经有此任务
    if (config.Type !== 3 || config.Type !== 6) {
        if (!this._checkIsSameTask(config.Type, resId)) {
            // logger.error("task resId is Same addTask", resId);
            return;
        }
    }

    //检查任务列表是否已满  日常任务需要检查
    // if (config.Type === 3) {
    //     if (!this._checkTaskIsFull(config.Type)) {
    //         logger.error("task list is Full~~~~~~~addTask~~~~~~~~");
    //         return;
    //     }
    // }

    //初始化任务
    let taskInfo = this._initTask(resId);
    //添加一个任务
    let id = config.Type - 1;
    this.allTaskList[id][commonEnum.TASK_INFO.taskType + id].push(taskInfo);
};

/**
 * 删除金牌任务  任务id
 */
Tasks.prototype.delAllGoldCoachTask = function () 
{
    let id = commonEnum.TASK_INFO.goldCoachType - 1;
    //logger.info("delAllGoldCoachTask", this.allTaskList[id]);
    let goldCoachTaskList = this.allTaskList[id][commonEnum.TASK_INFO.taskType + id];
    let delList = [];
    for (let i in goldCoachTaskList) 
    {
        let obj = goldCoachTaskList[i];
        delList.push(obj.resId);
    }

    for (let idx in delList)
    {
        this._delGoldCoachTask(delList[idx]);
        //logger.info("delAllGoldCoachTask: delete id", delList[idx]);
    }
};

Tasks.prototype._delGoldCoachTask = function (resId) {
    let config = dataApi.allData.data["Task"][resId];
    if (!config) 
    {
        return;
    }

    let id = config.Type - 1;
    let index = -1;
    for (let i in this.allTaskList[id][commonEnum.TASK_INFO.taskType + id]) {
        if (resId === this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].resId) {
            index = i;
            break;
        }
    }

    if (index !== -1) {
        this.allTaskList[id][commonEnum.TASK_INFO.taskType + id].splice(index, 1); //删除任务
    }
};

//获取金牌教练任务
//note: 只提供给金牌教练使用
Tasks.prototype.getGoldTask = function(resId)
{
    let taskObj = null;
    let id = commonEnum.TASK_INFO.goldCoachType - 1;
    //logger.info("getGoldTask: resId", resId);
    let goldCoachTaskList = this.allTaskList[id][commonEnum.TASK_INFO.taskType + id];
    //logger.error("ssgetGoldTaskss---------------", goldCoachTaskList);
    for(let idx in goldCoachTaskList)
    {
        let obj = goldCoachTaskList[idx];
        if (obj.resId === resId)
        {
            taskObj = utils.deepCopy(obj);
            break;
        }
    }
    //logger.info("getGoldTask: resId, taskObj", resId, taskObj);
    return taskObj;
};

Tasks.prototype.checkGoldTaskIsComplete = function (arr) {
    for (let i in arr) {
        let resId = arr[i].resId;
        this.checkIsExistGoldTask(resId);
    }
}

Tasks.prototype.checkIsExistGoldTask = function (resId) {
    let id = commonEnum.TASK_INFO.goldCoachType - 1;
    let goldCoachTaskList = this.allTaskList[id][commonEnum.TASK_INFO.taskType + id];
    let isHave = false;
    for (let i in goldCoachTaskList) {
        let obj = goldCoachTaskList[i];
        if (obj.resId === resId) {
            isHave =true;
            break;
        }
    }

    if(!isHave) {
        this.addTask(resId)
    }
}


/**
 * 添加一个日常任务  任务id
 */
Tasks.prototype.addEverydayTask = function (resId, index) {
    let config = dataApi.allData.data["Task"][resId];
    if (!config) {
        return;
    }
    //初始化任务
    let taskInfo = this._initTask(resId);
    //添加一个任务
    let id = config.Type - 1;
    this.allTaskList[id][commonEnum.TASK_INFO.taskType + id].splice(index, 0, taskInfo);
};

/**
 * 设置某个任务完成  任务id
 */
Tasks.prototype.csSetTaskFinish = function (type, resId) {
    if (!resId) {
        return Code.FAIL;
    }
    let id = type - 1;
    this._finishTask(id, resId);
    return Code.OK;
};

/**
 * 获取一个任务列表   type 任务列表类型   
 */
Tasks.prototype.getOneTaskList = function (type) {
    if (type < 1 || type > 5) {
        logger.error("getOneTaskList  type is fail~~~~~~~~~", type);
        return [];
    }
    let id = type - 1;
    if (type !== 3) {
        this.allTaskList[id][commonEnum.TASK_INFO.taskType + id].sort(this._compare("resId"));
    }
    let tasklist = [];
    for (let i = 0; i < 4; ++i) {
        tasklist[i] = {};
        if (id === i) {
            tasklist[i] = this.allTaskList[id];
            continue;
        }
        tasklist[i][commonEnum.TASK_INFO.taskType + i] = [];
    }
    // logger.error("u--------------------------", tasklist);
    return tasklist;
}

/**
 * 获取所有任务列表   type 任务列表类型   
 */
Tasks.prototype.getAllTaskList = function () {
    let ret = {};
    this.resetSignTask();
    this.resetTask(); //日常任务重置检查
    this.resetGiftTask();
    for (let i in this.allTaskList) {
        if (this.allTaskList[i][commonEnum.TASK_INFO.taskType + i].length < 1) {
            continue;
        }
        if (i === "2") {
            continue;
        }
        this.allTaskList[i][commonEnum.TASK_INFO.taskType + i].sort(this._compare("resId"));
    }

    let firstGearDay = timeUtils.dayInterval(this.firstGearTime);
    let secondGearDay = timeUtils.dayInterval(this.secondGearTime);
    ret.allTaskList = this.allTaskList;
    ret.reTaskCount = this.reTaskCount;
    ret.finishTask = this.finishTask;
    //礼包
    ret.firstGearDay = firstGearDay;
    ret.secondGearDay = secondGearDay;
    ret.everyDayGift = this.everyDayGift;
    ret.everyDayTaskNum = this.everyDayTaskNum;
    // logger.error("getAllTaskList：：：：：：：：：：：：：：：：：：：：", firstGearDay, secondGearDay, ret.everyDayTaskNum)
    return ret;
}

/**
 * 删除一个任务  任务类型type  任务id
 */
Tasks.prototype.delTask = function (type, resId) {
    let id = type - 1;
    let index = -1;
    for (let i in this.allTaskList[id][commonEnum.TASK_INFO.taskType + id]) {
        if (resId === this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].resId) {
            index = i;
            break;
        }
    }
    if (index !== -1) {
        if (type !== 3) {
            this.finishTask.push(resId);
        }

        this.allTaskList[id][commonEnum.TASK_INFO.taskType + id].splice(index, 1); //删除任务
    }
    return index;
};

/**
 * 领取任务奖励  任务类型type  任务resId
 */
Tasks.prototype.getTaskReward = function (type, resId) {
    if (!type || !resId) {
        return Code.FAIL;
    }
    let id = type - 1;
    let isFinish = this._checkIsFinish(id, resId);
    if (!isFinish) {
        return Code.TASKS.NOTFINISH;
    }
    let rewardList = [];
    let index = 0;
    let config = dataApi.allData.data["Task"][resId];
    for (let k = 1; k < 6; ++k) {
        let rewardId = config["Reward" + k];
        let rewardNum = config["Num" + k];
        if (rewardId <= 0 || rewardNum <= 0) {
            continue;
        }
        rewardList[index] = {};
        rewardList[index].ResId = rewardId;
        rewardList[index].Num = rewardNum;
        index++;
    }

    for (let i in rewardList) {
        this.player.bag.addItem(rewardList[i].ResId, rewardList[i].Num);
    }

    this.player.checkGuideTrigger(commonEnum.NEWER_GUIDE_TRIGGER_CONDITION.TASK, resId);

    //删除任务
    if (type !== 3 && type !== 7) {
        this.delTask(type, resId);
        let addResId = this._getOneTask(type, resId);
        if (addResId !== 0) {
            this.addTask(addResId);
        }
    }else if(type === 3 || type === 7) {
        for (let i in this.allTaskList[id][commonEnum.TASK_INFO.taskType + id]) {
            if (resId !== this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].resId) {
                continue;
            }
            let status = this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].status;
            if(status === 1) {
                this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].status = 2;   //日常 转播 2已领取奖励
            }
        }
    }
    //红点
    this.player.updateRedDotHintState(commonEnum.REDDOT_HINT.TASKS);
    return Code.OK;
};

/**
 * 一键领取任务奖励  任务类型type
 */
Tasks.prototype.ghostGetTaskReward = function (typeList) {
    if (!typeList) {
        return Code.FAIL;
    }
    let rewardList = [];
    let showReward = [];
    for(let i in typeList)
    {
        let type = typeList[i];
        let id = type - 1;
        let finishTask = [];
        //找出已完成未领取的任务
        for (let i in this.allTaskList[id][commonEnum.TASK_INFO.taskType + id]) {
            if (this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].status === 1) {
                finishTask.push(this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].resId);
            }
        }
        if (finishTask.length < 1) {
            continue;
        }
        // logger.error("已完成未领取的任务：：：：：：：：：", finishTask);
        let index = 0;
        for (let i in finishTask) {
            let config = dataApi.allData.data["Task"][finishTask[i]];
            for (let k = 1; k < 6; ++k) {
                let rewardId = config["Reward" + k];
                let rewardNum = config["Num" + k];
                if (rewardId <= 0 || rewardNum <= 0) {
                    continue;
                }
                let id = this._checkRewardById(rewardList, rewardId);
                if (id === -1) {
                    rewardList[index] = {};
                    rewardList[index].ResId = rewardId;
                    rewardList[index].Num = rewardNum;
                    index++;
                    continue;
                }
                //相同的物品叠加在一起
                rewardList[id].Num += rewardNum;
            }
        }
        //将物品添加到背包
        for (let i in rewardList) {
            // logger.error("添加物品：：：：：：：：", rewardList[i].ResId, rewardList[i].Num);
            showReward.push(utils.deepCopy({ResId: rewardList[i].ResId, Num: rewardList[i].Num}));
            this.player.bag.addItem(rewardList[i].ResId, rewardList[i].Num);
        }
        rewardList = [];
        //删除任务
        for (let i = 0, len = finishTask.length; i < len; ++i) {
            this.delTask(type, finishTask[i]);
        }
        //获取等额任务
        //let addResId = this._getSeveralTask(type, finishTask.length);
        let addResId = [];
        for(let p in finishTask)
        {
            addResId.push(this._getOneTask(type, finishTask[p]));
        }
        if (addResId.length < 1) {
            logger.error("ghostGetTaskReward---addResId.length is 0-~~~~~", addResId);
            continue;
        }
        //添加任务
        for (let i = 0, len = addResId.length; i < len; ++i) {
            this.addTask(addResId[i]);
        }
    }
    // logger.error("11111111111111111", showReward);
    let tmpList = [];
    let index = 0;
    for(let i in showReward)
    {
        let id = this._checkRewardById(tmpList, showReward[i].ResId);
        if (id === -1) {
            tmpList[index] = {};
            tmpList[index].ResId = showReward[i].ResId;
            tmpList[index].Num = showReward[i].Num;
            index++;
            continue;
        }
        //相同的物品叠加在一起
        tmpList[id].Num += showReward[i].Num;
    }
    showReward = tmpList;
    // logger.error("22222222222222222", showReward);
    return ({code: Code.OK, rewardList: showReward});
};

/**
 * 刷新任务  任务类型type  任务resId
 */
Tasks.prototype.refreshTask = function (type, resId) {
    let ret = {
        code: 0,
        resId: resId,
        reTaskCount: 0
    };
    if (!type || type !== 3 || !resId) {
        ret.code = Code.FAIL;
        return ret;
    }

    // 检查是否已领取过奖励
    let id = type - 1;
    if(this.checkGetRewradFlag(id, resId)) {
        ret.code = Code.WORLDCUP.GET;
        return ret;
    }

    let type2 = dataApi.allData.data["Task"][resId].Type2;
    let refresh = dataApi.allData.data["Task"][resId].Refresh;
    let reTaskCount = dataApi.allData.data["SystemParam"][commonEnum.TASK_INFO.reTaskNum].Param;
    if (refresh === 0) {
        ret.code = Code.FAIL;
        return ret;
    }
    //是否同一天
    if (!timeUtils.isToday(this.resetTime)) {
        this.reTaskCount = 0;
    }
    //次数是否足够
    if (this.reTaskCount >= reTaskCount) {
        ret.code = Code.COUNT_FALL;
        return ret;
    }
    let costNum = this.reTaskCount + 1;
    let costGold = dataApi.allData.data["TaskCost"][costNum].Num;
    let addResId = this._getRoundOneTasks(resId);
    //1.欧元  2.懂币
    if (type2 === 1) {
        if (!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, costGold)) {
            ret.code = Code.CASH_FALL;
            return ret;
        }
        //扣钱删任务
        this.player.deductMoney(commonEnum.PLAY_INFO.cash, costGold);
        this.reTaskCount++;
        let index = this.delTask(type, resId);
        this.addEverydayTask(addResId, index);
    } else {
        if (!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, costGold)) {
            ret.code = Code.GOLD_FALL;
            return ret;
        }
        this.player.deductMoney(commonEnum.PLAY_INFO.gold, costGold);
        this.reTaskCount++;
        let index = this.delTask(type, resId);
        this.addEverydayTask(addResId, index);
    }
    ret.code = Code.OK;
    ret.resId = addResId;
    ret.reTaskCount = this.reTaskCount;
    return ret;
};

Tasks.prototype.addEveryDayRechargeNum = function (num) {
    if(!!num) {
        this.everyDayRechargeNum += num;
        // logger.error("充值金额--------------------", this.everyDayRechargeNum)
        let firstGearDay = timeUtils.dayInterval(this.firstGearTime);
        let secondGearDay = timeUtils.dayInterval(this.secondGearTime);
        let before = this.everyDayRechargeNum - 60;
        //一档都没达到
        if(before < 0) {
            return;
        }

        //6元已激活
        if(this.everyDayRechargeNum >= 300 && firstGearDay < 7) {
            if(secondGearDay >= 7) {
                this.secondGearTime = timeUtils.now();
                this.changeGiftReward();
            }
        } else if(this.everyDayRechargeNum >= 60 && firstGearDay >= 7) {
            this.firstGearTime = timeUtils.now();
            this.changeGiftReward();
        }
    }
}

Tasks.prototype.checkEveryDayRechargeNum = function () {
    let firstGearDay = timeUtils.dayInterval(this.firstGearTime);
    let secondGearDay = timeUtils.dayInterval(this.secondGearTime);
    let before = this.everyDayRechargeNum - 60;
    // logger.error("充值金额--------------------", this.everyDayRechargeNum)
    //一档都没达到
    if(before < 0) {
        return;
    }

    //6元已激活
    if(this.everyDayRechargeNum >= 300 && firstGearDay < 7) {
        if(secondGearDay >= 7) {
            this.secondGearTime = timeUtils.now();
            this.changeGiftReward();
        }
    } else if(this.everyDayRechargeNum >= 60 && firstGearDay >= 7) {
        this.firstGearTime = timeUtils.now();
        this.changeGiftReward();
    }
}

Tasks.prototype.changeGiftReward = function () {
    let firstGearDay = timeUtils.dayInterval(this.firstGearTime);
    let secondGearDay = timeUtils.dayInterval(this.secondGearTime);
    for(let i in this.everyDayGift) {
        //充值前未领取奖励才有加成   球币礼包
        if(this.everyDayGift[i].index === 3 && this.everyDayGift[i].isGet === 0) {
            let id = this.everyDayGift[i].id;
            let goldNum = dataApi.allData.data["DailyGift"][id].Gold;
            if(!!goldNum) {
                if(firstGearDay < 7 && secondGearDay < 7) {
                    let temp = Math.ceil(goldNum + goldNum * 0.3);
                    goldNum = Math.ceil(temp + temp * 0.5);
                    this.everyDayGift[i].num = goldNum;
                    this.everyDayGift[i].blackNum = dataApi.allData.data["DailyGift"][id].BlackNum;
                }else if (secondGearDay < 7) {
                    this.everyDayGift[i].num = Math.ceil(goldNum + goldNum * 0.5);
                    this.everyDayGift[i].blackNum = dataApi.allData.data["DailyGift"][id].BlackNum;
                } else if (firstGearDay < 7) {
                    this.everyDayGift[i].num = Math.ceil(goldNum + goldNum * 0.3);
                }
            }
        }
    }
}

//领取日常礼包
Tasks.prototype.getGiftReward = function (index) {
    let ret = {code: Code.FAIL};
    let isFinish = false;
    let isGet =false;
    for(let i in this.everyDayGift) {
        if(index === this.everyDayGift[i].index && this.everyDayGift[i].status === 1) {
            isFinish = true;
        }

        if(index === this.everyDayGift[i].index && this.everyDayGift[i].isGet === 1) {
            isGet = true;
        }
    }

    if(!isFinish) {
        ret.code = Code.TASKS.NOTFINISH;
        return ret;
    }

    if(isGet) {
        ret.code = Code.GET_FAIL;
        return ret;
    }

    for (let i in this.everyDayGift) {
        if(index === this.everyDayGift[i].index) {
            let id = this.everyDayGift[i].id;
            let config = dataApi.allData.data["DailyGift"][id];
            let resId = 0;
            let num = this.everyDayGift[i].num;
            let blackNum = this.everyDayGift[i].blackNum;
            let blackId = dataApi.allData.data["DailyGift"][id].BlackId;
            if(index === 3) {
                resId = 2;
            }else {
                resId = config["Gift" + index];
            }

            if(resId > 0 && num > 0) {
                // let rewardList = [];
                // let item = {};
                // item.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                // item.ResId = resId;
                // item.Num = num;
                // rewardList.push(item);
                this.player.bag.addItem(resId, num);
                if(index === 3 && !!blackNum && !!blackId) {
                    // let blackInfo = {
                    //     ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
                    //     ResId: blackId,
                    //     Num: blackNum
                    // }
                    // rewardList.push(blackInfo);
                    this.player.bag.addItem(blackId, blackNum);
                }
                    //发邮件
                //     this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.TASK_REWARD, commonEnum.MailType.SYSMAIL, rewardList, {roomUid: ""}, "", "", "", "");
                // }else {
                //     //发邮件
                //     this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.TASK_REWARD, commonEnum.MailType.SYSMAIL, rewardList, {roomUid: ""}, "", "", "", "");
                // }
                
                this.everyDayGift[i].isGet = 1
            }
            break;
        }
    }

    ret.code = Code.OK;
    return ret;
}

/**
 * 重置礼包任务
 */
Tasks.prototype.resetGiftTask = function () {
    let time = timeUtils.now();
    let day = new Date().getDay();
    if(day === 0) {
        day = 1;
    }else if(day === 6) {
        day = 7;
    }
    let config = dataApi.allData.data["DailyGift"][day];
    if(!config) {
        return;
    }

    //没有数据，先初始化
    if(this.everyDayGift.length < 1) {
        for (let i = 0; i < 3; ++i) {
            let info = {};
            info.id = day;
            info.index = i + 1;        //哪个奖励
            info.num = 1;              //奖励数量
            info.status = 0;           //0未完成  1已完成
            info.isGet = 0;            //0未领取  1已领取
            info.blackNum = 0;
            if(i < 2) {
                this.everyDayGift.push(info);
            }else {
                info.id = 1;
                info.num = dataApi.allData.data["DailyGift"][1].Gold;
                info.blackNum = dataApi.allData.data["DailyGift"][1].BlackNum;;
                this.everyDayGift.push(info);
            }
        }
        this.giftReTime = time;
    }

    //数据修复
    this.fixEveryDayGift();
    if(this.fixId === 0) {
        this.onceFixEveryDayGift();
        this.fixId = 1;
    }

    if (!timeUtils.isToday(this.giftReTime)) {
        let finishGift = [];
        for (let i in this.everyDayGift) {
            if(this.everyDayGift[i].status === 1 && this.everyDayGift[i].isGet === 0) {
                this.everyDayGift[i].isGet = 1;
                let info = {};
                info.id = this.everyDayGift[i].id;
                info.num = this.everyDayGift[i].num;
                info.index = this.everyDayGift[i].index;
                info.blackNum = this.everyDayGift[i].blackNum;
                finishGift.push(info);
            }
        }

        if (finishGift.length > 0) {
            let rewardList = [];
            for (let i = 0; i < finishGift.length; ++i) {
                let id = finishGift[i].id;
                let index = finishGift[i].index;
                let freeConfig = dataApi.allData.data["DailyGift"][id];
                let info = {};
                info.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                //球币奖励
                if(finishGift[i].index === 3) {
                    info.ResId = 2;
                }else {
                    info.ResId = freeConfig["Gift" + index];
                }
                info.Num = finishGift[i].num;
                rewardList.push(info);

                if(finishGift[i].index === 3 && !!freeConfig.BlackId) {
                    let item = {};
                    item.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                    item.ResId = freeConfig.BlackId;
                    if(!!finishGift[i].blackNum) {
                        item.Num = finishGift[i].blackNum;
                        rewardList.push(item);
                    }else if(finishGift[i].blackNum === null){
                        item.Num = 1;
                        rewardList.push(item);
                    }
                }
            }

            //发邮件
            let specialAttachInfo = {
                roomUid: ""
            };
            this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.TASK_REWARD, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
        }

        // this.firstGearTime = 0;        //6元激活时间
        // this.secondGearTime = 0;       //30元激活时间
        let firstGearDay = timeUtils.dayInterval(this.firstGearTime);
        let secondGearDay = timeUtils.dayInterval(this.secondGearTime);
        //调服务器时间bug
        if(firstGearDay < 0 || secondGearDay < 0) {
            this.firstGearTime = 0;
            this.secondGearTime = 0;
            firstGearDay = 99999;
            secondGearDay = 99999
        }

        //刷新普通礼包
        for (let i = 0; i < this.everyDayGift.length; ++i) {
            //球币礼包 特殊处理
            if(this.everyDayGift[i].index === 3 && this.everyDayGift[i].status === 1) {
                let num = this.everyDayGift[i].id + 1;
                if(num > 7) {
                    num = 1;
                }
                this.everyDayGift[i].id = num;
                let blackNum = 0;
                let goldNum = dataApi.allData.data["DailyGift"][num].Gold;
                if(firstGearDay < 7 && secondGearDay < 7) {
                    let temp = Math.ceil(goldNum + goldNum * 0.3);
                    goldNum = Math.ceil(temp + temp * 0.5);
                    blackNum = dataApi.allData.data["DailyGift"][num].BlackNum;
                }else if(secondGearDay < 7) {
                    goldNum = Math.ceil(goldNum + goldNum * 0.5);
                    blackNum = dataApi.allData.data["DailyGift"][num].BlackNum;
                }else if(firstGearDay < 7) {
                    goldNum = Math.ceil(goldNum + goldNum * 0.3);
                }
                this.everyDayGift[i].num = goldNum;
                this.everyDayGift[i].blackNum = blackNum;
                this.everyDayGift[i].status = 0;
                this.everyDayGift[i].isGet = 0;
            }else if(this.everyDayGift[i].index === 3 && this.everyDayGift[i].status === 0){
                let num = this.everyDayGift[i].id;
                let goldNum = dataApi.allData.data["DailyGift"][num].Gold;
                let blackNum = 0;
                if(firstGearDay < 7 && secondGearDay < 7) {
                    let temp = Math.ceil(goldNum + goldNum * 0.3);
                    goldNum = Math.ceil(temp + temp * 0.5);
                    blackNum = dataApi.allData.data["DailyGift"][num].BlackNum;
                }else if(secondGearDay < 7) {
                    goldNum = Math.ceil(goldNum + goldNum * 0.5);
                    blackNum = dataApi.allData.data["DailyGift"][num].BlackNum;
                } else if(firstGearDay < 7) {
                    goldNum = Math.ceil(goldNum + goldNum * 0.3);
                }
                this.everyDayGift[i].num = goldNum;
                this.everyDayGift[i].blackNum = blackNum;
                this.everyDayGift[i].isGet = 0;
            }else if(this.everyDayGift[i].index !== 3){
                this.everyDayGift[i].id = day;
                this.everyDayGift[i].status = 0;           //0未完成  1已完成
                this.everyDayGift[i].isGet = 0;            //0未领取  1可领取
                this.everyDayGift[i].num = 1;
                this.everyDayGift[i].blackNum = 0;
            }
        }

        //重置免费礼包任务次数
        this.everyDayTaskNum = 0;

        this.giftReTime = time;
        //重置充值数量
        this.everyDayRechargeNum = 0;
    }
    this.player.saveTasks();
}

//修复数据
Tasks.prototype.fixEveryDayGift = function () {
    if(this.everyDayGift.length > 1) {
        for(let i in this.everyDayGift) {
            if(this.everyDayGift[i].blackNum === undefined) {
                this.everyDayGift[i].blackNum = 0;
            }
        }
    }
}


//一次性修复数据
Tasks.prototype.onceFixEveryDayGift = function () {
    if(this.everyDayGift.length > 1) {
        for(let i in this.everyDayGift) {
            if(this.everyDayGift[i].index === 3 && this.everyDayGift[i].id === 7) {
                this.everyDayGift[i].blackNum = 1;
            }else if(this.everyDayGift[i].index === 3) {
                if(!this.everyDayGift[i].blackNum) {
                    this.everyDayGift[i].blackNum = 0;
                }
            }
        }
    }
}

/**
 * 重置任务
 */
Tasks.prototype.resetTask = function () {
    let id = commonEnum.TASK_INFO.resetType - 1;
    let finishTask = [];
    if (!timeUtils.isToday(this.resetTime)) {
        //找出已完成未领取的任务
        for (let i in this.allTaskList[id][commonEnum.TASK_INFO.taskType + id]) {
            if (this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].status === 1) {
                finishTask.push(this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].resId);
            }
        }
        if (finishTask.length > 0) {
            let rewardList = [];
            let index = 0;
            for (let i in finishTask) {
                let config = dataApi.allData.data["Task"][finishTask[i]];
                for (let k = 1; k < 6; ++k) {
                    let rewardId = config["Reward" + k];
                    let rewardNum = config["Num" + k];
                    if (rewardId <= 0 || rewardNum <= 0) {
                        continue;
                    }
                    let id = this._checkRewardById(rewardList, rewardId);
                    if (id === -1) {
                        rewardList[index] = {};
                        rewardList[index].ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                        rewardList[index].ResId = rewardId;
                        rewardList[index].Num = rewardNum;
                        index++;
                        continue;
                    }
                    //相同的物品叠加在一起
                    rewardList[id].Num += rewardNum;
                }
            }
            //发邮件
            let specialAttachInfo = {
                roomUid: ""
            };
            this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.TASK_REWARD, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
        }
        //清空日常任务
        this.allTaskList[id][commonEnum.TASK_INFO.taskType + id] = [];
        let resIdList = this._getRoundTasks(); //获取随机不重复任务
        for (let i in resIdList) {
            this.addTask(resIdList[i]);
        }
        //添加完后进行排序
        this.allTaskList[id][commonEnum.TASK_INFO.taskType + id].sort(this._compare("resId"));
        this.reTaskCount = 0; //重置刷新任务个数
        this.resetTime = Date.now();
    }
};

/**
 * 重置转播任务
 */
Tasks.prototype.resetRelayTask = function () {
    let id = commonEnum.TASK_INFO.RelayType - 1;
    let finishTask = [];
     // if (!timeUtils.isToday(this.resetTime)) {
        //找出已完成未领取的任务
        for (let i in this.allTaskList[id][commonEnum.TASK_INFO.taskType + id]) {
            if (this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].status === 1) {
                finishTask.push(this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].resId);
            }
        }
        if (finishTask.length > 0) {
            let rewardList = [];
            let index = 0;
            for (let i in finishTask) {
                let config = dataApi.allData.data["Task"][finishTask[i]];
                for (let k = 1; k < 6; ++k) {
                    //logger.error("!!!!!!!!!!!!!!!!!!!!!", finishTask[i], config);
                    if(!config)
                    {
                        continue;
                    }
                    let rewardId = config["Reward" + k];
                    let rewardNum = config["Num" + k];
                    if (rewardId <= 0 || rewardNum <= 0) {
                        continue;
                    }
                    let id = this._checkRewardById(rewardList, rewardId);
                    if (id === -1) {
                        rewardList[index] = {};
                        rewardList[index].ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                        rewardList[index].ResId = rewardId;
                        rewardList[index].Num = rewardNum;
                        index++;
                        continue;
                    }
                    //相同的物品叠加在一起
                    rewardList[id].Num += rewardNum;
                }
            }
            //发邮件
            let specialAttachInfo = {
                roomUid: ""
            };
            this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.RELAY_TASKS, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
            //清空转播任务
            this.allTaskList[id][commonEnum.TASK_INFO.taskType + id] = [];
            this.player.saveTasks();
        }
        else
        {
            //清空转播任务
            this.allTaskList[id][commonEnum.TASK_INFO.taskType + id] = [];
            this.player.saveTasks();
        }
    //}
};

/*判断前置任务是否完成*/
Tasks.prototype.PrepositionTask = function(resId)
{
    let Preposition;
    for(let i in dataApi.allData.data["Task"])
    {
        if(dataApi.allData.data["Task"][i].Id === resId)
        {
            Preposition = dataApi.allData.data["Task"][i].PrepositionID;
            if(Preposition === 0)
                return true;
            break;
        }
    }

    if(this.finishTask.includes(Preposition))
    {
        return true;
    }
   return false;
};

// //国庆签到奖励邮件发送
Tasks.prototype.resetSignTask = function ()
{
    var config = dataApi.allData.data["ActiveControl"][commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN];
    if(timeUtils.dayInterval(config.StartTime) < 0|| timeUtils.dayInterval(config.EndTime) > 0)//活动没开启
        return;
    let id = commonEnum.TASK_INFO.nationalSign - 1;
    var actparconfig = dataApi.allData.data["ActiveParam"];//得到活动参数表
    let dbActRecord = {};
    for (let [k, v] of this.player.act.globalCurrActList) //活动列表
    {
        if (v.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN) {
            let actId = v.actId;
            dbActRecord = this.player.act.globalActMgrInfo.get(v.actId);//活动数据
            if (!dbActRecord) {
                continue;
            }
        }
    }
    if(!dbActRecord.actRecord)
    {
        return;
    }
    let logsign
    if(!dbActRecord.actRecord.logsign)//为空初始化
    {
        //logger.debug("!logsign");
        logsign = this.player.act.initSignData();
    }
    else
    {
        logsign = dbActRecord.actRecord.logsign;
    }
    rewardList = [];
    let index = 0;
    //logger.error("发奖励邮件~~~~~~~~~~",this.signResetTime, Date.now(), timeUtils.isToday(this.resetTime));
    if (!timeUtils.isToday(this.signResetTime)) {
        for(let k in logsign)
        {
            if((logsign[k].state === commonEnum.ACT_BTN_STATUS.CAN_TAKE) && (logsign[k].awardFlag === commonEnum.ACT_BTN_STATUS.CAN_TAKE))//得到哪天可以领取
            {
                let idx = logsign[k].index;
                let rewardId = actparconfig[idx].Reward1;
                let rewardNum = actparconfig[idx].Number1;
                // logger.error("rewardId, rewardNum", rewardId, rewardNum);
                if (rewardId <= 0 || rewardNum <= 0)
                {
                    continue;
                }
                let id = this._checkRewardById(rewardList, rewardId);
                if (id === -1)//如果参数正确加入奖励列表
                {
                    rewardList[index] = {};
                    rewardList[index].ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                    rewardList[index].ResId = rewardId;
                    rewardList[index].Num = rewardNum;
                    logsign[k].state = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
                    logsign[k].awardFlag = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
                    index++;
                    continue;
                }
                //相同的物品叠加在一起
                rewardList[id].Num += rewardNum;
            }
        }
        // logger.error("rewardList", rewardList);
        if(!rewardList)
        {
            return;
        }
        if(rewardList.length >= 1)
        {
            //发邮件
            let specialAttachInfo = {
                roomUid: ""
            };
            this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.National_Day, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");

        }
        //找出已完成未领取的任务
        for (let i in this.allTaskList[id][commonEnum.TASK_INFO.taskType + id]) {
            this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].status = 0;
        }

        //清空任务
        this.allTaskList[id][commonEnum.TASK_INFO.taskType + id] = [];
        this.signResetTime = Date.now();
    }

};

/**
 * 公共方法 触发任务  type目标类型  arg1 目标ID   arg2 星级   arg3 次数
 */
Tasks.prototype.triggerTask = function (type, arg1, arg2, arg3, arg4) {
    if (!type) {
        logger.error("triggerTask type is fail~~~~~~~~~~~");
        return;
    }

    this.resetSignTask();
    this.resetTask(); //日常任务重置检查
    this.resetGiftTask();
    this.resetBeliefTask();

    switch (type) {
        case 1:
            this._leagueTask_1(arg1, arg2, arg3, arg4);
            break;
        case 2:
            this._leagueTask_2(arg1, arg2, arg3, arg4);
            break;
        case 3:
            this._heroTask_1(type, arg1, arg2, arg3, arg4);
            break;
        case 4:
            this._leagueTask_3(type, arg1, arg2, arg3, arg4);
            break;
        case 5:
            this._teamFormationTask_1(arg1, arg2, arg3, arg4);
            break;
        case 6:
            this._heroTask_2(arg1, arg2, arg3, arg4);
            break;
        case 7:
            this._heroTask_3(arg1, arg2, arg3, arg4);
            break;
        case 8:
            this._heroTask_4(arg1, arg2, arg3, arg4);
            break;
        case 9:
            this._storeTask_1(arg1, arg2, arg3, arg4);
            break;
        case 10:
            this._heroTask_5(arg1, arg2, arg3, arg4);
            break;
        case 11:
            this._heroTask_6(type, arg1, arg2, arg3, arg4);
            break;
        case 12:
            this._footGroundTask_1(arg1, arg2, arg3, arg4);
            break;
        case 13:
            this._shareTask(arg1, arg2, arg3, arg4);
            break;
        case 14:
            this._joinLeagueTask(arg1, arg2, arg3, arg4);
            break;
        case 15:
            this._heroTrainTask(arg1, arg2, arg3, arg4);
            break;
        case 16:
            this._heroBreakThroughTask(arg1, arg2, arg3, arg4);
            break;
        case 17:
            this._havePerfectHeroTask(arg1, arg2, arg3, arg4);
            break;
        case 18:
            this._haveSixStarHeroTask(arg1, arg2, arg3, arg4);
            break;
        case 19:
            this._haveStarHeroTask(arg1, arg2, arg3, arg4);
            break;
        case 20:
            this._teamFormationPriceTask(arg1, arg2, arg3, arg4);
            break;
        case 21:
            this._totalMoneyTask(arg1, arg2, arg3, arg4);
            break;
        case 22:
            this._totalGoldTask(arg1, arg2, arg3, arg4);
            break;
        case 23:
            this._heroStarTask(arg1, arg2, arg3, arg4);
            break;
        case 24:
            this._getMoneyTask(arg1, arg2, arg3, arg4);
            break;
        case 25:
            this._costGoldTask(arg1, arg2, arg3, arg4);
            break;
        case 26:
            this._costMoneyTask(arg1, arg2, arg3, arg4);
            break;
        case 27:
            this._upgradeFormationTask(arg1, arg2, arg3, arg4);
            break;
        case 28:
            this._upgradeTacticsTask(arg1, arg2, arg3, arg4);
            break;
        case 29:
            this._worldChatTask(arg1, arg2, arg3, arg4);
            break;
        case 30:
            this._followTask(arg1, arg2, arg3, arg4);
            break;
        case 31:
            this._joinWorldCupTask(arg1, arg2, arg3, arg4);
            break;
        case 32:
            this._signChatTask(arg1, arg2, arg3, arg4);
            break;
        case 33:
            this._getGroundRewardTask(arg1, arg2, arg3, arg4);
            break;
        case 34:
            this._heroInGroundTrainTask(arg1, arg2, arg3, arg4);
            break;
        case 35:
            this._joinBusinessTask(arg1, arg2, arg3, arg4);
            break;
        case 36:
            this._scoutSearchTask(arg1, arg2, arg3, arg4);
            break;
        case 37:
            this._buyWorldCupCountTask(arg1, arg2, arg3, arg4);
            break;
        case 38:
            this._adjustHeroStatusTask(arg1, arg2, arg3, arg4);
            break;
        case 39:
            this._leagueCoachTask(arg1, arg2, arg3, arg4);
            break;
        case 40:
            this._joinMiddleEastCup(arg1, arg2, arg3, arg4);
            break;
        case 41:
            this._joinWorldBoss(arg1, arg2, arg3, arg4);
            break;
        case 42:
            this._killWorldBoss(arg1, arg2, arg3, arg4);
            break;
        case 43:
            this._joinDqCup(arg1, arg2, arg3, arg4);
            break;
        case 44:
            this._winMiddleEastCup(arg1, arg2, arg3, arg4);
            break;
        case 45:
            this._joinGulfCup(arg1, arg2, arg3, arg4);
            break;
        case 46:
            this._winGulfCup(arg1, arg2, arg3, arg4);
            break;
        case 47:
            this._winLeague(arg1, arg2, arg3, arg4);
            break;
        case 48:
            this._goalLeague(arg1, arg2, arg3, arg4);
            break;
        case 49:
            this._promoteLeague(arg1, arg2, arg3, arg4);
            break;
        case 50:
            this._championLeague(arg1, arg2, arg3, arg4);
            break;
        case 51:
            this._secondLeague(arg1, arg2, arg3, arg4);
            break;
        case 52:
            this._placeOneLeague(arg1, arg2, arg3, arg4);
            break;
        case 53:
            this._placeTwoLeague(arg1, arg2, arg3, arg4);
            break;
        case 54:
            this._giveEnergy(arg1, arg2, arg3, arg4);
            break;
        case 55:
            this._beliefCostCash(arg1, arg2, arg3, arg4);
            break;
        case 56:
            this._beliefCostCash_1(arg1, arg2, arg3, arg4);
            break;
        case 57:
            this._beliefCostCash_2(arg1, arg2, arg3, arg4);
            break;
        case 58:
            this._beliefCostCash_3(arg1, arg2, arg3, arg4);
            break;
        case 99:
            this._joinGameHub(arg1, arg2, arg3, arg4);
            break;
        default:
            break;
    }
    this.player.saveTasks();
};


/*--------------------------------------------内部函数----------------------------------------------------*/

/**
 * 初始化任务
 */
Tasks.prototype._initTask = function (resId) {
    let taskInfo = {
        resId: 0, //任务ID
        type: 0, //任务类型
        Num: 0, //数量
        status: 0 //0未完成， 1已完成
    }
    //to do 读表添加任务
    let config = dataApi.allData.data["Task"][resId];
    taskInfo.resId = config.Id;
    taskInfo.type = config.Type;
    return taskInfo;
};

Tasks.prototype.getInitTaskById = function(resId)
{
    let taskInfo = this._initTask(resId);
    return taskInfo;
}
/**
 * 不能有相同的任务  type任务类型   任务id
 */
Tasks.prototype._checkIsSameTask = function (type, resId) {
    let id = type - 1;
    let isOk = true;
    //当前任务是否已经有这个任务了
    for (let i in this.allTaskList[id][commonEnum.TASK_INFO.taskType + id]) {
        if (resId === this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].resId) {
            isOk = false;
            break;
        }
    }
    return isOk;
}

/**
 * 检查要添加的任务是否已完成  任务类型type  任务id
 */
Tasks.prototype._checkAddTaskIsFinish = function (resId) {
    if (!resId) {
        logger.error("_checkAddTaskIsFinish resId is fail~~~~~~~~");
        return;
    }
    let isOk = true;
    for (let i = 0, len = this.finishTask.length; i < len; ++i) {
        if (resId === this.finishTask[i]) {
            isOk = false;
            break;
        }
    }
    return isOk;
};

/**
 * 任务列表是否已满  type任务类型 
 */
Tasks.prototype._checkTaskIsFull = function (type) {
    let id = type - 1;
    let isOk = true;
    //读表获取各任务列表的容量
    let configLength = dataApi.allData.data["TaskMark"][type].NumPageTasks;
    if (this.allTaskList[id][commonEnum.TASK_INFO.taskType + id].length >= configLength) {
        isOk = false;
    }
    return isOk;
}

/**
 * 检查任务是否已经完成  resId任务ID 
 */
Tasks.prototype._checkIsFinish = function (type, resId) {
    let isOk = false;
    let status = -1;
    for (let i in this.allTaskList[type][commonEnum.TASK_INFO.taskType + type]) {
        if (resId !== this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].resId) {
            continue;
        }
        status = this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].status;
    }
    if (status === 1) {
        isOk = true;
    }
    return isOk;
}

/**
 * 检查是否已经领取过日常奖励 
 */
Tasks.prototype.checkGetRewradFlag = function (type, resId) {
    let isGet = false;
    let status = -1;
    for (let i in this.allTaskList[type][commonEnum.TASK_INFO.taskType + type]) {
        if (resId !== this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].resId) {
            continue;
        }
        status = this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].status;
    }
    if(status === 2) {
        isGet = true;
    }
    return isGet;
}

/**
 * 任务奖励ID检查  找到返回下标 没找到返回-1
 */
Tasks.prototype._checkRewardById = function (rewardList, rewardId) {
    if (rewardList === []) {
        return -1;
    }
    for (let i in rewardList) {
        if (rewardList[i].ResId === rewardId) {
            return i;
        }
    }
    return -1;
}

/**
 * 完成任务   任务id
 */
Tasks.prototype._finishTask = function (id, resId) {
    if (!resId) {
        return;
    }
    let beliefType = commonEnum.TASK_INFO.BELIEF_TASK_TYPE - 1;
    for (let i in this.allTaskList[id][commonEnum.TASK_INFO.taskType + id]) {
        if (resId === this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].resId) {
                    //logger.info("_finishTask: updateGoldCoachTaskProgress: resId ", resId)
            if (this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].status !== 1 && this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].status !== 2) {
                this.allTaskList[id][commonEnum.TASK_INFO.taskType + id][i].status = 1;
                //红点
                this.player.updateRedDotHintState(commonEnum.REDDOT_HINT.TASKS);
                let goldCoachType = commonEnum.TASK_INFO.goldCoachType - 1;
                if (id === goldCoachType) {
                    this.player.act.updateGoldCoachTaskProgress(resId);
                    //logger.info("_finishTask: updateGoldCoachTaskProgress: resId ", resId)
                }
                //日常任务
                if(id === 2) {
                    this.addGiftTaskNum();
                }

                //信仰任务
                if (id === beliefType) {
                    //读表获取任务数量
                    let config = dataApi.allData.data["Task"][resId];
                    //活跃度
                    this.player.addResource(commonEnum.PLAY_INFO.beliefLiveness, config.Num1);
                    //信仰积分
                    this.player.addResource(commonEnum.PLAY_INFO.beliefNum, config.Num2);
                    this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.beliefNum, value: this.player.beliefNum}]);
                    //增加信仰活跃度
                    this.player.addBeliefLiveness(this.uid, config.Num1);
                }
            }
        }
    }
};

Tasks.prototype.addGiftTaskNum = function () {
    this.everyDayTaskNum++;

    for(let i in this.everyDayGift) {
        let id = this.everyDayGift[i].id;
        let index = this.everyDayGift[i].index;
        let config = dataApi.allData.data["DailyGift"][id];
        if(!config) {
            continue;
        }

        if(this.everyDayTaskNum >= config["TextNum" + index] && this.everyDayGift[i].status === 0) {
            this.everyDayGift[i].status = 1    //置为已完成
        }
    }
}

/**
 * 添加完成任务次数   任务id 
 */
Tasks.prototype._addTaskNum = function (type, resId) {
    if (!resId) {
        return;
    }

    for (let i in this.allTaskList[type][commonEnum.TASK_INFO.taskType + type]) {
        if (resId !== this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].resId) {
            continue;
        }
        this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].Num++;
    }
};

/**
 * 添加完成任务次数   任务id  
 * @param num   数量
 */
Tasks.prototype._addTaskNumByNum = function (type, resId, num) {
    if (!resId) {
        return;
    }

    if(num <= 0 || typeof num !== "number") {
        return;
    }

    for (let i in this.allTaskList[type][commonEnum.TASK_INFO.taskType + type]) {
        if (resId !== this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].resId) {
            continue;
        }
        this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].Num += num;
    }
};

/**
 * 设置任务次数   任务id 
 */
Tasks.prototype._setTaskNum = function (type, resId, num) {
    if (!resId) {
        return;
    }

    if(num <= 0 || typeof num !== "number") {
        return;
    }

    for (let i in this.allTaskList[type][commonEnum.TASK_INFO.taskType + type]) {
        if (resId !== this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].resId) {
            continue;
        }
        this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].Num = num;
    }
};


/**
 * 获取完成任务次数   任务id 
 */
Tasks.prototype._getTaskNum = function (type, resId) {
    if (!resId) {
        return 0;
    }
    for (let i in this.allTaskList[type][commonEnum.TASK_INFO.taskType + type]) {
        if (resId !== this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].resId) {
            continue;
        }
        return this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].Num;
    }
};

/**
 * 获取一个任务  任务类型type
 */
Tasks.prototype._getOneTask = function (type, resId) {
    if (type === 3) {
        // logger.error("type is 3 so return~~~~~~~", type);
        return 0;
    }
    let config = dataApi.allData.data["Task"];
    let taskId = 0;
    let taskList = [];
    for (let i in config) {
        if (config[i].Type === type) {
            taskList.push(config[i].Id);
        }
    }
    taskList.sort(function (a, b) {
        return a - b;
    });

    //先判断下一个任务是否完成
    let nextId = resId + 1;
    if (this._checkIsSameTask(type, nextId) && this._checkAddTaskIsFinish(nextId)) {
        return nextId;
    }
    for (let i = 0, len = taskList.length; i < len; ++i) {
        if (this._checkIsSameTask(type, taskList[i]) && this._checkAddTaskIsFinish(taskList[i])) {
            taskId = taskList[i];
            break;
        }
    }
    return taskId;
}


/**
 * 获取指定数量任务  任务类型type   num数量
 */
Tasks.prototype._getSeveralTask = function (type, num) {
    if (type === 3) {
        logger.error("type is 3 so return~~~~~~~", type);
        return 0;
    }
    let config = dataApi.allData.data["Task"];
    let taskId = [];
    let taskList = [];
    for (let i in config) {
        if (config[i].Type === type) {
            taskList.push(config[i].Id);
        }
    }
    taskList.sort(function (a, b) {
        return a - b;
    });
    for (let i = 0, len = taskList.length; i < len; ++i) {
        if (this._checkIsSameTask(type, taskList[i]) && this._checkAddTaskIsFinish(taskList[i])) {
            if (taskId.length >= num) {
                break;
            }
            taskId.push(taskList[i]);
        }
    }
    return taskId;
}

/**
 * 检查任务次数是否够  type任务类型   任务id
 */
Tasks.prototype._checkTaskNumIsFull = function (type, resId) {
    if (!resId) {
        return 0;
    }

    //读表获取任务数量
    let configNum = dataApi.allData.data["Task"][resId].Num;
    for (let i in this.allTaskList[type][commonEnum.TASK_INFO.taskType + type]) {
        if (resId !== this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].resId) {
            continue;
        }
        //次数够了直接完成
        if (this.allTaskList[type][commonEnum.TASK_INFO.taskType + type][i].Num >= configNum) {
            this._finishTask(type, resId);
        }
    }
}

Tasks.prototype.loadConfig_1 = function (targetType, arg3) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }

    for (let i in config) {
        let id = config[i].Type - 1;
        if (!this._checkIsSameTask(config[i].Type, config[i].Id)) {
            this._setTaskNum(id, config[i].Id, arg3);
            this._checkTaskNumIsFull(id, config[i].Id);
        } else {
            this.addTask(config[i].Id);
            this._setTaskNum(id, config[i].Id, arg3);
            this._checkTaskNumIsFull(id, config[i].Id);
        }
    }
}

Tasks.prototype.loadConfig_2 = function (targetType) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }
    // let taskType = []; //任务类型
    // let taskList = [];
    // for (let i in config) {
    //     if (config[i].TargetType === targetType) {
    //         taskType.push(config[i].Type);
    //         taskList.push(config[i].Id);
    //     }
    // }
    // if (taskList.length < 1) {
    //     return;
    // }

    for (let i in config) {
        let id = config[i].Type - 1;
        if (!this._checkIsSameTask(config[i].Type, config[i].Id)) {
            this._addTaskNum(id, config[i].Id);
            this._checkTaskNumIsFull(id, config[i].Id)
        }
    }
}

Tasks.prototype.loadConfig_3 = function (targetType) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }

    for (let i in config) {
        let id = config[i].Type - 1;
        if (!this._checkIsSameTask(config[i].Type, config[i].Id)) {
            this._addTaskNum(id, config[i].Id);
            this._checkTaskNumIsFull(id, config[i].Id);
        } else {
            this.addTask(config[i].Id);
            this._addTaskNum(id, config[i].Id);
            this._checkTaskNumIsFull(id, config[i].Id);
        }
    }
}

Tasks.prototype.loadConfig_4 = function (targetType, arg3) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }

    for (let i in config) {
        let id = config[i].Type - 1;
        if (!this._checkIsSameTask(config[i].Type, config[i].Id)) {
            this._addTaskNumByNum(id, config[i].Id, arg3);
            this._checkTaskNumIsFull(id, config[i].Id);
        } else {
            this.addTask(config[i].Id);
            this._addTaskNumByNum(id, config[i].Id, arg3);
            this._checkTaskNumIsFull(id, config[i].Id);
        }
    }
}

Tasks.prototype.loadConfig_5 = function (targetType, arg3) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }

    for (let i in config) {
        let id = config[i].Type - 1;
        if (!this._checkIsSameTask(config[i].Type, config[i].Id)) {
            this._addTaskNumByNum(id, config[i].Id, arg3);
            this._checkTaskNumIsFull(id, config[i].Id);
        }
    }
}
/**
 * arg1与配置表的arg1相同时任务完成
 */
Tasks.prototype.loadConfig_6 = function (targetType, arg1) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }

    for (let i in config) {
        let id = config[i].Type - 1;
        if (!this._checkIsSameTask(config[i].Type, config[i].Id)) {
            if(config[i].Arg1 === arg1)
            {
                this._addTaskNum(id, config[i].Id);
                this._checkTaskNumIsFull(id, config[i].Id);
            }
        }
    }
}

Tasks.prototype.loadConfig_7 = function (targetType, arg1, arg2) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }

    let taskList = [];
    let index = 0;
    for (let i in config) {
        if (arg1 === config[i].Arg1) {
            taskList[index] = {};
            taskList[index].resId = config[i].Id;
            taskList[index].type = config[i].Type;
            taskList[index].star = config[i].Arg2;
            index++;
        }
    }

    if (taskList.length < 1) {
        return;
    }

    for (let i in taskList) {
        let id = taskList[i].type - 1;
        if (!this._checkIsSameTask(taskList[i].type, taskList[i].resId)) {
            if (arg2 >= taskList[i].star) {
                this._addTaskNum(id, taskList[i].resId);
                this._checkTaskNumIsFull(id, taskList[i].resId);
            }
        } else {
            if (arg2 >= taskList[i].star) {
                this.addTask(taskList[i].resId);
                this._addTaskNum(id, taskList[i].resId);
                this._checkTaskNumIsFull(id, taskList[i].resId);
            }
        }
    }
}

Tasks.prototype.loadConfig_8 = function (targetType, arg1, arg2) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }

    let taskList = [];
    let index = 0;
    for (let i in config) {
        if (arg1 === config[i].Arg1) {
            taskList[index] = {};
            taskList[index].resId = config[i].Id;
            taskList[index].type = config[i].Type;
            taskList[index].star = config[i].Arg2;
            index++;
        }
    }
    if (taskList.length < 1) {
        return;
    }

    for (let i in taskList) {
        let id = taskList[i].type - 1;
        if (!this._checkIsSameTask(taskList[i].type, taskList[i].resId)) {
            if (arg2 >= taskList[i].star) {
                this._setTaskNum(id, taskList[i].resId, 1);
                this._finishTask(id, taskList[i].resId);
            }
        } else {
            this.addTask(taskList[i].resId);
            if (arg2 >= taskList[i].star) {
                this._setTaskNum(id, taskList[i].resId, 1);
                this._finishTask(id, taskList[i].resId);
            }
        }
    }
}


/*---------------------------------------------任务类型----------------------------------------------------*/
/**
 * 1:X队伍X星通关X次    type  arg1 目标ID   arg2 星级   arg3 次数
 */
Tasks.prototype._leagueTask_1 = function (arg1, arg2, arg3, arg4) {
   this.loadConfig_7(commonEnum.TARGET_TYPE.ONE, arg1, arg2);
}

/**
 * 2:X星通关联赛   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._leagueTask_2 = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_8(commonEnum.TARGET_TYPE.TWO, arg1, arg2);
}

/**
 * 4:推图X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._leagueTask_3 = function (targetType, arg1, arg2, arg3, arg4) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }

    for (let i in config) {
        let id = config[i].Type - 1;
        if (!this._checkIsSameTask(config[i].Type, config[i].Id)) {
            this._addTaskNum(id, config[i].Id);
            this._checkTaskNumIsFull(id, config[i].Id);
        } else {
            //日常任务不能添加
            if (config[i].Type === 3) {
                continue;
            }
            this.addTask(config[i].Id);
            this._addTaskNum(id, config[i].Id);
            this._checkTaskNumIsFull(id, config[i].Id);
        }
    }
}

/**
 * 3:拥有X数量的球员   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroTask_1 = function (targetType, arg1, arg2, arg3, arg4) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }

    for (let i in config) {
        let id = config[i].Type - 1;
        if (!this._checkIsSameTask(config[i].Type, config[i].Id)) {
            this._addTaskNum(id, config[i].Id);
            this._checkTaskNumIsFull(id, config[i].Id);
        } else {
            this.addTask(config[i].Id);
            this._addTaskNum(id, config[i].Id);
            this._checkTaskNumIsFull(id, config[i].Id);
        }
    }
}

/**
 * 6:拥有X个新球员   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroTask_2 = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.SIX);
}

/**
 * 7：X个球员达到X级   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroTask_3 = function (arg1, arg2, arg3, arg4) {

}

/**
 * 8：单个球员达到X级   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroTask_4 = function (arg1, arg2, arg3, arg4) {

}

/**
 * 10：提升X次球员等级   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroTask_5 = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.TEN);
}

/**
 * 11：获得一个指定球员   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroTask_6 = function (targetType, arg1, arg2, arg3, arg4) {
    //读表获取任务
    let config = dataApi.allData.data["NewTask"][targetType];
    if (!config) {
        return;
    }
    let taskType = []; //任务类型
    let resId = []; //任务ID
    for (let i in config) {
        if (arg1 === config[i].Arg1) {
            resId.push(config[i].Id);
            taskType.push(config[i].Type);
        }
    }

    if (resId.length < 1) {
        return;
    }

    //看任务列表里面是否有这个任务
    for (let i in resId) {
        let id = taskType[i] - 1;
        if (!this._checkIsSameTask(taskType[i], resId[i])) {
            this._addTaskNum(id, resId[i]);
            this._checkTaskNumIsFull(id, resId[i]);
        } else {
            this.addTask(resId[i]);
            this._addTaskNum(id, resId[i]);
            this._checkTaskNumIsFull(id, resId[i]);
        }
    }
}

/**
 * 9：购买X个新道具   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._storeTask_1 = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.NINE);
}

/**
 * 5:球队达到X级   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._teamFormationTask_1 = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_1(commonEnum.TARGET_TYPE.FIVE, arg3);
}

/**
 * 12:升级球场X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._footGroundTask_1 = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.TWELVE);
}

/**
 * 13:分享游戏X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._shareTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTEEN);
    this.player.act.updateSignTaskPogress();
}

/**
 * 14:参与X次联赛   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._joinLeagueTask = function (arg1, arg2, arg3, arg4) {
    return;
}

/**
 * 15：球员特训X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroTrainTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.FIFTEEN);
}

/**
 * 16：球员突破X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroBreakThroughTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.SIXTEEN);
}

/**
 * 17：拥有X个完美突破球员   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._havePerfectHeroTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_3(commonEnum.TARGET_TYPE.SEVENTEEN);
}

/**
 * 18：拥有X个9星的球员   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._haveSixStarHeroTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_3(commonEnum.TARGET_TYPE.EIGHTEEN);
}

/**
 * 19：拥有1个X星的球员   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._haveStarHeroTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_1(commonEnum.TARGET_TYPE.NINETEEN, arg3);
    this._heroStarTask(arg1, arg2, arg3, arg4);
}

/**
 * 20：俱乐部身价达到X   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._teamFormationPriceTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_1(commonEnum.TARGET_TYPE.TWENTY, arg3);
}

/**
 * 21：累计拥有X欧元   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._totalMoneyTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_4(commonEnum.TARGET_TYPE.TWENTY_ONE, arg3);
    this._getMoneyTask(arg1, arg2, arg3, arg4);
}

/**
 * 22：累计拥有X球币   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._totalGoldTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_4(commonEnum.TARGET_TYPE.TWENTY_TWO, arg3);
}

/**
 * 23：球员升星X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroStarTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.TWENTY_THREE);
}

/**
 * 24：获得X欧元   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._getMoneyTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_5(commonEnum.TARGET_TYPE.TWENTY_FOUR, arg3);
}

/**
 * 25：消耗X球币   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._costGoldTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_5(commonEnum.TARGET_TYPE.TWENTY_FIVE, arg3);
}

/**
 * 26：消耗X欧元   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._costMoneyTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_5(commonEnum.TARGET_TYPE.TWENTY_SIX, arg3);
}

/**
 * 27：提升X次阵型等级   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._upgradeFormationTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.TWENTY_SEVEN);
}

/**
 * 28：提升X次战术等级   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._upgradeTacticsTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.TWENTY_EIGHT);
}

/**
 * 29：世界频道发言X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._worldChatTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.TWENTY_NINE);
    this.player.act.updateSignTaskPogress();
}

/**
 * 30：关注X位玩家   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._followTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY);
}

/**
 * 31：参与X次世界杯   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._joinWorldCupTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY_ONE);
}

/**
 * 32：国庆指定发言   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._signChatTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY_SIGN);
    this.player.act.updateSignTaskPogress();
}

/**
 * 33：球场收集   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._getGroundRewardTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY_THREE);
}

/**
 * 34：球员泡澡   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._heroInGroundTrainTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY_FOUR);
}

/**
 * 35：参加商业赛   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._joinBusinessTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY_FIVE);
}

/**
 * 36：球探搜索球员   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._scoutSearchTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY_SIX);
}

/**
 * 37：购买世界杯次数   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._buyWorldCupCountTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY_SEVEN);
}

/**
 * 38：球员状态调整   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._adjustHeroStatusTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY_EIGHT);
}

/**
 * 39：金牌教练巡回赛   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._leagueCoachTask = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.THIRTY_NINE);
}

/**
 * 40：参加X中东杯   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._joinMiddleEastCup = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.JOIN_MIDDLE_EAST_CUP);
    this._winMiddleEastCup();
}

/**
 * 41：参加X次世界BOSS   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._joinWorldBoss = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.JOIN_WORLD_BOSS);
}

/**
 * 42：击杀X次世界BOSS   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._killWorldBoss = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_3(commonEnum.TARGET_TYPE.KILL_WORLD_BOSS);
}

/**
 * 43：参与X五大次联赛   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._joinDqCup = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_3(commonEnum.TARGET_TYPE.JOIN_DQ_CUP);
}

/**
 * 44：中东杯获胜X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._winMiddleEastCup = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_3(commonEnum.TARGET_TYPE.WIN_MIDDLE_EAST_CUP);
}

/**
 * 45：参与海湾杯X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._joinGulfCup = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_3(commonEnum.TARGET_TYPE.JOIN_GULF_CUP);
    this._winGulfCup();
}

/**
 * 46：战胜海湾杯X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._winGulfCup = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.WIN_GULF_CUP);
}

/**
 * 47：本赛季联赛获胜X次   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._winLeague = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_2(commonEnum.TARGET_TYPE.WIN_LEAGIE);
}

/**
 * 48：本赛季进球x个   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._goalLeague = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_5(commonEnum.TARGET_TYPE.GOAL_LEAGUE, arg3);
}

/**
 * 49：当前赛季完成所有比赛   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._promoteLeague = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_6(commonEnum.TARGET_TYPE.PROMOTED_LEAGUE, arg1);
}

/**
 * 50：本赛季获得X级联赛冠军   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._championLeague = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_6(commonEnum.TARGET_TYPE.CHAMPION_LEAGUE, arg1);
}

/**
 * 51：本赛季获得X级联赛亚军   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._secondLeague = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_6(commonEnum.TARGET_TYPE.SECOND_LEAGUE, arg1);
}

/**
 * 52：本赛季获得X级联赛3-5名   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._placeOneLeague = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_6(commonEnum.TARGET_TYPE.PLACE_ONE_LEAGUE, arg1);
}

/**
 * 53：本赛季获得X级联赛6-11名   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._placeTwoLeague = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_6(commonEnum.TARGET_TYPE.PLACE_TWO_LEAGUE, arg1);
}

/**
 * 54：赠送精力   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._giveEnergy = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_3(commonEnum.TARGET_TYPE.GIVE_ENERGY);
}

/**
 * 55：信仰捐赠100W欧元   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._beliefCostCash = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_5(commonEnum.TARGET_TYPE.BELIEF_CASH, arg3);
}

/**
 * 56：信仰捐赠500W欧元   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._beliefCostCash_1 = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_5(commonEnum.TARGET_TYPE.BELIEF_CASH_1, arg3);
}


/**
 * 57：信仰捐赠1000W欧元   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._beliefCostCash_2 = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_5(commonEnum.TARGET_TYPE.BELIEF_CASH_2, arg3);
}


/**
 * 58：信仰捐赠5000W欧元   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._beliefCostCash_3 = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_5(commonEnum.TARGET_TYPE.BELIEF_CASH_3, arg3);
}



/**
 * 99：进入游戏圈子   arg1 目标ID   arg2 任务要求   arg3 次数
 */
Tasks.prototype._joinGameHub = function (arg1, arg2, arg3, arg4) {
    this.loadConfig_3(commonEnum.TARGET_TYPE.JOIN_GAME_HUB);
}


Tasks.prototype._getEverydayTasks = function () {
    let config = dataApi.allData.data["Task"];
    let taskList = [];
    let index = 0;
    for (let i in config) {
        if (config[i].Type === 3 && config[i].Del === 0) {
            taskList[index] = {};
            taskList[index].id = config[i].SimilarTask;
            taskList[index].resId = config[i].Id;
            index++;
        }
    }
    return taskList;
}

/**
 * 日常随机任务
 */
Tasks.prototype._getRoundTasks = function () {
    let taskList = this._getEverydayTasks();
    let result = [];
    let ranNum = dataApi.allData.data["TaskMark"][2].NumPageTasks;
    for (let i = 1; i < ranNum + 1; i++) {
        let temp = [];
        for(let j = 0; j < taskList.length; ++j) {
            if(i === taskList[j].id) {
                temp.push(taskList[j].resId)
            }

            if(j === taskList.length - 1) {
                let ran = Math.floor(Math.random() * temp.length);
                result.push(temp[ran]);
                break;
            }
        }
    }
    return result;
};

/**
 * 日常随机一个任务
 */
Tasks.prototype._getRoundOneTasks = function (srcId) {
    let config = dataApi.allData.data["Task"];
    let similarTask = dataApi.allData.data["Task"][srcId].SimilarTask;
    let arr = [];
    for (let i in config) {
        if (config[i].Type === 3 && similarTask === config[i].SimilarTask && config[i].Del === 0) {
            arr.push(config[i].Id);
        }
    }
    let resId = arr[Math.floor(Math.random() * arr.length)];
    return resId;
};

/**
 * 任务排序 按任务id从小到大
 */
Tasks.prototype._compare = function (resId) {
    return (obj1, obj2) => {
        let val1 = obj1[resId];
        let val2 = obj2[resId];
        if(val1 !== val2) {
            if(val1 > val2) {
                return 1;
            } else if(val1 < val2) {
                return -1;
            }
        }
        return 0;
    }
};

Tasks.prototype.setGiftBuyTime = function (type) {
    let time = timeUtils.now();
    if(type === 1) {
        this.firstGearTime = time;        //6元激活时间
    }else if(type === 2) {
        this.secondGearTime = time;       //30元激活时间
    }else if(type === 3) {
        this.firstGearTime = time;        //6元激活时间
        this.secondGearTime = time;       //30元激活时间
    }
    this.changeGiftReward();
}

//获取信仰任务
Tasks.prototype.getBeliefEverydayTask = function() {
    let taskType = commonEnum.TASK_INFO.BELIEF_TASK_TYPE;
    let config = dataApi.allData.data["Task"];
    let taskList = [];
    for(let i in config) {
        if(config[i].Type === taskType) {
            taskList.push(config[i].Id);
        }
    }
    return taskList;
}

/**
 * 重置信仰任务  每天6点刷新
 */
Tasks.prototype.resetBeliefTask = function () {
    let id = commonEnum.TASK_INFO.BELIEF_TASK_TYPE - 1;
    let dateHour = new Date(this.beliefResetTime).getHours();
    let nowHours = new Date().getHours();
    //玩家是0-6点之间刷新的
    let isUpdate = false;
    if(this.beliefResetTime === 0) {
        isUpdate = true;
    }

    if(dateHour < 6) {
        if(!timeUtils.isToday(this.beliefResetTime)) {
            isUpdate = true;
        } else {
            //如果已经超过6点就刷新
            if(nowHours >= 6) {
                isUpdate = true;
            }
        }
    } else {
        if(!timeUtils.isToday(this.beliefResetTime)) {
            //判断与当前时间相差几天
            let dayInterval = timeUtils.dayInterval(this.beliefResetTime)
            if(dayInterval < 2) {
                //如果已经超过6点就刷新
                if(nowHours >= 6) {
                    isUpdate = true;
                }
            } else {
                isUpdate = true;
            }
        }
    }

    if(isUpdate) {
        //清空信仰任务
        this.allTaskList[id][commonEnum.TASK_INFO.taskType + id] = [];
        this.beliefResetTime = timeUtils.now();
        let taskList = this.getBeliefEverydayTask();      //获取信仰任务
        for (let i in taskList) {
            this.addTask(taskList[i]);
        }
        //添加完后进行排序
        this.allTaskList[id][commonEnum.TASK_INFO.taskType + id].sort(this._compare("resId"));
    }
};

/**
 * 获取信仰任务
 */
Tasks.prototype.getBeliefTask = function () {
    this.resetBeliefTask();
    let id = commonEnum.TASK_INFO.BELIEF_TASK_TYPE - 1;
    return this.allTaskList[id][commonEnum.TASK_INFO.taskType + id];
}
