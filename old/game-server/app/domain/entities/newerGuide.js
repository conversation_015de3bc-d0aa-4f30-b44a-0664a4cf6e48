/**
 * Created by scott on 2019/7/08.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var commonEnum = require('../../../../shared/enum');
var dataApi = require('../../util/dataApi');
var TimeUtils = require('../../util/timeUtils');
var utils = require('../../util/utils');

let NewerGuide = function(player) 
{
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.nextId = 0;
    this.guideFinnishList = new Map();
    this.triggerList = new Map();
    this.triggerFinishList = new Map();
};

util.inherits(NewerGuide, EventEmitter);
module.exports = NewerGuide;

NewerGuide.prototype.initByDB = function(doc) 
{
    this.uid = doc.uid || this.uid;
    this.nextId = doc.nextId || 0;
    this.guideFinnishList = this.toMap(doc.guideFinnishList) || new Map();
    this.triggerFinishList = this.toMap(doc.triggerFinishList) || new Map();
    this.triggerList = this.toMap(doc.triggerList) || new Map();
};

NewerGuide.prototype.toJSONforDB = function()
{
	var doc = {
        uid: this.uid,
        nextId : this.nextId,
        guideFinnishList: this.toArr(this.guideFinnishList),
        triggerFinishList: this.toArr(this.triggerFinishList),
        triggerList: this.toArr(this.triggerList),
	};
	return doc;
};

NewerGuide.prototype.toMap = function(arr) {
    var map =  new Map();
    if (!arr)
    {
        return map;
    }
  
    for (var i in arr)
    {
       const object = arr[i];
       var id = object["id"];
       var guideInfo = object["guideInfo"];
       map.set(id, guideInfo);
    }
    return map;
};

NewerGuide.prototype.toArr = function(map) {
    var arr =  [];
    if (!map)
    {
        return arr;
    }
  
    for (var [k, v] of map)
    {
       let obj = {
           id: k,
           guideInfo: v,
       };
       arr.push(obj);
    }
    return arr;
};

NewerGuide.prototype.getNextId = function()
{
    return this.nextId;
};

NewerGuide.prototype.updateGuide = function(id)
{
    let retCode = { code: Code.FAIL, itemUidList: [] };
    let config = dataApi.allData.data["NewbieGuide"];
    if (!config) 
    {
        logger.error("updateGuide: NewbieGuide config not found!", id);
        return retCode;
    }

    //logger.info("updateGuide: id, nextId", id, this.nextId);

    if (!config[id])
    {
        logger.error("updateGuide: id not found!", id);
        return retCode;
    }

    if (this.checkGuideFinish(id))
    {
        let nId = id + 1;
        //中间插入数据
        if(!!config[nId]) {
            this.nextId = nId;
        }else {
            this.nextId = 0;
        }
        //这里返回成功给客户端 有可能这个id是属于某个组的中间id，但是客户端被中断了，无法从这个id开始执行
        retCode.code = Code.OK;
        return retCode;
    }

    //这里只记录硬性引导
    if (config[id].GuideType !== commonEnum.NEWER_GUIDE_TYPE.HARD)
    {
        logger.error("updateGuide: this guide only record hard guide!", id);
        retCode.code = Code.OK;
        return retCode; 
    }

    if (config[id].UserGroup === commonEnum.NEWER_GUIDE_USER_GROUP_TYPE.NEWER && !this.isNewer())
    {
        if (id !== commonEnum.NEWER_GUIDE_DEAL_ID.MatureTypeId && id !== commonEnum.NEWER_GUIDE_DEAL_ID.GetNewBallerId)
        {
            //logger.error("updateGuide: this player not newer!", id, config[id]);
            return retCode; 
        }
    }

    //触发类型的硬性引导
    if (this.triggerFinishList.has(id))
    {
        let obj = this.triggerFinishList.get(id);
        obj.status = commonEnum.NEWER_GUIDE_STATUS.FINISH;
    }

    this.setFinish(id);
    
    //logger.info("updateGuide: setFinish", id);
    let nextId = 0;
    for(let idx in config)
    {
        let data = config[idx];
        if (data.Id === id)
        {
            nextId = data.RearId;
            //看看有没有赠送物品
            let itemId = data.Gift;
            let itemNum = data.Num;
            if (itemId> 0 && itemNum > 0) //有物品
            {
                var has = this.player.bag.hasCanAddItemInBag(itemId, itemNum)
                if (!has) {
                    //发送背包满了邮件
                    this.player.email.BagIsFullMail(commonEnum.MAIL_ITEM_TYPE.ITEM, itemId, itemNum, 0);
                }else
                {
                    var ret =this.player.bag.addItem(itemId, itemNum);
                    if (ret === Code.OK)
                    {
                        let item = {};
                        item.resId = itemId;
                        item.num = itemNum;
                        retCode.itemUidList.push(item);
                        logger.info("updateGuide: add item: itemId, itemNum ", itemId, itemNum);
                    }
                }
            }
            break;
        }
    }

    if (nextId && config[nextId].UserGroup === commonEnum.NEWER_GUIDE_USER_GROUP_TYPE.NEWER)
    {
        if (!this.isNewer())
        {
            nextId = 0;
        }
    }

    this.nextId = nextId;

    //logger.error("updateGuide: nextId=================================", this.nextId);
    retCode.code = Code.OK;
    return retCode;
};

NewerGuide.prototype.setFinish = function(id)
{
    let obj = {
        id: id,
        status: commonEnum.NEWER_GUIDE_STATUS.FINISH,
        finishTime: TimeUtils.now(),
    };

    //进游戏圈子任务
    if(id === 300) {
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.JOIN_GAME_HUB);
    }

    this.guideFinnishList.set(id, obj);
};

NewerGuide.prototype.setCurrIdZero = function()
{
    this.nextId = 0;
};

NewerGuide.prototype.setNextId = function(id)
{
    this.nextId = id;
};

NewerGuide.prototype.checkGuideTrigger = function(triggerType, param)
{
    let triggerId = 0;
    let config = dataApi.allData.data["NewbieGuide"];
    if (!config)
    {
        logger.error("checkGuideTrigger: NewbieGuide config not found!", id);
        return triggerId;
    }

    for(let idx in config)
    {
        let data = config[idx];
        let id = data.Id;

        //老手需要过滤掉新手任务
        let userGroup = data.UserGroup;
        if (userGroup ===commonEnum.NEWER_GUIDE_USER_GROUP_TYPE.NEWER && !this.isNewer())
        {
            //logger.info("checkGuideTrigger: id, userGroup", id, userGroup);
            continue;
        }

        let condition = data.Condition;
        let conditionId = data.ConditionId;
        //logger.info("id, condition, conditionId", id, condition, conditionId);
        if (condition === triggerType && conditionId === param)
        {
            //检查前置条件是否达成
            let frontId = data.FrontId;
            if (frontId && !this.checkGuideFinish(frontId))  //没有达成前置条件
            {
                logger.info("checkGuideTrigger: the frontId not reach!", frontId);
                continue;
            }

            if (this.checkGuideFinish(id)) //已经完成跳过
            {
                //logger.info("checkGuideTrigger: the id already reach!", id);
                continue;
            }

            //是否已经添加到触发列表里面了
            if (this.triggerFinishList.has(id))
            {
                //logger.info("checkGuideTrigger: the id already add trigger finish list!", id);
                continue;
            }
        
            //logger.info("checkGuideTrigger: triggerId= 2", triggerId);
            triggerId = id;
            break;
        }
    }

    //logger.info("checkGuideTrigger: triggerType, param, triggerId=", triggerType, param, triggerId);
    if (triggerId > 0)
    {
        //硬性的id需要保存
        if (config[triggerId].GuideType === commonEnum.NEWER_GUIDE_TYPE.HARD)
        {
            let obj = {
                id: triggerId,
                status: commonEnum.NEWER_GUIDE_STATUS.PROCESS,
                triggerTime: TimeUtils.now(),
            };
            //logger.info("checkGuideTrigger: add triggerFinishList", triggerId, obj);
            this.triggerFinishList.set(triggerId, obj);
        }
    
        let obj1 = {
            id: triggerId,
            status: commonEnum.NEWER_GUIDE_STATUS.PROCESS,
            triggerTime: TimeUtils.now(),
        };
        this.triggerList.set(triggerId, obj1);
    }

    return triggerId;
};

NewerGuide.prototype.checkGuideFinish = function(id)
{
    let isFinish = false;
    if (this.guideFinnishList.has(id) && this.guideFinnishList.get(id).status === commonEnum.NEWER_GUIDE_STATUS.FINISH)
    {
        isFinish = true;
    }
    return isFinish;
};  

NewerGuide.prototype.restTriggerList = function()
{
    this.triggerList.clear();
};

NewerGuide.prototype.isNewer = function()
{
    let isNewer = false;
    if (this.player.qualified === commonEnum.CREATE_ROLE_QUALIFIED.NEWER)
    {
        isNewer = true;
    }
    return isNewer;
};

NewerGuide.prototype.checkIdIsRepeat = function(guideList, id)
{
	let isRepeat = false;
	if (!guideList || guideList.length <= 0)
	{
		return isRepeat;
	}

	for(let idx in guideList)
	{
		let data = guideList[idx];
		if (data.id === id)
		{
			isRepeat = true;
		}
	}
	return isRepeat;
};

function __trigger_compare_func(rankObj1, rankObj2) 
{
	let id1 = rankObj1.id;
	let type1 = rankObj1.type;
	let triggerTime1 = rankObj1.triggerTime;

	let id2 = rankObj2.id;
	let type2 = rankObj2.type;
	let triggerTime2 = rankObj2.triggerTime;

    // logger.info("id1, type1, triggerTime1", id1, type1, triggerTime1);
    // logger.info("id2, type2, triggerTime2", id2, type2, triggerTime2);
    //类型
	if (type1 !== type2 ) { 
		 //降序
		if (type1 < type2) {
            return 1;
        }else if (type1 > type2) {
            return -1;
        }
	}	

    //触发时间
	if (triggerTime1 !== triggerTime2) {
		//升序
		if (triggerTime1 > triggerTime2) {
            return 1;
        }else if (triggerTime1 < triggerTime2) {
            return -1;
        }
	}

    //id
    if (id1 > id2) {
        return 1;
    }else if (id1 < id2) {
        return -1;
    }
    return 0;
};


function __finish_compare_func(rankObj1, rankObj2) 
{
	let id1 = rankObj1.id;
	let id2 = rankObj2.id;
    //id
    if (id1 > id2) {
        return 1;
    }else if (id1 < id2) {
        return -1;
    }
    return 0;
};

NewerGuide.prototype.getGuideList = function()
{
	let guideList = [];
    let triggerCount = 0;
    for(let [k, v] of this.triggerList)
    {
        let obj = {
            id: v.id,
            type: commonEnum.NEWER_GUIDE_TRIGGER_TYPE.TRIGGER,
            triggerTime: v.triggerTime,
        };
        guideList.push(obj);
        triggerCount++;
    }

    if (triggerCount > 0)
    {
        this.restTriggerList();  //在这里把触发列表删除掉
    }

    // logger.error("uuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuuu", guideList)
    //还未完成的引导 和上面触发的引导id可能会出现重复
    triggerCount = 0;
    for(let [k,v] of this.triggerFinishList)
    {	
        if (v.status === commonEnum.NEWER_GUIDE_STATUS.PROCESS && !this.checkIdIsRepeat(guideList, k))
        {
			let obj = {
				id: v.id,
                type: commonEnum.NEWER_GUIDE_TRIGGER_TYPE.TRIGGER,
                triggerTime: v.triggerTime,
            }
            guideList.push(obj);
            triggerCount++;
        }
    }

	let nextId = this.getNextId();
	if (!this.checkIdIsRepeat(guideList, nextId)) //去重
	{
        let obj = {
            id: nextId, 
            type: commonEnum.NEWER_GUIDE_TRIGGER_TYPE.NORMAL, 
            triggerTime: TimeUtils.now(),
        }
		guideList.push(obj);
    }
    
    //排序
    //测试排序数据
    // guideList.push({id: 4, triggerTime: 1, type: 1});
    // guideList.push({id: 8, triggerTime: 2, type: 2});
    // guideList.push({id: 9, triggerTime: 1, type: 2});
    // guideList.push({id: 4, triggerTime: 4, type: 2});
    // guideList.push({id: 5, triggerTime: 4, type: 2});
    // guideList.push({id: 6, triggerTime: 1, type: 2});

    // logger.error("guideList 1===========================", guideList);
    guideList.sort(__trigger_compare_func);
    // logger.error("guideList 2====================== ==", guideList);
    let retGuideList = [];
    for (let idx in guideList) {
        let data = guideList[idx];
        let id = data.id;
        if (id <= 0)
        {
            continue;
        }

        let type = data.type;
        retGuideList.push({nextId: id, type: type});
    }
    return retGuideList;
};

NewerGuide.prototype.resumeGuide = function(id)
{
    let retCode = { itemUidList: [], heroUidList: []};
    let config = dataApi.allData.data["NewbieGuide"][id];
    if (!config) 
    {
        logger.error("resumeGuide: NewbieGuide config not found!", id);
        return retCode;
    }

    let canComplete = config.CanComplete;
    if (canComplete === commonEnum.NEWER_GUIDE_CAN_COMPLETE.SIGN_HERO) //签英雄
    {
        let scoutPackList = this.player.scout.getScoutPackInfo();
        if (scoutPackList.length <= 0)
        {
            logger.error("resumeGuide: scoutPackList is null", id);
        }else
        {
            //默认取第一个英雄
            let signResId = 0;
            for(let idx in scoutPackList)
            {
                let resId = scoutPackList[idx].resId;
                signResId = resId;
                break;
            }

            let ret = this.player.scout.signScoutHero(signResId);
            retCode.heroUidList.push(ret.uid);
            logger.info("resumeGuide: signScoutHero signResId, uid ret", signResId, ret.uid, ret);
        }
    }

    return  retCode;
};

NewerGuide.prototype.procGuideResume = function()
{
    let retCode = { itemUidList: [], heroUidList: []};
    let guideList = this.getGuideList();
    for(let idx in guideList)
    {
        let id = guideList[idx].nextId;
        let result = this.resumeGuide(id);
        for(let index in result.itemUidList)
        {
            retCode.itemUidList.push(result.itemUidList[index]);
        }

        for(let index in result.heroUidList)
        {
            retCode.heroUidList.push(result.heroUidList[index]);
        }
    }

    return retCode;
};

NewerGuide.prototype.getEveryDayTaskId = function(id)
{
    let everyDayTaskId = 0;
    let taskConfig = dataApi.allData.data["Task"][id];
    if (!taskConfig)
    {
        return everyDayTaskId;
    }

    let taskType = taskConfig.Type;
    if (taskType === 3) //每日任务类型为3 这个是暂时写死，因为任务类型还没有枚举
    {
        everyDayTaskId = id;
    }

    return everyDayTaskId;
};

NewerGuide.prototype.getFinishEveryDayList = function()
{
    let finishGuideList = [];
    //logger.info("getFinishEveryDayList: size", this.guideFinnishList.size());
    for (let [k, v] of this.guideFinnishList) 
    {
        //logger.info("getFinishEveryDayList: k,v", k, v);
        let id = v.id;
        if (id <= 0)
        {
            continue;
        }

        let config = dataApi.allData.data["NewbieGuide"][id];
        if (!config) 
        {
            // logger.error("checkIsEveryDayTask: NewbieGuide config not found!", id);
            continue;
        }

        let condition = config.Condition;
        let conditionId = config.ConditionId;
        if (condition !== commonEnum.NEWER_GUIDE_TRIGGER_CONDITION.TASK_JUMP)
        {
            continue;
        }

        let everyDayTaskId = this.getEveryDayTaskId(conditionId);
        //logger.info("getFinishEveryDayList: id, conditionId, everyDayTaskId", id, conditionId, everyDayTaskId);
        if (everyDayTaskId <= 0)
        {
            continue;
        }

        finishGuideList.push({id: everyDayTaskId});
    }

    finishGuideList.sort(__finish_compare_func);

    // logger.error("getFinishEveryDayList:-------------------- ", this.guideFinnishList);
    return finishGuideList;
};