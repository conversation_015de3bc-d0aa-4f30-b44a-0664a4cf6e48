var logger = require('pomelo-logger').getLogger('pomelo', __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var commonEnum = require('../../../../shared/enum');
var Code = require('../../../../shared/code');
var Constant = require("../../../../shared/constant");
var TimeUtils = require('../../util/timeUtils');
var CommunityLeague = require("./communityLeague");
var NormalLeague = require("./normalLeague");
var KnockoutLeague = require("./knockoutLeague");
var ProfessionLeague = require("./professionLeague");
var BattleQueue =  require("./battleQueue");
var BattleCodeData = require("./battleColdData");

//这个类处理与其他服务器通信的基层类
var League = function(seasonId, leagueFsm, app) 
{
    this.app = app;
    this.uid = seasonId;                                           //赛季id
    this.honorSeasonId = 0;                      //荣誉墙赛季
    this.leagueFsm      = leagueFsm;
    this.battleQueue    = new BattleQueue(this, app);
    this.battleCodeData =  new BattleCodeData(this, app);            //玩家冷数据
    this.community  = new CommunityLeague(seasonId, this);           //社区联赛数据
    this.normal     = new NormalLeague(seasonId, this);              //常规赛
    this.knockout   = new KnockoutLeague(seasonId, this);            //附加淘汰赛
    this.profession = new ProfessionLeague(seasonId, this);          //专业联赛(国际级别)
};

util.inherits(League, EventEmitter);
module.exports = League;

//初始化数据
League.prototype.init = function() 
{
};

League.prototype.getEnrollTime = function(uid)
{
    return this.leagueFsm.enrollTime.getEnrollTime(uid);
};

//通用函数
League.prototype.newPlayerObj = function(uid, isGetStrength)
{
    let power = 0;
    if (isGetStrength)
    {
        //console.trace();
        if (!this.checkRobot(uid))
        {
            power = this.battleCodeData.getBattlePlayerActualStrength(uid);
        }
    }

    let enrollTime = this.getEnrollTime(uid);
    var playerObj = {
        playerUid: uid,
        battleCount: 0,
        winCount: 0,
        drawCount: 0,
        lossCount: 0,
        goalCount: 0,
        missCount: 0,
        totalScore: 0,
        power: power,
        enrollTime: enrollTime,
    };
    return playerObj;
};

League.prototype.getName = function(uid)
{
    let name = this.battleCodeData.getBattlePlayerName(uid);
    return name;
};

League.prototype.getTeamTotalValue = function(uid)
{
    return this.battleCodeData.getTeamTotalValue(uid);
}

League.prototype.checkRobot = function(uid)
{
    let ret = false;
    if (!uid)
    {
        return ret;
    }

    ret = "robot_" === uid.substr(0, 6); //home是否为机器人
    return ret;
};

//轮次key，一般作为map的key存在
League.prototype.getRoundKey = function(round, groupId) 
{
  return round + "_" + groupId;
};

//获取组Id
League.prototype.getGroupId = function(round_key) {
  let arr = round_key.split("_");
  return arr[1];
};

League.prototype.getRound = function(round_key) {
  let arr = round_key.split("_");
  return arr[0];
};

League.prototype.checkRound = function(round, destRound, err)
{
    if (round !== destRound)
    {
       logger.error(err, ": the currRound value is error! round, destRound", round, destRound)
       return false;
    }
    return true;
};

League.prototype.sendBattleQueue = function(needSendBattleList) {
    for (let index in needSendBattleList) {
        let data = needSendBattleList[index];
        if (!data) continue;
        //logger.info("sendBattleQueue", data);
        this.battleQueue.addBattleToQueueByObj(data);
    }
};

League.prototype.commonGetBattleData = function(groupPlayerMap, cb) 
{
  let waitUidList = [];
  for (let [k, v] of groupPlayerMap) 
  {
      for (let index = 0; index < v.length; index++) 
      {
        var obj = v[index];
        let uid1 = obj.Host;
        let uid2 = obj.Gust;

        if (!this.checkRobot(uid1))
        {
          waitUidList.push(uid1);
          //logger.info("commonGetBattleData: uid1", uid1);
        }

        if (!this.checkRobot(uid2))
        {
          waitUidList.push(uid2);
          //logger.info("commonGetBattleData: uid2", uid2);
        }
      }
  }

  this.getBattleData(waitUidList, function(err){
    if (!!err)
    {
      logger.error("comGetBattleData catch a error!", err);
      return cb(err);
    }
    return cb(null);
  });
};

League.prototype.pfGetBattleData = function(waitUidList, cb)
{
    this.getBattleData(waitUidList, function(err){
        if (!!err)
        {
          logger.error("comGetBattleData catch a error!", err);
          return cb(err);
        }
        return cb(null);
    });  
};

League.prototype.getBattleData = function(waitUidList, cb)
{
    this.battleCodeData.reset();
    for(let idx in waitUidList)
    {
        let uid = waitUidList[idx];
        if (!uid)
        {
            continue;
        }
        this.battleCodeData.addWaitUid(uid);
    }

    this.battleCodeData.getBattleDataToSvr(function(err, code){
        if (!!err)
        {
            logger.error("getBattleData getBattleDataToSvr: error", err);
            return cb(err);
        }
        return cb(null);
    });
};

League.prototype.makeBattleList = function(typeId, round, groupId, battleObjList, length, historyRankData) 
{
  let battleList = []; //{uid, uid2}
  for (let index = 0; index < length; index++) {
    var obj = battleObjList[index];
    if (!obj) continue;
    let uid1 = obj.Host;
    let uid2 = obj.Gust;
    let beginTime = obj.BeginTime;
    var battleObj = {
        home: uid1,
        away: uid2,
        typeId: typeId,
        round: round,
        groupId: groupId,
        leagueName: historyRankData.leagueName,
        roundName: historyRankData.roundName,
        homeRank: this.getHistoryRank(historyRankData.historyRankObjMap, uid1),
        awayRank: this.getHistoryRank(historyRankData.historyRankObjMap, uid2),
        beginTime: beginTime,
    };

    battleList[index] = battleObj;
  }
  return battleList;
};

League.prototype.getHistoryRank = function(historyRankObjMap, uid)
{
    let obj = historyRankObjMap.get(uid);
    if (!obj)
    {
        logger.warn("getHistoryRank: not rank info", uid);
        //console.trace();
        obj = {winCount: 0, lossCount: 0, drawCount:0, rank:0, totalValue: 0}
    }
    return obj;
};

League.prototype.getProfessionLeagueName = function(groupId)
{
  let name= "";
  switch(groupId)
  {
    case 1:
    name = commonEnum.LEAGUE_NAME.AMATEUR;
    break;
    case 2:
    name = commonEnum.LEAGUE_NAME.LEAGUE_SECOND;
    break;
    case 3:
    name = commonEnum.LEAGUE_NAME.LEAGUE_FIRST;
    break;
    case 4:
    name = commonEnum.LEAGUE_NAME.CHAMPIONS;
    break;
    case 5:
    name = commonEnum.LEAGUE_NAME.SUPER;
    break;
    default:
    name = commonEnum.LEAGUE_NAME.PREPARE;
    break;
  }
  return name;
};

//处理战斗序列中后面的全是robot的情况，需要打散开来
//实现思路:
//1.先找到一组全是机器人序列表
//2.再全部遍历替换真人的队列 这里只替换队列表
//Note: 如下图所示
// 0 0 x x x
// 0 0 0 x x
//我只要保证玩家与玩家的对打的序列超过默认值就可以了
//我再某一组里面有5个人， 但是需要过滤两个人，如果直接使用数组的话。那么一定会有两个人被淘汰
//为了保证这个战斗结果一定要有收缩性，只需要保证下面的玩家等于或者小于所需要的数量即可
League.prototype.preProcessBattleList = function(battleList, required_every_group_num)
{
    let cp = utils.cloneArray(battleList);
    let fullPlayerIdxList = [];
    let fullPlayerCount = 0;
    let idx = 0;
    let isNeedSwap = true;
    for (let index in battleList) {
      let data = battleList[index];
      let robotA;
      let robotB;
      if(data.home || data.away)
      {
          robotA = this.checkRobot(data.home); //home是否为机器人
          robotB = this.checkRobot(data.away); //home是否为机器人
      }
      else if(data.Host || data.Gust)
      {
          robotA = this.checkRobot(data.Host); //home是否为机器人
          robotB = this.checkRobot(data.Gust); //home是否为机器人
      }

      if (!robotA && !robotB) {
        fullPlayerIdxList.push(parseInt(index));
        fullPlayerCount++;
      }
  
      if (!robotB) {
        idx++;
        if (idx >= required_every_group_num) {
          isNeedSwap = false;
          break;
        }
      }
    }
  
    //如果需要交换元素
    if (isNeedSwap && fullPlayerCount < required_every_group_num) {
      idx = 0;
      let playerCount = 0;
      for (let index in battleList) {
        let data = battleList[index];
          let robotA;
          let robotB;
          if(data.home || data.away)
          {
              robotA = this.checkRobot(data.home); //home是否为机器人
              robotB = this.checkRobot(data.away); //home是否为机器人
          }
          else if(data.Host || data.Gust)
          {
              robotA = this.checkRobot(data.Host); //home是否为机器人
              robotB = this.checkRobot(data.Gust); //home是否为机器人
          }
        if (!robotB || !robotA) {
          playerCount++;
        }
  
        if (robotA && robotB && playerCount < required_every_group_num) {
          let full_indx = fullPlayerIdxList[idx];
          let player_data = battleList[full_indx];
          //logger.info("robotB", robotB);
  
          if (!full_indx && 0 !== full_indx) {
            //logger.error("preProcessBattleList: not found full_indx! idx", idx);
            continue;
          }
  
          if (!player_data) {
            logger.error("preProcessBattleList: battleList", battleList);
            logger.error("preProcessBattleList: idx, full_indx", idx, full_indx);
            continue;
          }
          if(data.home || data.away)
          {
              let away = data.away;
              let home = player_data.home;

              player_data.home = away;
              data.away = home;
          }
          else if(data.Host || data.Gust)
          {
              let Gust = data.Gust;
              let Host = player_data.Host;

              player_data.Host = Gust;
              data.Gust = Host;
          }

          idx++;
        }
      }
    }

    if (this.checkBattleList(battleList))
    {
        logger.error("preProcessBattleList: error!", battleList);
    }
};

//战斗列表查重
League.prototype.checkBattleList = function(battleList)
{
    let isRepeat = false;
    if (!battleList)
    {
        logger.error("checkBattleList: battleList is error!");
        return isRepeat;
    }

    let hashMap = new Map();  //uid => 1
    for (let index in battleList) {
        let data = battleList[index];
        let uid1;
        let uid2;
        if(data.home || data.away)
        {
            uid1 = data.home; 
            uid2 = data.away; 
        }
        else if(data.Host || data.Gust)
        {
            uid1 = data.Host;
            uid2 = data.Gust;
        }
        if (hashMap.has(uid1))
        {
            isRepeat =  true;
            logger.error("checkBattleList isRepeat", uid1, data);
            break;
        }

        if (hashMap.has(uid2))
        {
            logger.error("checkBattleList isRepeat", uid2, data);
            isRepeat = true;
            break;
        }

        hashMap.set(uid1, 1);
        hashMap.set(uid2, 1);
    }

    return isRepeat;
};

League.prototype.checkBattleListHostGuest = function(battleList)
{
    let isRepeat = false;
    if (!battleList)
    {
        logger.error("checkBattleListHostGuest: battleList is error!");
        return isRepeat;
    }

    let hashMap = new Map();  //uid => 1
    for (let index in battleList) {
        let data = battleList[index];
        let uid1 = data.Host;
        let uid2= data.Gust;
        if (hashMap.has(uid1))
        {
            isRepeat =  true;
            logger.error("checkBattleListHostGuest isRepeat", uid1, data);
            break;
        }

        if (hashMap.has(uid2))
        {
            logger.error("checkBattleListHostGuest isRepeat", uid2, data);
            isRepeat = true;
            break;
        }

        hashMap.set(uid1, 1);
        hashMap.set(uid2, 1);
    }
    
    return isRepeat;
};

//公共代码
League.prototype.comGetRoundConfig = function(roundConfig, round)
{
    if (!roundConfig)
    {
        logger.error("not roundConfig", round);
        console.trace();
        return null;
    }

    for (let i in roundConfig) {
        const config = roundConfig[i];
        if (!config) continue;
        if (round === parseInt(config.round)) {
          return config;
        }
      }

    logger.error("not found config, please check config again!", round, roundConfig);
    console.trace();
    return null;
};

League.prototype.comGetLastRound = function(roundConfig)
{
    if (!roundConfig)
    {
        logger.error("not roundConfig");
        console.trace();
        return 0;
    }

  let last_round = 0;
  for (let i in roundConfig) {
    const config = roundConfig[i];
    if (!config) continue;
    if (last_round < parseInt(config.round)) {
      last_round = config.round;
    }
  }

  return last_round;
};

League.prototype.comCheckRunNextAction = function(privateConfig, round) 
{
    if (!privateConfig)
    {
        // logger.error("not privateConfig", round);
        // console.trace();
        return false;
    }

    for (let i in privateConfig) {
        const config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round)) 
        {
        return config.canAction;
        }
    }
    
  return false;
};

League.prototype.comSetCanRunNextAction = function(privateConfig, round) 
{
    if (!privateConfig)
    {
        // logger.error("not privateConfig", round);
        // console.trace();
        return;
    }

    for (let i in privateConfig) {
      let config = privateConfig[i];
      if (!config) continue;
      if (parseInt(round) === parseInt(config.round)) 
      {
        config.canAction = true;
        //logger.info("setCanRunNextAction", round, config.canAction );
        break;
      }
    }
};

League.prototype.comSetBattleStatus = function(privateConfig, round, battleStatus)
{
    if (!privateConfig)
    {
        // logger.error("not privateConfig", round);
        // console.trace();
        return;
    }

    for (let i in privateConfig) {
        let config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round)) {
        if (config.battleStatus !== battleStatus)
        {
            config.battleStatus = battleStatus; 
            break;
        }
        }
    }
};

League.prototype.comGetConfigBattleStatus = function(privateConfig, round)
{
    if (!privateConfig)
    {
        // logger.error("not privateConfig", round);
        // console.trace();
        return -1;
    }

    for (let i in privateConfig) {
        let config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round)) {
        return config.battleStatus;
        }
    }

    return -1;
};

League.prototype.comSetSendWaitStartNtf = function(privateConfig, round)
{
    if (!privateConfig)
    {
        logger.error("not privateConfig", round);
        console.trace();
        return;
    }

    for (let i in privateConfig) {
        let config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round)) {
        if (!config.sendWaitStartNtf)
        {
            config.sendWaitStartNtf = true; 
            break;
        }
        }
    }
};

League.prototype.comCheckSendWaitStartNtf = function(privateConfig, round)
{
    if (!privateConfig)
    {
        logger.error("not privateConfig", round);
        console.trace();
        return false;
    }

    for (let i in privateConfig) {
        let config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round)) {
            return config.sendWaitStartNtf; 
        }
    }

    return false;
};

League.prototype.comSetSendStartNtf = function(privateConfig, round)
{
    if (!privateConfig)
    {
        logger.error("not privateConfig", round);
        console.trace();
        return;
    }

    for (let i in privateConfig) {
        let config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round)) {
        if (!config.sendStartNtf)
        {
            config.sendStartNtf = true; 
            break;
        }
        }
    }
};

League.prototype.comCheckSendStartNtf = function(privateConfig, round)
{
    if (!privateConfig)
    {
        logger.error("not privateConfig", round);
        console.trace();
        return false;
    }

    for (let i in privateConfig) {
        let config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round)) {
            return config.sendStartNtf; 
        }
    }
    return false;
};

//是否已经运行过了，代码只运行一次
League.prototype.comCheckRunning = function(privateConfig, round) 
{
    if (!privateConfig)
    {
        logger.error("not privateConfig", round);
        console.trace();
        return false;
    }

    for (let i in privateConfig) {
        const config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round)) 
        {
            return config.isRunning;
        }
    }
    return false;
};

//是否已经运行过了，代码只运行一次
League.prototype.comCheckWait = function(privateConfig, round)
{
    if (!privateConfig)
    {
        logger.error("not privateConfig", round);
        console.trace();
        return false;
    }

    for (let i in privateConfig) {
        const config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round))
        {
            return config.wait;
        }
    }
    return false;
};

League.prototype.comSetIsRunning = function(privateConfig, round)
{
    if (!privateConfig)
    {
        logger.error("not privateConfig", round);
        console.trace();
        return;
    }

    for (let i in privateConfig) {
        let config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round)) 
        {
            config.isRunning = true;
            //logger.info("setIsRunning", round, config.isRunning );
            break;
        }
    }
};

League.prototype.comSetIsWait = function(privateConfig, round, flag)
{
    if (!privateConfig)
    {
        logger.error("not privateConfig", round);
        console.trace();
        return;
    }

    for (let i in privateConfig) {
        let config = privateConfig[i];
        if (!config) continue;
        if (parseInt(round) === parseInt(config.round))
        {
            config.wait = flag;
            //logger.info("setIsRunning", round, config.isRunning );
            break;
        }
    }
};

League.prototype.comGetNotifyMailObj = function(uid, battleObj, typeId, notifyType, leagueName, round)
{
  let selfScore = 0;
  let enemyScore = 0;
  let enemyUid = "";
  if (battleObj.home === uid)
  {
    selfScore = battleObj.homeScore;
    enemyScore = battleObj.awayScore;
    enemyUid = battleObj.away;
  }else
  {
    selfScore = battleObj.awayScore;
    enemyScore = battleObj.homeScore;
    enemyUid = battleObj.home;
  }

  let isNull = 0;
  if (this.checkRobot(enemyUid))
  {
    enemyUid = "";
    isNull = 1;
  }

  let pObj = {
    uid: uid,
    selfScore: selfScore,
    enemyScore: enemyScore,
    enemyUid: enemyUid,
    enemyName: "",
    isNull: isNull,
    roomUid: battleObj.roomUid,
    typeId: typeId,
    notifyType: notifyType,
    leagueName: leagueName,
    round: round,
  }

  return pObj;
};

//获取玩家对阵列表
League.prototype.getAllPlayerBattleList = function(battleList) {
    let needSendBattleList = []; //{uid, uid2} 对手都是真实玩家
    for (let index in battleList) {
      let data = battleList[index];
      //logger.info("data", data);
      if (!data) continue;
      let robotA = this.checkRobot(data.home); //home是否为机器人
      let robotB =this.checkRobot(data.away); //home是否为机器人
      if (!robotA && !robotB) {
        //人与人打
          needSendBattleList.push(data);
      }
    }
    return needSendBattleList;
};

//战斗结果
League.prototype.comGetBattleResultObj = function(data, typeId)
{
    if (!data)
    {
        logger.error("comGetBattleResultObj: not data!");
        console.trace();
    }

    var battleObj = {
        home: data.home, //主场
        away: data.away, //客场
        typeId: typeId,
        round: data.round,
        groupId: data.groupId,
        battleId: this.battleQueue.getBattleId(),
        roomUid: "",
        homeScore: 0,
        awayScore: 0,
        beginTime: data.beginTime,
        winTeam: data.winTeam,
      };
    
    return battleObj;
};

//辅助函数
League.prototype.getId = function() {
	return this.uid;
};

//检查玩家是否已经报名
League.prototype.checkEnroll = function(uid)
{
    let check = false;
    if (this.community.checkIsEnroll(uid) || this.isProfessionJoiner(uid))
    {
        check = true;
    }
    return check;
};

//数据流动 com -> normal  //改成直接晋升到职业联赛里的预备联赛
League.prototype.communitySendPlayerToNormal = function(playerObj)
{
    return this.profession.Enroll(playerObj);
};

League.prototype.normalSendPlayerToKnock = function(playerObj)
{
    return this.knockout.Enroll(playerObj, 0);
};

League.prototype.professionSendPlayerToKnock = function()
{
    let uidList = this.profession.getLast16TeamUidList();
    for (let idx = 0; idx < uidList.length; idx++) {
        const playerObj = uidList[idx];
        if (!playerObj)
        {
            continue;
        }
        this.knockout.Enroll(playerObj, 1);
    }
};

League.prototype.promotionPlayerToTeam = function(uidList)
{
    this.profession.promotionPlayerToTeam(uidList);
};

//第一次将前100名玩家插入到专业联赛中
//Note: 有可能不到100
League.prototype.insertProfessionRank = function(groupIdUidMap) 
{
    this.profession.insertProfessionRank(groupIdUidMap);
};

League.prototype.getSimpleTeamInfo = function(uid)
{
  let simpleTeamInfo = {
        playerUid: uid,
        name: "",       //这些到游戏服再进行填充
        faceIcon: 0,    //头像
        actualStrength: 0, //球队实力
   };

   return simpleTeamInfo;
};

/*-------------------------------------数据存储 Begin-----------------------------------------------------*/
League.prototype.saveCommunity = function()
{
	this.emit("saveCommunity", true);
};

League.prototype.saveNormal = function()
{
	this.emit("saveNormal", true);
};

League.prototype.saveKnockout = function()
{
	this.emit("saveKnockout", true);
};

League.prototype.saveProfession = function()
{
	this.emit("saveProfession", true);
};
/*-------------------------------------数据存储 End-----------------------------------------------------*/

/*-------------------------------------数据保存转化函数 Begin-----------------------------------------------------*/
League.prototype.groupPlayerMapToDB = function(groupPlayerMap)
{
    var arr = [];
    if (!groupPlayerMap)
    {
        return arr;
    }

    for(let [k,v] of groupPlayerMap)
    {
      var obj = {
        groupId: k,
        uidList: v,
      }
      arr.push(obj);
    }

    return arr;
};

League.prototype.groupPlayerMapLoadFromDB = function(arr)
{
    var map = new Map();
    if (!arr || arr.length <= 0)
    {
        return map;
    }

    for (let idx in arr) {
        let obj =  arr[idx];
        let groupId = obj.groupId;
        let uidList = obj.uidList;
        map.set(groupId, uidList);
    }

    return map;
};

League.prototype.playerGroupMapToDB = function(playerGroupMap)
{
    var arr = [];
    if (!playerGroupMap)
    {
        return arr;
    }

    for(let [k,v] of playerGroupMap)
    {
      var obj = {
        uid: k,
        groupId: v,
      }
      arr.push(obj);
    }
    
    return arr;
};

League.prototype.playerGroupMapLoadFromDB = function(arr)
{
    var map = new Map();
    if (!arr || arr.length <= 0)
    {
        return map;
    }

    for (let idx in arr) {
        let obj =  arr[idx];
        let uid = obj.uid;
        let groupId = obj.groupId;
        map.set(uid, groupId);
    }

    return map;
};

League.prototype.battleMapToDB = function(battleMap)
{
    var arr = [];
    if (!battleMap)
    {
        return arr;
    }

    for(let [k,v] of battleMap)
    {
      var obj = {
        battleId: k,
        battleObj: v,
      }
      arr.push(obj);
    }
    
    return arr;
};

League.prototype.battleMapLoadFromDB = function(arr)
{
    var map = new Map();
    if (!arr || arr.length <= 0)
    {
        return map;
    }

    for (let idx in arr) {
        let obj =  arr[idx];
        let battleId = obj.battleId;
        let battleObj = obj.battleObj;
        map.set(battleId, battleObj);
    }

    return map;
};

League.prototype.playerMapToDB = function(playerMap)
{
    var arr = [];
    if (!playerMap)
    {
        return arr;
    }

    for(let [k,v] of playerMap)
    {
      var obj = {
        uid: k,
        playerObj: v,
      }
      arr.push(obj);
    }
    return arr;
};

League.prototype.playerMapLoadFromDB = function(arr)
{
    var map = new Map();
    if (!arr || arr.length <= 0)
    {
        return map;
    }

    for (let idx in arr) {
        let obj =  arr[idx];
        let uid = obj.uid;
        let playerObj = obj.playerObj;
        map.set(uid, playerObj);
    }
    return map;
};

League.prototype.finalProMapToDB = function(playerMap)
{
    var arr = [];
    if (!playerMap)
    {
        return arr;
    }

    for(let [k,v] of playerMap)
    {
      var obj = {
        uid: k,
        groupId: v,
      }
      arr.push(obj);
    }
    return arr;
};

League.prototype.finalProMapLoadFromDB = function(arr)
{
    var map = new Map();
    if (!arr || arr.length <= 0)
    {
        return map;
    }

    for (let idx in arr) {
        let obj =  arr[idx];
        let uid = obj.uid;
        let groupId = obj.groupId;
        map.set(uid, groupId);
    }
    return map;
};

League.prototype.finalRankToDB = function(finalRank)
{
    var arr = [];
    if (!finalRank)
    {
        return arr;
    }

    for(let [k,v] of finalRank)
    {
      var obj = {
        groupId: k,
        rankObjList: v,
      }
      arr.push(obj);
    }
    return arr;
};

League.prototype.finalRankLoadFromDB = function(arr)
{
    var map = new Map();
    if (!arr || arr.length <= 0)
    {
        return map;
    }
    for (let idx in arr) {
        let obj =  arr[idx];
        let groupId = obj.groupId;
        let rankObjList = obj.rankObjList;
        map.set(groupId, rankObjList);
    }

    return map;
};

League.prototype.roundGroupIdToDB = function(map)
{
    var arr = [];
    if (!map)
    {
        return arr;
    }

    for(let [k,v] of map)
    {
      var obj = {
        roundGroupId: k,
        objList: v,
      }
      arr.push(obj);
    }
    return arr;
};

League.prototype.roundGroupIdLoadFromDB = function(arr)
{
    var map = new Map();
    if (!arr || arr.length <= 0)
    {
        return map;
    }

    for (let idx in arr) {
        let obj =  arr[idx];
        let roundGroupId = obj.roundGroupId;
        let objList = obj.objList;
        map.set(roundGroupId, objList);
    }

    return map;
};

League.prototype.finalGroupPlayerToDB = function(map)
{
    var arr = [];
    if (!map)
    {
        return arr;
    }

    for(let [k,v] of map)
    {
      var obj = {
        groupId: k,
        uidList: v,
      }
      arr.push(obj);
    }
    return arr;
};

League.prototype.finalGroupPlayerDB = function(arr)
{
    var map = new Map();
    if (!arr || arr.length <= 0)
    {
        return map;
    }

    for (let idx in arr) {
        let obj =  arr[idx];
        let groupId = obj.groupId;
        let uidList = obj.uidList;
        map.set(groupId, uidList);
    }

    return map;
};
//检查联赛数据是否异常并修复
League.prototype.checkPrivateConfigAbnormal = function(privateConfig)
{
    //错误情况：isRunning = true canAction = false
    for(let i in privateConfig)
    {
        if(privateConfig[i].isRunning !== privateConfig[i].canAction)
        {
            logger.warn("League.checkPrivateConfigAbnormal: ", privateConfig[i]);
            privateConfig[i].isRunning = privateConfig[i].canAction;
            return privateConfig;
        }
    }
    return privateConfig;
};

/*-------------------------------------数据保存转化函数 End-----------------------------------------------------*/

League.prototype.isKKTJoiner =  function(uid)
{
    let groupId = this.knockout.getPlayerGroupMap(uid);
    if (groupId > 0)
    {
        return true;
    }
    return false;
};

League.prototype.isNormalJoiner =  function(uid)
{
    let groupId = this.normal.getPlayerGroupMap(uid);
    if (groupId > 0)
    {
        return true;
    }
    return false;
};

League.prototype.isCommunityJoiner = function(uid)
{
    let groupId = this.community.getPlayerGroupMap(uid);
    if (groupId > 0)
    {
        return true;
    }
    return false;
};

League.prototype.isProfessionJoiner = function(uid)
{
    let groupId = this.profession.getPlayerGroupMap(uid);
    if (groupId > 0)
    {
        return true;
    }
    return false;
};

League.prototype.getProfessionGroupIdByUid = function(uid)
{
    let groupId = this.profession.getPlayerGroupMap(uid);
    if (groupId > 0)
    {
        return groupId;
    }

    return 0;
};

//玩家是否参赛
League.prototype.isJoiner = function(uid)
{
    //查社区联赛
    let ret = this.isCommunityJoiner(uid);
    if (ret)
    {
        return true;
    }

    //专业联赛
    ret = this.profession.getPlayerGroupMap(uid);
    if (ret)
    {
        return true;
    }

    return false;
};

//客户端显示类型
League.prototype.getTypeIdByStatus = function(curr_status)
{
    let leagueTypeId = 0;
    if (curr_status <= Constant.STATE.ENROLL_OVER)
    {
        leagueTypeId = 0;
    }
    else if (curr_status < Constant.STATE.PROFESSION_ROUND1_PLAY) //community
    {
        leagueTypeId = this.community.getTypeId();
    }
    // else if (curr_status < Constant.STATE.KNOCKOUT_ROUND1_PLAY) //normal
    // {
    //     leagueTypeId = this.normal.getTypeId();
    // }
    // else if (curr_status < Constant.STATE.PROFESSION_ROUND1_PLAY)//knockout
    // {
    //     leagueTypeId = this.knockout.getTypeId();
    // }
    else if(curr_status <= Constant.STATE.NEXT_GAME)
    {
        leagueTypeId = this.profession.getTypeId();
    }
    return leagueTypeId;
};

League.prototype.getScheduleTypeIdByStatus = function(curr_status)
{
    let leagueTypeId = 0;
    if (curr_status <= Constant.STATE.ENROLL_OVER)
    {
        leagueTypeId = 0;
    } else if (curr_status < Constant.STATE.SHOW_NORMAL_ROUND_INFO ) //community
    {
        leagueTypeId = this.community.getTypeId();
    } else if (curr_status < Constant.STATE.SHOW_KNOCKOUT_ROUND_INFO) //normal
    {
        leagueTypeId = this.normal.getTypeId();
    } else if (curr_status < Constant.STATE.SHOW_PROFESSION_ROUND_5_INFO_RESET)//knockout
    {
        leagueTypeId = this.knockout.getTypeId();
    } else if(curr_status <= Constant.STATE.NEXT_GAME)
    {
        leagueTypeId = this.profession.getTypeId();
    }

    return leagueTypeId;
};

//获取公布对阵时间
League.prototype.getBattleTimeCT = function()
{
    let fsmData = this.leagueFsm.getGlobalSysData();
	let showBattleTime = fsmData.global_sys_data.enroll_end_time;
    //logger.info("getBattleTimeCT", showBattleTime);
    return showBattleTime;
};

League.prototype.getCommonBattleTime = function(status)
{
    let nextTimeMap = this.leagueFsm.scheduleDetail.getScheduleDetail(); //加载debug时间配置
    let next_time = nextTimeMap.get(parseInt(status));
    if (!next_time)
    {
        logger.error("getCommonBattleTime SHOW_NORMAL_ROUND_INFO_RESET next_time IS 0", next_time, status);
        next_time = 0;
    }
    let gameInitTime = this.leagueFsm.getGameInitTime();
    let showBattleTime = gameInitTime + next_time;
    return showBattleTime;
};

//获取各个联赛开启时间
League.prototype.getBattleTime = function(typeId)
{
    let showBattleTime = 0;
    switch (typeId) {
        case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
            // showBattleTime = this.getBattleTimeCT();
            showBattleTime = this.getCommonBattleTime(Constant.STATE.SHOW_COMMUNITY_ROUND_INFO) + 1 * 1000; //加1秒 取整数
            break;

        // case commonEnum.LEAGUE_TYPE_ID.NORMAL:
        //     showBattleTime = this.getCommonBattleTime(Constant.STATE.SHOW_NORMAL_ROUND_INFO_RESET) + 1000; //加1秒 取整数
        //     break;
        //
        // case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
        //     showBattleTime = this.getCommonBattleTime(Constant.STATE.SHOW_KNOCKOUT_ROUND_INFO_RESET);
        //     break;
        case commonEnum.LEAGUE_TYPE_ID.PREPARE:
        case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
        case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
        case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
        case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
        case commonEnum.LEAGUE_TYPE_ID.SUPER:
            showBattleTime = this.getCommonBattleTime(Constant.STATE.SHOW_PROFESSION_ROUND1_INFO) + 1 * 1000; //加1秒 取整数
            break;           
        default:
            logger.error("no case", typeId);
            break;
    }

    return showBattleTime;
};

//**************************************************外部消息基类处理入口 Begin************************************************************* */ 
//报名
League.prototype.Enroll = function(playerObj)
{
    let ret = Code.FAIL;
    if (!playerObj) {
        return ret;
    }
    
    let curr_status = this.leagueFsm.getCurrStatus();
    if (curr_status !== Constant.STATE.ENROLL_RUNNING)
    {
        logger.error("Enroll: FSM status error", curr_status);
        return Code.LEAGUE.FSM_INNER_ERROR;
    }

    //查看是否到了活动开启时间
    if (!this.leagueFsm.checkCanEnroll())
    {
        logger.info("it can't enroll this race!");
        return ret;
    }

    //玩家是否存在专业联赛中
    let uid = playerObj.uid;
    if(this.isProfessionJoiner(uid))
    {
        let groupId = this.profession.getPlayerGroupMap(uid);
        logger.error("Enroll: this player is profession player", uid, groupId);
        return Code.LEAGUE.ENROLL_PROFESSION_NOT_ALLOW;
    }

    //logger.info("League Enroll playerObj");
    let retCode = this.community.Enroll(playerObj);
    return retCode;
};

League.prototype.getCommunityScoreRank = function(playerUid, typeId)
{
    let roundScoreRankData = {};
     let groupId = 1;
     if(this.isProfessionJoiner(playerUid))
     {
        groupId = this.profession.getGroupIdByUid(playerUid);
        roundScoreRankData = this.profession.getRoundScoreRankInfo(0, groupId);
        logger.info("community isProfessionJoiner", playerUid);
        return roundScoreRankData;
     }
     
     var round = this.community.getFixCurrRound();
     groupId = this.community.getGroupIdByUid(playerUid) + 5;//预选赛组加5才是预备赛的组
     if (round <= 0) //未开始
     {
         if (!this.isCommunityJoiner(playerUid))
         {
            groupId = 6;
         }

        //给默认数据
        let defaultRound = 1;
        var config = this.community.getRoundConfig(defaultRound);
        let defaultRet = 
        {
          typeId: typeId,
          roundId: defaultRound,
          name: config.name,
          groupId: groupId,
          finalRound: 1,
          maxGroupCount: this.community.getMaxGroupCount(),
          scheduleTime: this.getBattleTime(typeId),
          maxRound: this.community.getMaxRound(),
          teamInfoList: [],
        };

        logger.info("isCommunityJoiner not start", playerUid, groupId, defaultRet);
        return defaultRet;
     }

     //处理未报名，或者被淘汰的
     if (!this.isCommunityJoiner(playerUid)) //不是参赛者
     {
        groupId = 1;
     }

    // if (this.community.checkLastRoundEnd())
    // {
        roundScoreRankData = this.community.getFinalScoreRankInfo(groupId); //默认第一组
        logger.warn("joiner final rank", playerUid, typeId, groupId);
    // }else
    // {
    //     roundScoreRankData = this.community.getCurrScoreRankInfo(groupId);
    //     logger.warn("isCommunityJoiner", playerUid, typeId, groupId);
    // }

    return roundScoreRankData;
};

League.prototype.getNormalScoreRank = function(playerUid, typeId)
{
    let roundScoreRankData = {};
    let groupId = 0;
    if(this.isProfessionJoiner(playerUid))
    {
        groupId = this.profession.getGroupIdByUid(playerUid);
        roundScoreRankData = this.profession.getRoundScoreRankInfo(0, groupId);
        logger.warn("NORMAL isProfessionJoiner", playerUid);
        return roundScoreRankData;
    }

    groupId = this.normal.getGroupIdByUid(playerUid);
    var round = this.normal.getFixCurrRound();
    if (round <= 0)
    {
        return this.getCommunityScoreRank(playerUid, commonEnum.LEAGUE_RUN_TYPE_ID.COMMUNITY);
    }

    if (!this.isNormalJoiner(playerUid))
    {
        groupId = 1;
        logger.warn("no isNormalJoiner joiner", playerUid, typeId);
    }

    // if (this.normal.checkLastRoundEnd())
    // {
        roundScoreRankData = this.normal.getFinalScoreRankInfo(groupId); //默认第一组
        logger.warn("normal joiner final rank", playerUid, typeId);
    // }else
    // {
    //     roundScoreRankData = this.normal.getCurrScoreRankInfo(groupId);
    //     logger.warn("normal isNormalJoiner hint", playerUid);
    // }

    return roundScoreRankData;
};

League.prototype.getKnockoutScoreRank = function(playerUid, typeId)
{
    let roundScoreRankData = {};
    if(this.isProfessionJoiner(playerUid))
    {
        let groupId = this.profession.getGroupIdByUid(playerUid);
        roundScoreRankData = this.profession.getRoundScoreRankInfo(0, groupId);
        logger.warn("KKT isProfessionJoiner", playerUid, groupId);
        return roundScoreRankData;
    }

    var round = this.knockout.getFixCurrRound();
    if (round <= 0)
    {
        return this.getNormalScoreRank(playerUid, commonEnum.LEAGUE_RUN_TYPE_ID.NORMAL);
    }

    roundScoreRankData = this.knockout.getCurrScoreRankInfo(1);
    return roundScoreRankData;
};

//玩家点击后跳转到联赛默认显示的积分榜
League.prototype.getProfessionScoreRank = function(playerUid, typeId)
{
    let roundScoreRankData = {};
    //1.未参赛定位到第一组,当前轮次
    if(!this.isProfessionJoiner(playerUid))
    {
        // if (this.profession.checkLastRoundEnd())
        // {
            roundScoreRankData = this.profession.getFinalScoreRankInfo(1); //默认第一组
            logger.warn("joiner final rank profession", playerUid, typeId);
        // }else
        // {
        //     roundScoreRankData = this.profession.getCurrScoreRankInfo(1);
        //     logger.warn("isProfessionJoiner", playerUid);
        // }
        return roundScoreRankData;
    }

    var round = this.profession.getFixCurrRound();
    if (round <= 0)
    {
        return this.getKnockoutScoreRank(playerUid, commonEnum.LEAGUE_RUN_TYPE_ID.KNOCKOUT);
    }

    //专业赛的玩家不会被淘汰
    //2.100玩家定位到自己组
    //let groupId = this.profession.getGroupIdByUid(playerUid);
    // if (this.profession.checkLastRoundEnd())
    // {
    //     roundScoreRankData = this.profession.getFinalScoreRankInfo(groupId); //默认第一组
    //     logger.warn("profession joiner final rank profession", playerUid, typeId);
    // }else
    //{
        let groupId = this.profession.getGroupIdByUid(playerUid);
        roundScoreRankData = this.profession.getFinalScoreRankInfo(groupId);
        logger.warn("profession isProfessionJoiner", playerUid);
    //}

    return roundScoreRankData;
};

//Note:
//社区、常规赛、淘汰赛
//1.在专业联赛的100玩家 定位到自己组
//2.未参赛或者被淘汰 定位到最新
//3.未被淘汰的定位到所在组

//专业联赛
//1.未参赛定位到第一组,当前轮次
//2.100玩家定位到自己租
League.prototype.getCurrLeague = function(playerUid)
{
    let roundScoreRankData = {};
    //前面检测过了，这里不需要在进行检测
    let curr_status = this.leagueFsm.getCurrStatus();
    let typeId = this.getTypeIdByStatus(curr_status);
   // logger.info("typeId", playerUid, curr_status, typeId);
    switch (typeId) {
        case commonEnum.LEAGUE_RUN_TYPE_ID.COMMUNITY:
            roundScoreRankData = this.getCommunityScoreRank(playerUid, typeId);
            break;
        // case commonEnum.LEAGUE_RUN_TYPE_ID.NORMAL:
        //     roundScoreRankData = this.getNormalScoreRank(playerUid, typeId);
        //     break;
        // case commonEnum.LEAGUE_RUN_TYPE_ID.KNOCKOUT:
        //     roundScoreRankData = this.getKnockoutScoreRank(playerUid, typeId);
        //     break;
        case commonEnum.LEAGUE_RUN_TYPE_ID.PROFESSION:
            roundScoreRankData = this.getProfessionScoreRank(playerUid, typeId);
            break;
        default:
            logger.error("no case", typeId);
            break;
    }

    //logger.warn("joiner", playerUid, typeId, roundScoreRankData.teamInfoList.length);
    return roundScoreRankData;
};
//第三排赛程
League.prototype.getSchedule = function(typeId, roundId, groupId)
{
    let roundScheduleData = {};
    switch (typeId) {
        case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
            roundScheduleData = this.community.getRoundScheduleInfo(roundId, groupId);
            break;
        case commonEnum.LEAGUE_TYPE_ID.NORMAL:
            roundScheduleData = this.normal.getRoundScheduleInfo(roundId, groupId);
            break;

        case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
            roundScheduleData = this.knockout.getRoundScheduleInfo(1, 1);
            break;

        case commonEnum.LEAGUE_TYPE_ID.PREPARE:
            roundScheduleData = this.profession.getRoundScheduleInfo(roundId, groupId);
            break;

        case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
            roundScheduleData = this.profession.getRoundScheduleInfo(roundId, 1);
            break;

        case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
            roundScheduleData = this.profession.getRoundScheduleInfo(roundId, 2);
            break;

        case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
            roundScheduleData = this.profession.getRoundScheduleInfo(roundId, 3);
            break;

        case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
            roundScheduleData = this.profession.getRoundScheduleInfo(roundId, 4);
            break;

        case commonEnum.LEAGUE_TYPE_ID.SUPER:
            roundScheduleData = this.profession.getRoundScheduleInfo(roundId, 5);
            break;           
        default:
            logger.error("no case", typeId);
            break;
    }
    return roundScheduleData;
};
//点击第三排
League.prototype.getScoreRank = function(typeId, roundId, groupId)
{
    let roundScoreRankData = {};
    switch (typeId) {
        case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
            roundScoreRankData = this.community.getRoundScoreRankInfo(roundId, groupId);
            break;

        case commonEnum.LEAGUE_TYPE_ID.NORMAL:
            roundScoreRankData = this.normal.getRoundScoreRankInfo(roundId, groupId);
            break;

        case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
            roundScoreRankData = this.knockout.getRoundScoreRankInfo(1, 1);
            break;

        case commonEnum.LEAGUE_TYPE_ID.PREPARE:
            roundScoreRankData = this.profession.getRoundScoreRankInfo(roundId, groupId);
            break;

        case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
            roundScoreRankData = this.profession.getRoundScoreRankInfo(roundId, 1);
            break;

        case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
            roundScoreRankData = this.profession.getRoundScoreRankInfo(roundId, 2);
            break;

        case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
            roundScoreRankData = this.profession.getRoundScoreRankInfo(roundId, 3);
            break;

        case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
            roundScoreRankData = this.profession.getRoundScoreRankInfo(roundId, 4);
            break;

        case commonEnum.LEAGUE_TYPE_ID.SUPER:
            roundScoreRankData = this.profession.getRoundScoreRankInfo(roundId, 5);
            break;

        default:
            logger.error("no case", typeId);
            break;
    }

    return roundScoreRankData;
};

League.prototype.getDefaultCommunityScheduleInfo = function(uid, typeId)
{
    var round = this.community.getFixCurrRound();
    if (round <= 0)
    {
        round = 1;
    }

    let groupId = this.community.getGroupIdByUid(uid);
    //处理未报名，或者被淘汰的
    if (!this.isCommunityJoiner(uid)) //不是参赛者
    {
        groupId = 1;
        logger.warn("COMMUNITY no joiner final schdule", uid, typeId, groupId);
    }

    let roundScheduleData = this.community.getRoundScheduleInfo(round, groupId); //默认第一组
    logger.warn("COMMUNITY joiner final schdule", uid, typeId, groupId);
    return roundScheduleData;
};

League.prototype.getDefaultNormalScheduleInfo = function(uid, typeId)
{
    var round = this.normal.getFixCurrRound();
    if (round <= 0) //未开始
    {
        round = 1;
    }

    let groupId = this.normal.getGroupIdByUid(uid);
    if (!this.isNormalJoiner(uid))
    {
        groupId = 1;
        logger.warn("normal no joiner final schdule", uid, typeId, groupId);
    }

    let roundScheduleData = this.normal.getRoundScheduleInfo(round, groupId);
    logger.warn("isNormalJoiner hint schdule", uid, groupId);
    return roundScheduleData;
};
//获取赛程榜
League.prototype.getDefaultSchedule = function(uid, typeId, groupId)
{
    let roundScheduleData = 
    {
      typeId: typeId,
      roundId: round,
      name: "",
      groupId: 0,
      finalRound: 0,
      maxGroupCount: 0,
      scheduleTime: this.getBattleTime(typeId),
      scheduleList: [],
    };

    let retCode = Code.FAIL;
    let retData = {
        code: retCode,
        roundScheduleData: roundScheduleData,
    };

    //检查放到函数外面去做了
    let curr_status = this.leagueFsm.getCurrStatus();
    let currTypeId = this.getScheduleTypeIdByStatus(curr_status);
    retCode = Code.OK;
    if (currTypeId === commonEnum.LEAGUE_RUN_TYPE_ID.COMMUNITY)
    {
        switch (typeId)
        {
            case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
                roundScheduleData = this.getDefaultCommunityScheduleInfo(uid, typeId);
                break;
            // case commonEnum.LEAGUE_TYPE_ID.NORMAL:
            //     retCode = Code.LEAGUE.CURR_NOT_DATA;
            //     break;
            // case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
            //     retCode = Code.LEAGUE.CURR_NOT_DATA;
            //     break;
            case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.SUPER:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            default:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                // retCode = Code.FAIL;
                logger.error("no case", typeId);
                break;
        }
    }
    else if (currTypeId === commonEnum.LEAGUE_RUN_TYPE_ID.NORMAL)
    {
        switch (typeId) 
        {
            case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
                roundScheduleData = this.getDefaultCommunityScheduleInfo(uid, typeId);
                break;
            // case commonEnum.LEAGUE_TYPE_ID.NORMAL:
            //     roundScheduleData = this.getDefaultNormalScheduleInfo(uid, typeId);
            //     break;
            // case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
            //     retCode = Code.LEAGUE.CURR_NOT_DATA;
            //     break;
            case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.SUPER:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            default:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                // retCode = Code.FAIL;
                logger.error("no case", typeId);
                break;
        }
    }else if(currTypeId === commonEnum.LEAGUE_RUN_TYPE_ID.KNOCKOUT)
    {
        switch (typeId) 
        {
            case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
                roundScheduleData = this.getDefaultCommunityScheduleInfo(uid, typeId);
                break;
            // case commonEnum.LEAGUE_TYPE_ID.NORMAL:
            //     roundScheduleData = this.getDefaultNormalScheduleInfo(uid, typeId);
            //     break;
            // case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
            //     roundScheduleData = this.knockout.getRoundScheduleInfo(1, 1);
                break;
            case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.SUPER:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            default:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                // retCode = Code.FAIL;
                logger.error("no case", typeId);
                break;
        }
    }else if(currTypeId === commonEnum.LEAGUE_RUN_TYPE_ID.PROFESSION)
    {
        switch (typeId)
        {
            case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
                roundScheduleData = this.getDefaultCommunityScheduleInfo(uid, typeId);
                break;
            // case commonEnum.LEAGUE_TYPE_ID.NORMAL:
            //     roundScheduleData = this.getDefaultNormalScheduleInfo(uid, typeId);
            //     break;
            // case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
            //     roundScheduleData = this.knockout.getRoundScheduleInfo(1, 1);
            //     break;
            case commonEnum.LEAGUE_TYPE_ID.PREPARE:
                var round = this.profession.getFixCurrRound();
                if (round <= 0) round = 1;
                let group = 6;
                if(!!groupId)
                {
                    group = groupId;
                }
                roundScheduleData = this.profession.getRoundScheduleInfo(round, group);
                break;
            case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
                var round = this.profession.getFixCurrRound();
                if (round <= 0) round = 1;
                roundScheduleData = this.profession.getRoundScheduleInfo(round, 1);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
                var round = this.profession.getFixCurrRound();
                if (round <= 0) round = 1;
                roundScheduleData = this.profession.getRoundScheduleInfo(round, 2);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
                var round = this.profession.getFixCurrRound();
                if (round <= 0) round = 1;
                roundScheduleData = this.profession.getRoundScheduleInfo(round, 3);
                break;
            case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
                var round = this.profession.getFixCurrRound();
                if (round <= 0) round = 1;
                roundScheduleData = this.profession.getRoundScheduleInfo(round, 4);
                break;
            case commonEnum.LEAGUE_TYPE_ID.SUPER:
                var round = this.profession.getFixCurrRound();
                if (round <= 0) round = 1;
                roundScheduleData = this.profession.getRoundScheduleInfo(round, 5);
                break;
            default:
                retCode = Code.FAIL;
                logger.error("no case", typeId);
                break;
        }
        if(!roundScheduleData.scheduleList.length > 0)//还没公布赛程显示公布时间
        {
            retCode = Code.LEAGUE.CURR_NOT_DATA;
        }
    }
    retData.code = retCode;
    retData.roundScheduleData = roundScheduleData;
    return retData;
};

League.prototype.getDefaultCommunityRank = function(uid, typeId, group)
{
    var round = this.community.getFixCurrRound();
    if (round <= 0)
    {
        round = 1;
    }

    let groupId = this.community.getGroupIdByUid(uid);
    if (!this.isCommunityJoiner(uid)) //不是参赛者
    {
        groupId = 6;
        logger.warn("NORMAL no joiner final rank 2", uid, typeId);
    }

    if(!!group)
        groupId = group;

    let roundScoreRankData = this.community.getFinalScoreRankInfo(groupId); //默认第一组
    return roundScoreRankData;
};

League.prototype.getDefaultNormalRank = function(uid, typeId)
{
    var round = this.normal.getFixCurrRound();
    if (round <= 0)
    {
        round = 1;
    }

    let groupId = this.normal.getGroupIdByUid(uid);
    if (!this.isNormalJoiner(uid))
    {
        groupId = 1;
        logger.warn("normal no joiner final schdule", uid, typeId, groupId);
    }

    let roundScoreRankData = this.normal.getFinalScoreRankInfo(groupId); //玩家所在组
    return roundScoreRankData;
};
//获得默认分数排名 点击第一排
League.prototype.getDefaultScoreRank = function(uid, typeId, group)
{
    let roundScoreRankData = 
    {
      typeId: typeId,
      roundId: 0,
      name: "",
      groupId: 0,
      finalRound: 0,
      maxGroupCount: 0,
      scheduleTime: this.getBattleTime(typeId),
      maxRound: 0,
      teamInfoList: [],
    };

    let retCode = Code.LEAGUE.CURR_NOT_DATA;
    let retData = {
        code: retCode,
        roundScoreRankData: roundScoreRankData,
    }

    let curr_status = this.leagueFsm.getCurrStatus();
    let currTypeId = this.getTypeIdByStatus(curr_status);
    retCode = Code.OK;
    if (currTypeId === commonEnum.LEAGUE_RUN_TYPE_ID.COMMUNITY)
    {
        switch (typeId)
        {
            case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
                var round = this.community.getFixCurrRound();
                let groupId = this.community.getGroupIdByUid(uid);
                if (round <= 0) //未开始
                {
                    if (!this.isCommunityJoiner(uid))
                    {
                        groupId = 1;
                    }
        
                    //给默认数据
                    let defaultRound = 1;
                    var config = this.community.getRoundConfig(defaultRound);
                    let defaultRet = 
                    {
                        typeId: typeId,
                        roundId: defaultRound,
                        name: config.name,
                        groupId: groupId,
                        finalRound: 1,
                        maxGroupCount: this.community.getMaxGroupCount(),
                        scheduleTime: this.getBattleTime(typeId),
                        maxRound: this.community.getMaxRound(),
                        teamInfoList: [],
                    };
        
                    logger.info("isCommunityJoiner not start", uid, groupId, defaultRet);
                    roundScoreRankData = defaultRet;
                    break;
                }
        
                //处理未报名，或者被淘汰的
                if (!this.isCommunityJoiner(uid)) //不是参赛者
                {
                    groupId = 1;
                }

                if(!!group)
                {
                    groupId = group;
                }
        
                // if (this.community.checkLastRoundEnd())
                // {
                //     roundScoreRankData = this.community.getFinalScoreRankInfo(groupId); //默认第一组
                //     logger.warn("joiner final rank", uid, typeId, groupId);
                // }else
                // {
                    roundScoreRankData = this.community.getFinalScoreRankInfo(groupId);
                    logger.warn("isCommunityJoiner", uid, typeId, groupId);
                //}
                break;
            case commonEnum.LEAGUE_TYPE_ID.NORMAL:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.PREPARE:
                if(!!group)
                {
                    roundScoreRankData = this.profession.getRoundScoreRankInfo(0, group);
                }
                else
                {
                    roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 6);
                }
                break;
            case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 1);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 2);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 3);
                break;
            case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 4);
                break;
            case commonEnum.LEAGUE_TYPE_ID.SUPER:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 5);
                break;
            default:
                logger.error("no case", typeId);
                break;
        }
    }
    else if (currTypeId === commonEnum.LEAGUE_RUN_TYPE_ID.NORMAL)
    {
        switch (typeId) 
        {
            case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
                if(!!group)
                {
                    roundScoreRankData = this.getDefaultCommunityRank(uid, typeId, group);
                }
                else
                {
                    roundScoreRankData = this.getDefaultCommunityRank(uid, typeId);
                }
                break;
            case commonEnum.LEAGUE_TYPE_ID.NORMAL:
                let groupId = this.normal.getGroupIdByUid(uid);
                var round = this.normal.getFixCurrRound();
                if (round <= 0)
                {
                    if (!this.isNormalJoiner(uid))
                    {
                        groupId = 1;
                    }
                    //给默认数据
                    let defaultRound = 1;
                    let finalRound = 1;
                    var config = this.normal.getRoundConfig(defaultRound);
                    let defaultRet =
                    {
                        typeId: typeId,
                        roundId: defaultRound,
                        name: config.name,
                        groupId: groupId,
                        finalRound: finalRound,
                        maxGroupCount: this.normal.getMaxGroupCount(),
                        scheduleTime: this.getBattleTime(typeId),
                        maxRound: this.normal.getMaxRound(),
                        teamInfoList: [],
                    };
                    roundScoreRankData = defaultRet;
                    break;
                }
       
                // if (this.normal.checkLastRoundEnd())
                // {
                //     roundScoreRankData = this.normal.getFinalScoreRankInfo(groupId); //默认第一组
                //     logger.warn("joiner final rank", uid, typeId);
                // }else
                // {
                    roundScoreRankData = this.normal.getFinalScoreRankInfo(groupId);
                    logger.warn("isNormalJoiner hint", uid);
                //}
                break;
            case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
                retCode = Code.LEAGUE.CURR_NOT_DATA;
                break;
            case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 1);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 2);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 3);
                break;
            case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 4);
                break;
            case commonEnum.LEAGUE_TYPE_ID.SUPER:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 5);
                break;
            default:
                logger.error("no case", typeId);
                break;
        }
    }else if(currTypeId === commonEnum.LEAGUE_RUN_TYPE_ID.KNOCKOUT)
    {
        switch (typeId) 
        {
            case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
                roundScoreRankData =  this.getDefaultCommunityRank(uid, typeId);
                break;
            case commonEnum.LEAGUE_TYPE_ID.NORMAL:
                roundScoreRankData =  this.getDefaultNormalRank(uid, typeId);
                break;
            case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
                var round = this.knockout.getFixCurrRound();
                let groupId = 1;
                if (round <= 0)
                {
                    //给默认数据
                    let defaultRound = 1;
                    let finalRound = 1;
                    var config = this.knockout.getRoundConfig(defaultRound);
                    let defaultRet = 
                    {
                        typeId: typeId,
                        roundId: defaultRound,
                        name: config.name,
                        groupId: groupId,
                        finalRound: finalRound,
                        maxGroupCount: this.knockout.getMaxGroupCount(),
                        scheduleTime: this.getBattleTime(typeId),
                        maxRound: this.knockout.getMaxRound(),
                        teamInfoList: [],
                    };
                    roundScoreRankData = defaultRet;
                    break;
                }

                roundScoreRankData = this.knockout.getCurrScoreRankInfo(groupId);
                logger.warn("isNormalJoiner", uid, roundScoreRankData);
                break;
            case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 1);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 2);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 3);
                break;
            case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 4);
                break;
            case commonEnum.LEAGUE_TYPE_ID.SUPER:
                roundScoreRankData = this.profession.getRoundScoreRankInfo(0, 5);
                break;
            default:
                logger.error("no case", typeId);
                break;
        }
    }else if(currTypeId === commonEnum.LEAGUE_RUN_TYPE_ID.PROFESSION)
    {
        switch (typeId) 
        {
            case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
                if(!!group)
                {
                    roundScoreRankData = this.getDefaultCommunityRank(uid, typeId, group);
                }
                else
                {
                    roundScoreRankData = this.getDefaultCommunityRank(uid, typeId);
                }
                break;
            case commonEnum.LEAGUE_TYPE_ID.NORMAL:
                roundScoreRankData =  this.getDefaultNormalRank(uid, typeId);
                break;
            case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
                roundScoreRankData = this.knockout.getCurrScoreRankInfo(1);
                logger.warn("PROFESSION KNOCKOUT", uid, roundScoreRankData);
                break;
            case commonEnum.LEAGUE_TYPE_ID.PREPARE:
                if(!!group)
                {
                    roundScoreRankData = this.profession.getFinalScoreRankInfo(group);
                }
                else
                {
                    roundScoreRankData = this.profession.getFinalScoreRankInfo(6);
                }
                break;
            case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
                //var round = this.profession.getFixCurrRound();
                roundScoreRankData = this.profession.getFinalScoreRankInfo(1);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
                //var round = this.profession.getFixCurrRound();
                roundScoreRankData = this.profession.getFinalScoreRankInfo(2);
                break;
            case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
                //var round = this.profession.getFixCurrRound();
                roundScoreRankData = this.profession.getFinalScoreRankInfo(3);
                break;
            case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
                //var round = this.profession.getFixCurrRound();
                roundScoreRankData = this.profession.getFinalScoreRankInfo(4);
                break;
            case commonEnum.LEAGUE_TYPE_ID.SUPER:
                //var round = this.profession.getFixCurrRound();
                roundScoreRankData = this.profession.getFinalScoreRankInfo(5);
                break;
            default:
                logger.error("no case", typeId);
                break;
        }
    }

    retData.code = retCode;
    retData.roundScoreRankData = roundScoreRankData;
    return retData;
};

//合并个人赛程
League.prototype.mergeHistory = function(destHistory, srcHistory)
{
    if(!destHistory)
    {
        logger.error("merge_history: destHistory error!");
        return;
    }

    if(!srcHistory)
    {
        logger.error("merge_history: srcHistory error!");
        return;
    }

    if (srcHistory.length <= 0)
    {
        logger.warn("merge_history: srcHistory length is 0");
        return;
    }

    for (let idx in srcHistory) {
        let data =  srcHistory[idx];
        if(!data) continue;
        destHistory.push(data);
    }

    logger.info("mergeHistory", destHistory);
};

//个人赛程排序
function __person_schdule_compare_func(rankObj1, rankObj2) {
    let typeId1 = rankObj1.typeId;
    let roundId1 = rankObj1.roundId;

    let typeId2 = rankObj2.typeId;
    let roundId2 = rankObj2.roundId;

    //积分
    if (typeId1 !== typeId2) {
      //降序
      if (typeId1 > typeId2) {
        return 1;
      } else if (typeId1 < typeId2) {
        return -1;
      }
    }

    if (roundId1 < roundId2)
    {
        return 1;
    }else if (roundId1 > roundId2)
    {
        return -1;
    }
    
    return 0;
};

//查找个人赛程
League.prototype.getPersonalHistory = function(destUid)
{
    let personalHistoryList = [];
    let com_history = this.community.getHistory(destUid);
    let normal_history = this.knockout.getHistory(destUid);
    let knockout_history = this.normal.getHistory(destUid);
    let profession_history = this.profession.getHistory(destUid);
    this.mergeHistory(personalHistoryList, com_history);
    this.mergeHistory(personalHistoryList, normal_history);
    this.mergeHistory(personalHistoryList, knockout_history);
    this.mergeHistory(personalHistoryList, profession_history);
    //个人赛程排序
    personalHistoryList.sort(__person_schdule_compare_func);

    logger.info("getPersonalHistory", personalHistoryList);
    return personalHistoryList;
};

//查找个人预选赛程
League.prototype.getPrimaryHistory = function(destUid)
{
    let PrimaryHistoryInfo = [];
    let com_history = this.community.getHistory(destUid);
    this.mergeHistory(PrimaryHistoryInfo, com_history);
    //个人赛程排序
    PrimaryHistoryInfo.sort(__person_schdule_compare_func);

    logger.info("getPersonalHistory", PrimaryHistoryInfo);
    return PrimaryHistoryInfo;
};

//查找个人全部赛程
League.prototype.getPersonalAllSchedule = function(destUid)
{
    let PersonalAllSchedule = [];
    let com_history = this.community.getAllSchedule(destUid);
    let normal_history = this.normal.getAllSchedule(destUid);
    let profession_history = this.profession.getAllSchedule(destUid);
    this.mergeHistory(PersonalAllSchedule, com_history);
    this.mergeHistory(PersonalAllSchedule, normal_history);
    this.mergeHistory(PersonalAllSchedule, profession_history);
    //个人赛程排序
    PersonalAllSchedule.sort(__person_schdule_compare_func);

    logger.info("getPersonalAllSchedule", PersonalAllSchedule);
    return PersonalAllSchedule;
};

//检查能够设置阵容
League.prototype.checkSetTeam = function()
{
    let retCode = Code.LEAGUE.CAN_NOT_SET_TEAM;
    let curr_status = this.leagueFsm.getCurrStatus();
    let status_obj = this.leagueFsm.getStatus(curr_status);
	if (!status_obj) {
		logger.error("checkSetTeam: invalid status", curr_status, status_obj);
		return retCode;
    }
    
    let setTeam = status_obj.setTeam;
    if (setTeam)
    {
        retCode = Code.OK;
    }

    return retCode;
};
//**************************************************外部消息基类处理入口 End************************************************************* */ 
