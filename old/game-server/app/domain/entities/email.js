/**
 * Created by scott on 2019/4/26. weather: sunny
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var TimeUtils = require('../../util/timeUtils');
var commonEnum = require('../../../../shared/enum');
var Calc = require('../../util/calc');
var Code = require('../../../../shared/code');
var Constant = require("../../../../shared/constant");

var EMail = function(player) {
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.emails = new Map(); // Uid => mailObj
    this.index = 0;
    this.seq = 0;
    this.isHaveNewMail = false;
};

util.inherits(EMail, EventEmitter);

module.exports = EMail;

EMail.prototype.initByDB = function(doc) {
    this.uid = doc.uid;
    this.emails = utils.toMap(doc.emails) || new Map();
    this.checkMail(); //检查邮件
};

EMail.prototype.initByConfig = function() {
};

EMail.prototype.toJSONforDB = function() {
    var mail = {
        uid: this.uid,
        emails: utils.toArray(this.emails)
    };
    return mail;
};

EMail.prototype.toJSONforClient = function() {
    return this.makeClientList();
};

//添加一个邮件
EMail.prototype.addMail = function(mailId, mailType, mail_info) {
    this.delOldestMail(); //删除最老的邮件
    var oneMail = this.newMail(mailId, mailType, mail_info);
    var Uid = oneMail.Uid;
    this.setMail(Uid, oneMail);
    this.isHaveNewMail = true;
    return this.getMail(Uid);
};

//可编辑邮件(外部调用)
EMail.prototype.addEditMail = function(senderUid, delType, title, content, spliceAttachList) {
    this.delOldestMail(); //删除最老的邮件
    var oneMail = this.newEditMail(senderUid, delType, title, content, spliceAttachList);
    var Uid = oneMail.Uid;
    this.setMail(Uid, oneMail);
    this.isHaveNewMail = true;
    return this.getMail(Uid);
};

EMail.prototype.setMail = function(Uid, oneMail) {
    this.emails.set(Uid, oneMail);
};

//获取一封邮件
EMail.prototype.getMail = function(Uid) {
    return this.emails.get(Uid);
};

EMail.prototype.delMail = function(Uid) {
    return this.emails.delete(Uid);
};

EMail.prototype.IsHaveNewMail = function() {
    return this.isHaveNewMail;
};

//mailInfo数据结构
/*
var mailInfo = {
    senderUid: SenderUid,
    attachList: [{itemType, resId, num, param1}],
    param1: param1,
    param2: param2,
    param3: param3,
    param4: param4,
    specialAttachInfo: specialAttachInfo,
}
*/
EMail.prototype.sendMailReward = function(senderUid, mailId, mailType, attachList, specialAttachInfo, param1, param2, param3, param4) {
    let spliceAttachListClone;
    //logger.info("mailId, param1, param2, param3, param4", mailId, param1, param2, param3, param4 );
    
    //切分邮件
    if (attachList.length > 0) {
        var spliceAttachList = [];
        var spliceIndex = 0;

        for (let idx in attachList) {
            let attachobj = attachList[idx];
            if (!attachobj)  continue;

            spliceAttachList[spliceIndex] = {};
            spliceAttachList[spliceIndex].ItemType = attachobj.ItemType;
            spliceAttachList[spliceIndex].ResId = attachobj.ResId;
            spliceAttachList[spliceIndex].Num = attachobj.Num;
            if (!attachobj.Param1 && attachobj.Param1 !== 0)
            {
                attachobj.Param1 = 0;
            }
            spliceAttachList[spliceIndex].Param1 = attachobj.Param1;

            if (attachobj.ItemType === undefined || attachobj.ResId=== undefined || attachobj.Num === undefined) {
                    logger.error("sendMailReward: data error senderUid, mailId, ItemType, ResId, Num, Param1 ***********************", 
                    senderUid, mailId, mailType, attachobj.ItemType, attachobj.ResId, attachobj.Num, attachobj.Param1);
                    continue;
            }
            
            spliceIndex ++;
            
            //超过数量了就要重新发送一封
            if (spliceIndex >= Constant.MAIL.MAX_SPLICE_ATTACH_ITEM_COUNT) {
                spliceAttachListClone = utils.cloneArray(spliceAttachList);
                var mail_info = this.makeMailInfo(senderUid, spliceAttachListClone, specialAttachInfo, param1, param2, param3, param4);
                this.addMail(mailId, mailType, mail_info);
                logger.info("sendMailReward spliet mail: senderUid, mailId, spliceAttachListClone ***********************", spliceAttachListClone);
                //重新加载
                spliceAttachList = [];
                spliceIndex = 0;
            }
        }

        if (spliceAttachList.length > 0)
        {
            spliceAttachListClone = utils.cloneArray(spliceAttachList);
            var mail_info = this.makeMailInfo(senderUid, spliceAttachListClone, specialAttachInfo, param1, param2, param3, param4);
            this.addMail(mailId, mailType, mail_info);
        }
        //logger.info("sendMailReward senderUid, mailId ", spliceAttachListClone, mail_info);
    }else
    {
        var mail_info = this.makeMailInfo(senderUid, attachList, specialAttachInfo, param1, param2, param3, param4);
        this.addMail(mailId, mailType, mail_info);
        //logger.info("sendMailReward senderUid, mailId attachList", mail_info);
    }
    this.player.updateRedDotHintState(commonEnum.REDDOT_HINT.EMAIL);
    return Code.OK;
};

//外部调用
EMail.prototype.sendEditMailReward = function(senderUid, delType, title, content, attachList) 
{
    var spliceAttachList = [];
    //切分邮件
    if (attachList.length > 0) 
    {
      
        var spliceIndex = 0;

        for (let idx in attachList) {
            let attachobj = attachList[idx];
            if (!attachobj)  continue;

            if (attachobj.ItemType === undefined || attachobj.ResId=== undefined || attachobj.Num === undefined) {
                    logger.error("sendMailReward: data error senderUid, mailId, ItemType, ResId, Num, Param1 ***********************", 
                    senderUid, mailId, mailType, attachobj.ItemType, attachobj.ResId, attachobj.Num, attachobj.Param1);
                    continue;
            }

            spliceAttachList[spliceIndex] = {};
            spliceAttachList[spliceIndex].ItemType = attachobj.ItemType;
            spliceAttachList[spliceIndex].ResId = attachobj.ResId;
            spliceAttachList[spliceIndex].Num = attachobj.Num;

            if (!attachobj.Param1 && attachobj.Param1 !== 0)
            {
                attachobj.Param1 = 0;
            }
            spliceAttachList[spliceIndex].Param1 = attachobj.Param1;
            spliceIndex ++;
            
            //超过数量了就要重新发送一封
            if (spliceIndex >= Constant.MAIL.MAX_SPLICE_ATTACH_ITEM_COUNT) {
                this.addEditMail(senderUid, delType, title, content, spliceAttachList);
                //重新加载
                spliceAttachList = [];
                spliceIndex = 0;
            }
        }

        if (spliceAttachList.length > 0)
        {
            this.addEditMail(senderUid, delType, title, content, spliceAttachList);
        }
    }else
    {
        this.addEditMail(senderUid, delType, title, content, spliceAttachList);
    }
    this.player.updateRedDotHintState(commonEnum.REDDOT_HINT.EMAIL);
    return Code.OK;
};

EMail.prototype.makeMailInfo = function(senderUid, attachList, specialAttachInfo, param1, param2, param3, param4) {
    var _senderUid = senderUid;
    if (!_senderUid || _senderUid === undefined) {
        _senderUid = "Sys";
    }

    var _attachList = attachList
    if (!_attachList) {
        _attachList = [];
    }

    if (!param1) {
        param1 = "";
    }

    if (!param2) {
        param2 = "";
    }

    if (!param3) {
        param3 = "";
    }

    if (!param4) {
        param4 = "";
    }

    var mail_info = {
        senderUid: _senderUid,
        attachList: _attachList,
        param1: param1,
        param2: param2,
        param3: param3,
        param4: param4,
        specialAttachInfo: specialAttachInfo,
    };

    return mail_info;
};

EMail.prototype.newMail = function(mailId, mailType, mail_info) {
    var mail = {};
    var mailTextConfig = dataApi.allData.data["MailText"][mailId];
    if (!mailTextConfig) {
        logger.error("newMail: mailTextConfig not found!", mailId);
        return mail;
    }

    mail.Uid = utils.syncCreateUid();
    mail.Type = mailType;
    mail.DelType = mailTextConfig.Mailtype;

    var TextMsg = {};
    TextMsg.Title = mailTextConfig.Title;
    TextMsg.ContentTypeId = mailId;
    TextMsg.Content = this.getMailContent(mailId, mail_info);
    mail.TextMsg = TextMsg;

    var clientAttachList = [];
    let index= 0;
    for (let idx in mail_info.attachList ) {
        const data = mail_info.attachList[idx];
        //logger.info("newMail _________ data", data);
        clientAttachList[index] = {};
        clientAttachList[index].ItemType = data.ItemType;
        clientAttachList[index].ResId = data.ResId;
        clientAttachList[index].Num = data.Num;
        clientAttachList[index].Param1 = data.Param1;
        index++;
    }

    mail.AttachList = clientAttachList;
    mail.CreateTime = TimeUtils.now();
    mail.IsOpen = 0;
    mail.TakeTime = 0;
    mail.SenderUid = mail_info.senderUid;
    mail.SpecialAttachData =  mail_info.specialAttachInfo;
    return mail;
};

//外部调用（提供）
//DelType 邮件删除类型
//title   标题
EMail.prototype.newEditMail = function(senderUid, delType, title, content, attachList) {
    var mail = {};
    mail.Uid = utils.syncCreateUid();
    mail.Type = delType;
    mail.DelType = 0;

    var clientAttachList = [];
    let index= 0;
    for (let idx in attachList ) {
        const data = attachList[idx];
        clientAttachList[index] = {};
        clientAttachList[index].ItemType = data.ItemType;
        clientAttachList[index].ResId = data.ResId;
        clientAttachList[index].Num = data.Num;
        clientAttachList[index].Param1 = data.Param1;
        index++;
    }

    var TextMsg = {};
    TextMsg.Title = title;
    TextMsg.ContentTypeId = 0;
    TextMsg.Content = content;
    mail.TextMsg = TextMsg;
    mail.AttachList = clientAttachList;
    mail.CreateTime = TimeUtils.now();
    mail.IsOpen = 0;
    mail.TakeTime = 0;
    mail.SenderUid = senderUid;
    mail.SpecialAttachData = { roomUid: ""};
    logger.info("newEditMail: mail", mail, attachList);
    return mail;
};

//获取邮件总数
EMail.prototype.getMailCount = function() {
    var count = 0;
    for(let [id, v] of this.emails)
    {
        if (!id) {
            continue;
        }
        count++;
    }
    return count;
};

function __mail_compare_func(obj1, obj2) {
	let uid1 = obj1.uid;
	let isOpen1 = obj1.isOpen;
	let createTime1 = obj1.createTime;

	let uid2 = obj2.uid;
	let isOpen2 = obj2.isOpen;
	let createTime2 = obj2.createTime;

    //logger.info("__mail_compare_func", isOpen1, isOpen2, createTime1, createTime2);
  
    //升序
	if (isOpen1 !== isOpen2 ) { 
        if (isOpen1 > isOpen2) {
            return 1;
        }else if (isOpen1 < isOpen2) {
            return -1;
        }
        else {
            return 0;
        }
    }

    //降序
	if (createTime1 !== createTime2) {     
        if (createTime1 < createTime2) {
            return 1;
        }else if (createTime1 > createTime2) {
            return -1;
        }
        else {
            return 0;
        }
	}

	return uid1 - uid2; //上面都相等的话，那就用assic排序
};

//删除最老邮件，不管是否打开，是否过期,就是要删除
EMail.prototype.delOldestMail = function() {
    this.checkMail();  //删除过期和超过多余邮件
    let count = this.getMailCount();
    let maxMailCount = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.maxMailCount].Param;
    let mailObjList = [];

    if (count < maxMailCount) {
        //logger.info("delOldestMail: count", count, maxMailCount);
        return false;
    }

    let oldestMailUid = "";
    for(let [id, v] of this.emails)
    {
        if (!id) {
            continue;
        }
        
        var mailObj = {
            uid: id,
            isOpen: v.IsOpen,
            createTime: v.CreateTime,
            type: v.DelType,
        };

        mailObjList.push(mailObj);
    }

    mailObjList.sort(__mail_compare_func);

    for (let idx in mailObjList) {
        const mailObj = mailObjList[idx];
        if (!mailObj) {
            continue;
        }

        if ( "" === mailObj.uid) {
            continue;
        }

        let uid = mailObj.uid;
        oldestMailUid = uid;
    }

    if ( "" !== oldestMailUid) {
        this.delMail(oldestMailUid); //直接删除
        //logger.info("delOldestMail_____________________________oldestMailUid, type", oldestMailUid, mailObj.type);
    }

    return true;
};

//邮件超过允许最大数量
EMail.prototype.checkReachMaxCountMail= function() {
    let count  = this.getMailCount();
    let maxMailCount = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.maxMailCount].Param;
    let mailObjList = [];

    if (count <= maxMailCount) {
        //logger.info("checkReachMaxCountMail: count", count, maxMailCount);
        return;
    }

    //logger.info("checkReachMaxCountMail maxMailCount, count", maxMailCount, count);
    for(let [id, v] of this.emails)
    {
        if (!id) {
            continue;
        }
        
        var mailObj = {
            uid: id,
            isOpen: v.IsOpen,
            createTime: v.CreateTime,
            type: v.DelType,
        };

        mailObjList.push(mailObj);
    }

    //logger.info("checkReachMaxCountMail mailObjList", mailObjList);
    mailObjList.sort(__mail_compare_func);
    //logger.info("checkReachMaxCountMail mailObjList", mailObjList);

    for (let idx in mailObjList) {
        const mailObj = mailObjList[idx];
        if (!mailObj) {
            continue;
        }

        if ( "" === mailObj.uid) {
            continue;
        }

        let uid = mailObj.uid;
        //把后面的数据邮件删除掉
        if (idx >= maxMailCount) {
            this.delMail(uid);
        }
    }
};

EMail.prototype.delAllMail = function()
{
    for(let [id, v] of this.emails)
    {
        if (!id) continue;
        this.delMail(id); //直接删除
    }
};

EMail.prototype.checkExpriedMail = function() {
    let maxExprideSecond = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.maxExprideMinute].Param * 60 * 1000;
    let nowTime = TimeUtils.now();
    let minTime = nowTime - maxExprideSecond;

    //logger.info("checkExpriedMail ", maxExprideSecond, nowTime, minTime);
    for(let [id, v] of this.emails)
    {
        if (!id) {
            continue;
        }
        
        if  (v.CreateTime <= minTime) { //超过最低时间
            this.delMail(id); //直接删除
            //logger.info("checkExpriedMail ___", id, v.CreateTime, nowTime);
        }
    }
};

EMail.prototype.checkMail = function() {
    //先删除过期邮件
    this.checkExpriedMail();
    //然后在检测是否超过限制(剩下的邮件都是未过期的)
    this.checkReachMaxCountMail();  
};

//背包满了转存邮件(已有邮件)
EMail.prototype.transeferBagIsFullMail = function(uid, mailId) {
    var retCode = false;
    var mail = this.getMail(uid);
    if (!mail) {
        logger.error("takeMail: not mail", uid);
        return retCode;
    }

    var attachListClone = utils.cloneArray(mail.AttachList);
    var mail_info = {
        senderUid: "SysTranfer_" + uid,
        attachList: attachListClone,
        param1: "",
        param2: "",
        param3: "",
        param4: "",
        specialAttachInfo: {roomUid: ""},
    };

    var mailType = commonEnum.MailType.SYSMAIL;
    logger.info("transeferSystemMail ________", mailType);
    this.addMail(mailId, mailType, mail_info);
    return true;
};

EMail.prototype.BagIsFullMail = function(type, resId, num, param1) {
    var attachList = [];
    let index=0;
    attachList[index] = {};
    attachList[index].ItemType = type;
    attachList[index].ResId = resId;
    attachList[index].Num = num;
    attachList[index].Param1 = param1;

    var mail_info = {
        senderUid: "System",
        attachList: attachList,
        param1: "",
        param2: "",
        param3: "",
        param4: "",
        specialAttachInfo: {roomUid: ""},
    };

    var mailType = commonEnum.MailType.SYSMAIL;
    var mailId = commonEnum.MAIL_TRANSLATE_CONTENT.BAG_IS_FULL;
    logger.info("BagIsFullMail ________", mailType);
    this.addMail(mailId, mailType, mail_info);
    return true;
};

EMail.prototype.takeMail = function(uid, itemUidList, heroUidList)
{
    var retCode = Code.FAIL;
    var mail = this.getMail(uid);
    if (!mail) {
        logger.error("takeMail: not mail", uid);
        return retCode;
    }

    if (commonEnum.MAIL_TAKE_STATE.ALREADY_OPEN === mail.IsOpen) { //已领取
        logger.error("takeMail: this mail already open, not repeated open", uid);
        return Code.MAIL.MAIL_ALREADY_OPEN;
    }

    var attachList = mail.AttachList;
    //检查背包是否能够装得下
    var canAddInBag = this.player.bag.checkItemListIsContain(attachList);
    if (!canAddInBag) {
        logger.error("bag is full---------------------", uid, attachList);
        return Code.MAIL.BAG_IS_FULL;
    }

    //logger.info("attachList: ", mail, attachList.length);
    if (commonEnum.MAIL_TAKE_STATE.NOT_OPEN === mail.IsOpen) { //未领取
        //取附件
        for (let i in attachList) {
            const data = attachList[i];
            let ItemType = data.ItemType;
            let ResId = data.ResId;
            let Num = data.Num;
            //logger.info("ItemType, ResId, Num", ItemType, ResId, Num);
            if (Num === 0) continue;
            switch (ItemType) {
                case commonEnum.MAIL_ITEM_TYPE.ITEM:
                    let obj = {
                        ResId: ResId,
                        Num: Num,
                    };
                    if(ResId === 18)//信仰经验直接加
                    {
                       this.player.mailAddBeliefLiveness(this.player.playerId, Num);
                    }
                    else
                    {
                        this.player.bag.addItem(ResId, Num);
                    }
                    itemUidList.push(obj);
                    //logger.info("MAIL_ITEM_TYPE attachList: ", mail, attachList.length, itemUidList.length);
                    break;

                case commonEnum.MAIL_ITEM_TYPE.HERO:
                    for (let index = 0; index < Num; index++) 
                    {
                        //1.检查球员是否存在
                        let exist = this.player.checkHeroResId(ResId);
                        if (exist) //转化为物品
                        {
                            let heroConfig = dataApi.allData.data["Footballer"][ResId];
                            let heroItemId = heroConfig.ItemID;
                            if (!heroItemId)
                            {
                                continue;
                            }
                            var has = this.player.bag.hasCanAddItemInBag(heroItemId, 1)
                            if (!has) {
                                //发送背包满了邮件

                                let itemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                                this.BagIsFullMail(itemType, heroItemId, 1, 0);
                                continue;
                            }

                            this.player.bag.addItem(heroItemId, 1);
                            let obj = {
                                ResId: heroItemId,
                                Num: 1,
                            };
                            itemUidList.push(obj);
                        }else
                        {
                            let result = this.player.addHero(ResId);
                            if (result.code === Code.OK )
                            {
                                    let hero = result.hero;
                                    let heroUid = hero.Uid;
                                heroUidList.push(heroUid);
                            }
                        }
                    }
                    break;
                    case commonEnum.MAIL_ITEM_TYPE.CURRENCY:
                            switch(ResId){
                                case commonEnum.CURRENCY_TYPE.CASH:
                                    break;
                                case commonEnum.CURRENCY_TYPE.GOLD:
                                    break;
                                case commonEnum.CURRENCY_TYPE.ENERGY:
                                    break;
                                case commonEnum.CURRENCY_TYPE.TRAIN:
                                    
                                    break;
                                case commonEnum.CURRENCY_TYPE.FAME:
                                    this.player.addExp(Num);
                                break;	
                            }
                    break;
            }
        }

        //按照类型处理邮件
        switch (mail.DelType) {
            case commonEnum.MailExpiredDelType.DIRECT:  //只领取，不删除
                mail.IsOpen = commonEnum.MAIL_TAKE_STATE.ALREADY_OPEN;
                mail.TakeTime = TimeUtils.now();
                logger.info("takeMail open mail", uid);
                retCode= Code.OK;
                break;
            case commonEnum.MailExpiredDelType.TAKE_ATTACH: //领取后删除
                this.delMail();
                logger.info("takeMail open and delete mail", uid);
                retCode= Code.OK;
                break;

            case commonEnum.MailExpiredDelType.PARAM1:  //未知 直接删除
                this.delMail();
                retCode= Code.OK;
                break;

            case commonEnum.MailExpiredDelType.PARAM2: //未知 直接删除
                this.delMail();
                retCode= Code.OK;
                break;
        }
    }

    return retCode;
};

//替换参数0
EMail.prototype._translateLeagueMailReplaceArgc0 = function(mail_info, result1, result2)  {
    if (!result1) {
        logger.error("_translateLeagueMailReplaceArgc0: result1 not exists", result1);
        return;
    }
    return result1;
};

EMail.prototype._translateLeagueMailReplaceArgc1 = function(mail_info, result1, result2)  {
    if (!result1) {
        logger.error("_translateLeagueMailReplaceArgc1: result1 not exists", result1);
        return;
    }
    var str = "";
    str = result1.replace("#0#", mail_info.param1);
    return str;
};

EMail.prototype._translateLeagueMailReplaceArgc2 = function(mail_info, result1, result2)  {
    if (!result1) {
        logger.error("_translateLeagueMailReplaceArgc2: result1 not exists", result1);
        return;
    }
    var str = "";
    str = result1.replace("#0#", mail_info.param1);
    str = str.replace("#0#", mail_info.param2);
    return str;
};

EMail.prototype._translateLeagueMailReplaceArgc3 = function(mail_info, result1, result2)  {
    if (!result1) {
        logger.error("_translateLeagueMailReplaceArgc3: result1 not exists", result1);
        return;
    }
    var str = "";
    str = result1.replace("#0#", mail_info.param1);
    str = str.replace("#0#", mail_info.param2);
    str = str.replace("#0#", mail_info.param3);
    return str;
};

EMail.prototype._translateLeagueMailReplaceArgc4 = function(mail_info, result1, result2)  {
    if (!result1) {
        logger.error("_translateLeagueMailReplaceArgc4: result1 not exists", result1);
        return;
    }
    var str = "";
    str = result1.replace("#0#", mail_info.param1);
    str = str.replace("#0#", mail_info.param2);
    str = str.replace("#0#", mail_info.param3);
    str = str.replace("#0#", mail_info.param4);
    return str;
};

EMail.prototype.getMailContent = function(mailId, mail_info) {
    let content = "";
    if (!mailId || mailId <= 0) {
        return content;
    }

    var mailTextConfig = dataApi.allData.data["MailText"][mailId];
    if (!mailTextConfig) {
        logger.error("getMailContent: mailTextConfig not found!", mailId);
        return "";
    }

    var result1 =  mailTextConfig.Result1;
    var result2 =  mailTextConfig.Result2;
    switch (mailId) {
        case commonEnum.MAIL_TRANSLATE_CONTENT.LEAGUE_COPY_REWARD:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2); //联赛通关奖励
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.LEAGUE_ALL_PASSED_REWARD:
            content = this._translateLeagueMailReplaceArgc1(mail_info, result1, result2); //联赛全星通关奖励
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.BAG_IS_FULL:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.CommunityReward:
            content = this._translateLeagueMailReplaceArgc3(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.NormalReward:
            content = this._translateLeagueMailReplaceArgc3(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_REWARD:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;
            
        case commonEnum.MAIL_TRANSLATE_CONTENT.CommunityNotifyWin:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.CommunityNotifyLost:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.CommunityPromotion:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.NormalNotifyWin:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.NormalNotifyLost:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.NormalPromotion:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.KnockOutNotifyWin:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;
    
        case commonEnum.MAIL_TRANSLATE_CONTENT.KnockOutNotifyLost:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;
            
        case commonEnum.MAIL_TRANSLATE_CONTENT.ProfessionPromotion:
            content = this._translateLeagueMailReplaceArgc1(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.ProfessionFailed:
            content = this._translateLeagueMailReplaceArgc1(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.ProfessionNotifyWin:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.ProfessionNotifyLost:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.Result_Is_Null:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.RESULT_DRAW:
            content = this._translateLeagueMailReplaceArgc2(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.ENROLL_SUCCESS:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.ENROLL_FAILED:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
            
        case commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_ENROLL:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.EXCHANGE_REWARD:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.TASK_REWARD:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.STORE_REWARD:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.SEASON_STORE:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.MONTH_CARD:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.LIMIT_STORE:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.ACT_AWARD_MAIL:
            content = this._translateLeagueMailReplaceArgc1(mail_info, result1, result2);
            break;
            
        case commonEnum.MAIL_TRANSLATE_CONTENT.WORLD_CUP:
            content = this._translateLeagueMailReplaceArgc1(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_ENROLL_START:
            content = this._translateLeagueMailReplaceArgc1(mail_info, result1, result2);
            break;

        case commonEnum.MAIL_TRANSLATE_CONTENT.PROFESSION_DIRECT_START:
            content = this._translateLeagueMailReplaceArgc1(mail_info, result1, result2);//test
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.National_Day:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.RELAY_TASKS:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.RELAY_SCHEDULE:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.BEST_FOOTBALL:
            content = this._translateLeagueMailReplaceArgc1(mail_info, result1, result2);
            break;
            
        case commonEnum.MAIL_TRANSLATE_CONTENT.BEST_FOOTBALL_REWARD:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.ENERGY_FEEDBACK:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.REGRESSION:
            content = this._translateLeagueMailReplaceArgc3(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.TURNTABLE:
            content = this._translateLeagueMailReplaceArgc3(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.MIDDLECUP:
            content = this._translateLeagueMailReplaceArgc3(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.GULFCUP:
            content = this._translateLeagueMailReplaceArgc4(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.MLS:
            content = this._translateLeagueMailReplaceArgc4(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.GULFCUP_FAIL:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.MLS_FAIL:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        case commonEnum.MAIL_TRANSLATE_CONTENT.MIDDLECUP_FAIL:
            content = this._translateLeagueMailReplaceArgc0(mail_info, result1, result2);
            break;
        default:
            break;
    }
    return content;
};

EMail.prototype.checkMailIsOpen = function(uid) {
    var isOpen = true;
    var mail = this.getMail(uid);
    if (!mail) {
        return isOpen;
    }

    if (commonEnum.MAIL_TAKE_STATE.NOT_OPEN === mail.IsOpen) {
        isOpen = false;
    }

    return isOpen;
};

EMail.prototype._getTakeMail = function(uid) {
    var takeMail = {Uid: "", AttachList: []};
    var mail =  this.getMail(uid);
    if (!mail) return takeMail;

    takeMail = {
        Uid: uid,
        AttachList: utils.cloneArray(mail.AttachList)
    };

    return takeMail;
};

EMail.prototype._getAllMailTakeMail = function() {
    var takeMailList = [];
    let idx = 0;
    for(let [id, v] of this.emails)
    {
        if (!id) {
            continue;
        }
        if (this.checkMailIsOpen(id)) continue;
        takeMailList[idx] = this._getTakeMail(id);
        idx++;
    }

    return takeMailList;
};

EMail.prototype._getMailTakeMailByUidList = function(mailUidList) {
    var takeMailList = [];
    if (!mailUidList || mailUidList.length === 0) {
        return takeMailList;
    }
    logger.info("_getMailTakeMailByUidList list mailUidList", mailUidList);

    for (let idx in mailUidList) {
        let uid = mailUidList[idx];
        logger.info("_getMailTakeMailByUidList uid", uid);
        if ( "" === uid) continue;
        var mail = this.getMail(uid);
        if (!mail) continue;
        if (this.checkMailIsOpen(uid)) continue;

        takeMailList[idx] = this._getTakeMail(uid);
        idx++;

        logger.info("_getMailTakeMailByUidList list uid", uid);
    }

    logger.info("_getMailTakeMailByUidList takeMailList", takeMailList);

    return takeMailList;
};

EMail.prototype._getAttachListCollectionTakeMail = function() {
    var attachListCollection = [];
    var itemMap = new Map();
    var heroMap = new Map();
    for(let [id, v] of this.emails)
    {
        if (!id) continue;

        var mail = this.getMail(id);
        if (!mail) continue;
        if (this.checkMailIsOpen(id)) continue;

        for (let i in mail.AttachList) {
            const data = mail.AttachList[i];
            switch (data.ItemType) {
                case commonEnum.MAIL_ITEM_TYPE.ITEM:
                    if (!itemMap.has(data.ResId)) {
                        itemMap.set(data.ResId, data.Num);
                    }else
                    {
                        itemMap.set(data.ResId, itemMap.get(data.ResId)+ data.Num);
                    }
                    break;
                case commonEnum.MAIL_ITEM_TYPE.HERO:
                    if (!heroMap.has(data.ResId)) {
                        heroMap.set(data.ResId, data.Num);
                    }else
                    {
                        heroMap.set(data.ResId, heroMap.get(data.ResId)+ data.Num);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    let idx = 0;
    for (let [key, value] of itemMap) {
        var attachObj = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, 
            ResId: key,
            Num: value,
            Param1: 0,
        };
        attachListCollection[idx] = attachObj;
        idx++;
    }

    for (let [key, value] of heroMap) {
        var attachObj = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.HERO, 
            ResId: key,
            Num: value,
            Param1: 0,
        };
        attachListCollection[idx] = attachObj;
        idx++;
    }

    return attachListCollection;
};

EMail.prototype._getAttachListCollectionByUidList = function(mailUidList) {
    var attachListCollection = [];
    var itemMap = new Map();
    var heroMap = new Map();

    if (!mailUidList || mailUidList.length === 0) {
        return attachListCollection;
    }

    for (let idx in mailUidList) {
        let uid = mailUidList[idx];
        if ( "" === uid) continue;
        var mail = this.getMail(uid);
        if (!mail) continue;
        if (this.checkMailIsOpen(uid)) continue;
        for (let i in mail.AttachList) {
            const data = mail.AttachList[i];
            switch (data.ItemType) {
                case commonEnum.MAIL_ITEM_TYPE.ITEM:
                    if (!itemMap.has(data.ResId)) {
                        itemMap.set(data.ResId, data.Num);
                    }else
                    {
                        itemMap.set(data.ResId, itemMap.get(data.ResId)+ data.Num);
                    }
                    break;
                case commonEnum.MAIL_ITEM_TYPE.HERO:
                    if (!heroMap.has(data.ResId)) {
                        heroMap.set(data.ResId, data.Num);
                    }else
                    {
                        heroMap.set(data.ResId, heroMap.get(data.ResId)+ data.Num);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    let idx = 0;
    for (let [key, value] of itemMap) {
        var attachObj = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM, 
            ResId: key,
            Num: value,
            Param1: 0,
        };
        attachListCollection[idx] = attachObj;
        idx++;
    }

    for (let [key, value] of heroMap) {
        var attachObj = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.HERO, 
            ResId: key,
            Num: value,
            Param1: 0,
        };
        attachListCollection[idx] = attachObj;
        idx++;
    }

    return attachListCollection;
};

//******************************************消息结构体构造 End******************************************************* */
//领取邮件
EMail.prototype.takeMailAll = function(mailUidList) 
{
    let idx = 0;
    let attachList = [];
    var takeMailList = [];
    var heroUidList = []; //添加需要通知新球员的列表
    var itemUidList = [];//添加需要更新的物品列表
    
    if (!mailUidList || mailUidList.length <= 0) {
        //修复死循环问题
        //领取全部邮件 (可领取邮件列表)
        let canGetMailList = [];
        let checkAttachList = [];
        for(let [id, v] of this.emails) {
            if (!id) continue;
            if (this.checkMailIsOpen(id)) continue;
            let mail = this.getMail(id);
            checkAttachList.concat(mail.AttachList);
            let canAddInBag = this.player.bag.checkItemListIsContain(checkAttachList);
            if (!canAddInBag) {
                logger.error("takeMailAll: bag is full. ", checkAttachList);
                break;
            }else {
                canGetMailList.push(id);
            }
        }
        //for(let [id, v] of this.emails)
        for(let i=0,lens=canGetMailList.length; i<lens; i++)
        {
            let id = canGetMailList[i];
            if (!id) continue;
            if (this.checkMailIsOpen(id)) continue;
            var mail = this.getMail(id);
            if (mail) attachList = utils.cloneArray(mail.AttachList);
            logger.debug("takeMailAll id, itemUidList", id, itemUidList.length);
            var ret = this.takeMail(id, itemUidList, heroUidList);
            takeMailList[idx] = {};
            takeMailList[idx].code = ret;
            takeMailList[idx].Uid = id;
            takeMailList[idx].AttachList = attachList;
            idx++;
        }

    }else
    {
        for (let index in mailUidList)
        {
            let id = mailUidList[index];
            if ( "" === id) continue;
            if (this.checkMailIsOpen(id)) continue;
            var mail =  this.getMail(id);
            if (mail) attachList = utils.cloneArray(mail.AttachList);
            var ret = this.takeMail(id, itemUidList, heroUidList);
            takeMailList[idx] = {};
            takeMailList[idx].code = ret;
            takeMailList[idx].Uid = id;
            takeMailList[idx].AttachList = attachList;
            idx++;
        }
    }

    //logger.info("takeMail _____________________takeMailList", takeMailList, itemUidList, itemUidList.length,  heroUidList, heroUidList.length);
    return {code: Code.OK, takeMailList: takeMailList, itemUidList: itemUidList, heroUidList: heroUidList};
};

//删除邮件
EMail.prototype.deleteMail = function(mailUidList) {
    //logger.info("deleteMail ______________________mailUidList", mailUidList);
    if (!mailUidList || mailUidList.length === 0) {
        //领取全部邮件
        for(let [id, v] of this.emails)
        {
            if (!id) {
                continue;
            }
            logger.info("deleteMail all", id);
            this.delMail(id);
        }
    }else
    {
        for (let idx in mailUidList) {
            let uid = mailUidList[idx];
            if ( "" === uid) continue;

            var email = this.getMail(uid);
            if (!email) continue;

            logger.info("deleteMail list uid", uid);
            this.delMail(uid);
        }
    }
    
    return Code.OK;
};

EMail.prototype.makeClientEmail = function(id) {
	var clientEntry = {};
	var email = this.getMail(id);
	if (!email) {
		logger.error("makeClientEmail fail ! not email!", id);
		return clientEntry;
	}

    clientEntry.Uid = email.Uid;
    clientEntry.Type = email.Type;
    clientEntry.DelType = email.DelType;
    clientEntry.TextMsg = email.TextMsg;

    var clientAttachList = [];
    var AttachList = email.AttachList;
    let index = 0;
    for (let i in AttachList) {
        const data = AttachList[i];
        clientAttachList[index] = {};
        clientAttachList[index].ItemType = data.ItemType;
        clientAttachList[index].ResId = data.ResId;
        clientAttachList[index].Num = data.Num;
        if (!data.Param1 && data.Param1 !== 0 )
        {
            data.Param1 = 0;
        }
        clientAttachList[index].Param1 = data.Param1;
        //logger.info("makeClientEmail data.ItemType, data.ResId, data.Num, data.Param1", data.ItemType, data.ResId, data.Num, data.Param1);
        index++;
    }

    clientEntry.AttachList = clientAttachList;
    // logger.info("makeClientEmail", id);
    // logger.info("makeClientEmail", clientEntry.AttachList.length);
	clientEntry.CreateTime = email.CreateTime;
	clientEntry.IsOpen = email.IsOpen;
	clientEntry.TakeTime = email.TakeTime;
    clientEntry.SenderUid = email.SenderUid;
    clientEntry.SpecialAttachData = email.SpecialAttachData;

    if (!clientEntry.SpecialAttachData)
    {   email.SpecialAttachData = { roomUid : ""};
        clientEntry.SpecialAttachData = { roomUid : ""};
    }

	return clientEntry;
};

EMail.prototype.makeClientList = function() {
    var clientList = [];
    var mailObjList= [];
    //修复黑卡合约问题
    this.fixMailData();
    for(let [id, v] of this.emails)
    {
        if (!id || "" === id) {
            continue;
        }
        var mailObj = {
            uid: id,
            isOpen: v.IsOpen,
            createTime: v.CreateTime,
            type: v.DelType,
        };
        mailObjList.push(mailObj);
    }

    mailObjList.sort(__mail_compare_func);

    let index = 0;
    for (let idx in mailObjList) {
        const uid = mailObjList[idx].uid;
        clientList[index] = this.makeClientEmail(uid);
        index++;
    }

    //logger.info("makeClientEmailList", clientList);
    return clientList;
};

//修复日常礼包 黑卡合约问题
EMail.prototype.fixMailData = function() {
    let Config = dataApi.allData.data["Item"];
    for(let [id, v] of this.emails) {
        let email = this.getMail(id);
        if(!email) {
            continue;
        }
        if(email.TextMsg.Title === "老虎机奖励")
        {
            email.TextMsg.Title = "幸运转转乐奖励"
        }
        if(email.TextMsg.Content.indexOf("《我是教练3.0》") !== -1)
        {
            email.TextMsg.Content = email.TextMsg.Content.replace(/《我是教练3.0》/, "500联赛");
            email.TextMsg.Content = email.TextMsg.Content.replace(/2019-11-1 15:0:0/, "2020-8-18 15:0:0");
        }
        let isDel = false;
        let len = email.AttachList.length;
        for(let i in email.AttachList) {
            if(email.AttachList[i].ResId === 90099 && email.AttachList[i].Num === null) {
                email.AttachList.splice(i, 1);
                isDel = true;
            }

            if(email.AttachList[i].ResId === 90090 && email.AttachList[i].Num === null) {
                email.AttachList.splice(i, 1);
                isDel = true;
            }
            //错误物品id删除该邮件
            if(typeof(Config[email.AttachList[i].ResId]) === "undefined")
            {
                this.delMail(id);
            }
        }

        //只有1个附件 直接把邮件删除
        if(isDel && len === 1) {
            this.emails.delete(id);
        }
    }
};


//**********************************************消息结构体构造 End*********************************************** */
