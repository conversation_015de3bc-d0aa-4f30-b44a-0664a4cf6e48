/**
 * Created by <PERSON> on 2020/7/1.
 */
let logger = require('pomelo-logger').getLogger(__filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let utils = require('../../util/utils');
let timeUtils = require("../../util/timeUtils");
let dataApi = require('../../util/dataApi');
let commonEnum = require('../../../../shared/enum');

let honorWall = function (player) {
    this.player = player;
    this.uid = player.playerId;

    this.title = "默默无闻";                    //称号
    this.integral = 0;                        //积分
    this.peakMatchData = {joinNum: 0, seasonMap: new Map()};                  //信仰颠峰战   //总参与次数、最高名次、8强次数、第3名次数、第2名次数、第1名次数
    this.chairmanMatchData = {joinNum: 0, seasonMap: new Map()};              //首席争夺战   //总参加次数、最高名次、获得职位次数、总监次数、总经理次数、副董事长次数、董事长次数
    this.beliefMatchData = {joinNum: 0, seasonMap: new Map()};                //信仰争夺战   //总参加次数、胜场、存活次数、守卫球场成功次数、占领敌方球场次数
    this.leagueData = {joinNum: 0, seasonMap: new Map()};                     //懂联赛      //总参加次数、最高名次（联赛等级加名次）、懂超冠军次数
    this.dqCupMatchData = {joinNum: 0, seasonMap: new Map()};                 //五大联赛杯   //总参加次数、最高名次、总冠军次数、xx杯冠军xx次
    this.allTaskList = this.initTaskList(); //任务列表
    this.finishTask = [];                   //已完成的任务
    // this.init();
};

util.inherits(honorWall, EventEmitter);
module.exports = honorWall;

honorWall.prototype.initByDB = function (doc) {
    // logger.error("11111111111111111111111", doc);
    this.title = doc.title || "默默无闻";
    this.integral = doc.integral || 0;
    if(!!doc.peakMatchData && !!doc.chairmanMatchData && !!doc.beliefMatchData && !!doc.leagueData && !!doc.dqCupMatchData)
    {
        this.peakMatchData.joinNum = doc.peakMatchData.joinNum || 0;
        this.peakMatchData.seasonMap = this.honorWallDataToMap(doc.peakMatchData.seasonData);
        this.chairmanMatchData.joinNum = doc.chairmanMatchData.joinNum || 0;
        this.chairmanMatchData.seasonMap = this.honorWallDataToMap(doc.chairmanMatchData.seasonData);
        this.beliefMatchData.joinNum = doc.beliefMatchData.joinNum || 0;
        this.beliefMatchData.seasonMap = this.honorWallDataToMap(doc.beliefMatchData.seasonData);
        this.leagueData.joinNum = doc.leagueData.joinNum || 0;
        this.leagueData.seasonMap = this.honorWallDataToMap(doc.leagueData.seasonData);
        this.dqCupMatchData.joinNum = doc.dqCupMatchData.joinNum || 0;
        this.dqCupMatchData.seasonMap = this.honorWallDataToMap(doc.dqCupMatchData.seasonData);
        this.allTaskList = doc.allTaskList || this.initTaskList(); //任务列表
        this.finishTask = doc.finishTask || [];
        // logger.error("!!!!!!!!!!!!!!初始化!!!!!!!!!!!!!!");
    }
    //检查是否有新增任务
    this.checkTasksAdd();
    //检查参赛次数是否错误
    this.checkJoinNumAndRepair();
};

honorWall.prototype.toJSONforDB = function () {
    let honorWall = {
        title: this.title,
        integral: this.integral,
        peakMatchData: {joinNum: this.peakMatchData.joinNum, seasonData: this.honorWallDataToarr(this.peakMatchData.seasonMap)},
        chairmanMatchData: {joinNum: this.chairmanMatchData.joinNum, seasonData: this.honorWallDataToarr(this.chairmanMatchData.seasonMap)},
        beliefMatchData: {joinNum: this.beliefMatchData.joinNum, seasonData: this.honorWallDataToarr(this.beliefMatchData.seasonMap)},
        leagueData: {joinNum: this.leagueData.joinNum, seasonData: this.honorWallDataToarr(this.leagueData.seasonMap)},
        dqCupMatchData: {joinNum: this.dqCupMatchData.joinNum, seasonData: this.honorWallDataToarr(this.dqCupMatchData.seasonMap)},
        allTaskList: this.allTaskList,
        finishTask: this.finishTask,
    };
    // logger.error("!!!!!!!!!!!!!!保存!!!!!!!!!!!!!!", this.uid, honorWall);
    return honorWall;
};

honorWall.prototype.init = function () {
    this.peakMatchData.joinNum = 0;
    this.peakMatchData.seasonMap = new Map();//seasonId => rank time
    this.chairmanMatchData.joinNum = 0;
    this.chairmanMatchData.seasonMap = new Map();//seasonId => rank pos(职位 董事长：1、副董事长：2、总经理：3、总教练：4) time
    this.beliefMatchData.joinNum = 0;
    this.beliefMatchData.seasonMap = new Map();//
    this.leagueData.joinNum = 0;
    this.leagueData.seasonMap = new Map();//
    this.dqCupMatchData.joinNum = 0;
    this.dqCupMatchData.seasonMap = new Map();//
};
//初始化任务列表
honorWall.prototype.initTaskList = function() {
    let config = dataApi.allData.data["HonorTask"];
    let taskList = [];
    let index = 0;
    for (let i in config) {
        taskList[index] = {};
        taskList[index].resId = config[i].Id;
        taskList[index].num = 0;
        taskList[index].status = commonEnum.ACT_BTN_STATUS.NOT_TAKE;       //枚举：ACT_BTN_STATUS
        index++;
    }
    return taskList;
};

honorWall.prototype.honorWallDataToMap = function (arr) {
    let map = new Map();
    if(!!arr)
    {
        for(let i in arr)
        {
            map.set(arr[i].seasonId, arr[i].data);
        }
    }
    return map;
};
honorWall.prototype.honorWallDataToarr = function (map) {
    let list = [];
    if(!!map)
    {
        for(let [k, v] of map)
        {
            list.push({seasonId: k, data: v});
        }
    }
    return list;
};

//更新荣誉数据参加次数
honorWall.prototype.updateHonorWallJoinNum = function (type) {
    switch (type) {
        case commonEnum.HONOR_WALL_TYPE.PEAK://信仰巅峰赛
            this.peakMatchData.joinNum += 1;
            break;
        case commonEnum.HONOR_WALL_TYPE.CHAIRMAN://首席争夺赛
            this.chairmanMatchData.joinNum += 1;
            break;
        case commonEnum.HONOR_WALL_TYPE.BELIEF://信仰争夺赛
            this.beliefMatchData.joinNum += 1;
            break;
        case commonEnum.HONOR_WALL_TYPE.LEAGUE://懂联赛
            this.leagueData.joinNum += 1;
            break;
        case commonEnum.HONOR_WALL_TYPE.DQCUP://五大联赛杯
            this.dqCupMatchData.joinNum += 1;
            break;
        default:
            return;
    }
    //参赛任务
    this.triggerTask(commonEnum.HONOR_TASK_TYPE.JOIN, type);
};
//更新巅峰赛名次
honorWall.prototype.updatePeakHonorRank = function (seasonId, rank) {
    let seasonMap = this.peakMatchData.seasonMap;
    if(!seasonMap.has(seasonId))
    {
        seasonMap.set(seasonId, {rank: rank, time: Date.now()});//排名
        //排名任务
        this.triggerTask(commonEnum.HONOR_TASK_TYPE.TOP, commonEnum.HONOR_WALL_TYPE.PEAK, rank);
    }
};
//更新首席争夺战数据
honorWall.prototype.updateChairmanHonorRank = function (seasonId, beliefId, rank, pos) {
    let seasonMap = this.chairmanMatchData.seasonMap;
    if(!seasonMap.has(seasonId))
    {
        seasonMap.set(seasonId, {beliefId: beliefId, rank: rank, pos: pos, time: Date.now()});//排名， 职位 1-4(董事长、副董事长、总监、总教练) 0 没有职位 获奖时间
        //排名任务
        this.triggerTask(commonEnum.HONOR_TASK_TYPE.TOP, commonEnum.HONOR_WALL_TYPE.CHAIRMAN, rank);
    }
};
//更新信仰争夺战数据
honorWall.prototype.updateBeliefHonorRank = function (seasonId, win, live, attack, defend) {
    let seasonMap = this.beliefMatchData.seasonMap;
    if(!seasonMap.has(seasonId))
    {
        seasonMap.set(seasonId, {win: win, live: live, attack: attack, defend: defend, time: Date.now()});//胜场 存活 占领成功 守卫成功
        //防守任务
        this.triggerTask(commonEnum.HONOR_TASK_TYPE.BELIEF_DEFEND, commonEnum.HONOR_WALL_TYPE.BELIEF, defend);
        //进攻任务
        this.triggerTask(commonEnum.HONOR_TASK_TYPE.BELIEF_ATTACK, commonEnum.HONOR_WALL_TYPE.BELIEF, attack);
    }
};
//更新联赛数据
honorWall.prototype.updateLeagueHonorRank = function (seasonId, typeId, rank) {
    let seasonMap = this.leagueData.seasonMap;
    if(!seasonMap.has(seasonId))
    {
        seasonMap.set(seasonId, {typeId: typeId, rank: rank});//联赛等级(枚举：LEAGUE_TYPE_ID) 排名
        //排名任务
        this.triggerTask(commonEnum.HONOR_TASK_TYPE.LEVEL_TOP, commonEnum.HONOR_WALL_TYPE.LEAGUE, rank, typeId);
    }
};
//更新五大联赛杯数据
honorWall.prototype.updateDQCupHonorRank = function (seasonId, type, rank) {
    let seasonMap = this.dqCupMatchData.seasonMap;
    if(!seasonMap.has(seasonId))
    {
        seasonMap.set(seasonId, {type: type, rank: rank, time: Date.now()});//联赛类型(枚举：DQCUP_TYPE) 排名
        //排名任务
        this.triggerTask(commonEnum.HONOR_TASK_TYPE.LEVEL_TOP, commonEnum.HONOR_WALL_TYPE.DQCUP, rank, type);
    }
};
//检查参赛次数并且修正
honorWall.prototype.checkJoinNumAndRepair = function()
{
    if(this.peakMatchData.joinNum < this.peakMatchData.seasonMap.size)
    {
        this.peakMatchData.joinNum = this.peakMatchData.seasonMap.size;
    }
    if(this.chairmanMatchData.joinNum < this.chairmanMatchData.seasonMap.size)
    {
        this.chairmanMatchData.joinNum = this.chairmanMatchData.seasonMap.size;
    }
    if(this.beliefMatchData.joinNum < this.beliefMatchData.seasonMap.size)
    {
        this.beliefMatchData.joinNum = this.beliefMatchData.seasonMap.size;
    }
    if(this.leagueData.joinNum < this.leagueData.seasonMap.size)
    {
        this.leagueData.joinNum = this.leagueData.seasonMap.size;
    }
    if(this.dqCupMatchData.joinNum < this.dqCupMatchData.seasonMap.size)
    {
        this.dqCupMatchData.joinNum = this.dqCupMatchData.seasonMap.size;
    }
}
//获取任务进度
honorWall.prototype.getTaskProgress = function (type) {
    let config = dataApi.allData.data["HonorTask"];
    let taskNum = 0;
    //任务总数
    for(let i in config)
    {
        if(config[i].Type === type)
            taskNum++;
    }
    let num = 0;
    //未完成任务数量
    for(let i in this.allTaskList)
    {
        let Id = this.allTaskList[i].resId;
        let status = this.allTaskList[i].status;
        if(config[Id].Type === type && status === commonEnum.ACT_BTN_STATUS.NOT_TAKE)
            num++;
    }
    let finishNum = taskNum - num;//总数减去未完成的数量
    return Math.round((finishNum/taskNum) * 100);
};
//获取巅峰赛信息
honorWall.prototype.getPeakHonorInfo = function () {
    //总参加次数 最高名次 8强次数 第三名次数 第二名次数 第一名次数
    let seasonMap = this.peakMatchData.seasonMap;
    let data = {
        joinNum: 0,
        ranking: 0,
        eight: 0,
        three: 0,
        two: 0,
        one: 0,
        seasonList: [],
        progress: 0,
    };
    data.joinNum =  this.peakMatchData.joinNum;
    let list = [];
    for(let [k,v] of seasonMap)
    {
        list.push({seasonId: k, rank: v.rank, time: v.time});
        if(data.ranking === 0 || data.ranking > v.rank)
        {
            data.ranking = v.rank;
        }
        if(v.rank <= 8)
        {
            data.eight += 1;
        }
        if(v.rank === 3)
        {
            data.three += 1;
        }
        if(v.rank === 2)
        {
            data.two += 1;
        }
        if(v.rank === 1)
        {
            data.one += 1;
        }
    }
    data.seasonList = list;
    //任务进度
    data.progress = this.getTaskProgress(commonEnum.HONOR_WALL_TYPE.PEAK);
    return data;
};
//获取首席争夺战信息
honorWall.prototype.getChairmanHonorInfo = function () {
    //总参加次数 最高名次 获取职位次数
    let seasonMap = this.chairmanMatchData.seasonMap;
    let data = {
        joinNum: 0,
        ranking: 0,
        seasonList: [],
        officeNum: 0,
        progress: 0,
    };
    data.joinNum =  this.chairmanMatchData.joinNum;
    let list = [];
    for(let [k,v] of seasonMap)
    {
        list.push({seasonId: k, rank: v.rank, pos: v.pos, time: v.time});
        //最高名次
        if(data.ranking === 0 || data.ranking > v.rank)
        {
            data.ranking = v.rank;
        }
        //任职次数
        if(v.pos !== 0)
        {
            data.officeNum += 1;
        }
    }
    data.seasonList = list;
    //任务进度
    data.progress = this.getTaskProgress(commonEnum.HONOR_WALL_TYPE.CHAIRMAN);
    return data;
};
//获取信仰争夺战信息
honorWall.prototype.getBeliefHonorInfo = function () {
    //总参加次数 总胜场 存活次数 守卫成功次数 占领成功次数
    let seasonMap = this.beliefMatchData.seasonMap;
    let data = {
        joinNum: 0,
        winNum: 0,
        liveNum: 0,
        defendNum: 0,
        attackNum: 0,
        seasonList: [],
        progress: 0,
    };
    data.joinNum =  this.beliefMatchData.joinNum;
    let list = [];
    for(let [k,v] of seasonMap)
    {
        data.winNum += v.win;       //胜场
        data.liveNum += v.live;     //存活
        data.defendNum += v.defend; //守卫
        data.attackNum += v.attack; //占领
        list.push({seasonId: k, win: v.win, live: v.live, attack: v.attack, defend: v.defend, time: v.time});
    }
    data.seasonList = list;
    //任务进度
    data.progress = this.getTaskProgress(commonEnum.HONOR_WALL_TYPE.BELIEF);
    return data;
};
//获取联赛信息
honorWall.prototype.getLeagueHonorInfo = function () {
    //总参加次数 最高名次 懂超冠军次数
    let seasonMap = this.leagueData.seasonMap;
    let data = {
        joinNum: 0,
        leagueRanking: {typeId: 8, rank: 0},
        superFirstNum: 0,
        seasonList: [],
        progress: 0,
    };
    data.joinNum =  this.leagueData.joinNum;
    let list = [];
    for(let [k,v] of seasonMap)
    {
        list.push({seasonId: k, typeId: v.typeId, rank: v.rank});
        //最高名次
        if(data.leagueRanking.typeId > v.typeId)
        {
            data.leagueRanking.typeId = v.typeId;
            data.leagueRanking.rank = v.rank;
        }
        if(data.leagueRanking.typeId === v.typeId && data.leagueRanking.rank > v.rank)
        {
            data.leagueRanking.typeId = v.typeId;
            data.leagueRanking.rank = v.rank;
        }
        //懂超冠军
        if(v.typeId === commonEnum.LEAGUE_TYPE_ID.SUPER && v.rank === 1)
        {
            data.superFirstNum += 1;
        }
    }
    data.seasonList = list;
    //任务进度
    data.progress = this.getTaskProgress(commonEnum.HONOR_WALL_TYPE.LEAGUE);
    return data;
};
//获取五大联赛杯信息
honorWall.prototype.getDQCupHonorInfo = function () {
    //总参加次数 最高名次 蝉联冠军次数
    let seasonMap = this.dqCupMatchData.seasonMap;
    let data = {
        joinNum: 0,
        ranking: 0,
        reelected: 0,
        seasonList: [],
        progress: 0,
    };
    data.joinNum =  this.dqCupMatchData.joinNum;
    let list = [];
    for(let [k,v] of seasonMap)
    {
        list.push({seasonId: k, type: v.type, rank: v.rank, time: v.time});
        //最高名次
        if(data.ranking === 0 || data.ranking > v.rank)
        {
            data.ranking = v.rank;
        }
        //蝉联次数
        if(seasonMap.has(k - 1))
        {
            let oldData = seasonMap.get(k - 1);
            if(oldData.rank === 1 && v.rank === 1)
            {
                data.reelected += 1;
            }
        }
    }
    data.seasonList = list;
    //任务进度
    data.progress = this.getTaskProgress(commonEnum.HONOR_WALL_TYPE.DQCUP);
    return data;
};
//获取荣耀墙任务
honorWall.prototype.getHonorTaskList = function () {
    let tasks = [];
    for(let i in this.allTaskList)
    {
        let task = this.allTaskList[i];
        tasks.push({resId: task.resId, Num: task.num, status: task.status});
    }
    return tasks;
};
/**
 * 检测称号更新
 */
honorWall.prototype.checkTitleUpdata = function () {
    let config = dataApi.allData.data["Title"];
    for(let i in config)
    {
        if(this.integral >= config[i].Min && this.integral <= config[i].Max)
        {
            this.title = config[i].Name;
            this.player.saveHonorWall();
            break;
        }
    }
};
//================================= 荣誉任务 =================================//
/**
 * 公共方法 触发任务  TargetType 目标类型  type 赛事类型（HONOR_WALL_TYPE）   arg2 名次   arg3 赛事级别
 */
honorWall.prototype.triggerTask = function (TargetType, type, arg1, arg2, arg3, arg4) {
    if (!TargetType) {
        logger.error("triggerTask type is fail~~~~~~~~~~~");
        return;
    }
    let isUpdate = false;
    //检查是否有新增任务
    this.checkTasksAdd();
    switch (TargetType) {
        case commonEnum.HONOR_TASK_TYPE.JOIN:
            isUpdate = this.loadConfig_1(TargetType, type);
            break;
        case commonEnum.HONOR_TASK_TYPE.TOP:
            isUpdate = this.loadConfig_2(TargetType, type, arg1);
            break;
        case commonEnum.HONOR_TASK_TYPE.LEVEL_TOP:
            isUpdate = this.loadConfig_3(TargetType, type, arg1, arg2);
            break;
        case commonEnum.HONOR_TASK_TYPE.BELIEF_DEFEND:
            isUpdate = this.loadConfig_4(TargetType, type, arg1);
            break;
        case commonEnum.HONOR_TASK_TYPE.BELIEF_ATTACK:
            isUpdate = this.loadConfig_4(TargetType, type, arg1);
            break;
        default:
            break;
    }

    if(isUpdate) {
        this.player.saveHonorWall();
    }
};
/**
 * 检查是否有新任务添加
 */
honorWall.prototype.checkTasksAdd = function () {
    let config = dataApi.allData.data["HonorTask"];
    for(let i in config)
    {
        let isHave = false;
        for(let k in this.allTaskList)
        {
            if(this.allTaskList[k].resId === config[i].Id)
            {
                isHave = true;
            }
        }
        //添加新增任务
        if(!isHave)
        {
            this.allTaskList.push({resId: config[i].Id, num: 0, status: 0});
        }
    }
};
/**
 * 设置任务状态已领取 任务id
 */
honorWall.prototype.setTaskAlreadyReceived = function (resId) {
    let index = -1;
    for (let i in this.allTaskList) {
        if (resId === this.allTaskList[i].resId) {
            index = Number(i);
            break;
        }
    }

    if (index !== -1) {
        // this.finishTask.push(resId);
        this.allTaskList[index].status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;//设置为已领取状态
        // this.allTaskList.splice(index, 1); //删除任务
    }
};

/**
 * 领取任务奖励  任务resId
 */
honorWall.prototype.getHonorTaskReward = function (resId) {
    if (!resId) {
        return Code.FAIL;
    }

    let isFinish = this._checkIsFinish(resId);
    if (!isFinish) {
        return Code.TASKS.NOTFINISH;
    }

    let config = dataApi.allData.data["HonorTask"][resId];
    for (let k = 1; k <= 5; ++k) {
        let rewardId = config["Reward" + k];
        let rewardNum = config["Num" + k];
        if (!rewardId || !rewardNum) {
            continue;
        }
        this.player.bag.addItem(rewardId, rewardNum);
    }

    //设置任务状态已领取
    this.setTaskAlreadyReceived(resId);
    return Code.OK;
};
/**
 * 检查任务是否已经完成  resId任务ID
 */
honorWall.prototype._checkIsFinish = function (resId) {
    let isOk = false;
    let status = -1;
    for (let i in this.allTaskList) {
        if (resId === this.allTaskList[i].resId) {
            status = this.allTaskList[i].status;
            break;
        }
    }

    if (status === 1) {
        isOk = true;
    }
    return isOk;
}

/**
 * 完成任务   任务id
 */
honorWall.prototype._finishTask = function (resId) {
    if (!resId) {
        return;
    }
    for (let i in this.allTaskList) {
        if (resId === this.allTaskList[i].resId) {
            this.allTaskList[i].status = commonEnum.ACT_BTN_STATUS.CAN_TAKE; //可领取
            this.finishTask.push(resId);
            break;
        }
    }
};
/**
 * 检查是否存在此任务   任务id
 */
honorWall.prototype.checkIsHaveTask = function (resId) {
    let isHave = false;
    for (let i in this.allTaskList) {
        if (resId === this.allTaskList[i].resId && this.allTaskList[i].status === commonEnum.ACT_BTN_STATUS.NOT_TAKE) {
            isHave = true;
            break;
        }
    }
    return isHave;
}

/**
 * 添加完成任务次数   任务id
 */
honorWall.prototype._addTaskNum = function (resId) {
    if (!resId) {
        return;
    }

    for (let i in this.allTaskList) {
        if (resId === this.allTaskList[i].resId) {
            if(this.allTaskList[i].status === commonEnum.ACT_BTN_STATUS.NOT_TAKE) {
                this.allTaskList[i].num++;
                break;
            }
        }
    }
};
/**
 * 添加任务完成次数   任务id
 */
honorWall.prototype._addTaskNumByNum = function (resId, num) {
    if (!resId) {
        return;
    }
    for (let i in this.allTaskList) {
        if (resId === this.allTaskList[i].resId) {
            if(this.allTaskList[i].status === commonEnum.ACT_BTN_STATUS.NOT_TAKE) {
                this.allTaskList[i].num += num;
            }
        }
    }
};
/**
 * 检查任务次数是否够  任务id
 */
honorWall.prototype._checkTaskNumIsFull = function (resId) {
    if (!resId) {
        return;
    }

    //读表获取任务数量
    let configNum = dataApi.allData.data["HonorTask"][resId].Num;
    if(!configNum) {
        configNum = 0;
    }
    for (let i in this.allTaskList) {
        if (resId === this.allTaskList[i].resId) {
            //次数够了直接完成
            if (this.allTaskList[i].num >= configNum) {
                this._finishTask(resId);
                break;
            }
        }
    }
}
//type相等完成
honorWall.prototype.loadConfig_1 = function (targetType, type) {
    //读表获取任务
    let config = dataApi.allData.data["HonorTask"];
    if (!config) {
        return false;
    }

    let taskList = [];
    let index = 0;
    for (let i in config) {
        if (config[i].TargetType === targetType && config[i].Type === type) {
            taskList[index] = {};
            taskList[index].resId = config[i].Id;
            index++;
        }
    }
    logger.error("更新任务11111111111111111112", targetType, type, taskList);
    let isUpdate = false;
    for (let i in taskList) {
        if(this.checkIsHaveTask(taskList[i].resId)) {
            this._addTaskNum(taskList[i].resId);
            this._checkTaskNumIsFull(taskList[i].resId);
            isUpdate =true;
        }
    }
    return isUpdate;
}
//type相等并且小于等于arg1时完成
honorWall.prototype.loadConfig_2 = function (targetType, type, arg1) {
    //读表获取任务
    let config = dataApi.allData.data["HonorTask"];
    if (!config) {
        return false;
    }

    let taskList = [];
    let index = 0;
    for (let i in config) {
        if (config[i].TargetType === targetType && config[i].Type === type && config[i].Arg1 >= arg1) {
            taskList[index] = {};
            taskList[index].resId = config[i].Id;
            index++;
        }
    }

    let isUpdate = false;
    for (let i in taskList) {
        if(this.checkIsHaveTask(taskList[i].resId)) {
            this._addTaskNum(taskList[i].resId);
            this._checkTaskNumIsFull(taskList[i].resId);
            isUpdate =true;
        }
    }
    return isUpdate;
};
//type相等并且小于等于arg1并且arg2相等时完成
honorWall.prototype.loadConfig_3 = function (targetType, type, arg1, arg2) {
    //读表获取任务
    let config = dataApi.allData.data["HonorTask"];
    if (!config) {
        return false;
    }

    let taskList = [];
    let index = 0;
    for (let i in config) {
        if (config[i].TargetType === targetType && config[i].Type === type && config[i].Arg1 >= arg1 && config[i].Arg2 === arg2) {
            taskList[index] = {};
            taskList[index].resId = config[i].Id;
            index++;
        }
    }

    let isUpdate = false;
    for (let i in taskList) {
        if(this.checkIsHaveTask(taskList[i].resId)) {
            this._addTaskNum(taskList[i].resId);
            this._checkTaskNumIsFull(taskList[i].resId);
            isUpdate =true;
        }
    }
    return isUpdate;
};
//任务增加arg1次
honorWall.prototype.loadConfig_4 = function (targetType, type, arg1) {
    //读表获取任务
    let config = dataApi.allData.data["HonorTask"];
    if (!config) {
        return false;
    }

    let taskList = [];
    let index = 0;
    for (let i in config) {
        if (config[i].TargetType === targetType && config[i].Type === type) {
            taskList[index] = {};
            taskList[index].resId = config[i].Id;
            index++;
        }
    }

    let isUpdate = false;
    for (let i in taskList) {
        if(this.checkIsHaveTask(taskList[i].resId)) {
            this._addTaskNumByNum(taskList[i].resId, arg1);
            this._checkTaskNumIsFull(taskList[i].resId);
            isUpdate =true;
        }
    }
    return isUpdate;
};