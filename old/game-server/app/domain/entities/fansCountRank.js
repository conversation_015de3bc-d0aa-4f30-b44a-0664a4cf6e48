var logger = require("pomelo-logger").getLogger('pomelo', __filename);
var EventEmitter = require("events").EventEmitter;
var util = require("util");
var dataApi = require("../../util/dataApi");
var utils = require("../../util/utils");
var commonEnum = require("../../../../shared/enum");
var Calc = require("../../util/calc");
var Code = require("../../../../shared/code");
var Constant = require("../../../../shared/constant");
var TimeUtils = require("../../util/timeUtils");

var FansCountRank = function(globalRank, rankLimit) {      
    this.globalRank = globalRank;
    this.rankLimit = rankLimit;
    this.rank = [];
    this.accountList = new Map();          //uid => rankObj;
};

util.inherits(FansCountRank, EventEmitter);
module.exports = FansCountRank;

FansCountRank.prototype.test = function()
{

};

function __fans_count_compare_func(rankObj1, rankObj2) 
{
    let fansCount1 = rankObj1.fansCount;
    let level1  = rankObj1.level;

    let fansCount2 = rankObj2.fansCount;
    let level2  = rankObj2.level;

    //战力
    if (fansCount1 !== fansCount2 ) { 
        //降序
        if (fansCount1 < fansCount2) {
            return 1;
        }else if (fansCount1 > fansCount2) {
            return -1;
        }
    }	

    //等级
    if (level1 !== level2) {
        //降序
        if (level1 < level2) {
            return 1;
        }else if (level1 > level2) {
            return -1;
        }
    }
        
    return 0;
};

FansCountRank.prototype.init = function(accountList) 
{
    if (!accountList || accountList.length <= 0)
    {
        logger.error("FansCountRank: init not account data");
        return;
    }

    let count = 0;
    for (let [idx, data] of accountList) {
        let uid = data.uid;
        let level = data.level;
        let fansCount = data.fansCount;
        
        let obj = {
            uid: uid,
            level: level,
            fansCount: fansCount,
        };
        count++;
        this.accountList.set(uid, obj);
    }

    this.sort();
    //logger.info("FansCountRank init: accountList count", count);
};

FansCountRank.prototype.updateRank = function(uid, level, fansCount)
{
    let obj = {
        uid: uid,
        level: level,
        fansCount: fansCount,
    };

    //1.是否超过最大限制
    if (!this.accountList.has(uid) && this.rank.length < this.rankLimit)
    {
        this.accountList.set(uid, obj);
        this.sort();
        //logger.info("FansCountRank updateRank", uid, this.getRank(uid));
        return;
    }

    //2.是否存在于当前榜单中
    if (this.accountList.has(uid)) 
    {
        let data = this.accountList.get(uid);
        let rank = data.rank;
        let rankObj =  this.rank[rank - 1];
        let isUpdate = false;

        if (rankObj.level !== level)
        {
            rankObj.level = level;
            isUpdate =  true;
        }

        if (rankObj.fansCount !== fansCount)
        {
            rankObj.fansCount = fansCount;
            isUpdate =  true;
        }

        if (isUpdate)
        {
            this.sort();
            //logger.info("FansCountRank updateRank: ", uid, this.getRank(uid));
        }
        return;
    }

    let lastOneRank = this.rank.length - 1;
    let rankObj = this.rank[lastOneRank];
    let lastLevel = rankObj.level;
    let lastFansCount = rankObj.fansCount;
    if ( lastFansCount < fansCount && level >= lastLevel) //可以进榜最低条件
    {
        this.accountList.set(uid, obj);
        this.sort();
        //logger.info("FansCountRank updateRank: ", uid, this.getRank(uid));
        return;
    }

    //logger.info("FansCountRank: not go rank", uid);
};

FansCountRank.prototype.sort = function()
{
    let tmpRankArr = [];
    for(let [k,v] of this.accountList)
    {
        tmpRankArr.push(utils.deepCopy(v));
    }

    tmpRankArr.sort(__fans_count_compare_func);
    this.rank = [];
    this.accountList.clear();
    let rank = 1;
    for (let idx in tmpRankArr) {
        if (rank > this.rankLimit) //超过限制，退出
        {
            break; 
        }

        let data = tmpRankArr[idx];
        data.rank = rank;
        this.accountList.set(data.uid, data);
        this.rank.push(data);
        //logger.info("FansCountRank sort: ", rank, data.uid, data.actualStrength, data.level);
        rank++;
    }
    //logger.info("rank", this.rank);
};

FansCountRank.prototype.getRank = function(uid)
{
    let rank = 0;
    if (!this.accountList.has(uid))
    {   
        return rank;
    }

    let data = this.accountList.get(uid);
    return data.rank;
};

FansCountRank.prototype.getFansCount = function(uid)
{
    let fansCount = 0;
    if (!this.accountList.has(uid))
    {
        return fansCount;
    }

    let data = this.accountList.get(uid);
    return data.fansCount;
};
