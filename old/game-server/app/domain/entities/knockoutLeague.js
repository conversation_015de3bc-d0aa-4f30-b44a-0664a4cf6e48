var logger = require('pomelo-logger').getLogger('pomelo', __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var dataApi = require('../../util/dataApi');
var utils = require('../../util/utils');
var commonEnum = require('../../../../shared/enum');
var Calc = require('../../util/calc');
var Code = require('../../../../shared/code');
var Player = require('./player');
var Constant = require("../../../../shared/constant");
var TimeUtils = require('../../util/timeUtils');
var async = require('async');

//常规赛
var KnockoutLeague = function(seasonId, league) {
    this.uid            = seasonId;   //赛季Id
    this.league         = league;
    //分组后第一轮数据
    this.groupPlayerMap = new Map();    //groupId -> [{ uid1}, {uid2}] //1-128 [uid1,uid2] //参赛人数据
    this.playerGroupMap = new Map();    //Uid->groupId //反向映射

    this.promotionMap   = new Map();    //round_groupId -> [uid, uid] //晋级玩家列表      
    this.eliminateMap   = new Map();    //round_groupId -> [uid, uid] //淘汰玩家列表
    this.roundBattleMap = new Map();    //round_groupId -> [battleId, battleId2] //没人对打的情况不记录 ,轮空也要记录
    this.roundRank      = new Map();    //round_groupId -> [rankObj, rankObj] //因为是过程数据也是最终数据,没有办法,只能维护一个队列了
    this.battleScheduleMap = new Map(); //round_groupId = [ {uid, uid}]; //战斗赛程

    this.playerMap      = new Map(); // uid->obj //玩家自己信息数据 进球率
    this.battleMap      = new Map(); //[battleId]-> [battleObj];  //big data 所有轮次的数据都存在上面
    this.finalRank      = new Map(); //总榜排名 // groupId -> [rankObjList]

    this.homeTeam       = [];
    this.awayTeam       = [];

    //系统支持数据
    this.currRound      = 0;         //当前轮次
    this.privateConfig = [];

    //辅助数据(不保存数据)
    this.roundConfig    = [];   //轮次配置表
    this.groupMaxCount  = 0;
    this.typeId         = commonEnum.LEAGUE_RUN_TYPE_ID.KNOCKOUT;
    this.awaitTime = 0;     //战斗服返回时间不用存DB
    this.MAXTimeOut = 60 * 30;  //战斗服返回超时最大时间  30分钟
    //初始化配置表
    this.initByConfig();
    this.initPrivateConfig();
};

util.inherits(KnockoutLeague, EventEmitter);

module.exports = KnockoutLeague;

//初始化配置数据
KnockoutLeague.prototype.initByConfig = function() 
{   
    //round 轮次 name: 轮次名称 required_num 每小组最大保存最大人数
    var roundConfig = [
        {round: 0, name: "splitPrepare",  required_num: 32},
        {round: 1, name: "晋级职业赛",     required_num: 16},
    ];
    this.groupMaxCount  = 1;
    this.roundConfig = roundConfig;
};

KnockoutLeague.prototype.initPrivateConfig = function()
{
  let battleStatus = commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN;
  var privateConfig = 
  [
    { round: 0, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false}, //sendWaitStartNtf 发送等待比赛通知 sendStartNtf 发送比赛开始通知
    { round: 1, isRunning: false, canAction: false, battleStatus: battleStatus, sendWaitStartNtf: false, sendStartNtf: false},
  ];
    /*isRunning: 某个函数某些地方只运行一次,
   canAction: 当前函数是否全部把事情完成(等回调), 
   battleStatus: battleStatus,
   sendWaitStartNtf: 发送等待比赛开始通知
   false, sendStartNtf: 发送比赛开始通知
  */
  this.privateConfig = privateConfig;
};

KnockoutLeague.prototype.toJSONforDB = function() {
  var kkt = {
    uid: this.uid,
    currRound: this.currRound,
    groupPlayerMap: this.league.groupPlayerMapToDB(this.groupPlayerMap),
    playerGroupMap: this.league.playerGroupMapToDB(this.playerGroupMap),

    battleMap: this.league.battleMapToDB(this.battleMap),
    playerMap: this.league.playerMapToDB(this.playerMap),
    finalRank: this.league.finalRankToDB(this.finalRank),

    promotionMap: this.league.roundGroupIdToDB(this.promotionMap),
    eliminateMap: this.league.roundGroupIdToDB(this.eliminateMap),
    roundBattleMap: this.league.roundGroupIdToDB(this.roundBattleMap),
    roundRank: this.league.roundGroupIdToDB(this.roundRank),
    battleScheduleMap: this.league.roundGroupIdToDB(this.battleScheduleMap),

    homeTeam: this.homeTeam,
    awayTeam: this.awayTeam,
    privateConfig: this.privateConfig,
  };

  return kkt;
};

KnockoutLeague.prototype.initByDB = function(doc)
{
    this.uid = doc.uid;
    this.groupPlayerMap = this.league.groupPlayerMapLoadFromDB(doc.groupPlayerMap) || new Map();
    this.playerGroupMap = this.league.playerGroupMapLoadFromDB(doc.playerGroupMap) || new Map();

    this.battleMap = this.league.battleMapLoadFromDB(doc.battleMap) || new Map();
    this.playerMap = this.league.playerMapLoadFromDB(doc.playerMap) || new Map();
    this.finalRank = this.league.finalRankLoadFromDB(doc.finalRank) || new Map();

    this.promotionMap = this.league.roundGroupIdLoadFromDB(doc.promotionMap) || new Map();
    this.eliminateMap = this.league.roundGroupIdLoadFromDB(doc.eliminateMap) || new Map();
    this.roundBattleMap = this.league.roundGroupIdLoadFromDB(doc.roundBattleMap) || new Map();
    this.roundRank = this.league.roundGroupIdLoadFromDB(doc.roundRank) || new Map();
    this.battleScheduleMap = this.league.roundGroupIdLoadFromDB(doc.battleScheduleMap) || new Map();
    this.privateConfig = this.league.checkPrivateConfigAbnormal(doc.privateConfig);
    if (!this.privateConfig)
    {
      logger.error("NormalLeague.initByDB: not privateConfig! doc.privateConfig", doc.privateConfig);
      this.initPrivateConfig();
    }

    this.homeTeam       = doc.homeTeam || [];
    this.awayTeam       = doc.awayTeam || [];
    //系统支持数据
    this.currRound      =  doc.currRound || 0;         //当前轮次
};

KnockoutLeague.prototype.test = function()
{
};

KnockoutLeague.prototype.getRoundConfig = function(round)
{
  return this.league.comGetRoundConfig(this.roundConfig, round);
};

KnockoutLeague.prototype.getLastRound = function()
{
  return this.league.comGetLastRound(this.roundConfig);
};

KnockoutLeague.prototype.getFinalRound = function(isScoreRank)
{
  let finalRound = this.getLastRound();
  if (isScoreRank)
  {
    finalRound = finalRound + 1;
  }
  return finalRound;
};

KnockoutLeague.prototype.getCurrFinalRound = function(isScoreRank)
{
  let currRound = this.getCurrRound();
  let lastRound = this.getLastRound();
  let finalRound = currRound; 
  return finalRound;
};

KnockoutLeague.prototype.canRunNextAction = function(round) 
{
  return this.league.comCheckRunNextAction(this.privateConfig, round);
};

KnockoutLeague.prototype.setCanRunNextAction = function(round) 
{
   this.league.comSetCanRunNextAction(this.privateConfig, round);
};

KnockoutLeague.prototype.setBattleStatus = function(round, battleStatus)
{
  this.league.comSetBattleStatus(this.privateConfig, round, battleStatus);
};

KnockoutLeague.prototype.getConfigBattleStatus = function(round)
{
  return this.league.comGetConfigBattleStatus(this.privateConfig, round);
};

KnockoutLeague.prototype.setSendWaitStartNtf = function(round)
{
  this.league.comSetSendWaitStartNtf(this.privateConfig, round);
};

KnockoutLeague.prototype.checkSendWaitStartNtf = function(round)
{
  return this.league.comCheckSendWaitStartNtf(this.privateConfig, round);
};

KnockoutLeague.prototype.setSendStartNtf = function(round)
{
  this.league.comSetSendStartNtf(this.privateConfig, round);
};

KnockoutLeague.prototype.checkSendStartNtf = function(round)
{
  return this.league.comCheckSendStartNtf(this.privateConfig, round);
};

KnockoutLeague.prototype.checkRunning = function(round) 
{
  return this.league.comCheckRunning(this.privateConfig, round);
};

KnockoutLeague.prototype.setIsRunning = function(round)
{
  this.league.comSetIsRunning(this.privateConfig, round);
};

KnockoutLeague.prototype.Enroll = function(simplePlayerObj, teamType)
{   
    if (!simplePlayerObj)
    {
        return Code.FAIL;
    }

    let uid = simplePlayerObj.playerUid;
    var playerObj = this.league.newPlayerObj(uid, false);
    playerObj.power = simplePlayerObj.power;
    playerObj.enrollTime = simplePlayerObj.enrollTime;
  
    this.setPlayerMap(uid, playerObj);
    if (0 === teamType)
    {
      this.homeTeam.push(uid);
    }else
    {
      this.awayTeam.push(uid);
    }

    let groupId = 1;
    if (!this.hasPlayerGroupMap(uid))
    {
      this.setPlayerGroupMap(uid, groupId);
    }

    if (!this.hasGroupPlayerMap(groupId))
    {
      this.setGroupPlayerMap(groupId, []);
    }

    let uidList = this.getGroupPlayerMap(groupId);
    uidList.push(uid);
    return Code.OK;
};

KnockoutLeague.prototype.checkGroup = function() 
{
  //处理组内人数
  //少的使用机器人天空，多的删除、不多不少不处理
  var config = this.getRoundConfig(0);
  let required_num = config.required_num;
  let robotIdx = 1;
  for(let [k,v ] of this.groupPlayerMap)
  {
      let uidList = v;
      let length = uidList.length;
      if (length !== required_num)
      {
        if (required_num < length )
        {
          let count = 1;
          for (let idx in uidList) {
              const uid = uidList[idx];
              if (count > required_num)
              {
                  utils.removeElement(uidList, uid);
                  if (this.hasPlayerMap(uid)) {
                     this.delPlayerMap(uid);
                     logger.warn("delete uid", uid);
                  }
              }
              count++;
          }
        }
        else if(required_num > length)
        {
          let robotNum = required_num - length;
          let count = 0;
          for (let idx = 1; idx <= robotNum; idx++) {
            let uid = "robot_" + robotIdx  + "_"+ utils.syncCreateUid();
            let uidList = this.getGroupPlayerMap(k);
            uidList.push(uid);
            this.setPlayerGroupMap(uid, k);
        
            //初始化玩家数据
            if (!this.hasPlayerMap(uid)) {
              var playerObj = this.league.newPlayerObj(uid, false);
              this.setPlayerMap(uid, playerObj);
            }

            logger.warn("add robot uid", uid);
            robotIdx++;
            count++;
          }

          logger.warn("add robot uid: count", count);
        }
      }
  }

  //logger.info("checkGroup: awayTeam, homeTeam, idx", this.awayTeam.length, this.homeTeam.length);
};

//积分排序
function __score_compare_func(rankObj1, rankObj2) {
	let totalScore1 = rankObj1.totalScore;
	let goalCount1  = rankObj1.goalCount;
	let missCount1  = rankObj1.missCount;
	let power1      = rankObj1.power;
  let enrollTime1 = rankObj1.enrollTime;
  let diffGoalCount1 = goalCount1 - missCount1;

	let totalScore2 = rankObj2.totalScore;
	let goalCount2  = rankObj2.goalCount;
	let missCount2  = rankObj2.missCount;
  let power2      = rankObj2.power;
  let enrollTime2 = rankObj2.enrollTime;
  let diffGoalCount2 = goalCount2 - missCount2;

    //积分
	if (totalScore1 !== totalScore2 ) { 
		 //降序
		if (totalScore1 < totalScore2) {
            return 1;
        }else if (totalScore1 > totalScore2) {
            return -1;
        }
  }	
  
    //进球数
  if (diffGoalCount1 !== diffGoalCount2) 
  {
		//降序
		if (diffGoalCount1 < diffGoalCount2) {
            return 1;
        }else if (diffGoalCount1 > diffGoalCount2) {
            return -1;
        }
	}

    //进球数
    if (goalCount1 !== goalCount2) {
		 //降序
		if (goalCount1 < goalCount2) {
            return 1;
        }else if (goalCount1 > goalCount2) {
            return -1;
        }
    }

    //战力
    if (power1 != power2)
    {
        //降序
        if (power1 < power2) {
            return 1;
        }else if (power1 > power2) {
            return -1;
        }
    }

  //报名时间 靠前的人胜利
  //升序
  if (enrollTime1 > enrollTime2)
  {
		return 1;
  }else if (enrollTime1 < enrollTime2) 
  {
		return -1;
	}
    
  return 0;
};

//note: 这两个map里面的数据结构不一样，所以分开写(人数刚好，或者小于规定的人数 <= 128*64)
KnockoutLeague.prototype.directPromotion = function(round)
{
  for (let [k, v] of this.groupPlayerMap) {
    let round_key = this.league.getRoundKey(round, k);
    //晋级玩家
    this.setPromotionMap(round_key, v);
    //logger.info("KKT directPromotion: round, k, round_key", round, k, v.length, v);
  }
};

KnockoutLeague.prototype.getPromotionPlayer = function(round)
{
    let groupPlayerMap = new Map();
    for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
        let round_key = this.league.getRoundKey(round, groupId);
        let uidList =  this.getPromotionMap(round_key);
        //logger.info("getPromotionPlayer: round, round_key", round, round_key, uidList);
        if (!uidList || uidList.length <= 0) continue;
        //logger.info("getPromotionPlayer: round, round_key, uidList, uidList.length", round, round_key, uidList, uidList.length);
        groupPlayerMap.set(round_key, uidList);
    }

    return groupPlayerMap;
};

KnockoutLeague.prototype.getPromotionUidList = function(round)
{
    let proUidList = [];
    let count = 1;
    for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
        let round_key = this.league.getRoundKey(round, groupId);
        let uidList =  this.getPromotionMap(round_key);
        if (!uidList || uidList.length <= 0) continue;
        for (let i in uidList) {
            const uid = uidList[i];
            if (!uid ||  "" === uid) continue;
            proUidList.push(uid);
        }
       //logger.info("getPromotionUidList: uidList", uidList.length, proUidList.length);
    }

    //logger.info("getPromotionUidList: getPromotionUidList", proUidList);
    return proUidList;
};

KnockoutLeague.prototype.getPromotionUidListByGroupId = function(round, groupId) 
{
  let proUidList = [];
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasPromotionMap(round_key))
  {
    return proUidList;
  }

  let uidList = this.getPromotionMap(round_key);
  if (!uidList || uidList.length <= 0) 
  { 
    return proUidList;
  }

  for (let i in uidList) {
    const uid = uidList[i];
    if (!uid || "" === uid) 
    {
      continue;
    }

    proUidList.push(uid);
  }
  return proUidList;
};


KnockoutLeague.prototype.getEliminateUidListByGroupId = function(round, groupId) 
{
  let proUidList = [];
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasEliminateMap(round_key))
  {
    return proUidList;
  }

  let uidList = this.getEliminateMap(round_key);
  if (!uidList || uidList.length <= 0) 
  { 
    return proUidList;
  }

  for (let i in uidList) {
    const uid = uidList[i];
    if (!uid || "" === uid) 
    {
      continue;
    }

    proUidList.push(uid);
  }
  return proUidList;
};

KnockoutLeague.prototype.getEliminatePlayer = function(round) {
  let groupPlayerMap = new Map();
  //从第一组开始进行分人
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
    let round_key = this.league.getRoundKey(round, groupId);
    if (!this.hasEliminateMap(round_key))
    {
      continue;
    }

    let uidList = this.getEliminateMap(round_key);
    if (!uidList || uidList.length <= 0) 
    {
      continue;
    }

    groupPlayerMap.set(round_key, uidList);
  }
  return groupPlayerMap;
};

KnockoutLeague.prototype.getEliminateUidList = function(round) {
  let proUidList = [];
  //从第一组开始进行分人
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
      let round_key = this.league.getRoundKey(round, groupId);
      if (!this.hasEliminateMap(round_key))
      {
        continue;
      }

      let uidList = this.getEliminateMap(round_key);
      if (!uidList || uidList.length <= 0) 
      {
        continue;
      }

      for (let i in uidList) {
        const uid = uidList[i];
        if (!uid || "" === uid) 
        {
          continue;
        }
        proUidList.push(uid);
      }
  }
  //logger.info("getPromotionPlayer: getEliminateUidList", proUidList);
  return proUidList;
};


KnockoutLeague.prototype.printDirectPromotion = function(round)
{
  for (let [k, v] of this.groupPlayerMap) 
  {
    logger.info("printDirectPromotion: round, groupId", round, k, v.length);
  }
};

KnockoutLeague.prototype.printRank = function(round, groupId)
{  
  let round_key = this.league.getRoundKey(round, groupId);
  var rankObjList = this.getRoundRank(round_key);
  let count  = 0;
  let uidList = [];
  for (let i in rankObjList) {
    const playerObj = rankObjList[i];
    if (!playerObj) continue;
    //logger.info("KKT rankList: playerUid, totalScore, diff, goalCount, power1", playerObj.playerUid, playerObj.totalScore, playerObj.goalCount, playerObj.power, playerObj.enrollTime);
    count ++;
    uidList.push(playerObj.playerUid);
  }
};

KnockoutLeague.prototype.printPromotion = function(round)
{
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
    let round_key = this.league.getRoundKey(round, groupId);
    let uidList = this.getPromotionMap(round_key);
    if (!uidList) continue;
    if (groupId === 8)
      logger.info("KKT printPromotion", round, groupId, uidList.length, uidList);
  }
};

//获取当前轮次战斗队列
KnockoutLeague.prototype.getRoundBattleQueue = function(round)
{
    var map = new Map();  //round_groupId = > [Host: uid1, Gust:uid2];
    for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
        let round_key = this.league.getRoundKey(round, groupId);
        let objList = this.getBattleScheduleMap(round_key);
        if (!objList) continue;
        let cpObjList = utils.cloneArray(objList);
        //logger.info("getRoundBattleQueue", cpObjList);
        map.set(round_key, cpObjList);
    }
    return map;
};

//分组对阵
KnockoutLeague.prototype.SplitPrepare = function(status_obj) 
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.KnockoutGroupSplitPrepare;
    let check = this.league.checkRound(round, localRound, "SplitPrepare");
    if (!check)
    { 
      return false;
    }
  
    let ret = this.checkRunning(round);
    if (ret)
    {
      ret = this.canRunNextAction(round);
      logger.info("_eliminationAudition: canRunNextAction", ret);
      if (ret) return true;
      return false;
    }

    this.checkGroup();

    //直接晋级到下一轮
    this.directPromotion(round);
    this.setIsRunning(round);
    this.setSendWaitStartNtf(round);
    this.setSendStartNtf(round);
    this.setCanRunNextAction(round);
    this.setBattleStatus(round, commonEnum.LEAGUE_BATTLE_STATUS.END);
    return false;
};
//战斗服返回超时重新重设状态
KnockoutLeague.prototype.battleTimeOutResetPrivateConfig = function()
{
    let atConfig;
    let k;
    //得到当前轮的状态
    for(let i in this.privateConfig)
    {
        if(this.privateConfig[i].round === this.currRound)
        {
            k = i;
            atConfig = this.privateConfig[i];
        }
    }
    //进入等待时间，可以开始计时
    if(atConfig.isRunning === true && atConfig.canAction === false)
    {
        this.awaitTime += 1;//因为updateFsmState函数是一秒一次所以每次加1为1秒
    }
    //超时了重置
    if((this.awaitTime >= this.MAXTimeOut) && (this.privateConfig[k].isRunning === true && this.privateConfig[k].canAction === false))
    {
        logger.warn("battle TimeOut PrivateConfig:", this.typeId, this.awaitTime, this.privateConfig[k]);
        this.privateConfig[k].isRunning = false;
        this.awaitTime = 0;
        logger.warn("battle TimeOut ResetPrivateConfig:", this.typeId, this.awaitTime, this.privateConfig[k]);
    }
};
KnockoutLeague.prototype.eliminationGroup32to16 = function(status_obj)
{
    let round = this.currRound;
    let localRound = commonEnum.LEAGUE_RUN_ROUND.KnockoutGroup32to16;
    let check = this.league.checkRound(round, localRound, "NLGroup64to32");
    if (!check)
    { 
      return false;
    }
    //检查战斗服返回是否超时
    this.battleTimeOutResetPrivateConfig();
    //检查该部分运行过没有
    let ret = this.checkRunning(round);
    if (ret)
    {
      //检查是否能够到达下一转
      ret = this.canRunNextAction(round);
      if (ret) return true; //事情做完了，但是还没有到达下一伦次开始的时间
      return false; //还没有做完，继续等待
    }

    //只打一场
    this.commProcessFlow(status_obj, round);
    return false;
};

//通用处理流程
KnockoutLeague.prototype.commProcessFlow = function(status_obj, round) {
  //取当前比赛各个战斗队列
  let groupPlayerMap = this.getRoundBattleQueue(round);
  //当前轮次淘汰
  let self = this;
  this.KKTGetBattleFlow(round, groupPlayerMap, function(err){
    if (!!err)
    {
      logger.error("comGetBattleFlow: error", err);
      return;
    }
    self.setCanRunNextAction(round);
    self.awaitTime = 0;
  });

  this.setIsRunning(round);
};

KnockoutLeague.prototype.KKTGetBattleFlow = function(round, groupPlayerMap, cb)
{
  let self = this;
  //1.拉取玩家数据
  this.league.commonGetBattleData(groupPlayerMap, function(err){
    if (!!err)
    {
      logger.error("comGetBattleData: callback error", err);
      return cb(err);
    }

    //通用战斗流程
    self.comKTEliminationGroup(round, groupPlayerMap, function(err) {
      if (!!err)
      {
        logger.error("comKTEliminationGroup: catch a error!", err);
        cb(err);
        return;
      }

      logger.info("comKTEliminationGroup commProcessFlow .............", round);

      self.printDirectPromotion(round);
      for (let groupId = 1; groupId <= self.groupMaxCount; groupId++) 
      {
        self.printRank(round, groupId);
      }

      //self.printPromotion(round);

      cb(null);
      return;
    });
  });
};

KnockoutLeague.prototype.comKTEliminationGroup = function(round, groupPlayerMap, cb) 
{
  for (let [k, v] of groupPlayerMap) 
  {
    let groupId = this.league.getGroupId(k);
    //logger.info("comKTEliminationGroup k,v", k,v.length)
    this.updateBattleQueueProcess(round, parseInt(groupId), v, v.length);
  }

  //全部入队列后，一起发送请求,一起处理
  this.league.battleQueue.sendBattleToBattleSvr(function(err, code){
    //logger.info("comKTEliminationGroup", err, code);
    if (!!err)
    {
      logger.error("sendBattleToBattleSvr failed! err", err);
      cb(err);
      return;
    }

    if (code !== Code.OK)
    {
      logger.error("comKTEliminationGroup: ret error! err", code);
      cb("ret error! err");
      return;
    }

    cb(null)
    return;
  });
};

//处理战斗队列
KnockoutLeague.prototype.updateBattleQueueProcess = function( round, groupId, battleObjList, length)
{
  //logger.info("updateBattleQueueProcess: round, groupId, length", round, groupId, battleObjList, length);
  var config = this.getRoundConfig(round);
  let required_every_group_num = config.required_num;

  let lastRound = round - 1;
  let historyRankData = this.makeTotalLastRoundGroupRank(groupId, lastRound);

  //把随机后玩家放入到队列表中
  let battleList = this.league.makeBattleList(this.typeId, round, groupId, battleObjList, length, historyRankData);
  //处理轮空情况
  //联赛没有位置为空的情况，就有算，也上上一轮留下的机器人，位置不可能留空
  this.league.preProcessBattleList(battleList, required_every_group_num);
  let needSendBattleList = this.league.getAllPlayerBattleList(battleList);
  //logger.info("updateBattleQueueProcess 1: round, groupId, length", round, groupId, needSendBattleList.length, battleList.length);

  if (needSendBattleList.length > 0) {
    logger.info("NL: updateBattleQueueProcess 2: round, groupId, robot_length, length", round, groupId, battleList.length - needSendBattleList.length, needSendBattleList.length);
    this.league.sendBattleQueue(needSendBattleList);
  }

  //这里直接处理不需要战斗的情况 比如玩家轮空，两个机器人对打，直接处理
  this.proRobotBattleResult(round, groupId, battleList);
};

//处理机器人战斗结果
//Note: 这里没有对战分数
KnockoutLeague.prototype.proRobotBattleResult = function(round, groupId, battleResultList)
{
    for (let index in battleResultList) 
    {
      let winTeam = "";
      let lostTeam = "";
      let data = battleResultList[index];
      let robotA = "robot_" === data.home.substr(0, 6); //home是否为机器人
      let robotB = "robot_" === data.away.substr(0, 6); //away是否为机器人
      if (!robotA && !robotB) continue;
  
      let newHomePlayerObj = this.league.newPlayerObj(data.home, false);
      let newAwayPlayerObj = this.league.newPlayerObj(data.away, false);
      //都为机器人
      if (robotA && robotB) {
        //都是机器人 算主场赢
        winTeam = data.home;
        lostTeam = data.away;
        newHomePlayerObj.battleCount += 1;
        newHomePlayerObj.winCount += 1;
        newHomePlayerObj.goalCount += 0;
        newHomePlayerObj.missCount += 0;
        newHomePlayerObj.totalScore += 3;
  
        newAwayPlayerObj.battleCount += 1;
        newAwayPlayerObj.lossCount += 1;
        newAwayPlayerObj.goalCount += 0;
        newAwayPlayerObj.missCount += 0;
        newAwayPlayerObj.totalScore += 0;
      }
      else if (!robotA && robotB) 
      {  //这里的情况是人对机器人，是轮空直接判定为人赢
        //A为真人 B为机器人
        winTeam = data.home;
        lostTeam = data.away;
        newHomePlayerObj.battleCount += 1;
        newHomePlayerObj.winCount += 1;
        newHomePlayerObj.totalScore += 3;

        newAwayPlayerObj.battleCount += 1;
        newAwayPlayerObj.lossCount += 1;
        newAwayPlayerObj.totalScore += 0;
      } //A为机器人 B为真人
      else {
        winTeam = data.away;
        lostTeam = data.home;
        newHomePlayerObj.battleCount += 1;
        newHomePlayerObj.lossCount += 1;
        newHomePlayerObj.totalScore += 0;
  
        newAwayPlayerObj.battleCount += 1;
        newAwayPlayerObj.winCount += 1;
        newAwayPlayerObj.totalScore += 3;
      }

      data.winTeam = winTeam;
  
      this.addPlayerToRoundGroupRank(round, groupId, newHomePlayerObj);
      this.addPlayerToRoundGroupRank(round, groupId, newAwayPlayerObj);
      this.addPlayerToPromotion(round, groupId, winTeam);
      this.addPlayerToEliminate(round, groupId, lostTeam);
      this.addRoundBattle(round, groupId, this.league.comGetBattleResultObj(data, this.typeId));   //战斗结果保存
    }
};

//处理战斗服返回来的结果
//Note: 对战双方都是真人
KnockoutLeague.prototype.proPlayerBattleResult = function(result) 
{
  if (!result) {
    logger.error("proPlayerBattleResult: not result", result);
    return;
  }

  let winTeam = "";
  let lostTeam = "";
  let data = result;
  let homeScore = data.homeScore;
  let awayScore = data.awayScore;
  let round = data.round;
  let groupId = data.groupId;

  let robotA = "robot_" === data.home.substr(0, 6); //home是否为机器人
  let robotB = "robot_" === data.away.substr(0, 6); //away是否为机器人
  if (robotA || robotB) {
    logger.error("proPlayerBattleResult: home and away is robot", result);
    return;
  }
  
  let newHomePlayerObj = this.league.newPlayerObj(data.home);
  let newAwayPlayerObj = this.league.newPlayerObj(data.away);
  if (homeScore > awayScore) {
    //A赢
    winTeam = data.home;
    lostTeam = data.away;
    newHomePlayerObj.battleCount += 1;
    newHomePlayerObj.winCount += 1;
    newHomePlayerObj.goalCount += data.homeScore;
    newHomePlayerObj.missCount += data.awayScore;
    newHomePlayerObj.totalScore += 3;

    newAwayPlayerObj.battleCount += 1;
    newAwayPlayerObj.lossCount += 1;
    newAwayPlayerObj.goalCount += data.awayScore;
    newAwayPlayerObj.missCount += data.homeScore;
    newAwayPlayerObj.totalScore += 0; //失败加0分
  } else if (homeScore < awayScore) {
    //B赢
    winTeam = data.away;
    lostTeam = data.home;
    newHomePlayerObj.battleCount += 1;
    newHomePlayerObj.lossCount += 1;
    newHomePlayerObj.goalCount += data.homeScore;
    newHomePlayerObj.missCount += data.awayScore;

    newAwayPlayerObj.battleCount += 1;
    newAwayPlayerObj.winCount += 1;
    newAwayPlayerObj.goalCount += data.awayScore;
    newAwayPlayerObj.missCount += data.homeScore;
    newAwayPlayerObj.totalScore += 3; //失败加0分
    //logger.info("player 2 data.home, playerObj1", data.home, playerObj1);
  } //A平
  else {
    let power1 = newHomePlayerObj.power;
    let enrollTime1 = newHomePlayerObj.enrollTime;
    let power2 = newAwayPlayerObj.power;
    let enrollTime2 = newAwayPlayerObj.enrollTime;
    //战力
    if (power1 != power2) {
      if (power1 > power2) {
        winTeam = data.home;
        lostTeam = data.away;
      } else if (power1 < power2) {
        winTeam = data.away;
        lostTeam = data.home;
      }
    } else {
      //报名时间 靠前的人胜利
      if (enrollTime1 < enrollTime2) {
        winTeam = data.home;
        lostTeam = data.away;
      } else if (enrollTime1 > enrollTime2) {
        winTeam = data.away;
        lostTeam = data.home;
      } else {
        winTeam = data.home;
        lostTeam = data.away;
      }
    }

    if (winTeam === data.home)
    {
      newHomePlayerObj.winCount += 1;
      newHomePlayerObj.totalScore += 3; 

      newAwayPlayerObj.lossCount += 1;
      newAwayPlayerObj.totalScore += 0; 
    }else
    {

      newHomePlayerObj.lossCount += 1;
      newHomePlayerObj.totalScore += 0; 

      newAwayPlayerObj.winCount += 1;
      newAwayPlayerObj.totalScore += 3; 
    }

    newHomePlayerObj.battleCount += 1;
    newHomePlayerObj.goalCount += data.homeScore;
    newHomePlayerObj.missCount += data.awayScore;

    newAwayPlayerObj.battleCount += 1;
    newAwayPlayerObj.goalCount += data.awayScore;
    newAwayPlayerObj.missCount += data.homeScore;
  }

  data.winTeam = winTeam;

  this.addPlayerToRoundGroupRank(round, groupId, newHomePlayerObj);
  this.addPlayerToRoundGroupRank(round, groupId, newAwayPlayerObj);
  this.addPlayerToPromotion(round, groupId, winTeam);
  this.addPlayerToEliminate(round, groupId, lostTeam);
  this.addRoundBattle(round, groupId, data);   //战斗结果保存
};

//验证最终小组排名
KnockoutLeague.prototype.makeFinalGroupRank = function(groupId)
{
    let map = new Map(); //uid => [uid,Obj]
    let rankObjList = [];
    let uidList = this.getGroupPlayerMap(parseInt(groupId));
    for (let i = 0; i < uidList.length; i++) {
        const uid = uidList[i];
        if (!uid) continue;

        let obj = this.league.newPlayerObj(uid, false);
        map.set(uid, obj)
    }

    let round = 1;
    let round_key = this.league.getRoundKey(round, groupId);
    var rankList = this.getRoundRank(round_key);
    for (let i in rankList) 
    {
        const data = rankList[i];
        let playerObj = map.get(data.playerUid);
        playerObj.battleCount +=  data.battleCount
        playerObj.winCount += data.winCount;
        playerObj.drawCount += data.drawCount;
        playerObj.lossCount += data.lossCount;
        playerObj.goalCount += data.goalCount;
        playerObj.missCount +=  data.missCount;
        playerObj.totalScore += data.totalScore;
        playerObj.power = data.power; //使用最近一场的战力
        //playerObj.enrollTime = data.enrollTime;
    }
    
    for (let [k,v ] of map) 
    {
        rankObjList.push(v);
    }

    //排序
    rankObjList.sort(__score_compare_func);

    if (groupId === 8) {
    for (let i in rankObjList) {
        const data = rankObjList[i];
        let totalScore1 = data.totalScore;
        let goalCount1  = data.goalCount;
        let missCount1  = data.missCount;
        let power1      = data.power;
        let diffGoalCount1 = goalCount1 - missCount1;
        //  logger.info("makeFinalGroupRank: groupId, playerUid, totalScore, diff, goalCount, power1",
        //  groupId, data.playerUid, totalScore1, diffGoalCount1, goalCount1, power1);
    }
  } 
    this.finalRank.set(groupId, rankObjList);
};

KnockoutLeague.prototype.makeTotalLastRoundGroupRank = function(groupId, lastRound)
{
    let historyRankObjMap = this.makeLastRoundGroupRank(groupId, lastRound);
    let leagueName =  commonEnum.LEAGUE_NAME.KNOCKOUT + "联赛";
    let config = this.getRoundConfig(lastRound + 1);
    let roundName = config.name;
    let rankObjList = 
    {
        historyRankObjMap: historyRankObjMap,
        leagueName: leagueName,
        roundName: roundName,
    };

  return rankObjList;
};

KnockoutLeague.prototype.makeLastRoundGroupRank = function(groupId, finalRound)
{
  let map = new Map(); //uid => [uid,Obj]
  let historyRankObjMap = new Map();
  let rankObjList = [];
  let uidList = this.getGroupPlayerMap(parseInt(groupId));
  if (!uidList || uidList.length <= 0)
  {
    logger.error("makeLastRoundGroupRank: not found groupId player", groupId, finalRound);
    return historyRankObjMap; 
  }
  
  for (let i = 0; i < uidList.length; i++) {
      const uid = uidList[i];
      if (!uid) continue;
      let obj = this.league.newPlayerObj(uid, false);
      map.set(uid, obj)
  }

  for (let round = 1; round <= finalRound; round++) 
  {
      let round_key = this.league.getRoundKey(round, groupId);
      var rankList = this.getRoundRank(round_key);
      for (let i in rankList) 
      {
          const data = rankList[i];
          let playerObj = map.get(data.playerUid);
          playerObj.battleCount +=  data.battleCount
          playerObj.winCount += data.winCount;
          playerObj.drawCount += data.drawCount;
          playerObj.lossCount += data.lossCount;
          playerObj.goalCount += data.goalCount;
          playerObj.missCount +=  data.missCount;
          playerObj.totalScore += data.totalScore;
          playerObj.power = data.power; //使用最近一场的战力
          if (this.league.checkRobot(data.playerUid)) //过滤掉机器人
          {
            playerObj.power = 0;
          }
      }
  }

  for (let [k,v ] of map) 
  {
      rankObjList.push(v);
  }

  //排序
  rankObjList.sort(__score_compare_func);

  let rank = 1;
  for (let i in rankObjList) {
      const data = rankObjList[i];
      let uid = data.playerUid;
      let winCount = data.winCount;
      let lossCount  = data.lossCount;
      let drawCount  = data.drawCount;
      let totalValue = 0;
      if (!this.league.checkRobot(uid))
      {
        totalValue = this.league.getTeamTotalValue(uid);
      }
      let obj = {
        winCount: winCount,
        lossCount: lossCount,
        drawCount: drawCount,
        rank: rank,
        totalValue: totalValue,
      };

      historyRankObjMap.set(uid, obj);
      //logger.info("makeFinalGroupRank: groupId, playerUid, ", finalRound, groupId, data.playerUid, obj);
      rank++;
  }

  return historyRankObjMap;
};

KnockoutLeague.prototype.getFinalGroupIdRank = function() 
{
  let ctyRank = [];
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
  {
      let rankObjList = this.finalRank.get(groupId);
      if (!rankObjList)
      {
        logger.warn("getFinalGroupIdRank: groupId not rank data", groupId);
        continue;
      }
  
      let rank = 1;
      for (let i in rankObjList) 
      {
        const data = rankObjList[i];
        let uid = data.playerUid;
        if (this.league.checkRobot(uid)) continue;  //过滤机器人
        let simpleRankObj = {
          uid: uid,
          groupId: groupId,
          rank: rank,
          typeId: this.getTypeId(),
        };
    
        ctyRank.push(simpleRankObj);
        rank++;
      }
  }
  return ctyRank;
};

KnockoutLeague.prototype.addPlayerToPromotion = function(round, groupId, uid) 
{
    let round_key = this.league.getRoundKey(round, groupId);
    if (!this.hasPromotionMap(round_key)) {
      this.setPromotionMap(round_key, []);
    }
  
    let promotionUidList = this.getPromotionMap(round_key);
    //检查排名中是否存在uid，如果有就不用添加
    if (utils.hasUidInList(promotionUidList, uid)) {
      logger.error("addPlayerToPromotion: repeat add promotion uid", uid);
      return;
    }
    promotionUidList.push(uid);
};

KnockoutLeague.prototype.addPlayerToEliminate = function(round, groupId, uid)
{
  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasEliminateMap(round_key)) {
    this.setEliminateMap(round_key, []);
  }

  let uidList = this.getEliminateMap(round_key);
  //检查排名中是否存在uid，如果有就不用添加
  if (utils.hasUidInList(uidList, uid)) 
  {
    logger.error("_addPlayerToPromotion: repeat add promotion uid", uid, round_key);
    return;
  }
  //logger.info("addPlayerToEliminate", uidList);
  uidList.push(uid);
};

KnockoutLeague.prototype.addRoundBattle = function(round, groupId, data)
{
    this.setBattleMap(data.battleId, data);
    let round_key =  this.league.getRoundKey(round, groupId);
    if (!this.hasRoundBattleMap(round_key))
    {
      this.setRoundBattleMap(round_key, []);
    }
    let battleUidList = this.getRoundBattleMap(round_key);
    battleUidList.push(data.battleId);
};

KnockoutLeague.prototype.addPlayerToRoundGroupRank = function( round, groupId, playerObj)
{
  if (!playerObj) {
    logger.error("addPlayerToRoundGroupRank: not playerObj uid", playerObj);
    return;
  }

  let round_key = this.league.getRoundKey(round, groupId);
  if (!this.hasRoundRank(round_key)) {
    this.setRoundRank(round_key, []);
  }

  var rankObjList = this.getRoundRank(round_key);
  rankObjList.push(utils.clone(playerObj));
  rankObjList.sort(__score_compare_func);
  this.setRoundRank(round_key, rankObjList);

  rankObjList = this.getRoundRank(round_key);
};

//发送消息通知客户端比赛即将开始
//找到该轮次所有的人,打包出来
KnockoutLeague.prototype.commonWait = function() 
{
  let roundNotifyData = [];
  let round = this.currRound;
  if (!this.checkSendWaitStartNtf(round))
  {
    logger.info("KnockoutLeague commonWait nextRound", round);
    roundNotifyData = this.getRoundNtfPlayerData(round);
    this.setSendWaitStartNtf(round);
  }
  return roundNotifyData;
};

//发送的玩家数据已经生成好了，需要从记录里面去拿
KnockoutLeague.prototype.getRoundNtfPlayerData = function(round)
{
  let roundNtfPlayerStartData = [];
  if (round > this.currRound)
  {
    return roundNtfPlayerStartData;
  }

  let leagueName = commonEnum.LEAGUE_NAME.KNOCKOUT + "联赛";
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) {
      let round_key = this.league.getRoundKey(round, groupId);
      let battleUidList = this.getRoundBattleMap(round_key);
      if (!battleUidList || battleUidList.length <= 0) continue;
      
      for (let idx in battleUidList) {
       let battleId = battleUidList[idx];
       let battleObj = this.getBattleMap(battleId);
       if (!battleObj) continue;
       
       let home = battleObj.home;
       let away = battleObj.away;
       let roomUid = battleObj.roomUid;

       //过滤有机器人的情况
       if (this.league.checkRobot(home) || this.league.checkRobot(away)) continue;
       
       let teamA = this.league.getSimpleTeamInfo(home);
       let teamB = this.league.getSimpleTeamInfo(away);
       let homeSimpleRolePrepareInfo = {
          uid: home,
          roomUid: roomUid,
          competitorUid: away,
          teamA: teamA,
          teamB: teamB,
          beginTime: battleObj.beginTime,
          leagueName: leagueName,
          groupId: groupId,
          round: round,
        };

       let awaySimpleRolePrepareInfo = {
          uid: away,
          roomUid: roomUid,
          competitorUid: home,
          teamA: teamA,
          teamB: teamB,
          beginTime: battleObj.beginTime,
          leagueName: leagueName,
          groupId: groupId,
          round: round,
         };
       roundNtfPlayerStartData.push(homeSimpleRolePrepareInfo);
       roundNtfPlayerStartData.push(awaySimpleRolePrepareInfo);
      }
    }

    logger.info("KnockoutLeague getRoundNtfPlayerData", round, roundNtfPlayerStartData.length);
    return roundNtfPlayerStartData;
};

KnockoutLeague.prototype.commonPlayRecord = function(status_obj)
{
  let round = this.currRound;
  let leagueName = commonEnum.LEAGUE_NAME.KNOCKOUT + "联赛";
  let data = {
    typeId: this.getTypeId(),
    leagueName: leagueName,
    round: round,
  };

  let isSend = false;
  if (!this.checkSendStartNtf(round))
  {
    logger.info("KnockoutLeague commonPlay nextRound", round);
    this.setSendStartNtf(this.currRound);
    isSend = true;
  }

  let battleStatus = this.getConfigBattleStatus(round)
  if(commonEnum.LEAGUE_BATTLE_STATUS.RUNNING !== battleStatus)
  {
    this.setBattleStatus(round, commonEnum.LEAGUE_BATTLE_STATUS.RUNNING);
  }

  return {isNeedSend: isSend, playRecord: data};
};

KnockoutLeague.prototype.commonSettle = function(status_obj) 
{   
    logger.info("KKT commonSettle", this.currRound);
    let nextRound = this.currRound + 1;
    this.genNextRoundBattleQueue(nextRound);
    this.setBattleStatus(this.currRound, commonEnum.LEAGUE_BATTLE_STATUS.END);
    this.league.saveKnockout();
    return true;
};

KnockoutLeague.prototype.getComNotifyList = function(round, isPromotion)
{
  let notifyMailList = [];
  let leagueName = commonEnum.LEAGUE_NAME.KNOCKOUT + "联赛";
  for (let groupId = 1; groupId <= this.groupMaxCount; groupId++) 
  {
      let notifyType = 0;
      let uidList = [];
      if (isPromotion)
      {      
        uidList = this.getPromotionUidListByGroupId(round, groupId);
        notifyType = commonEnum.FINAL_MAIL_NOTIFY_TYPE.PROMOTION;
      }else
      {
        uidList = this.getEliminateUidListByGroupId(round, groupId);
        notifyType = commonEnum.FINAL_MAIL_NOTIFY_TYPE.ELIMINATE;
      }

      for(let idx in uidList)
      { 
        let pUid = uidList[idx];
        if (this.league.checkRobot(pUid)) //机器人过滤
        {
           continue;
        }

        let pBattleId = this.getBattleIdByRoundGroupId(round, groupId, pUid);
        //logger.info("getNotifyMailPromotionList: pBattleId", pBattleId);
        if (!pBattleId)
        {
          logger.info("getComNotifyList: not found pBattleId", round, groupId, pUid, pBattleId);
          continue;
        }

        let battleObj =  this.getBattleMap(pBattleId);
        if (!battleObj)
        {
          logger.info("getComNotifyList: not found battleObj", round, groupId, pUid, pBattleId);
          continue;
        }

        let pObj = this.league.comGetNotifyMailObj(pUid, battleObj, this.getTypeId(), notifyType, leagueName, round);
        notifyMailList.push(pObj);
      } 
    }

    logger.info("getComNotifyList: notifyMailList", notifyMailList.length);
    return notifyMailList;
};

KnockoutLeague.prototype.getNotifyMailPromotionList = function(round)
{
  return this.getComNotifyList(round, true);
};

KnockoutLeague.prototype.getNotifyMailEliminateList = function(round)
{
  return this.getComNotifyList(round, false);
};

KnockoutLeague.prototype.commonNextRound = function(status_obj)
{
  this.currRound = this.currRound + 1;
  logger.info("KnockoutLeague commonNextRound currRound", this.currRound);
  return true;
};

//最终结算
KnockoutLeague.prototype.finalSettle = function()
{
    //淘汰升级的玩家晋级到业余组
    this.promotionPlayer();
    this.setBattleStatus(this.currRound, commonEnum.LEAGUE_BATTLE_STATUS.END);
    this.league.saveKnockout();
    return true;
};

//生成下一轮比赛战斗队列
//由于不存在超人的情况,只考虑少人情况
KnockoutLeague.prototype.genNextRoundBattleQueue = function(nextRound) 
{
  var config = this.getRoundConfig(nextRound);
  let required_num = config.required_num;
  let round_key = this.league.getRoundKey(nextRound, 1);
  let homeLength =  this.homeTeam.length;
  let awayLength = this.awayTeam.length;
  let homeRandomUidList = utils.arrayIndexRandom(this.homeTeam);
  let beginTime = this.league.leagueFsm.getRoundBattleTime(this.typeId, nextRound);
  //logger.info("genNextRoundBattleQueue", this.homeTeam, this.awayTeam, homeRandomUidList);

  let robot_idx = 1; //机器人的索引为 20-robot_num ~ 20-1
  for (let idx = 0; idx < required_num; idx++) {
      let uid1 = "";
      let uid2 = "";
      if (idx >= homeLength)
      {
        uid1 = "robot_" + robot_idx  + "_"+ utils.syncCreateUid();
        robot_idx++;
      }else
      {
        uid1 = homeRandomUidList[idx];
      }
    
      if (idx >= awayLength)
      {
        uid2 = "robot_" + robot_idx  + "_"+ utils.syncCreateUid();
        robot_idx++;
      }
      else
      {
        uid2 = this.awayTeam[idx];
      }

      let obj = {
        Host: uid1, //客场
        Gust: uid2,
        BeginTime: beginTime,
      };

      //logger.info("genNextRoundBattleQueue", nextRound, obj);

      if (!this.battleScheduleMap.has(round_key)) {
        this.battleScheduleMap.set(round_key, []);
      }

      let objList = this.battleScheduleMap.get(round_key);
      objList.push(obj);
      this.battleScheduleMap.set(round_key, objList);
  }
};

//晋级到业余联赛
KnockoutLeague.prototype.promotionPlayer = function()
{
    let uidList = this.getFinalPrUidList();
    this.league.promotionPlayerToTeam(uidList);
};

KnockoutLeague.prototype.onShowKnockout = function(status_obj)
{
    let round = this.currRound;
    if (round === commonEnum.LEAGUE_RUN_ROUND.KnockoutGroupSplitPrepare && !this.checkRunning(round))
    {   
        this.SplitPrepare(status_obj);
        this.commonSettle();
    }
    return true;
};

//最近晋级玩家列表
KnockoutLeague.prototype.getFinalPrUidList = function()
{
    let uidList = [];
    let last_round = this.getLastRound();
    
    if (!last_round) return uidList;

    uidList = this.getPromotionUidList(last_round);
    logger.info("getFinalPrUidList uidList, length", uidList, uidList.length);
    return uidList;
};

//************************************************************测试函数************************** */

//************************************************************消息************************** */
KnockoutLeague.prototype.getCurrRound = function()
{
  return this.currRound;
};

KnockoutLeague.prototype.getFixCurrRound = function()
{
  let realCurrRound = this.getCurrRound();
  let fixCurrRound = realCurrRound;
  let battleStatus = this.getConfigBattleStatus(realCurrRound);
  if(realCurrRound > 0 && commonEnum.LEAGUE_BATTLE_STATUS.END !== battleStatus) //比赛未开始、取上一轮
  {
    fixCurrRound = realCurrRound - 1;
    logger.info("KnockoutLeague getFixCurrRound: realCurrRound, fixCurrRound", realCurrRound, fixCurrRound);
  }

  return fixCurrRound;
};

KnockoutLeague.prototype.getBattleIdByRoundGroupId = function(round, groupId, uid)
{
    let battleId = "";
    let round_key = this.league.getRoundKey(round, groupId);
    let battleUidList =  this.getRoundBattleMap(round_key);
    if (!battleUidList)
    {
        logger.info("getBattleIdByRoundGroupId: not found battleUidList! round_key", round_key);
        return battleId;
    }

    for(let idx in battleUidList)
    {
      let __battleId = battleUidList[idx];
      let battleObj =  this.getBattleMap(__battleId);
      if (uid === battleObj.home || uid === battleObj.away )
      {
        battleId = __battleId;
        break;
      }
    }
    
    return battleId;
};

//获取赛程榜
KnockoutLeague.prototype.getRoundScheduleInfo = function(round, groupId)
{
  let roundScheduleData = 
  {
    typeId: this.getTypeId(),
    roundId: round,
    name: "",
    groupId: groupId,
    finalRound: this.getCurrFinalRound(false),
    maxGroupCount: this.groupMaxCount,
    scheduleTime: this.league.getBattleTime(this.getTypeId()),
    maxRound: this.getMaxRound(),
    scheduleList: [],
  };

  if (round <= 0)
  {
    logger.error("getRoundScheduleInfo: round less 0!", round);
    return roundScheduleData;
  }

  let config= this.getRoundConfig(round);
  if (!config)
  {
    logger.error("getRoundScheduleInfo: round error!", round);
    return roundScheduleData;
  }

  roundScheduleData.name = config.name;
  if (!groupId || groupId <= 0)
  {
    logger.error("getRoundScheduleInfo: groupId error!", round, groupId);
    return roundScheduleData;
  }

  logger.info("getRoundScheduleInfo: round, groupId", round, groupId);
  //不能超过当前轮次，因为当期轮次还没有完成，无法获取下一轮的赛程
  let currRoundBattleStatus = this.getConfigBattleStatus(this.currRound);
  if (round > this.currRound && currRoundBattleStatus === commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN)
  {
    logger.error("getRoundScheduleInfo: round more than currRound!", round, this.currRound);
    return roundScheduleData;
  }

  let required_every_group_num = config.required_num;
  
  let roundGroupId = this.league.getRoundKey(round, groupId);
  let battleObjList = this.getBattleScheduleMap(roundGroupId);
  let battleUidList = this.getRoundBattleMap(roundGroupId);
  if(battleObjList)
  {
      this.league.preProcessBattleList(battleObjList, required_every_group_num);//人数不够打乱对战列表
  }

  if (!battleObjList) 
  {
    logger.error("getRoundScheduleInfo: battleObjList error!", round, groupId);
    return roundScheduleData;
  }
  
  let scheduleList = [];
  let battleStatus = this.getConfigBattleStatus(round);
  for (const idx in battleObjList) 
  {
    let obj = battleObjList[idx];
    let winTeam = "";
    if(battleUidList)
    {
        for(let i in battleUidList)
        {
            let battleObj =this.getBattleMap(battleUidList[i]);
            if(battleObj.home === obj.Host)
            {
                //logger.error("：：：：：：：：：：：：：：：：", obj, battleObj);
                winTeam = battleObj.winTeam;
            }
        }
    }

    if (!obj) continue;
    let uid1 = obj.Host;
    let uid2 = obj.Gust;
    let beginTime = obj.BeginTime;
    let roomUid = "";
    let teamAScore = 0;
    let teamBScore = 0;
    let robotA = "robot_" === uid1.substr(0, 6); //home是否为机器人
    let robotB = "robot_" === uid2.substr(0, 6); //away是否为机器人
    //if (robotA && robotB) continue;
    if (battleStatus !== commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN) {
        let battleId = this.getBattleIdByRoundGroupId(round, groupId, uid1);
        let battleObj =  this.getBattleMap(battleId);
        roomUid = battleObj.roomUid;
        teamAScore = battleObj.homeScore;
        teamBScore = battleObj.awayScore;
    }

    if (robotA)
    {
      uid1 = "";
    }

    if (robotB)
    {
      uid2 = "";
    }

    let ScheduleGroupInfo = {
      battleId: roomUid,
      teamA: uid1,
      teamB: uid2,
      teamAScore: teamAScore,
      teamBScore: teamBScore,
      beginTime: beginTime,
      status: battleStatus,
      teamAName: "",
      teamBName: "",
      teamAfaceUrl: "",
      teamBfaceUrl: "",
      winTeam: winTeam,
    };

    scheduleList.push(ScheduleGroupInfo);
  }

  roundScheduleData.scheduleList = scheduleList;
  logger.info("roundScheduleData", roundScheduleData.scheduleList.length);
  return roundScheduleData;
};

KnockoutLeague.prototype.getRoundScoreRankInfo = function(round, groupId)
{
  let roundScoreRankData = 
  {
    typeId: this.getTypeId(),
    roundId: round,
    name: "",
    groupId: groupId,
    finalRound: this.getCurrFinalRound(true),
    maxGroupCount: this.groupMaxCount,
    scheduleTime: this.league.getBattleTime(this.getTypeId()),
    maxRound: this.getMaxRound(),
    teamInfoList: [],
  };

  if (round <= 0)
  {
    logger.error("getRoundScoreRankInfo: round less 0!", round);
    return roundScoreRankData;
  }
  
  if (round === this.getFinalRound(true))
  {
    return this.getFinalScoreRankInfo(groupId);
  }

  let config= this.getRoundConfig(round);
  if (!config)
  {
    logger.error("getRoundScoreRankInfo: round error!", round);
    return roundScoreRankData;
  }

  roundScoreRankData.name = config.name;
  if (!groupId || groupId <= 0)
  {
    logger.error("getRoundScoreRankInfo: groupId error!", round, groupId);
    return roundScoreRankData;
  }

  logger.info("getRoundScoreRankInfo: round, groupId", round, groupId);
  //不能超过当前轮次，因为当期轮次还没有完成，无法获取下一轮的赛程
  if (round > this.currRound)
  {
    logger.error("getRoundScoreRankInfo: round more than currRound!", round, this.currRound);
    return roundScoreRankData;
  }
  
  let battleStatus = this.getConfigBattleStatus(round);
  if (battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.NOT_BEGIN)
  {
    logger.error("getRoundScoreRankInfo: battleStatus error! round, groupId, battleStatus", round, groupId, battleStatus);
    return roundScoreRankData;
  }

  let teamInfoList = [];
  if (commonEnum.LEAGUE_BATTLE_STATUS.END === battleStatus)
  {
    let roundGroupId = this.league.getRoundKey(round, groupId);
    let rankObjList = this.getRoundRank(roundGroupId);
    if (!rankObjList) 
    {
      logger.error("getRoundScoreRankInfo: rankObjList error!", round, groupId);
      return roundScoreRankData;
    }

    for(let idx in rankObjList)
    {
      let rank = rankObjList[idx];
      if ("" === rank.playerUid) continue;
      if (this.league.checkRobot(rank.playerUid)) continue;

      let knockoutStatus = this.getKnockoutStatus(rank.playerUid);
      let playerObj = {
        playerUid: rank.playerUid,
        name: "",
        faceIcon: 0,
        battleCount: rank.battleCount,
        winCount: rank.winCount,
        drawCount: rank.drawCount,
        lossCount: rank.lossCount,
        goalCount: rank.goalCount,
        missCount: rank.missCount,
        totalScore: rank.totalScore,
        knockoutStatus: knockoutStatus,
      }
      teamInfoList.push(playerObj);
    }
  }

  roundScoreRankData.teamInfoList = teamInfoList;
  logger.info("roundScoreRankData", roundScoreRankData);
  return roundScoreRankData;
};

KnockoutLeague.prototype.getFinalScoreRankInfo = function(groupId)
{
  let roundScoreRankData = 
  {
    typeId: this.getTypeId(),
    roundId: this.getLastRound() + 1,
    name: "总榜",
    groupId: groupId,
    finalRound: this.getFinalRound(false),
    maxGroupCount: this.groupMaxCount,
    scheduleTime: this.league.getBattleTime(this.getTypeId()),
    maxRound: this.getMaxRound(),
    teamInfoList: [],
  };

  let lastRound = this.getLastRound();
  if (lastRound != this.getCurrRound()) //还没有到最后一轮
  {
    logger.error("getRoundScoreRankInfo: lastRound error! round, groupId, battleStatus", lastRound, groupId);
    return roundScoreRankData;
  }

  let battleStatus = this.getConfigBattleStatus(lastRound);
  if (battleStatus !== commonEnum.LEAGUE_BATTLE_STATUS.END) //还没有结束
  {
    logger.error("getRoundScoreRankInfo: battleStatus error! round, groupId, battleStatus", lastRound, groupId, battleStatus);
    return roundScoreRankData;
  }

  let rankObjList = this.finalRank.get(groupId);
  if (!rankObjList)
  {
    logger.error("getRoundScoreRankInfo: rankObjList error! round, groupId, battleStatus", lastRound, groupId, battleStatus);
    return roundScoreRankData;
  }

  let teamInfoList = [];
  for (let i in rankObjList) 
  {
    const rank = rankObjList[i];
    let uid = rank.playerUid;
    if ("" === rank.playerUid) continue;
    if (this.league.checkRobot(uid)) continue;
    let knockoutStatus = this.getKnockoutStatus(rank.playerUid);
    let playerObj = {
      playerUid: rank.playerUid,
      name: "",
      faceIcon: 0,
      battleCount: rank.battleCount,
      winCount: rank.winCount,
      drawCount: rank.drawCount,
      lossCount: rank.lossCount,
      goalCount: rank.goalCount,
      missCount: rank.missCount,
      totalScore: rank.totalScore,
      knockoutStatus: knockoutStatus,
    }
    //logger.info("rank.playerUid 2", rank.playerUid);
    teamInfoList.push(playerObj);
  }
  
  roundScoreRankData.teamInfoList = teamInfoList;
  //logger.info("roundScoreRankData", roundScoreRankData);
  return roundScoreRankData;
};

KnockoutLeague.prototype.isHomeTeam = function(uid)
{
  return utils.hasUidInList(this.homeTeam, uid);
};

KnockoutLeague.prototype.isAwayTeam = function(uid)
{
  return utils.hasUidInList(this.awayTeam, uid);
};

KnockoutLeague.prototype.isWin = function(uid)
{
  let round_key = this.league.getRoundKey(1, 1);
  let promotionUidList = this.getPromotionMap(round_key);
  //检查排名中是否存在uid，如果有就不用添加
  if (!utils.hasUidInList(promotionUidList, uid)) {
    return false;
  }
  return true;
};

//获取天天赛状态
KnockoutLeague.prototype.getKnockoutStatus = function(uid)
{
  let knockoutStatus = commonEnum.KNOCOUT_LEVEL.DOWNGRADE;
  //REMAIN: 1,     //保级
  //PROMOTION: 2,  //晋级
  //HOME为社区联赛
  //AWAY为专业联赛
  if (this.isHomeTeam(uid))  //
  {
    if (this.isWin(uid))  // 赢了升级,输了保级
    {
      knockoutStatus = commonEnum.KNOCOUT_LEVEL.PROMOTION;
    }else
    {
      knockoutStatus = commonEnum.KNOCOUT_LEVEL.REMAIN;
    }
    return knockoutStatus;
  }

  if (this.isWin(uid))
  {
    if (this.isWin(uid))  // 赢了保级,输了降级
    {
      knockoutStatus = commonEnum.KNOCOUT_LEVEL.REMAIN;
    }else
    {
      knockoutStatus = commonEnum.KNOCOUT_LEVEL.DOWNGRADE;
    }
  }

  return knockoutStatus;
};

KnockoutLeague.prototype.checkLastRoundEnd = function()
{
  let lastRound = this.getLastRound();
  let currRound = this.getCurrRound();
  let battleStatus = this.getConfigBattleStatus(currRound);
  if (lastRound === currRound && battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END) //还没有到最后一轮
  {
    return true;
  }

  logger.info("checkLastRoundEnd: lastRound, currRound, battleStatus", lastRound, currRound, battleStatus);
  return false;
};

KnockoutLeague.prototype.getCurrScheduleInfo =  function(groupId)
{
  let round = this.getFixCurrRound();
  let roundScheduleData = this.getRoundScheduleInfo(round, groupId);
  return roundScheduleData;
};

KnockoutLeague.prototype.getCurrScoreRankInfo = function(groupId)
{
  let round = this.getFixCurrRound();
  let roundScoreRankData = this.getRoundScoreRankInfo(round, groupId);
  return roundScoreRankData;
};

KnockoutLeague.prototype.getGroupIdByUid = function(uid)
{
  let groupId = this.getPlayerGroupMap(uid);
  if (!groupId)
  {
    return 0;
  }

  return groupId;
};

//检查玩家在某一小组，某一轮次是否
KnockoutLeague.prototype.checkBattleScheduleByUid = function(round, groupId, uid)
{
  let battleId = this.getBattleIdByRoundGroupId(round, groupId, uid);
  if ( "" !== battleId)
  {
    return true;
  }

  return false;
}

KnockoutLeague.prototype.getBattleObjByRoundGroupId = function(round, groupId, uid)
{
    let round_key = this.league.getRoundKey(round, groupId)
    let battleUidList = this.getRoundBattleMap(round_key);
    if (!battleUidList)
    {
      logger.error("getBattleObjByRoundGroupId: get battleUidList failed!", round, groupId);
      return null;
    }
    
    for (let idx in battleUidList) 
    {
      let battleId = battleUidList[idx];
      let data = this.getBattleMap(battleId);
      if (!data) continue;
      let home = data.home;
      let away = data.away;
      if (home === uid || away === uid)
      {
        return data;
      }
    }

    return null;
};

//已经结束比赛的要上积分
KnockoutLeague.prototype.addHistory = function(groupId, round, battleObj)
{
  let uidA = battleObj.Host;
  let uidB = battleObj.Gust;
  let beginTime = battleObj.BeginTime;
  let teamA = this.league.getSimpleTeamInfo(uidA);
  let teamB = this.league.getSimpleTeamInfo(uidB);
  let battleStatus = this.getConfigBattleStatus(round);
  if (battleStatus < 0)
  {
    logger.error("addHistory: battleStatus error! round, groupId, battleStatus", round, groupId, battleStatus);
    return null;
  }

  let teamAScore = 0;
  let teamBScore = 0;
  //只有打完了才有分数
  if (battleStatus === commonEnum.LEAGUE_BATTLE_STATUS.END)
  {
    let data = this.getBattleObjByRoundGroupId(round, groupId, uidA);
    if (!battleObj)
    {
      logger.error("addHistory: getBattleObjByRoundGroupId failed", round, groupId, uidA);
      return null;
    }
    teamAScore = data.homeScore;
    teamBScore = data.awayScore;
  }

  let isNull = 0;
  if (this.league.checkRobot(uidA))
  {
    teamA.playerUid = "";
    isNull = 1;
  }

  if (this.league.checkRobot(uidB))
  {
    teamB.playerUid = "";
    isNull = 1;
  }

  let history = {
    typeId: this.typeId,
    groupId: groupId,
    roundId: round,
    beginTime: beginTime,
    teamA: teamA,
    teamB: teamB,
    teamAScore: teamAScore,
    teamBScore: teamBScore,
    status: battleStatus,
    isNull: isNull
  };
  return history;
};

KnockoutLeague.prototype.getHistory = function(uid)
{
  if (!uid)
  {
    logger.error("getHistory: not uid");
    return;
  }

  let personalHistoryInfo = [];
  //检查玩家是否参赛
  if(!this.getPlayerMap(uid)) //未参赛
  {
    logger.warn("getHistory: player not join here", uid);
    return personalHistoryInfo;
  }

  let groupId = this.getPlayerGroupMap(uid);
  if (!groupId || groupId === 0)
  {
    logger.error("getHistory: not found groupId", uid);
    return personalHistoryInfo;
  }

  logger.info("getHistory", groupId, uid, this.currRound);
  if (this.currRound <= 0)
  {
    return personalHistoryInfo;
  }

    //把赛程拿出来
  for (let round = 1; round <= this.currRound; round++) 
  {
    let round_key = this.league.getRoundKey(round, groupId)
    let objList = this.getBattleScheduleMap(round_key);
    logger.error("getHistory: ", round_key, objList);
    if (!objList) continue;
    for(let idx in objList)
    {
      let battleObj = objList[idx];
      if (!battleObj) continue;
      if (uid === battleObj.Host || uid === battleObj.Gust)
      {
        let history = this.addHistory(groupId, round, battleObj);
        if (!history)
        {
          continue;
        }
        personalHistoryInfo.push(history);
      }
    }
  }
  return personalHistoryInfo;
};

KnockoutLeague.prototype.getMaxGroupCount = function()
{
  return this.groupMaxCount;
};

KnockoutLeague.prototype.getMaxRound = function()
{
  let lastRound = this.getLastRound();
  return lastRound;
};

//*************************************辅助函数*************************************************** */
//battleScheduleMap
KnockoutLeague.prototype.setBattleScheduleMap = function(round_key, battleUidList) 
{
    this.battleScheduleMap.set(round_key, battleUidList);
};

KnockoutLeague.prototype.getBattleScheduleMap = function(round_key) 
{
    return this.battleScheduleMap.get(round_key);
};

KnockoutLeague.prototype.hasBattleScheduleMap = function(round_key) 
{
    return this.battleScheduleMap.has(round_key);
};

//playerMap
KnockoutLeague.prototype.setPlayerMap = function(uid, playerObj) 
{
    this.playerMap.set(uid, playerObj);
};

KnockoutLeague.prototype.getPlayerMap = function(uid) 
{
    return this.playerMap.get(uid);
};

KnockoutLeague.prototype.hasPlayerMap = function(uid) 
{
    return this.playerMap.has(uid);
};

//playerGroupMap
KnockoutLeague.prototype.setPlayerGroupMap = function(uid, groupId) 
{
    this.playerGroupMap.set(uid, groupId);
};

KnockoutLeague.prototype.getPlayerGroupMap = function(uid) 
{
    return this.playerGroupMap.get(uid);
};

KnockoutLeague.prototype.hasPlayerGroupMap = function(uid) 
{
    return this.playerGroupMap.has(uid);
};

//groupPlayerMap
KnockoutLeague.prototype.setGroupPlayerMap = function(groupId, uidList) 
{
    this.groupPlayerMap.set(groupId, uidList);
};

KnockoutLeague.prototype.getGroupPlayerMap = function(groupId) 
{
    return this.groupPlayerMap.get(groupId);
};

KnockoutLeague.prototype.hasGroupPlayerMap = function(groupId) 
{
    return this.groupPlayerMap.has(groupId);
};

//PromotionMap
KnockoutLeague.prototype.setPromotionMap = function(round_key, promotionUidList) 
{
    this.promotionMap.set(round_key, promotionUidList);
};

KnockoutLeague.prototype.getPromotionMap = function(round_key) 
{
    return this.promotionMap.get(round_key);
};

KnockoutLeague.prototype.hasPromotionMap = function(round_key) 
{
    return this.promotionMap.has(round_key);
};

//EliminateMap
KnockoutLeague.prototype.setEliminateMap = function( round_key, uidList) {
  this.eliminateMap.set(round_key, uidList);
};

KnockoutLeague.prototype.getEliminateMap = function(round_key) {
  return this.eliminateMap.get(round_key);
};

KnockoutLeague.prototype.hasEliminateMap = function(round_key) {
  return this.eliminateMap.has(round_key);
};

//RoundBattleMap
KnockoutLeague.prototype.setRoundBattleMap = function(round_key, battleUidList) 
{
    this.roundBattleMap.set(round_key, battleUidList);
};

KnockoutLeague.prototype.getRoundBattleMap = function(round_key) 
{
    return this.roundBattleMap.get(round_key);
};

KnockoutLeague.prototype.hasRoundBattleMap = function(round_key) 
{
    return this.roundBattleMap.has(round_key);
};

//battleMap
KnockoutLeague.prototype.setBattleMap = function(battleUid, battleObj) 
{
    this.battleMap.set(battleUid, battleObj);
};

KnockoutLeague.prototype.getBattleMap = function(battleUid) 
{
    return this.battleMap.get(battleUid);
};

KnockoutLeague.prototype.hasBattleMap = function(battleUid) 
{
    return this.battleMap.has(battleUid);
};

//roundRank
KnockoutLeague.prototype.setRoundRank = function(round_key, rankList) 
{
    this.roundRank.set(round_key, rankList);
};

KnockoutLeague.prototype.getRoundRank = function(round_key) 
{
    return this.roundRank.get(round_key);
};

KnockoutLeague.prototype.hasRoundRank = function(round_key) 
{
    return this.roundRank.has(round_key);
};

KnockoutLeague.prototype.getTypeId = function() 
{
    return this.typeId
};

//*************************************辅助函数*************************************************** */