/**
 * Created by sea on 2019/6/27.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var timeUtils = require("../../util/timeUtils");
var dataApi = require('../../util/dataApi');
var commonEnum = require('../../../../shared/enum');
var debugConfig = require('../../../config/debugConfig');

var FootballGround = function (player) {
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.adminGround = new Map(); //行政楼
    this.mainGround = new Map(); //主球场
    this.trainGround = new Map(); //训练场
    this.transferGround = new Map(); //转会中心
    this.hospitalGround = new Map(); //医疗中心
    this.notableGround = new Map(); //名人堂
    this.prestige = 0; //声望值
    this.ballFan = 0; //球迷数
    this.isOpen = commonEnum.FOOTBALL_GROUND_STATUS.CLOSE;  //是否开房  0未开放  1已开放
    this.trainCount = 0; //训练点数
    this.refreshTime = 1; //刷新入住的时间
    this.ballHandbook = [];  //记录已激活球员ID
    this.ballHandbookActual = 0;   //球员实力
    this.ballComHandbook = initHandbook();  //记录已激活组合
    this.ballComHandbookActual = 0;  //组合总实力
    this.ballComCount = 0;      //组合图鉴激活个数
    this.retirementList = [];   //退役列表
    this.notablePrestige = 0;   //名人堂声望
    this.isGetBallFansRewrd = 0;    //0未领取  1已领取
    this.getBallFansRewrdTime = 0;   //领取球迷奖励时间
    this.firstJoinHofList = [];      //首次进入名人堂名单

    this.groundMatch = {};      //球场争夺战数据

    this.maxBattleLockTime = 5 * 60 * 1000;    //战斗锁定最大时间 5分钟
};

//组合图鉴初始化
var initHandbook = function(){
    let ball = [];
    let config = dataApi.allData.data["CombinationHandbook"];
    let index = 0;
    for(let i in config){
        let hero = config[i].Player.split("|");
        ball[index] = {};
        ball[index].bookId = config[i].ID;
        ball[index].player = [];
        let id = 0;
        for(let i in hero){
            ball[index].player[id] = {}
            ball[index].player[id].id = Number(hero[i]);
            ball[index].player[id].isHave = 0;   //0未激活  1已激活
            id++;
        }
        ball[index].isCalc = 0;    //0未计算过   1已计算过
        index++;
    } 
    return ball;
}

util.inherits(FootballGround, EventEmitter);

module.exports = FootballGround;

//读数据库
FootballGround.prototype.initByDB = function (doc) {
    this.uid = doc.uid;
    this.adminGround = utils.toMap(doc.adminGround) || new Map();
    this.mainGround = utils.toMap(doc.mainGround) || new Map();
    this.trainGround = utils.toMap(doc.trainGround) || new Map();
    this.transferGround = utils.toMap(doc.transferGround) || new Map();
    this.hospitalGround = utils.toMap(doc.hospitalGround) || new Map();
    this.notableGround = utils.toMap(doc.notableGround) || new Map();
    this.prestige = doc.prestige || 0;
    this.ballFan = doc.ballFan || 0;
    this.trainCount = doc.trainCount || 0;
    this.ballHandbook = doc.ballHandbook || [];
    this.ballHandbookActual = doc.ballHandbookActual ||0;
    this.ballComHandbook = doc.ballComHandbook || [];
    this.ballComHandbookActual = doc.ballComHandbookActual || 0;
    this.ballComCount = doc.ballComCount || 0;
    this.retirementList = doc.retirementList || [];
    this.notablePrestige = doc.notablePrestige || 0;
    this.isOpen = doc.isOpen || commonEnum.FOOTBALL_GROUND_STATUS.CLOSE;
    this.isGetBallFansRewrd = doc.isGetBallFansRewrd || 0;
    this.getBallFansRewrdTime = doc.getBallFansRewrdTime || 0;
    this.firstJoinHofList = doc.firstJoinHofList || [];

    //june hide
    // if(!debugConfig.groundMatchHide) {
        if(doc.groundMatch && doc.groundMatch.fieldList) {
            this.groundMatch = doc.groundMatch;
            //logger.debug("doc.groundMatch ", doc.groundMatch);
        }else {
            //数据兼容
            //logger.debug("initGroundMatch ", doc.groundMatch);
            this.initGroundMatch();
            //this.player.saveBallGround();
        }
    // }

};

//数据修复
FootballGround.prototype.fixGroundData = function () { 
    let uid = this.uid;
    let ground = {};
    for (let i = 1; i < 7; ++i) {
        switch (i) {
            case 1:
                ground = this.adminGround.get(uid);
                if(!ground) {
                    break;
                }
                if(!ground.rewardPack){
                    ground.rewardPack = this.initRewardPack();
                }
                break;
            case 2:
                ground = this.mainGround.get(uid);
                if(!ground) {
                    break;
                }
                if(!ground.rewardPack){
                    ground.rewardPack = this.initRewardPack();
                }
                break;
            case 3:
                ground = this.trainGround.get(uid);
                if(!ground) {
                    break;
                }
                //泡澡  初始化数据
                for(let k = 0; k < ground.TrainPos.length; ++k) {
                    if(ground.TrainPos[k].type == undefined) {   //加成类型
                        if(ground.TrainPos[k].pos !== "") {
                            let hero = this.player.getOneHero(ground.TrainPos[k].pos);
                            hero.isTrain = 0;  
                        }
                        ground.TrainPos[k].pos = "";
                        ground.TrainPos[k].type = 0;     //训练类型  1素质  2防守  3射术  4技巧 5定位球 6守门
                        if (k === 0) {
                            ground.TrainPos[k].state = 2;
                        } else {
                            ground.TrainPos[k].state = 1; //状态  1锁住 2可添加 3训练中 4已完成
                        }
                        ground.TrainPos[k].beginTime = 0; //开始训练的时间   
                    }
                }

                if(!ground.rewardPack){
                    ground.rewardPack = this.initRewardPack();
                }
                break;
            case 4:
                ground = this.transferGround.get(uid);
                if(!ground) {
                    break;
                }
                if(!ground.rewardPack){
                    ground.rewardPack = this.initRewardPack();
                }
                if(ground.TransferPos.length > 5) {
                    // let transferPos = {};
                    // transferPos.resId = 0;
                    // transferPos.level = 1;
                    // transferPos.name = "";
                    // transferPos.actual = 0;  //实力
                    // transferPos.state = 1; //状态  1锁住  2可添加  3已有人
                    ground.TransferPos.pop();
                }
                break;
            case 5:
                ground = this.hospitalGround.get(uid);
                if(!ground) {
                    break;
                }
                if(!ground.rewardPack){
                    ground.rewardPack = this.initRewardPack();
                }
                break;
            case 6:
                ground = this.notableGround.get(uid);
                if(!ground) {
                    break;
                }
                if(!ground.rewardPack){
                    ground.rewardPack = this.initRewardPack();
                }

                for(let i in ground.NotablePos) {
                    if(typeof(ground.NotablePos[i].uid) === "undefined") {
                        ground.NotablePos[i].uid = "";
                    }
                }
            break;
        }
    }

    //修复球场记录数据初始化
    if(this.groundMatch && this.groundMatch.fieldList) {
        let isNeedToFixResData = false;
        for(let i=0;i<3;i++) {
            if(!this.groundMatch.fieldList[i].recordList) {
                this.groundMatch.fieldList[i].recordList = [];
            }
            if(!this.groundMatch.occupyFieldList[i].recordList) {
                this.groundMatch.occupyFieldList[i].recordList = [];
            }
            //修复resId = 0的情况
            if(this.groundMatch.fieldList[i].resId === 0) {
                isNeedToFixResData = true;
            }
        }
        if(isNeedToFixResData) {
            // let mainGroundLevel = this.mainGround.get(uid).Level || 1;
            // let maxResId = this.calcGroundMatchFieldMaxResId(mainGroundLevel);
            // for(let i=0;i<3;i++) {
                // this.groundMatch.fieldList[i].resId = maxResId - i;
            // }
        }
    }
};

//道具产出初始化
FootballGround.prototype.initRewardPack = function () { 
    let rewardPack = [];
    let index = 0;
    for(let i = 0; i < 5; ++i) {
        rewardPack[index] = {};
        rewardPack[index].Cur = 0;               //道具产出数量
        rewardPack[index].GetTime = 0;           //领取时间
        index++;
    }
    return rewardPack;
}

FootballGround.prototype.toJSONforClient = function () {};

//存数据库
FootballGround.prototype.toJSONforDB = function () {
    let footballGround = {
        uid: this.uid,
        adminGround: utils.toArray(this.adminGround),
        mainGround: utils.toArray(this.mainGround),
        trainGround: utils.toArray(this.trainGround),
        transferGround: utils.toArray(this.transferGround),
        hospitalGround: utils.toArray(this.hospitalGround),
        notableGround: utils.toArray(this.notableGround),
        prestige: this.prestige,
        ballFan: this.ballFan,
        trainCount: this.trainCount,
        ballHandbook: this.ballHandbook,
        ballHandbookActual: this.ballHandbookActual,
        ballComHandbook: this.ballComHandbook,
        ballComHandbookActual: this.ballComHandbookActual,
        ballComCount: this.ballComCount,
        retirementList: this.retirementList,
        notablePrestige: this.notablePrestige,
        isGetBallFansRewrd: this.isGetBallFansRewrd,
        getBallFansRewrdTime: this.getBallFansRewrdTime,
        isOpen: this.isOpen,
        firstJoinHofList: this.firstJoinHofList,
        //groundMatch: this.groundMatch
    };
    if(!debugConfig.groundMatchHide) {
        footballGround.groundMatch = this.groundMatch;
    }

    return footballGround;
};

/**
 * 创建球场 
 * @param uid 玩家UID
 */
FootballGround.prototype.createFootballGround = function (uid) {
    let admin = this.initAdminGround(uid);
    this.adminGround.set(uid, admin);
    let main = this.initMainGround(uid);
    this.mainGround.set(uid, main);
    let train = this.initTrainGround(uid);
    this.trainGround.set(uid, train);
    let transfer = this.initTransferGround(uid);
    this.transferGround.set(uid, transfer);
    let hospital = this.initHospitalGround(uid);
    this.hospitalGround.set(uid, hospital);
    let notable = this.initNotableGround(uid);
    this.notableGround.set(uid, notable);
}

/**
 * 行政楼数据初始化
 * @param uid 玩家UID
 */
FootballGround.prototype.initAdminGround = function (uid) {
    let ground = {}
    ground.Uid = uid;
    ground.Type = 1; //建筑类型
    ground.Level = 1; //建筑等级
    ground.UpTime = 1; //升级时间
    ground.IsUpgrade = 0; //0不在升级   1正在升级
    ground.GetTime = 1; //奖励领取时间
    ground.IsLive = 0; //是否可入住   0可入住  1不可入住
    ground.LiveName = ""; //入住者名字
    ground.LiveTime = 1; //入住时间
    ground.IsUnLock = 0; //0未解锁,  1已解锁
    ground.Cur = 0; //当前存储的欧元数量 
    ground.Item1Count = 0; //当前存储的道具1数量
    ground.Item2Count = 0; //当前存储的道具2数量
    ground.IsFristGet = 1;        //是否第一次领取
    ground.rewardPack = this.initRewardPack();
    return ground;
}

/**
 * 主球场数据初始化
 * @param uid 玩家UID
 */
FootballGround.prototype.initMainGround = function (uid) {
    let ground = {}
    ground.Uid = uid;
    ground.Type = 2; //建筑类型
    ground.Level = 1; //建筑等级
    ground.UpTime = 1; //升级时间
    ground.IsUpgrade = 0; //0不在升级   1正在升级
    ground.GetTime = 1; //奖励领取时间
    ground.IsLive = 0; //是否可入住   0可入住  1不可入住
    ground.LiveName = ""; //入住者名字
    ground.LiveTime = 1; //入住时间
    ground.IsUnLock = 0; //0未解锁,  1已解锁
    ground.Cur = 0; //当前存储的欧元数量 
    ground.Item1Count = 0; //当前存储的道具1数量
    ground.Item2Count = 0; //当前存储的道具2数量
    ground.IsFristGet = 1;        //是否第一次领取
    ground.rewardPack = this.initRewardPack();
    return ground;
}

/**
 * 训练场数据初始化
 * @param uid 玩家UID
 */
FootballGround.prototype.initTrainGround = function (uid) {
    let ground = {}
    ground.Uid = uid;
    ground.Type = 3; //建筑类型
    ground.Level = 1; //建筑等级
    ground.UpTime = 1; //升级时间
    ground.IsUpgrade = 0; //0不在升级   1正在升级
    ground.GetTime = 1; //奖励领取时间
    ground.IsLive = 0; //是否可入住   0可入住  1不可入住
    ground.LiveName = ""; //入住者名字
    ground.LiveTime = 1; //入住时间
    ground.IsUnLock = 0; //0未解锁,  1已解锁
    ground.Cur = 0; //当前存储的欧元数量 
    ground.Item1Count = 0; //当前存储的道具1数量
    ground.Item2Count = 0; //当前存储的道具2数量
    ground.IsFristGet = 1;        //是否第一次领取
    let trainPos = []
    for (let i = 0; i < 6; ++i) {
        trainPos[i] = {};
        trainPos[i].pos = "";
        trainPos[i].type = 0;      //训练类型  1素质  2防守  3射术  4技巧 5定位球 6守门
        if (i === 0) {
            trainPos[i].state = 2;
        } else {
            trainPos[i].state = 1; //状态  1锁住 2可添加 3训练中 4已完成
        }
        trainPos[i].beginTime = 0; //开始训练的时间   
    }
    ground.TrainPos = trainPos; //训练位置
    ground.rewardPack = this.initRewardPack();
    return ground;
}

/**
 * 转会数据初始化
 * @param uid 玩家UID
 */
FootballGround.prototype.initTransferGround = function (uid) {
    let ground = {}
    ground.Uid = uid;
    ground.Type = 4; //建筑类型
    ground.Level = 1; //建筑等级
    ground.UpTime = 1; //升级时间
    ground.IsUpgrade = 0; //0不在升级   1正在升级
    ground.GetTime = 1; //奖励领取时间
    ground.IsLive = 0; //是否可入住   0可入住  1不可入住
    ground.LiveName = ""; //入住者名字
    ground.LiveTime = 1; //入住时间
    ground.IsUnLock = 0; //0未解锁,  1已解锁
    ground.Cur = 0; //当前存储的欧元数量 
    ground.Item1Count = 0; //当前存储的道具1数量
    ground.Item2Count = 0; //当前存储的道具2数量
    ground.IsFristGet = 1;        //是否第一次领取
    let transferPos = [];
    for (let i = 0; i < 6; ++i) {
        transferPos[i] = {};
        transferPos[i].resId = 0;
        transferPos[i].level = 1;
        transferPos[i].name = "";
        transferPos[i].actual = 0;  //实力
        if(i == 0){
            transferPos[i].state = 2; //状态  1锁住  2可添加  3已有人 
            continue;
        }
        transferPos[i].state = 1; //状态  1锁住  2可添加  3已有人
    }
    ground.TransferPos = transferPos; //训练位置
    ground.rewardPack = this.initRewardPack();
    return ground;
};

/**
 * 医疗中心数据初始化
 * @param uid 玩家UID
 */
FootballGround.prototype.initHospitalGround = function (uid) {
    let ground = {}
    ground.Uid = uid;
    ground.Type = 5; //建筑类型
    ground.Level = 1; //建筑等级
    ground.UpTime = 1; //升级时间
    ground.IsUpgrade = 0; //0不在升级   1正在升级
    ground.GetTime = 1; //奖励领取时间
    ground.IsLive = 0; //是否可入住   0可入住  1不可入住
    ground.LiveName = ""; //入住者名字
    ground.LiveTime = 1; //入住时间
    ground.IsUnLock = 0; //0未解锁,  1已解锁
    ground.Cur = 0; //当前存储的欧元数量 
    ground.Item1Count = 0; //当前存储的道具1数量
    ground.Item2Count = 0; //当前存储的道具2数量
    ground.IsFristGet = 1;        //是否第一次领取
    let hospitalPos = []
    for (let i = 0; i < 2; ++i) {
        hospitalPos[i] = {};
        hospitalPos[i].uid = "";
        hospitalPos[i].beginTime = 0;   //开始时间
        hospitalPos[i].endTime = 0;     //所需要的时间
        if(i === 0){
            hospitalPos[i].state = 2; //状态  1锁住  2可添加  3已有人 
            continue;
        }
        hospitalPos[i].state = 1; //状态  1锁住  2可添加  3已有人
    }
    ground.HospitalPos = hospitalPos; //训练位置
    ground.rewardPack = this.initRewardPack();
    return ground;
}

/**
 * 名人堂数据初始化
 * @param uid 玩家UID
 */
FootballGround.prototype.initNotableGround = function (uid) {
    let ground = {}
    ground.Uid = uid;
    ground.Type = 6; //建筑类型
    ground.Level = 1; //建筑等级
    ground.UpTime = 1; //升级时间
    ground.IsUpgrade = 0; //0不在升级   1正在升级
    ground.GetTime = 1; //奖励领取时间
    ground.IsLive = 0; //是否可入住   0可入住  1不可入住
    ground.LiveName = ""; //入住者名字
    ground.LiveTime = 1; //入住时间
    ground.IsUnLock = 0; //0未解锁,  1已解锁
    ground.Cur = 0; //当前存储的欧元数量 
    ground.Item1Count = 0; //当前存储的道具1数量
    ground.Item2Count = 0; //当前存储的道具2数量
    ground.IsFristGet = 1;        //是否第一次领取
    let notablePos = []
    for (let i = 0; i < 20; ++i) {
        notablePos[i] = {};
        notablePos[i].resId = 0;
        notablePos[i].uid = "";
        if(i < 2){
            notablePos[i].state = 2; //状态  1锁住  2可添加  3已有人 
            continue;
        }
        notablePos[i].state = 1; //状态  1锁住  2可添加  3已有人
    }
    ground.NotablePos = notablePos; //训练位置
    ground.rewardPack = this.initRewardPack();
    return ground;
}

/**
 * 获取系统时间  单位:秒
 */
FootballGround.prototype.getSystemTime = function () {
    return Date.now() / 1000;
}

/**
 * 获取配置表的参数
 * @param type   建筑类型
 * @param level  建筑等级
 */
FootballGround.prototype.getConfigByType = function (type, level) {
    let configParam = {}
    let config = dataApi.allData.data["Field"];
    if (!config) {
        return configParam;
    }
    for (let i in config) {
        if (type !== config[i].Type || level !== config[i].Level) {
            continue;
        }
        configParam.Time = config[i].Time; //升级所需时间
        configParam.CostMoney = config[i].CostMoney; //升级消耗
        configParam.Level = config[i].Level; //建筑等级
        configParam.Grade = config[i].Grade; //升级条件及等级[]
        configParam.Storage = config[i].Storage; //物品存储量[]
        configParam.Output = config[i].Output; //一天产出的数量[]
        configParam.Fans = config[i].Fans; //增加球迷数量
        configParam.FansShow = config[i].FansShow; //增加球迷数量
        configParam.Prestige = config[i].Prestige; //增加声望数量
    }
    return configParam;
}

/**
 *根据类型获取球场某个建筑信息
 * @param uid  建筑类型
 * 1行政  2主球场  3训练  4转会  5医疗  6名人堂
 */
FootballGround.prototype.getGroundInfoByType = function (uid, type) {
    let ret = {}
    if (type < 1 || type > 6) {
        ret.code = Code.FAIL;
        return ret;
    }
    let ground = {}
    switch (type) {
        case 1:
            ground = this.adminGround.get(uid);
            ret = this.getGroundInfo(ground, type);
            break;
        case 2:
            ground = this.mainGround.get(uid);
            ret = this.getGroundInfo(ground, type);
            break;
        case 3:
            ground = this.trainGround.get(uid);
            ret = this.getGroundInfo(ground, type);
            break;
        case 4:
            ground = this.transferGround.get(uid);
            ret = this.getGroundInfo(ground, type);
            break;
        case 5:
            ground = this.hospitalGround.get(uid);
            ret = this.getGroundInfo(ground, type);
            break;
        case 6:
            ground = this.notableGround.get(uid);
            ret = this.getGroundInfo(ground, type);
            break;
        default:
            break;
    }
    //this.initBallFan();
    return ret;
}

/**
 * 获取建筑信息
 * @param ground   建筑对象
 * @param type  建筑类型
 */
FootballGround.prototype.getGroundInfo = function (ground, type) {
    let ret = {};
    if (!ground) {
        ret.code = Code.FAIL;
        return ret;
    }
    let level = ground.Level;
    let config = this.getConfigByType(type, level);
    if (JSON.stringify(config) === "{}") {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    //检查解锁、升级
    let result = this.checkIsMeetDemand(config, ground, type);
    if(type === 6){
        this.unlockNotablePos(ground, ground.Level);
    }
    if (result.code !== Code.OK) {
        ret.code = result.code;
    }
    //计算产出
    this.calcOutput(ground, config);
    ret.code = Code.OK;
    ret.isUpgrade = result.isUpgrade;
    return ret;
}

//检查是否达到要求
FootballGround.prototype.checkIsMeetDemand = function (config, ground, type) {
    let isUpgrade = false;
    if (type == 3 || type == 4 || type == 5) {
        //未解锁
        if (!ground.IsUnLock) {
            if (this.player.level < 1) {
                return {code: Code.LEVEL_FAIL, isUpgrade: isUpgrade};
            }
            ground.IsUnLock = 1; //解锁
            ground.GetTime = this.getSystemTime();
        }
        //在升级
        if (ground.IsUpgrade) {
            if (timeUtils.secInterval(ground.UpTime) >= config.Time) {
                ground.Level++;
                ground.IsUpgrade = 0; //升完置0
                isUpgrade = true;
            }
        }
    } else {
        //未解锁
        if (!ground.IsUnLock) {
            if (this.player.level < 1) {
                return {code: Code.LEVEL_FAIL, isUpgrade: isUpgrade};
            }
            let prestigeNum = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.mainPrestige].Param;
            let fansNum = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.mainFans].Param;
            this.addPrestige(prestigeNum);
            this.player.addExp(prestigeNum);
            this.addBallFans(fansNum);
            ground.IsUnLock = 1; //解锁
            ground.GetTime = this.getSystemTime();
        }
        //在升级

        if (ground.IsUpgrade) {
            if (timeUtils.secInterval(ground.UpTime) >= config.Time) {
                ground.Level++;
                if (type == 2) {
                    this.addPrestige(config.Prestige);
                    this.player.addExp(config.Prestige);
                    this.addBallFans(config.Fans);
                    //主球场升级-刷新训练场数据
                    // let maxResId = this.calcGroundMatchFieldMaxResId(ground.Level);
                    // for(let i=0;i<3;i++) {
                        // this.groundMatch.fieldList[i].resId = maxResId - i;
                    // }
                } else if(type == 6){
                    this.addPrestige(config.Prestige);
                    this.player.addExp(config.Prestige);
                    this.addBallFans(config.Fans);
                } else {
                    this.addPrestige(config.Prestige);
                    this.player.addExp(config.Prestige);
                }
                ground.IsUpgrade = 0; //升完置0
                isUpgrade = true;
            }
        }
    }
    return {code: Code.OK, isUpgrade: isUpgrade};
}

//计算产出
FootballGround.prototype.calcOutput = function (ground, config) {
    if (config.Output.length < 1) {
        ground.rewardPack[0].Cur = 0;
    }

    //是否首次领奖
    if(ground.IsFristGet === 1) {
        let reward = 0;
        if(ground.Type === 1) {
            reward = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.ADMIN_REWAED].Param;
            if(!reward) {
                reward = 0;
            }
        }else if(ground.Type === 3) {
            reward = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.TRAIN_REWARD].Param;
            if(!reward) {
                reward = 0;
            }
        }else if(ground.Type === 4) {
            reward = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.TRANSFER_REWARD].Param;
            if(!reward) {
                reward = 0;
            }
        }else if(ground.Type === 5) {
            reward = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.HOSPITAL_REWARD].Param;
            if(!reward) {
                reward = 0;
            }
        }else if(ground.Type === 6) {
            reward = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.NOTABLE_REWARD].Param;
            if(!reward) {
                reward = 0;
            }
        }
        ground.rewardPack[0].Cur = reward;
        return;
    }

    for(let i = 0; i < config.Output.length; ++i) {
        let num = config.Output[i].count / 86400000;     //每毫秒产出
        if(ground.rewardPack[i].GetTime === 0) {
            ground.rewardPack[i].GetTime = timeUtils.now();
        }
		let n = timeUtils.now() - ground.rewardPack[i].GetTime;
        let sum = num * n;
		ground.rewardPack[i].Cur += sum;
        ground.rewardPack[i].GetTime += n;
        if(ground.rewardPack[i].Cur < 0) {
            ground.rewardPack[i].Cur = config.Output[i].count;
            ground.rewardPack[i].GetTime = timeUtils.now();
        }
    }


    if (config.Storage.length < 1) {
        ground.rewardPack[0].Cur = 0;
    }

    let vipUpperAddition = 0;
    if(this.player.vip > 0 && this.player.vip < 15) {
        vipUpperAddition = dataApi.allData.data["Vip"][this.player.vip].UpperAddition / 100;
    }

    //是否超出最大存储量
    for (let n = 0; n < config.Storage.length; ++n) {
        let storage = config.Storage[n].count + Math.floor(config.Storage[n].count * vipUpperAddition);
        //欧元才有加成
        if(ground.Type === 1 || ground.Type === 4 || ground.Type === 6) {
            if (ground.rewardPack[n].Cur >= storage) {
                ground.rewardPack[n].Cur = storage;
            }
        }else {
            if (ground.rewardPack[n].Cur >= config.Storage[n].count) {
                ground.rewardPack[n].Cur = config.Storage[n].count;
            }
        }
    }
}

/**
 * 获取球场所有建筑信息
 * @param uid   个人UID
 */
FootballGround.prototype.getGroundAllInfo = function (uid) {
    let allGround = {
        adminGround: {},
        mainGround: {},
        trainGround: {},
        transferGround: {},
        hospitalGround: {},
        notableGround: {},
        prestige: this.prestige,
        ballFan: this.ballFan,
        trainCount: this.trainCount,
        isGetBallFansRewrd: this.isGetBallFansRewrd
    }
    
    if(this.isOpen === commonEnum.FOOTBALL_GROUND_STATUS.CLOSE){
        //球场开放等级
        let openLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.openLevel].Param;
        if (this.player.level >= openLevel ) // 条件符合,状态变更
        {
            this.isOpen = commonEnum.FOOTBALL_GROUND_STATUS.OPEN;
        }

        if (this.isOpen === commonEnum.FOOTBALL_GROUND_STATUS.CLOSE) //状态没变，直接return
        {
            return allGround;
        }
    }

    //检查球迷奖励刷新
    this.checkBallFansRewardTime();

    for (let i = 1; i < 7; ++i) {
        let ground = {};
        switch (i) {
            case 1:
                ground = this.adminGround.get(uid);
                this.getGroundInfo(ground, 1);
                allGround.adminGround = this.makeClientGroundInfo(ground);
                break;
            case 2:
                ground = this.mainGround.get(uid);
                this.getGroundInfo(ground, 2);
                allGround.mainGround = this.makeClientGroundInfo(ground);
                break;
            case 3:
                ground = this.trainGround.get(uid);
                this.getGroundInfo(ground, 3);
                allGround.trainGround = this.makeClientGroundInfo(ground);
                break;
            case 4:
                ground = this.transferGround.get(uid);
                this.getGroundInfo(ground, 4);
                allGround.transferGround = this.makeClientGroundInfo(ground);
                break;
            case 5:
                ground = this.hospitalGround.get(uid);
                this.getGroundInfo(ground, 5);
                allGround.hospitalGround = this.makeClientGroundInfo(ground);
                break;
            case 6:
                ground = this.notableGround.get(uid);
                this.getGroundInfo(ground, 6);
                allGround.notableGround = this.makeClientGroundInfo(ground);
                break;
            default:
                break;
        }
    }

    allGround.prestige = this.prestige;
    allGround.ballFan = this.ballFan;
    allGround.trainCount = this.trainCount;
    allGround.isGetBallFansRewrd = this.isGetBallFansRewrd;
    return allGround;
}

/**
 * 根据类型领取球场某个建筑奖励
 * @param uid   个人UID
 * @param type  建筑类型
 * 1行政  2主球场  3训练  4转会  5医疗  6名人堂
 */
FootballGround.prototype.getGroundRewardByType = function (uid, type) {
    let ret = {};
    let ground = {};
    switch (type) {
        case 1:
            ground = this.adminGround.get(uid);
            ret = this.getGroundReward(ground, type);
            break;
        case 2:
            ground = this.mainGround.get(uid);
            ret = this.getGroundReward(ground, type);
            break;
        case 3:
            ground = this.trainGround.get(uid);
            ret = this.getGroundReward(ground, type);
            break;
        case 4:
            ground = this.transferGround.get(uid);
            ret = this.getGroundReward(ground, type);
            break;
        case 5:
            ground = this.hospitalGround.get(uid);
            ret = this.getGroundReward(ground, type);
            break;
        case 6:
            ground = this.notableGround.get(uid);
            ret = this.getGroundReward(ground, type);
            break;
        default:
            ret.code = Code.FAIL;
            break;
    }

    //触发任务
    if(ret.code === Code.OK) {
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.THIRTY_THREE);
        this.player.newerTask.triggerTask(commonEnum.NEWER_TASK.GET_MONEY);
    }

    return ret;
}

/**
 * 领取建筑奖励
 * @param ground   建筑对象
 * @param type     建筑类型
 */
FootballGround.prototype.getGroundReward = function (ground, type) {
    let ret = {
        code: Code.FAIL,
        rewardNum: []
    };
    if (!ground) {
        ret.code = Code.FAIL;
        return ret;
    }

    let rewardNum = [];
    let config = this.getConfigByType(type, ground.Level);
    for(let i = 0; i < config.Output.length; ++i) {
        //产量
        let num = Math.floor(ground.rewardPack[i].Cur);
        if(num < 1) {
            continue;
        }
        this.player.bag.addItem(config.Output[i].type, num);
        //清数据
        ground.rewardPack[i].Cur = 0;
        ground.rewardPack[i].GetTime = timeUtils.now();
        rewardNum.push(num);
    }

    //首次领取置0
    if(ground.IsFristGet) {
        ground.IsFristGet = 0;
    }

    if (rewardNum.length < 1) {
        ret.code = Code.REWARD_FAIL;
        return ret;
    }
    
    ret.code = Code.OK;
    ret.rewardNum = rewardNum;
    return ret;
}

/**
 * 根据类型获取相应等级信息
 * @param uid   个人UID
 * @param type  建筑类型
 * 1行政  2主球场  3训练  4转会  5医疗  6名人堂 7个人等级  0无条件
 */
FootballGround.prototype.getLevelByType = function (uid, type) {
    let level = 0;
    switch (type) {
        case 1:
            level = this.adminGround.get(uid).Level;
            break;
        case 2:
            level = this.mainGround.get(uid).Level;
            break;
        case 3:
            level = this.trainGround.get(uid).Level;
            break;
        case 4:
            level = this.transferGround.get(uid).Level;
            break;
        case 5:
            level = this.hospitalGround.get(uid).Level;
            break;
        case 6:
            level = this.notableGround.get(uid).Level;
            break;
        case 7:
            level = this.player.level;
            break;
        default:
            break;
    }
    return level;
}

/**
 * 根据类型升级球场某个建筑信息
 * @param uid   个人UID
 * @param type  建筑类型
 * 1行政  2主球场  3训练  4转会  5医疗  6名人堂
 */
FootballGround.prototype.upgradeGroundByType = function (uid, type) {
    let ret = {}
    let ground = {};
    //检查是否有球场建筑在升级
    let isUpgrade = this.checkAllGroundStatus();
    if(!isUpgrade) {
        ret.code = Code.FOOTGROUND_CODE.IN_UPGRADW;
        ret.upTime = 0;
        return ret;
    }
    switch (type) {
        case 1:
            ground = this.adminGround.get(uid);
            ret = this.upgradeGround(uid, ground, type);
            break;
        case 2:
            ground = this.mainGround.get(uid);
            ret = this.upgradeGround(uid, ground, type);
            break;
        case 3:
            ground = this.trainGround.get(uid);
            ret = this.upgradeGround(uid, ground, type);
            break;
        case 4:
            ground = this.transferGround.get(uid);
            ret = this.upgradeGround(uid, ground, type);
            break;
        case 5:
            ground = this.hospitalGround.get(uid);
            ret = this.upgradeGround(uid, ground, type);
            break;
        case 6:
            ground = this.notableGround.get(uid);
            ret = this.upgradeGround(uid, ground, type);
            break;
        default:
            ret.code = Code.FAIL;
            ret.upTime = 0;
            break;
    }
    //升级成功触发任务
    if(ret.code === Code.OK){
        this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.TWELVE);
    }
    return ret;
}

/**
 * 升级建筑
 * @param uid      个人UID
 * @param ground   建筑对象
 * @param type     建筑类型
 * 1行政  2主球场  3训练  4转会  5医疗  6名人堂 7个人等级  0无条件
 */
FootballGround.prototype.upgradeGround = function (uid, ground, type) {
    let ret = {
        code: Code.FAIL,
        upTime: 0
    };
    if (!ground) {
        return ret;
    }
    //超过等级
    if (ground.Level > 20) {
        ret.code = Code.MAXLEVEL_FAIL;
        return ret;
    }
    //获取配置
    let config = this.getConfigByType(type, ground.Level);
    if (JSON.stringify(config) === "{}") {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }
    //已经在升级了就不能继续升级
    if (ground.IsUpgrade) {
        logger.error("upgradeAdminGround is on upgrade~~~~~~~~~~~~~~");
        ret.code = Code.REQUEST_FAIL;
        return ret;
    }

    let type_1 = -1 //条件1
    let type_2 = -1; //条件2
    if (config.Grade.length === 1) {
        type_1 = config.Grade[0].type;
        // logger.error("onfig.Grade[0].type-",config.Grade[0].type, config.Grade[0].count)
    } else if (config.Grade.length === 2) {
        type_1 = config.Grade[0].type;
        type_2 = config.Grade[1].type;
        // logger.error("onfig.Grade[0].type-",config.Grade[0].type, config.Grade[0].count)
        // logger.error("onfig.Grade[1].type-",config.Grade[1].type, config.Grade[1].count)
    }

    let level_1 = 0;
    let level_2 = 0;
    if (type_2 == -1) {
        level_1 = this.getLevelByType(uid, type_1);
        if (level_1 < config.Grade[0].count) {
            ret.code = Code.LEVEL_FAIL;
            return ret;
        }
    } else {
        level_1 = this.getLevelByType(uid, type_1);
        level_2 = this.getLevelByType(uid, type_2);
        //未达到等级条件
        if (level_1 < config.Grade[0].count || level_2 < config.Grade[1].count) {
            ret.code = Code.LEVEL_FAIL;
            return ret;
        }
    }

    //检查钱是否足够
    if (this.player.cash < config.CostMoney) {
        ret.code = Code.CASH_FALL;
        return ret;
    }

    //更新信息
    ground.UpTime = this.getSystemTime();
    ground.IsUpgrade = 1;
    this.player.subtractResource(commonEnum.PLAY_INFO.cash, config.CostMoney);
    ret.upTime = ground.UpTime;
    ret.code = Code.OK;
    return ret;
}

/**
 * 添加球迷
 * @param num    数量
 */
FootballGround.prototype.addBallFans = function (num) {
    this.ballFan += num;
    if(this.ballFan < 0)
    {
        this.ballFan = 0;
    }
};

/**
 * 添加声望
 * @param num    数量
 */
FootballGround.prototype.addPrestige = function (num) {
    this.prestige += num;
}

/**
 * 球员训练
 * @param playerUid  玩家UID
 * @param heroUid    球员UID
 * @param index      位置        
 * @param type       训练类型
 */
FootballGround.prototype.heroTrainInGround = function (playerUid, heroUid, index, type, isLock) {
    let ret = {
        code: Code.FAIL,
        beginTime: 0,
        state: 0,
        isLockTrain: 0
    }

    if (index < 0 || index > 5) {
        return ret;
    }

    if(type < 1 || type > 6) {
        return ret;
    }

    let train = this.trainGround.get(playerUid);
    let hero = this.player.heros.getHero(heroUid);
    if (!train) {
        return ret;
    }

    //检查次数
    if(hero.TrainCount >= train.Level) {
        ret.code = Code.FOOTGROUND_CODE.LEVEL_FAIL;
        return ret;
    }
    
    //已经在训练中了
    if(hero.isTrain) {
        ret.code = Code.HERO_CODE.IN_TRAIN;
        return ret;
    }

    //未解锁
    if (train.TrainPos[index].state === 1) {
        ret.code = Code.UNLOCK_FAIL;
        return ret;
    }

    //已经有球员在训练了
    if (train.TrainPos[index].pos !== "") {
        return ret;
    }

    //锁定  1锁定
    if(isLock) {
        let config = dataApi.allData.data["FeildTrainning"];
        if (!config) {
            ret.code = Code.CONFIG_FAIL;
            return ret;
        }

        let heroTrainCount = hero.TrainCount + 1;
        let costNum = -1;
        for (let i in config) {
            if (heroTrainCount === config[i].Level) {
                costNum = config[i].CostNum;
            }
        }

        if (costNum === -1) {
            ret.code = Code.CONFIG_FAIL;
            return ret;
        }

        let itemCount = this.player.item.getItemCountByResId(10805);
        let itemUid = this.player.item.getItemUidByResId(10805);
        if(itemCount < costNum){
            ret.code = Code.ITEM_FAIL;
            return ret;
        }

        //删除物品
        this.player.item.delItem(itemUid, costNum);
        hero.isLockTrain = 1;
    }

    train.TrainPos[index].pos = heroUid;
    train.TrainPos[index].state = 3; //状态  1锁住 2可添加 3训练中 4已完成
    train.TrainPos[index].type = type;  //训练类型  1素质  2防守  3射术  4技巧 5定位球 6守门
    //置状态
    hero.isTrain = 1;
    train.TrainPos[index].beginTime = this.getSystemTime();

    //触发任务
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.THIRTY_FOUR);
    ret.beginTime = train.TrainPos[index].beginTime;
    ret.state = train.TrainPos[index].state;
    ret.isLockTrain = hero.isLockTrain;
    ret.code = Code.OK;
    return ret;
}

/**
 * 领取训练奖励
 * @param playerUid  玩家UID
 * @param index      位置        
 */
FootballGround.prototype.getHeroTrainReward = function (playerUid, index) {
    let ret = {
        code: Code.FAIL,
        type: 0,
        rand: 0,
        state: 0,
        hero: {}
    };
    let train = this.trainGround.get(playerUid);
    if (!train) {
        return ret;
    }
    if (index < 0 || index > 5) {
        ret.code = Code.RANGE_FAIL;
        return ret;
    }
    //检查位置是否有球员
    if (train.TrainPos[index].pos === "") {
        return ret;
    }
    let lowNum = 0;
    let upNum = 0;
    let config = dataApi.allData.data["FeildTrainning"];
    if (!config) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    let heroUid = train.TrainPos[index].pos;
    let hero = this.player.getOneHero(heroUid);
    if(!hero) {
        logger.error("getHeroTrainReward hero undefine. playerId[%s], heroUid[%s]", playerUid, heroUid);
        return ret;
    }
    let heroTrainCount = hero.TrainCount + 1;
    for (let i in config) {
        if (heroTrainCount !== config[i].Level) {
            continue;
        }
        lowNum = config[i].AttributeMin;
        upNum = config[i].AttributeMax;
        timeReduce = config[i].TimeReduce;
        trainTime = config[i].Time;
    }

    let secTime = trainTime * 60 * 60;
    let actualTime = Math.floor(secTime - (secTime * timeReduce / 10000));
    if (timeUtils.secInterval(train.TrainPos[index].beginTime) < actualTime) {
        ret.code = Code.TIME_FAIL;
        return ret;
    }
    // if (timeUtils.secInterval(train.TrainPos[index].beginTime) < 10) {
    //     ret.code = Code.TIME_FAIL;
    //     return ret;
    // }
    let type = train.TrainPos[index].type;
    //计算球员属性
    let rand = this.calcHeroProperty(type, heroUid, upNum, lowNum);
    
    this.player.reCalcAttrRevision(heroUid);
    // this.player.reCalcAttr(heroUid);
    hero.isTrain = 0;        //置状态
    if(hero.isLockTrain) {
        hero.isLockTrain = 0;
    }
    //加次数
    hero.TrainCount ++;
    train.TrainPos[index].pos = "";
    train.TrainPos[index].state = 2; //状态  1锁住 2可添加 3训练中 4已完成
    ret.rand = rand;
    ret.type = type;
    ret.state = train.TrainPos[index].state;
    ret.hero = this.player.heros.makeClientHero(heroUid);
    ret.code = Code.OK;
    return ret;
}

/** 计算球员数据加成
 * @param  {}  type   属性类型
 * @param  {}  uid   球员uid
 */
FootballGround.prototype.calcHeroProperty = function(type, heroUid, upNum, lowNum) {
    let hero = this.player.getOneHero(heroUid);
    if(!hero) {
        return;
    }

    let randNum = 0;
    if(hero.isLockTrain) {
        randNum = upNum;
    }else {
        randNum = utils.random(upNum, lowNum);
    }

    for(let key in commonEnum.ONE_LEVEL_ATTR_NAMES){
        switch(type){
            case 1:           //素质
                if( key === "Speed" || key === "Strength" || key === "ExplosiveForce"){
                    hero.oneLevelAttr[key].GroundTrain += randNum;
                }
            break;
            case 2:          //防守
                if( key === "Jumping" || key === "StandingTackle" || key === "SlidingTackle"){
                    hero.oneLevelAttr[key].GroundTrain += randNum;
                }
            break;
            case 3:           //射术
                if( key === "Finishing" || key === "Heading" || key === "LongShots"){
                    hero.oneLevelAttr[key].GroundTrain += randNum;
                }
            break;
            case 4:           //技巧
                if( key === "Dribbling" || key === "Passing" || key === "LongPassing"){
                    hero.oneLevelAttr[key].GroundTrain += randNum;
                }
            break;
            case 5:           //定位球
                if( key === "Penalties" || key === "CornerKick" || key === "FreeKick"){
                    hero.oneLevelAttr[key].GroundTrain += randNum;
                }
            break;
            case 6:           //守门
                if( key === "Attack" || key === "Volleys" || key === "Save"){
                    hero.oneLevelAttr[key].GroundTrain += randNum;
                }
            break;
        }
    }

    //重新计算球员属性
    this.player.reCalcAttrRevision(heroUid);
    // this.player.reCalcAttr(heroUid);
    //检查是否在阵容中 在的话需要重新计算球队属性
    this.player.checkHeroInArbitrarilyFormation(heroUid);

    return randNum;
};

/**
 * 获取球员训练信息
 * @param UID  玩家UID      
 */
FootballGround.prototype.getHeroTrainInfo = function (uid) {
    let ret = {}
    let train = this.trainGround.get(uid);
    if (!train) {
        ret.code = Code.FAIL;
        return ret;
    }
    let config = dataApi.allData.data["FeildTrainning"];
    if (!config) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }
    
    let trainPos = []
    for (let i = 0; i < train.TrainPos.length; ++i) {
        trainPos[i] = {};
        //检查是否有此球员
        let hero = {};
        if(train.TrainPos[i].pos !== "") {
            hero = this.player.getOneHero(train.TrainPos[i].pos);
            if(!this.player.heros.checkIsHaveHero(train.TrainPos[i].pos)) {
                train.TrainPos[i].type = 0;
                train.TrainPos[i].state = 2;
                train.TrainPos[i].pos = "";
                train.TrainPos[i].beginTime = 0;
                // train.TrainPos[i].isLockTrain = 0;
            }

            //3个以上直接清楚数据
            if(i >= 3) {
                hero.isTrain = 0;    
                train.TrainPos[i].type = 0;
                train.TrainPos[i].state = 2;
                train.TrainPos[i].pos = "";
                train.TrainPos[i].beginTime = 0;
            }
        }

        let timeReduce = 0;
        let trainTime = 0;
        let heroTrainNum = hero.TrainCount + 1;
        for (let i in config) {
            if (heroTrainNum !== config[i].Level) {
                continue;
            }
            timeReduce = config[i].TimeReduce;
            trainTime = config[i].Time;
        }
        //化成秒
        let secTime = trainTime * 60 * 60;
        let actualTime = Math.floor(secTime - (secTime * timeReduce / 10000));
        if ((timeUtils.secInterval(train.TrainPos[i].beginTime) >= actualTime) && (train.TrainPos[i].state === 3)) {
            train.TrainPos[i].state = 4; //状态  1锁住 2可添加 3训练中 4已完成
        }
        // if ((timeUtils.secInterval(train.TrainPos[i].beginTime) >= 10) && (train.TrainPos[i].state === 3)) {
        //     train.TrainPos[i].state = 4; //状态  1锁住 2可添加 3训练中 4已完成
        // }
        trainPos[i].state = train.TrainPos[i].state;
        trainPos[i].pos = train.TrainPos[i].pos;
        trainPos[i].beginTime = train.TrainPos[i].beginTime; //开始训练的时间
        if(JSON.stringify(hero) === "{}") {
            trainPos[i].isLockTrain = 0;
        } else  {
            trainPos[i].isLockTrain = hero.isLockTrain;
        }
    }
    ret.code = Code.OK;
    ret.trainPos = trainPos;
    return ret;
}

/**
 * 解锁训练场格子
 * @param UID  玩家UID 
 * @param index  位置     
 */
FootballGround.prototype.unlockTrainPos = function (uid, index) {
    let ret = {
        code: Code.FAIL,
        state: 0
    };
    let train = this.trainGround.get(uid);
    if (!train) {
        return ret;
    }
    if (index < 0 || index > 5) {
        return ret;
    }
    let needLevel = 0;
    let needVip = 0;
    switch (index) {
        case 0:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.firstPos].Param;
            break;
        case 1:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.secondPos].Param;
            needVip = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.SECOND_POS_VIP].Param;
            break;
        case 2:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.thirdPos].Param;
            needVip = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.THIRD_POS_VIP].Param;
            break;
        case 3:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.fourthPos].Param;
            break;
        case 4:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.fifthPos].Param;
            break;
        case 5:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.sixthPos].Param;
            break;
        default:
            needLevel = 99999;
            break;
    }

    //判断个人等级是否达标与Vip等级是否达标
    if (this.player.level < needLevel && this.player.vip < needVip) {
        ret.code = Code.LEVEL_FAIL;
        return ret;
    }

    //解锁
    train.TrainPos[index].state = 2; //状态  1锁住 2可添加 3训练中 4已完成
    ret.state = train.TrainPos[index].state;
    ret.code = Code.OK;
    return ret;
}

/**
 * 获取转会教练团队信息
 * @param UID  玩家UID      
 */
FootballGround.prototype.getTransferTrainerInfo = function (uid) {
    let ret = {
        code: Code.FAIL,
        transferPos: [],
        allActual: 0,
    }
    let transfer = this.transferGround.get(uid);
    if (!transfer) {
        return ret;
    }

    this.player.trainer.calcAllTrainerAttr();
    let transferPos = [];
    for (let i = 0; i < transfer.TransferPos.length; ++i) {
        transferPos[i] = {};
        let resId = transfer.TransferPos[i].resId;
        transferPos[i].resId = resId;
        transferPos[i].state = transfer.TransferPos[i].state;
        transferPos[i].level = transfer.TransferPos[i].level; 
        transferPos[i].actual = this.player.trainer.getOneTrainerByResId(resId).Actual || 0;
        if(transfer.TransferPos[i].resId > 0){
            let name = this.getTrainerNameByResId(transfer.TransferPos[i].resId);
            transferPos[i].name = name;
            continue;
        }
        transferPos[i].name = transfer.TransferPos[i].name;  
    }

    ret.code = Code.OK;
    ret.transferPos = transferPos;
    ret.allActual = this.calcTotalTrainerActual(uid);
    return ret;
}

//根据教练ID查名字
FootballGround.prototype.getTrainerNameByResId = function(resId){
    let name = "";
    let config = dataApi.allData.data["TrainningCoach"][resId];
    if(!config){
        name = "配置表错误";
        return name;
    }
    name = config.Name;
    return name;
}

/**
 * 检查上场教练是否已在其他位置上
 * @param trainObj  建筑对象
 * @param resId    球员UID
 * @param index      位置        
 */
FootballGround.prototype.checkTrainerInOtherPos = function (trainObj, resId) {
    let info  = {
        resId: 0,
        level: 1,
        name : "",
        actual: 0,
        index: -1,
        state: 1,
    };
    for(let i = 0; i < trainObj.TransferPos.length; ++i){
        if(resId === trainObj.TransferPos[i].resId){
            info.resId = trainObj.TransferPos[i].resId;
            info.level = trainObj.TransferPos[i].level;
            info.name = trainObj.TransferPos[i].name;
            info.actual = trainObj.TransferPos[i].actual;
            info.state = trainObj.TransferPos[i].state;
            info.index = i;
        }
    }
    return info;
}

/**
 * 添加教练
 * @param uid  玩家UID
 * @param resId    球员UID
 * @param index      位置        
 */
FootballGround.prototype.addTrainInTransfer = function (uid, resId, index) {
    let ret = {
        code: Code.FAIL,
        state: 1,
        allActual: 0,
    }
    if (index < 0 || index > 5) {
        ret.code = Code.RANGE_FAIL;
        return ret;
    }
    let transfer = this.transferGround.get(uid);
    //获取教练数据
    let trainer = this.player.trainer.getOneTrainerByResId(resId);
    if (!transfer || JSON.stringify(trainer) === "{}") {
        return ret;
    }

    //未解锁
    if (transfer.TransferPos[index].state === 1) {
        ret.code = Code.UNLOCK_FAIL;
        return ret;
    }

    //检查其他位置是否有此教练
    let other = this.checkTrainerInOtherPos(transfer, resId);
    //如果有
    if(other.index !== -1){
        let temp = {
            resId: transfer.TransferPos[index].resId,
            level: transfer.TransferPos[index].level,
            name : transfer.TransferPos[index].name,
            actual: transfer.TransferPos[index].actual,
            state: transfer.TransferPos[index].state,
        }

        transfer.TransferPos[index].resId = other.resId;
        transfer.TransferPos[index].level = other.level;
        transfer.TransferPos[index].name = other.name;
        transfer.TransferPos[index].actual = other.actual;
        transfer.TransferPos[index].state = other.state; //状态  1锁住  2可添加  3已有人

        transfer.TransferPos[other.index].resId = temp.resId;
        transfer.TransferPos[other.index].level = temp.level;
        transfer.TransferPos[other.index].name = temp.name;
        transfer.TransferPos[other.index].actual = temp.actual;
        transfer.TransferPos[other.index].state = temp.state; //状态  1锁住  2可添加  3已有人
    }else
    {
        let name = this.getTrainerNameByResId(resId);
        transfer.TransferPos[index].resId = trainer.ResId;
        transfer.TransferPos[index].level = trainer.Level;
        transfer.TransferPos[index].name = name;
        transfer.TransferPos[index].actual = trainer.Actual;
        transfer.TransferPos[index].state = 3; //状态  1锁住  2可添加  3已有人

        this.reCalcAllHeroAttr();
    }

    this.player.trainer.calcAllTrainerAttr();
    ret.code = Code.OK;
    ret.allActual = this.calcTotalTrainerActual(uid);
    return ret;
}

/**
 * 获取教练列表
 */
// FootballGround.prototype.getAllTrainer = function () {
//     return this.player.trainer.getAllTrainer();
// };

FootballGround.prototype.calcTotalTrainerActual = function(uid)
{
    let totalActual = 0;
    let transfer = this.transferGround.get(uid);
    if (!transfer)
    {
        logger.error("getTotalTrainerActual: not transfer!", uid);
        return totalActual;
    }

    let transferPos = transfer.TransferPos;
    //logger.info("transferPos", transferPos)
    for(let idx in transferPos)
    {
        let obj = transferPos[idx];
        //logger.info("calcTotalTrainerActual", obj);
        let resId = obj.resId;
        if (!resId || resId <= 0 )
        {
            continue;
        }

        totalActual += obj.actual;
    }

    //logger.info("getTotalTrainerActual: totalActual", uid, totalActual);
    return totalActual;
};
/**
 * 解锁教练团格子
 * @param UID  玩家UID 
 * @param index  位置     
 */
FootballGround.prototype.unlockTransferPos = function (uid, index) {
    let ret = {
        code: Code.FAIL,
        state: 1
    };
    let transfer = this.transferGround.get(uid);
    if (!transfer) {
        return ret;
    }
    if (index < 0 || index > 4) {
        ret.code = Code.RANGE_FAIL;
        return ret;
    }
    let needLevel = 0;
    switch (index) {
        case 0:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_1].Param;
            break;
        case 1:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_2].Param;
            break;
        case 2:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_3].Param;
            break;
        case 3:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_4].Param;
            break;
        case 4:
            needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.transferPos_5].Param;
            break;
        default:
            needLevel = 99999;
            break;
    }

    //判断个人等级是否达标
    if (this.player.level < needLevel) {
        ret.code = Code.LEVEL_FAIL;
        return ret;
    }
    //解锁
    transfer.TransferPos[index].state = 2; //状态  1锁住 2可添加 3已有人
    ret.state = transfer.TransferPos[index].state;
    ret.code = Code.OK;
    return ret;
}

/**
 * 合成教练
 * @param resId  教练ID     
 */
FootballGround.prototype.compoundTrainer = function (resId) {
    let ret = {
        code: Code.FAIL,
        uid: ""
    };
    let config = dataApi.allData.data["TrainningCoach"][resId];
    if(!config){
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    //已经有了就不能再合成了
    let trainer = this.player.trainer.getOneTrainerByResId(resId);
    if (JSON.stringify(trainer) !== "{}") {
        ret.code = Code.HAVE_FAIL;
        return ret;
    }

    let itemCount = this.player.item.getItemCountByResId(config.ChipId);
    let itemUid = this.player.item.getItemUidByResId(config.ChipId);
    let needNum = config.Chip;
    if(itemCount < needNum){
        ret.code = Code.ITEM_FAIL;
        return ret;
    }

    //删除物品
    this.player.item.delItem(itemUid, needNum);
    let trainerUid = this.player.trainer.addTrainer(resId);
    ret.code = Code.OK;
    ret.uid = trainerUid;
    return ret;
}

/**
 * 计算球员图鉴实力加成 
 * @param resId  教练ID    
 * @param index  1球员  2组合
 */
FootballGround.prototype.calcActual = function (resId, index) {
    let config = {};
    if(index === 1){
        config = dataApi.allData.data["PlayerHandbook"][resId];
    } else{
        config = dataApi.allData.data["CombinationHandbook"][resId];
    }
    let count = 0;
    let sum = 0;
    for(let i in commonEnum.ONE_LEVEL_ATTR_NAMES){
        if(i !== "ResistanceDamage"){
            let value = config[i];
            if(value !== 0){
                sum += value;
            }
            count++;
        } 
    }
    sum = utils.MathRound(sum / count);
    if(index === 1){
        this.ballHandbookActual += sum;
    }else {
        this.ballComHandbookActual += sum;
    }
};


//note: 此函数用户优化修改了hero属性进行全部更新，不放在addAllHeroProperty为了性能考虑
FootballGround.prototype.reCalcAllHeroAttr = function()
{
    let allHero = this.player.heros.allheros;
    for(let [uid, obj ] of allHero) 
    {
        //重新计算球员属性
        this.player.reCalcAttrRevision(uid);
        // this.player.reCalcAttr(uid);
        //更新上场球员属性
        this.player.checkHeroInArbitrarilyFormation(uid);
    }

    //通知客户端更新球员
    this.player.updateHeros();
};
/**
 * 获取球员图鉴信息   
 */
FootballGround.prototype.getBallHandbook = function () {
    let ret = {
        ball: this.ballHandbook,      //已激活球员
        ballCom: this.ballComHandbook, //组合
        ballActual: this.ballHandbookActual * 11,     //球员实力加成
        ballComActual: this.ballComHandbookActual * 11,   //组合实力加成
        ballComCount: this.ballComCount   //组合激活个数
    }
    return ret;
}

//检查图鉴是否有球员被激活
FootballGround.prototype.checkIsHaveHandbookHeroActive = function(resId){
    let ball = this.ballComHandbook;
    for(let i in ball){
        let hero = ball[i].player;
        for(let j in hero){
            if(resId !== hero[j].id){
                continue;
            }
            //已激活
            if(hero[j].isHave === 1){
                break;
            }
            this.ballComHandbook[i].player[j].isHave = 1;   //0未激活  1已激活
        }
    }
};

//检查图鉴加成
FootballGround.prototype.checkHeroHandbookAttr = function(uid) {
    let hero = this.player.heros.getHero(uid);

    //置0
    for(let j in commonEnum.ONE_LEVEL_ATTR_NAMES){ 
        if (j !== "ResistanceDamage") {
            hero.oneLevelAttr[j].Handbook = 0;
        }
    }

    //激活图鉴
    let ball = this.ballHandbook;
    //遍历激活的球员并加上相应的加成值
    for(let i = 0; i  < ball.length; ++i) {
        let resId = ball[i];
        let config = dataApi.allData.data["PlayerHandbook"][resId];
        if(!config) {
            continue;
        }
        for(let j in commonEnum.ONE_LEVEL_ATTR_NAMES){ 
            if (j !== "ResistanceDamage") {
                hero.oneLevelAttr[j].Handbook += config[j];        
            }
        }
    }

    //检查是否有组合图鉴激活
    let ballComHandbook = this.ballComHandbook;
    for(let i in ballComHandbook){ 
        //已经算过的说明激活了
        if(ballComHandbook[i].isCalc === 1){
            let bookId = ballComHandbook[i].bookId;
            let comConfig = dataApi.allData.data["CombinationHandbook"][bookId];
            if(!comConfig) {
                continue;
            }
            for(let j in commonEnum.ONE_LEVEL_ATTR_NAMES){ 
                if (j !== "ResistanceDamage") {
                    hero.oneLevelAttr[j].Handbook += comConfig[j];
                }
            } 
        }
    }
}

//检查是否有组合图鉴被激活
FootballGround.prototype.checkIsHaveHandbookActive = function(){
    let ball = this.ballComHandbook;
    for(let i in ball){
        //过滤算过的
        if(ball[i].isCalc === 1){
            continue;
        }
        let isAllHave = false;
        let hero = ball[i].player;
        for(let j in hero){
            if(hero[j].isHave !== 1){
                isAllHave = false;
                break;
            }
            isAllHave = true;
        }
        //如果全部激活并且未被激活
        if(isAllHave){
            let bookId = ball[i].bookId;
            this.calcActual(bookId, 2);
            this.ballComHandbook[i].isCalc = 1;
            this.ballComCount ++;
        }
    }
}

/**
 * 激活球员
 * @param resId  教练ID    
 * @param index  1球员激活  2组合激活 
 */
FootballGround.prototype.activeBallHandbook = function (resId) {
    let ret = {code: Code.FAIL, heroNum: 0, handbookNum: 0, ballComCount: 0};
    let config = dataApi.allData.data["PlayerHandbook"][resId].Card;
    let item = config.substring(0, config.length - 2);
    let needCount = config.substring(config.length- 1, config.length);
    let itemCount = this.player.item.getItemCountByResId(Number(item));
    let itemUid = this.player.item.getItemUidByResId(Number(item));
    if(itemCount < Number(needCount)){
        ret.code = Code.ITEM_FAIL;
        return ret;
    }
    //已经激活
    if(this.ballHandbook.indexOf(resId) != -1){
        ret.code = Code.HAVE_FAIL;
        return ret;
    }
    //删除物品
    this.player.item.delItem(itemUid, Number(needCount));
    //激活
    this.ballHandbook.push(resId);
    //检查图鉴是否有球员被激活
    this.checkIsHaveHandbookHeroActive(resId);
    //检查是否有组合图鉴激活
    this.checkIsHaveHandbookActive();
    //计算实力加成
    this.calcActual(resId, 1);
    this.reCalcAllHeroAttr();
    ret.code = Code.OK;
    ret.heroNum = this.ballHandbookActual * 11;
    ret.handbookNum = this.ballComHandbookActual * 11;
    ret.ballComCount = this.ballComCount;
    ret.ballCount = this.ballHandbook.length;
    return ret;
}

//组合图鉴修复
FootballGround.prototype.fixBallHandbook = function () {
    if(!this.ballComHandbook) {
        this.ballComHandbook = [];
    }

    if(this.ballComHandbook.length < 1) {
        this.ballComHandbook = initHandbook();
    }

    //先清一遍组合图鉴
    for(let j in this.ballComHandbook){
        this.ballComHandbook[j].isCalc = 0;
    }

    //重置组合实力
    this.ballComHandbookActual = 0;
    //重置激活个数
    this.ballComCount = 0;

    for(let i = 0; i < this.ballHandbook.length; ++i) {
        let resId = this.ballHandbook[i];
        //检查图鉴是否有球员被激活
        this.checkIsHaveHandbookHeroActive(resId);
        //检查是否有组合图鉴激活
        this.checkIsHaveHandbookActive();
    }

    let train = this.trainGround.get(this.uid);
    if(!train) {
        return;
    }
    let level = train.Level;
    //训练场检查
    for(let [k, v] of this.player.heros.allheros) {
        let hero = this.player.heros.getHero(k);
        if(!hero) {
            continue;
        }

        if(hero.TrainCount > level) {
            hero.TrainCount = level;
        }
    }
}

/**
 * 获取球迷  
 */
FootballGround.prototype.getBallFans = function () {
    if(this.ballFan < 0)
        this.ballFan = 0;
    return this.ballFan;
}

/**
 * 获取声望  
 */
FootballGround.prototype.getPrestige = function () {
    return this.prestige;
}
/***********************************************名人堂*********************************************************/

//检查所有球员
FootballGround.prototype.checkAllHeroIsRetirement = function(){
    return [];
}

/**
 * 解锁名人堂格子
 * @param ground 建筑类型
 * @param level 名人堂等级
 */
FootballGround.prototype.unlockNotablePos = function(ground, level){
    let config = dataApi.allData.data["HallOfFame"];
    for(let i in config){
        let index = config[i].ID;
        //最大20格子
        if(index > 19) {
            continue;
        }
        if(config[i].Level < level){
            //过滤已解锁
            if(ground.NotablePos[index].state === 2 || ground.NotablePos[index].state === 3){
                continue;
            }
            ground.NotablePos[index].state = 2;
        }
    }
}


/**
 * 把球员放入名人堂
 * @param uid
 */
FootballGround.prototype.inputNotablePos = function(uid){
    let ret = {code: Code.FAIL, notablePrestige: 0, notablePos: [], retirementList: []};
    let hero = this.player.getOneHero(uid);
    if(!hero) {
        return ret;
    }
    let notable = this.notableGround.get(this.uid);
    if(!notable){
        return ret;
    }

    //检查是否在退役列表
    let retirementIndex = this.checkRetirementListIsHaveHero(uid);
    if(retirementIndex === -1) {
        return ret;
    }

    let resId = hero.ResID;
    let index = this.firstJoinHofList.indexOf(resId);
    //首次进入增加声望
    let addNum = 0;
    if(index === -1){
        //获取球员所加声望值
        addNum = dataApi.allData.data["Footballer"][resId].Prestige;
        if(typeof(addNum) === "undefined"){
            ret.code = Code.CONFIG_FAIL;
            return ret;
        }
    }

    //检查名人堂是否已有相同球员
    let findIndex = this.checkHofIsHaveHero(notable.NotablePos, resId);
    if(findIndex === -1) {
        let isOk = false;
        for(let i in notable.NotablePos){
            if(notable.NotablePos[i].state === 2){
                notable.NotablePos[i].uid = uid;
                notable.NotablePos[i].resId = resId;
                notable.NotablePos[i].state = 3;
                isOk = true;
                break;
            }
        }

        //如果没成功就是格子不够
        if(!isOk){
            ret.code = Code.POS_FAIL;
            return ret;
        }
        this.firstJoinHofList.push(resId);
        //删除退役列表里的记录
        this.retirementList.splice(retirementIndex, 1);
    }else {
        let tempUid = notable.NotablePos[findIndex].uid;
        notable.NotablePos[findIndex].uid = uid;
        this.retirementList[retirementIndex].uid = tempUid;
    }

    this.notablePrestige += addNum;

    ret.code = Code.OK;
    ret.notablePrestige = this.notablePrestige;
    ret.notablePos = this.getHofList();
    ret.retirementList = this.getRetirementList();
    return ret;
};

/**
 * 把球员加入退役列表
 * @param uid
 */
FootballGround.prototype.addHeroToRetirementList = function(heroList){
    let ret = {code: Code.FAIL, retirementList: []};
    //检查是否满足进入退役列表要求
    let result = this.checkIsMeetJoin(heroList);
    if(result.code !== Code.OK) {
        ret.code = result.code;
        return ret;
    }

    for(let i = 0; i < heroList.length; ++i) {
        let uid = heroList[i];
        let hero = this.player.getOneHero(uid);
        let resId = hero.ResID;
        let heroInfo = {
            resId: resId,
            uid: uid
        }
        this.retirementList.push(heroInfo);
        hero.isDie = 1;    //退役
        hero.Status = commonEnum.HERO_STATUS.COMMONLY;   //状态一般
        this.player.heros.reCalcAttrRevision(uid);
        // this.player.heros.reCalcAttr(uid);
    }

    ret.code = Code.OK;
    ret.retirementList = this.getRetirementList();
    return ret;
};

//获取名人堂列表
FootballGround.prototype.getHofList = function() {
    let notable = this.notableGround.get(this.uid);
    if(!notable){
        return [];
    }

    let hofList = [];
    let index = 0;
    for (let i in notable.NotablePos) {
        let uid = notable.NotablePos[i].uid;
        let resId = notable.NotablePos[i].resId;
        if(uid !== "") {
            let hero = this.player.getOneHero(uid);
            if(!hero) {
                continue;
            }
            let rating = this.player.heros.getRating(uid);
            hofList[index] = {};
            hofList[index].uid = uid;
            hofList[index].resId = resId;
            hofList[index].star = hero.Star;
            hofList[index].trainCount = hero.TrainCount;
            hofList[index].isUseYonger = hero.isUseCareerMedicine;
            hofList[index].rating = rating;
            hofList[index].state = notable.NotablePos[i].state;
            index ++;
        }else {
            hofList[index] = {};
            hofList[index].uid = uid;
            hofList[index].resId = resId;
            hofList[index].star = 0;
            hofList[index].trainCount = 0;
            hofList[index].isUseYonger = 0;
            hofList[index].rating = 0;
            hofList[index].state = notable.NotablePos[i].state;
            index ++;
        }
    }
    return hofList;
}

//检查是否满足需求
FootballGround.prototype.checkIsMeetJoin= function(heroList){
    let ret = {code: Code.OK};
    for(let i = 0; i < heroList.length; ++i) {
        let uid = heroList[i];
        let hero = this.player.getOneHero(uid);
        if (!hero) {
            ret.code = Code.FAIL;
            break;
        }
        let config = dataApi.allData.data["Footballer"][hero.ResID];
        //超过退休期,检查球员是否在阵容中
        let team = this.player.teamFormations.checkHeroInArbitrarilyFormation(uid);
        //检查是否在退役列表
        let retirementIndex = this.checkRetirementListIsHaveHero(uid);
        //球员在场上
        if (team.length > 0) {            //在阵容中
            ret.code = Code.FOOTGROUND_CODE.IN_TEAM_FAIL;
            break;
        } else if (hero.isTreat === 1) {   //在治疗中
            ret.code = Code.FOOTGROUND_CODE.IN_TREAT_FAIL;
            break;
        } else if (hero.isTrain === 1) {    //在训练中
            ret.code = Code.FOOTGROUND_CODE.IN_TRAIN_FAIL;
            break;
        } else if (hero.TreatyDay < 1) {    //合约到期
            ret.code = Code.FOOTGROUND_CODE.TREATYDAY_FAIL;
            break;
        }else if (hero.isDie === 1) {    //已经退役
            ret.code = Code.FOOTGROUND_CODE.JOIN_HOF_FAIL;
            break;
        } else if (timeUtils.futureDayInterval(hero.leftTime) > 0) {    //未到退役时间
            ret.code = Code.FOOTGROUND_CODE.JOIN_HOF_TIME_FAIL;
            break;
        } else if (hero.Health !== 1) {   //受伤不能进
            ret.code = Code.FOOTGROUND_CODE.ILL_FAIL;
            break;
        } else if (retirementIndex !== -1) {       //已经在退役列表
            ret.code = Code.FOOTGROUND_CODE.JOIN_HOF_FAIL;
            break;
        }else if((config.Color == commonEnum.HERO_SYSTEM.COLOR_1 || config.Color == commonEnum.HERO_SYSTEM.COLOR_3
            || config.Color == commonEnum.HERO_SYSTEM.COLOR_6 || config.Color == commonEnum.HERO_SYSTEM.COLOR_9) && hero.Star < 9) {
            ret.code = Code.FOOTGROUND_CODE.HERO_STAR_FAIL;
            break;
        }
    }

    return ret;
}


//检查名人堂是否有此球员
FootballGround.prototype.checkHofIsHaveHero = function(arr, resId){
    let index = -1;
    for(let i = 0; i < arr.length; ++i) {
        if(resId === arr[i].resId) {
            index = i;
            break;
        }
    }
    return index;
}

//检查退役列表是否有此球员
FootballGround.prototype.checkRetirementListIsHaveHero = function(uid){
    let index = -1;
    for(let i = 0; i < this.retirementList.length; ++i) {
        if(uid === this.retirementList[i].uid) {
            index = i;
            break;
        }
    }
    return index;
}

FootballGround.prototype.delRetirementListAndHofHero = function(uid){
    let index = -1;
    for(let i = 0; i < this.retirementList.length; ++i) {
        if(uid === this.retirementList[i].uid) {
            index = i;
            break;
        }
    }

    if(index !== -1) {
        this.retirementList.splice(index, 1);
    }

    let hof = this.notableGround.get(this.uid);
    if(!hof) {
        return;
    }

    for(let i in hof.NotablePos){
        if(hof.NotablePos[i].uid === uid){
            hof.NotablePos[i].uid = "";
            hof.NotablePos[i].resId = 0;
            hof.NotablePos[i].state = 2;
            break;
        }
    }
}

//获取退役球员列表
FootballGround.prototype.getRetirementList = function(){
    let retirementList = [];
    let index = 0;

    for(let i in this.retirementList) {
        let uid = this.retirementList[i].uid;
        let resId = this.retirementList[i].resId;
        let hero = this.player.getOneHero(uid);
        let rating = this.player.heros.getRating(uid);
        retirementList[index] = {};
        retirementList[index].uid = uid;
        retirementList[index].resId = resId;
        retirementList[index].star = hero.Star;
        retirementList[index].trainCount = hero.TrainCount;
        retirementList[index].isUseYonger = hero.isUseCareerMedicine;
        retirementList[index].rating = rating;
        index ++;
    }
    return retirementList;
}

//获取名人堂列表
FootballGround.prototype.getNotablePosInfo = function(){
    let ret = {notablePos: [], notablePrestige: 0};
    let notable = this.notableGround.get(this.uid);
    //先检查有没有格子解锁
    this.unlockNotablePos(notable, notable.Level);
    ret.notablePos = this.getHofList();
    ret.notablePrestige = this.notablePrestige;
    ret.retirementList = this.getRetirementList();
    return ret;
}

/********************************************************名人堂*****************************************************************/
//修复部分玩家数据
FootballGround.prototype.fixHospitalPosData = function(){
    let hospital = this.hospitalGround.get(this.uid);
    let trainObj = this.trainGround.get(this.uid);
    for(let [k,v] of this.player.heros.allheros) {
        let hero = this.player.getOneHero(k);
        if(!hero) {
            continue;
        }

        //治疗中
        if(hero.isTreat === 1) {
            let isTreat = false;
            for (let i = 0; i < hospital.HospitalPos.length; ++i) {
                if (hospital.HospitalPos[i].uid === k) {
                    isTreat = true;
                    break;
                }
            }

            if(!isTreat) {
                hero.isTreat = 0;
            }
        }

        //训练中
        if(hero.isTrain === 1) {
            let isTrain = false;
            for(let j = 0; j < trainObj.TrainPos.length; ++j) {
                if(trainObj.TrainPos[j].pos === k) {
                    isTrain = true;
                    break;
                }
            }

            if(!isTrain) {
                hero.isTrain = 0;
            }
        }
    }
}


/**
 *获取医疗室信息
 * @param uid  球员UID
 */
FootballGround.prototype.getHospitalPosInfo = function(){ 
    let ret = {
        code:Code.FAIL, 
        hospitalPos: []
    };
    let hospital = this.hospitalGround.get(this.uid);
    //是否有解锁
    let needLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.HOSPITAL_POS].Param;
    let vipLevel = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.HOSPITAL_VIP].Param;
    if(!needLevel || !vipLevel) {
        needLevel = 10;
        vipLevel = 2;
    }
    if(hospital.Level >= needLevel || this.player.vip >= vipLevel){
        if(hospital.HospitalPos[1].state === 1) {
            hospital.HospitalPos[1].state = 2;
        }
    }

    let hospitalPos = [];
    let nowTime = timeUtils.now();
    for (let i = 0; i < hospital.HospitalPos.length; ++i) {
        hospitalPos[i] = {};
        if(hospital.HospitalPos[i].uid !== "") {
            //如果球员列表不存在
            if(!this.player.heros.checkIsHaveHero(hospital.HospitalPos[i].uid)) {
                hospital.HospitalPos[i].uid = "";
                hospital.HospitalPos[i].beginTime = 0;
                hospital.HospitalPos[i].endTime = 0;
                hospital.HospitalPos[i].state = 2;
            }
        }
        //有人
        let endTime = hospital.HospitalPos[i].endTime * 1000;
        if(hospital.HospitalPos[i].uid !== "" && hospital.HospitalPos[i].state !== 4) {
            if(nowTime - hospital.HospitalPos[i].beginTime >= endTime) {
                hospital.HospitalPos[i].beginTime = 0;
                hospital.HospitalPos[i].endTime = 0;
                hospital.HospitalPos[i].state = 4;      
            }
        }
        hospitalPos[i].uid = hospital.HospitalPos[i].uid;
        hospitalPos[i].state = hospital.HospitalPos[i].state;
        hospitalPos[i].endTime = hospital.HospitalPos[i].beginTime + endTime;
    }
    ret.code = Code.OK; 
    ret.hospitalPos = hospitalPos;
    return ret;
}

/**
 * 把球员放入医疗中心
 * @param uid  球员UID
 * @param index  位置
 */
FootballGround.prototype.inputHospitalPos = function(uid, index){ 
    let ret = {code: Code.FAIL};
    let hospital = this.hospitalGround.get(this.uid);
    let hero = this.player.heros.getHero(uid);
    if(!hero || !hospital) {
        return ret;
    }

    if(index < 0 || index > 1) {
        ret.code = Code.RANGE_FAIL;
        return ret;
    }

    //检查是否在阵容中
    // let team = this.player.teamFormations.checkHeroInArbitrarilyFormation(uid);
    // if(team.length > 0) {
    //     ret.code = Code.HERO_CODE.IN_TEAM;
    //     return ret;
    // }

    //未解锁
    if(hospital.HospitalPos[index].state === 1) {
        ret.code = Code.UNLOCK_FAIL;
        return ret; 
    }

    //已经在治疗了不能再放
    if(hero.isTreat) {
        ret.code = Code.HERO_CODE.IN_TREAT;
        return ret;
    }

    //已经有人了就不能再放了
    if(hospital.HospitalPos[index].uid !== "") {
        ret.code = Code.HERO_CODE.HAVE_HERO;
        return ret;
    }

    let config = dataApi.allData.data["FieldHospital"];
    let timeReduce = 0;
    for(let i in config) {
        if(config[i].Level === hospital.Level) {
            timeReduce = config[i].TimeReduce;
            break;
        }
    }

    let needTime = 0;
    let temp = 0;
    if(hero.Health === commonEnum.HERO_HEALTH_STATUS.SLIGHT_INJURY) {
        //轻微伤
        temp = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.SLIGHT_INJURY_TIME].Param
        needTime = Math.floor(temp - (temp * timeReduce / 10000)) ;
    }else if(hero.Health === commonEnum.HERO_HEALTH_STATUS.MINOR_INJURY) {
        //轻伤
        temp = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.MINOR_INJURY_TIME].Param;
        needTime = Math.floor(temp - (temp * timeReduce / 10000)) ;
    }else if(hero.Health === commonEnum.HERO_HEALTH_STATUS.SERIOUS_INJURY) {
        //重伤
        temp = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.SERIOUS_INJURY_TIME].Param;
        needTime = Math.floor(temp - (temp * timeReduce / 10000)) ;
    }else {
        //健康状态直接返回
        ret.code = Code.HERO_CODE.HEALTH;
        return ret;
    }
    if(!needTime) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    //将球员治疗状态置为 1
    hero.isTreat = 1;
    hospital.HospitalPos[index].uid = uid;
    hospital.HospitalPos[index].state = 3;
    hospital.HospitalPos[index].beginTime = timeUtils.now();
    hospital.HospitalPos[index].endTime = needTime;
    ret.code = Code.OK; 
    return ret;
}

/**
 * 领取医疗中心球员
 * @param index
 */
FootballGround.prototype.getHospitalPosHero = function(index){ 
    let ret = {code: Code.FAIL};
    let hospital = this.hospitalGround.get(this.uid);
    if(!hospital) {
        return ret;
    }

    //没有球员
    if(hospital.HospitalPos[index].uid === "") {
        ret.code = Code.HERO_CODE.NOT_CHOOSE;
        return ret;
    }

    let nowTime = timeUtils.now();
    let beginTime = hospital.HospitalPos[index].beginTime;
    let endTime = hospital.HospitalPos[index].endTime * 1000;
    if(nowTime - beginTime < endTime) {
        ret.code = Code.TIME_FAIL;
        return ret;
    }

    let uid = hospital.HospitalPos[index].uid;
    let hero = this.player.heros.getHero(uid);
    if(!hero) {
        return ret;
    }
    //置状态
    hero.Health = commonEnum.HERO_HEALTH_STATUS.HEALTH;
    //重新计算球员实力
    this.player.heros.reCalcAttrRevision(uid);
    // this.player.heros.reCalcAttr(uid);
    //检查是否在阵容中 在的话需要重新计算球队属性
    this.player.checkHeroInArbitrarilyFormation(uid)

    //将球员治疗状态置为 0
    hero.isTreat = 0;
    //清空数据
    hospital.HospitalPos[index].uid = "";
    hospital.HospitalPos[index].state = 2;
    hospital.HospitalPos[index].beginTime = 0;
    hospital.HospitalPos[index].endTime = 0;
    ret.code = Code.OK;
    this.player.updateRedDotHintState(commonEnum.REDDOT_HINT.HOSPITAL);//红点
    return ret;
}

/**
 * 球员治疗加速
 * @param index  位置
 * @param resId  物品ID
 * @param num    物品数量
 */
FootballGround.prototype.accelerationHospitalPosTime = function(index, resId, num){ 
    let ret = {code: Code.FAIL};
    if(num > 999 || num < 1) {
        ret.code = Code.PARAM_FAIL;
        return ret;
    }

    let hospital = this.hospitalGround.get(this.uid);
    if(!hospital) {
        return ret;
    }
    
    //没人
    if(hospital.HospitalPos[index].uid === "") {
        return ret;
    }

    //已经完成了
    if(hospital.HospitalPos[index].state === 4) {
        ret.code = Code.OK
        return ret;
    }

    let timeReduce = 0;
    let itemCount = 0;
    let itemUid = "";
    let uid = hospital.HospitalPos[index].uid;
    let hero = this.player.heros.getHero(uid);
    if(!hero) {
        return ret;
    }

    if(resId === 10800) {
        //普通治疗箱
        timeReduce = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.NORMAL_MEDKIT].Param;
        itemCount = this.player.item.getItemCountByResId(resId);
        itemUid = this.player.item.getItemUidByResId(resId);
    }else if(resId === 10801) {  
        //高级治疗箱
        timeReduce = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.SENIOR_MEDKIT].Param;
        itemCount = this.player.item.getItemCountByResId(resId);
        itemUid = this.player.item.getItemUidByResId(resId);
    }else if(resId === 10802) {
        //完美治疗箱
        timeReduce = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.PERFECT_MEDKIT].Param;
        itemCount = this.player.item.getItemCountByResId(resId);
        itemUid = this.player.item.getItemUidByResId(resId);
    }else if(resId === 10803) {
        //应急治疗箱
        if(hero.Health !== commonEnum.HERO_HEALTH_STATUS.SLIGHT_INJURY 
            && hero.Health !== commonEnum.HERO_HEALTH_STATUS.MINOR_INJURY) {
            ret.code = Code.CARD_FAIL;
            return ret;
        }
        itemCount = this.player.item.getItemCountByResId(resId);
        itemUid = this.player.item.getItemUidByResId(resId);
    }else if(resId === 10804) {
        //紧急治疗箱
        if(hero.Health !== commonEnum.HERO_HEALTH_STATUS.SERIOUS_INJURY) {
            ret.code = Code.CARD_FAIL;
            return ret;
        }
        itemCount = this.player.item.getItemCountByResId(resId);
        itemUid = this.player.item.getItemUidByResId(resId);
    }else {
        ret.code = Code.PARAM_FAIL;
        return ret;
    }

    if(itemCount < num) {
        ret.code = Code.ITEM_FAIL;
        return ret;
    }

    if(itemUid === "") {
        ret.code = Code.ITEM_FAIL;
        return ret;
    }

    //删除物品
    this.player.item.delItem(itemUid, num);
    if(resId === 10803 || resId === 10804) {
        hospital.HospitalPos[index].endTime = 0;
    } else {
        hospital.HospitalPos[index].endTime -= (timeReduce * num);
    }

    if(hospital.HospitalPos[index].endTime <= 0) {
        hospital.HospitalPos[index].beginTime = 0;
        hospital.HospitalPos[index].endTime = 0;
        hospital.HospitalPos[index].state = 4;
    }
    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.MEDICAL_COST, [resId, num], {});
    ret.code = Code.OK;
    return ret;
}

FootballGround.prototype.addTrainCount = function(num)
{
    logger.info("addTrainCount", this.trainCount, num);
    this.trainCount  += num;
};

FootballGround.prototype.getTrainCount = function()
{
   return this.trainCount;
};

FootballGround.prototype.checkAllGroundStatus = function()
{   
    let isUpgrade = false;
    let isUpgrade1 = this.adminGround.get(this.uid).IsUpgrade;
    let isUpgrade2 = this.mainGround.get(this.uid).IsUpgrade;
    let isUpgrade3 = this.trainGround.get(this.uid).IsUpgrade;
    let isUpgrade4 = this.transferGround.get(this.uid).IsUpgrade;
    let isUpgrade5 = this.hospitalGround.get(this.uid).IsUpgrade;
    let isUpgrade6 = this.notableGround.get(this.uid).IsUpgrade;
    //没有一个建筑在升级才能升级
    if(!isUpgrade1 && !isUpgrade2 && !isUpgrade3 && !isUpgrade4
        && !isUpgrade5 && !isUpgrade6) {
        isUpgrade = true;
    }
    return isUpgrade;
};

//检查球迷奖励   6点刷新
FootballGround.prototype.checkBallFansRewardTime = function() {
    //刷新过了
    if(!this.isGetBallFansRewrd) {
        return;
    }
    let dateHour = new Date(this.getBallFansRewrdTime).getHours();
    let nowTime = new Date().getHours();
    //玩家是0-6点之间领取的
    if(dateHour < 6) {
        if(!timeUtils.isToday(this.getBallFansRewrdTime)) {
            this.isGetBallFansRewrd = 0;
        } else {
            //如果已经超过6点就刷新
            if(nowTime >= 6) {
                this.isGetBallFansRewrd = 0;
            }
        }
    } else {
        if(!timeUtils.isToday(this.getBallFansRewrdTime)) {
            //判断与当前时间相差几天
            let dayInterval = timeUtils.dayInterval(this.getBallFansRewrdTime)
            if(dayInterval == 1) {
                //如果已经超过6点就刷新
                if(nowTime >= 6) {
                    this.isGetBallFansRewrd = 0;
                }
            } else {
                this.isGetBallFansRewrd = 0;
            }
        }
    }
}

//领取球迷奖励
FootballGround.prototype.getBallFansReward = function() {
    let ret = {code:Code.FAIL, num: 0, isGetBallFansRewrd: this.isGetBallFansRewrd}
    let ballFan = this.getBallFans();
    let ballRatio = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.BALL_FANS_RATIO].Param;
    if(!ballRatio) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    //已领取过
    if(this.isGetBallFansRewrd) {
        ret.code = Code.GET_FAIL;
        return ret;
    }

    this.isGetBallFansRewrd = 1;
    //记录领取时间
    this.getBallFansRewrdTime = timeUtils.now();
    let num = Math.floor(ballFan * ballRatio);
    this.player.addResource(commonEnum.PLAY_INFO.cash, num);

    ret.code = Code.OK;
    ret.num = num;
    ret.isGetBallFansRewrd = this.isGetBallFansRewrd;
    return ret;
}


//秒球场CD  type
FootballGround.prototype.seckillGroundTime = function(type) {
    let ret ={};
    let ground = {};
    let uid = this.uid;
    switch (type) {
        case 1:
            ground = this.adminGround.get(uid);
            break;
        case 2:
            ground = this.mainGround.get(uid);
            break;
        case 3:
            ground = this.trainGround.get(uid);
            break;
        case 4:
            ground = this.transferGround.get(uid);
            break;
        case 5:
            ground = this.hospitalGround.get(uid);
            break;
        case 6:
            ground = this.notableGround.get(uid);
            break;
        default:
            ret.code = Code.FAIL;
            break;
    }

    if(!ground || JSON.stringify(ground) === "{}") {
        ret.code = Code.FAIL;
        return ret;
    }

    //球场不在升级
    if(!ground.IsUpgrade) {
        ret.code = Code.FAIL;
    }

    let config = this.getConfigByType(type, ground.Level);
    if(!config) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }
    //当前时间
    let nowTime = this.getSystemTime();
    //所需时间
    let needTime = ground.UpTime + config.Time;
    //剩余时间 单位:分
    let surplusTime = Math.ceil((needTime - nowTime) / 60);
    let needGold = 0;

    let first_down_stage = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.FIRST_DOWN_STAGE].Param;
    let first_up_stage = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.FIRST_UP_STAGE].Param;
    let second_down_stage = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.SECOND_DOWN_STAGE].Param;
    let second_up_stage = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.SECOND_UP_STAGE].Param;
    let three_down_stage = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.THREE_DOWN_STAGE].Param;
    if(!first_down_stage || !first_up_stage || !second_down_stage || !second_up_stage || !three_down_stage) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    let first_stage_gold = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.FIRST_STAGE_GOLD].Param;
    let second_stage_gold = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.SECOND_STAGE_GOLD].Param;
    let three_stage_gold = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.THREE_STAGE_GOLD].Param;
    if(!first_stage_gold || !second_stage_gold || !three_stage_gold) {
        ret.code = Code.CONFIG_FAIL;
        return ret;
    }

    let timeOut = 0;
    let timeOutGold = 0;
    if((needTime - nowTime) <= 0) {
        needGold = 0;
    }else if(first_down_stage <= surplusTime && surplusTime <= first_up_stage) {
        needGold = first_stage_gold;
    } else if(second_down_stage <= surplusTime && surplusTime <= second_up_stage) {
        timeOut = surplusTime - first_up_stage;
        timeOutGold = Math.ceil(timeOut * second_stage_gold / 10);
        needGold = first_stage_gold + timeOutGold;
    }else if (surplusTime >= three_down_stage) {
        timeOut = surplusTime - second_up_stage;
        let t2 =  Math.ceil((second_up_stage - first_up_stage) * second_stage_gold / 10);
        timeOutGold = Math.ceil(timeOut * three_stage_gold / 10);
        needGold = first_stage_gold + timeOutGold + t2;
    }

    
    //钱不够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, needGold)){
        ret.code = Code.GOLD_FALL;
        return ret;
    }

    //扣钱
    this.player.subtractResource(commonEnum.PLAY_INFO.gold, needGold);
    ground.Level ++;
    ground.IsUpgrade = 0;
    if (type == 2) {
        this.addPrestige(config.Prestige);
        this.player.addExp(config.Prestige);
        this.addBallFans(config.Fans);
        //主球场升级-刷新训练场数据
        // let maxResId = this.calcGroundMatchFieldMaxResId(ground.Level);
        // for(let i=0;i<3;i++) {
        //     this.groundMatch.fieldList[i].resId = maxResId - i;
        // }
    } else if(type == 6){
        this.addPrestige(config.Prestige);
        this.player.addExp(config.Prestige);
        this.addBallFans(config.Fans);
    } else {
        this.addPrestige(config.Prestige);
        this.player.addExp(config.Prestige);
    }

    ret.code = Code.OK;
    return ret;
}   

/******************************************************消息结构体********************************************************/
//获取球场信息--给客户端的消息结构体
FootballGround.prototype.makeClientGroundInfo = function (ground) {
    let groundInfo = {}
    if (!ground) {
        return groundInfo;
    }
    groundInfo.type = ground.Type;
    groundInfo.level = ground.Level;
    groundInfo.upTime = ground.UpTime;
    groundInfo.isUpgrade = ground.IsUpgrade;
    groundInfo.isUnLock = ground.IsUnLock;
    groundInfo.isLive = ground.IsLive;
    let rewardList = []
    for(let i = 0; i < ground.rewardPack.length; ++i) {
        let num = Math.floor(ground.rewardPack[i].Cur);
        let item = {
            id: i,
            num: num
        }
        rewardList.push(item);
    }
    groundInfo.rewardList = rewardList;
    return groundInfo;
}

//根据类型打包球场信息
FootballGround.prototype.makeClientGroundInfoByType = function (type) {
    let uid = this.uid;
    let ground = {};
    switch (type) {
        case 1:
            ground = this.adminGround.get(uid);
            break;
        case 2:
            ground = this.mainGround.get(uid);
            break;
        case 3:
            ground = this.trainGround.get(uid);
            break;
        case 4:
            ground = this.transferGround.get(uid);
            break;
        case 5:
            ground = this.hospitalGround.get(uid);
            break;
        case 6:
            ground = this.notableGround.get(uid);
            break;
        default:
            break;
    }

    let groundInfo = {}
    if (!ground || JSON.stringify(ground) ==="{}") {
        return groundInfo;
    }
    groundInfo = this.makeClientGroundInfo(ground);
    return groundInfo;
}

//获取别人球场信息--给客户端的消息结构体
FootballGround.prototype.makeClientOtherGroundInfo = function (ground) {
    let groundInfo = {}
    if (!ground) {
        return groundInfo;
    }
    groundInfo.type = ground.Type;
    groundInfo.level = ground.Level;
    groundInfo.isUnLock = ground.IsUnLock;
    groundInfo.isLive = ground.IsLive;
    return groundInfo;
}
/**
 * 获取别人球场所有建筑信息
 * @param uid   个人UID
 */
FootballGround.prototype.getOtherGroundAllInfo = function (uid) {
    let allGround = {
        adminGround: {},
        mainGround: {},
        trainGround: {},
        transferGround: {},
        hospitalGround: {},
        notableGround: {},
    }
    for (let i = 1; i < 7; ++i) {
        let ground = {};
        switch (i) {
            case 1:
                ground = this.adminGround.get(uid);
                allGround.adminGround = this.makeClientOtherGroundInfo(ground);
                break;
            case 2:
                ground = this.mainGround.get(uid);
                allGround.mainGround = this.makeClientOtherGroundInfo(ground);
                break;
            case 3:
                ground = this.trainGround.get(uid);
                allGround.trainGround = this.makeClientOtherGroundInfo(ground);
                break;
            case 4:
                ground = this.transferGround.get(uid);
                allGround.transferGround = this.makeClientOtherGroundInfo(ground);
                break;
            case 5:
                ground = this.hospitalGround.get(uid);
                allGround.hospitalGround = this.makeClientOtherGroundInfo(ground);
                break;
            case 6:
                ground = this.notableGround.get(uid);
                allGround.notableGround = this.makeClientOtherGroundInfo(ground);
                break;
            default:
                break;
        }
    }
    return allGround;
}

FootballGround.prototype.calcHeroTrainerAttr = function(heroUid) {
    let transfer = this.transferGround.get(this.player.playerId);
    if (!transfer)
    {
        //logger.error("calcHeroTrainerAttr: not transfer!", heroUid);
        return;
    }

    let hero = this.player.heros.getHero(heroUid);
    if (!hero)
    {
        return;
    }

    for(let j in commonEnum.ONE_LEVEL_ATTR_NAMES)
    {
        //logger.info("calcHeroTrainerAttr: ", heroUid, hero.oneLevelAttr);
        if(j == "ResistanceDamage")
        {
            continue;
        }
        hero.oneLevelAttr[j].Trainer = 0;
    }
    
    let transferPos = transfer.TransferPos;
    //logger.info("calcHeroTrainerAttr: heroUid, transferPos", heroUid, transferPos);
    for(let i in transferPos) {
        if (transferPos[i].resId <= 0)
        {
            continue;
        }

        let resId = transferPos[i].resId;
        let config = dataApi.allData.data["TrainningCoach"][resId];
        if (!config)
        {
            continue;
        }

        for(let j in commonEnum.ONE_LEVEL_ATTR_NAMES){
            if(j == "ResistanceDamage")
            {
                continue;
            }

            let value = config[j];
            if(value !== 0)
            {
                hero.oneLevelAttr[j].Trainer += config[j];
            }
        } 
    }
};

FootballGround.prototype.calcGroundMatchFieldMaxResId = function(mainGroundLevel) {
    let config = dataApi.allData.data["GroundMatchField"];
    let maxId = 0;
    if(mainGroundLevel < config[1].Level) {
        maxId = 3;
    }else {
        maxId = 20;
        for(let i in config) {
            if(mainGroundLevel < config[i].Level && parseInt(maxId) > parseInt(i)) {
                maxId = i;
            }
        }
        if(maxId > 15) {
            maxId = 15;
        }else {
            maxId = parseInt(maxId) + 2;
        }
    }
    logger.debug("calcGroundMatchFieldMaxResId maxId: ", maxId);
    return maxId;
};

//个人球场争夺战 ---------- Start --------------
FootballGround.prototype.initGroundMatch = function () {
    logger.debug("initGroundMatch ------------");
    //数据异常不加载
    if(!this.uid) {
        logger.error("initGroundMatch data error: uid: ", this.uid);
        return false;
    }
    if(!this.mainGround.get(this.uid)) {
        this.createFootballGround(this.uid);
    }

    //举报次数
    this.groundMatch.reportNum = 10;
    this.groundMatch.reportFreshTime = timeUtils.now();
    this.groundMatch.lastReportTime = 0;

    //我的训练场
    this.groundMatch.fieldList = [];
    //我的占领
    this.groundMatch.occupyFieldList = [];

    let level = this.mainGround.get(this.uid).Level;
    let maxId = this.calcGroundMatchFieldMaxResId(level);

    for(let i=0; i<3; i++) {
        //我的训练场数据
        let obj = {};
        obj.resId = maxId-i;               //等级从高到低
        //obj.battleLockTime = 0;            //战斗锁定
        obj.teamUid = "";                  //防守队伍uid
        obj.startTime = timeUtils.now();   //开始自产时间
        //占领用户信息
        obj.beOccupiedUid = "";            //占领的玩家uid
        obj.beOccupiedGid = "";            //占领的玩家gid
        obj.beOccupiedTeamUid = "";        //占领的阵容uid
        obj.beOccupiedTeamName = "";       //被占领
        obj.name = "";                     //占领玩家的名字
        obj.faceUrl = "";                  //占领玩家的头像
        obj.formationResId = 0;            //阵型id
        obj.attack = 0;                    //进攻值
        obj.defend = 0;                    //防守值
        obj.atkTactic = 0;                 //占领玩家的进攻战术
        obj.defTactic = 0;                 //占领玩家的防守战术
        obj.str = 0;                       //占领玩家的实力
        obj.occupyStartTime = 0;           //占领开始时间
        //保护信息
        obj.protectType = 0;               //保护类型: 0.无保护 1. 系统保护, 2.道具保护
        obj.protectEndTime = 0;            //保护结束时间

        obj.recordList = [];                   //记录
        this.groundMatch.fieldList.push(obj);

        //我的占领
        let occObj = {};
        occObj.teamUid = "";            //占领的队伍uid (进攻队伍)
        occObj.teamName = "";           //占领队伍的名字 (进攻队伍名字)

        occObj.resId = 0;               //占领的训练场resId
        occObj.occupyUid = "";          //占领的玩家Uid
        occObj.occupyGid = "";          //占领的玩家的Gid
        occObj.occupyTeamUid = "";      //占领队伍的teamUid (防守队伍)
        occObj.occupyFaceUrl = "";
        occObj.occupyTeamIndex = 3;           //占领坑位的index (index等于3, 等于3即是没有占领)
        occObj.name = "";
        occObj.protectType = 0;         //保护类型: 0.无保护 1. 系统保护, 2.道具保护
        occObj.protectEndTime = 0;      //保护结束时间
        occObj.occupyTime = 0;          //占领开始时间

        occObj.ballFan = 0;             //占领时的球迷数
        occObj.mainGroundLevel = 0;     //占领时的球场等级

        //occObj.searchList = [];         //搜索列表 (可能不需要)
        occObj.lastBeReportTime = 0;    //上次被举报时间 (计算CD)
        occObj.recordList = [];             //占领记录
        occObj.beReportedList = [];     //被举报列表
        this.groundMatch.occupyFieldList.push(occObj);
    }
    this.groundMatch.searchList = [];

    let defaultTeamUid = "";
    for(let [k,v] of this.player.teamFormations.allTeamFormations) {
        if(v.Type === commonEnum.FORMATION_TYPE.GROUND_DEF) {
            defaultTeamUid = v.Uid;
            break;
        }
    }

    //设置默认的防守阵型
    this.groundMatch.fieldList[0].teamUid = defaultTeamUid;

    //重置球迷数
    this.initBallFan();

    //logger.debug("this.groundMatch.fieldList: ", this.groundMatch.fieldList);
    return true;
};

//球场升级 -> 训练场数据变动
FootballGround.prototype.groundMatchFieldUpgrade = function () {
    //升级后设置训练场数值
    let level = this.mainGround.get(this.uid).Level;
    let config = dataApi.allData.data["GroundMatchField"];
    let maxId = 0;
    for(let i in config) {
        if(level <= config[i].Level) {
            maxId = i;
        }else {
            break;
        }
    }
    for(let i=0;i<3;i++) {
        this.groundMatch.fieldList[i].resId = maxId - i;
    }
};

//计算资产 俱乐部身价*50% + 球场储存上限*30% + 粉丝数量 * 36 * 3
FootballGround.prototype.calcTotalAssets = function() {
    let total = Math.floor(this.player.teamFormations.calcTeamValue(this.player.teamFormations.currTeamFormationId) * 50 / 100
        + this.calcAllBuildingCashStoreLimit() * 30 / 100 + this.ballFan * 36 * 3);
    logger.debug("FootballGround.calcTotalAssets total:", total);
    return total;
};

FootballGround.prototype.calcAllBuildingCashStoreLimit = function() {
    let vipUpperAddition = 0;
    if(this.player.vip > 0 && this.player.vip < 15) {
        vipUpperAddition = dataApi.allData.data["Vip"][this.player.vip].UpperAddition / 100;
    }
    let calcPerStoreLimit = function(config) {
        let storage = 0;
        for (let n = 0; n < config.Storage.length; ++n) {
            if(config.Storage[n].type === 1) {
                storage += config.Storage[n].count + Math.floor(config.Storage[n].count * vipUpperAddition);
            }
        }
        return storage;
    };
    let keyArr = ["adminGround", "transferGround", "notableGround"];
    let allStorage = 0;
    for(let i=0,len=keyArr.length; i<len; i++) {
        let ground = this[keyArr[i]].get(this.uid);
        let config = this.getConfigByType(ground.Type, ground.Level);
        allStorage += calcPerStoreLimit(config);
    }
    logger.debug("FootballGround.calcAllBuildingCashStoreLimit allStorage: ",  allStorage);
    return allStorage;
};

//初始化球迷数
FootballGround.prototype.initBallFan = function() {
    this.ballFan = 0;
    let keyArr = ["adminGround", "mainGround", "trainGround", "transferGround", "hospitalGround", "notableGround"];
    for(let i=0,len=keyArr.length; i<len; i++) {
        let ground = this[keyArr[i]].get(this.uid);
        let addFans = 0;
        if(ground.Type === 2 && ground.Level === 1) {
            addFans = dataApi.allData.data["SystemParam"][commonEnum.FOOTBALLGROUND.mainFans].Param;
        }else if(ground.Level > 1) {
            let config = this.getConfigByType(ground.Type, ground.Level - 1);
            addFans = config.FansShow;
        }
        logger.debug("initBallFan key, type, level: ", keyArr[i], ground.Type, ground.Level, addFans);
        this.addBallFans(addFans);
    }
    logger.debug("FootballGround.initBallFan ballFan: ", this.ballFan);
};

//预计奖励
FootballGround.prototype.calcExpectCash = function(list, index, isMine, fanNum, passHalfHour) {
    if(index < 0 || index >= 3 || !list[index]) {
        return 0;
    }
    let mainGroundLevel = this.mainGround.get(this.uid).Level;
    let fieldResId = list[index].resId;
    let teamUid = list[index].teamUid;
    //没有该训练场或者防守队伍时 收益为0
    if(!fieldResId || !teamUid) {
        logger.debug("calcMyExpectCash zero. ", fieldResId, teamUid);
        return 0;
    }
    let maxHalfHour = Math.floor(dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchRobMaxTime].Param / 1800);
    if(!passHalfHour && passHalfHour !== 0) {
        passHalfHour = maxHalfHour;
    }
    if(passHalfHour > maxHalfHour) {
        passHalfHour = maxHalfHour;
    }
    if(passHalfHour < 0) {
        passHalfHour = 0;
    }
    //上限
    let factor = dataApi.allData.data["GroundMatchField"][fieldResId].Ratio;
    let config = dataApi.allData.data["GroundMatchReward"];
    let rewardId = 0;
    for(let i in config) {
        if(config[i].Type === 1 && config[i].Level === mainGroundLevel) {
            rewardId = config[i].Id;
            break;
        }
    }
    logger.debug("rewardId -------- ", rewardId, mainGroundLevel, fanNum, factor, passHalfHour);
    //8小时占领
    let rewardCash = 0;
    if(rewardId !== 0 && config[rewardId]) {
        if(isMine) {
            //自产不计算粉丝
            rewardCash = Math.floor((config[rewardId]["Awardid2_1"]) * passHalfHour);
        }else {
            rewardCash = Math.floor((factor * config[rewardId]["Awardid1_1"] + fanNum*3/4) * passHalfHour);
        }
    }
    if(rewardCash > dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchMaxReward].Param) {
        rewardCash = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.GroundMatchMaxReward].Param;
    }
    return rewardCash;
};

//计算自产的预计收益
FootballGround.prototype.calcOwnFieldTotalCash = function() {
    let fieldList = this.groundMatch.fieldList;
    let reward = 0;
    let now = timeUtils.now();
    for(let i=0; i<fieldList.length; i++) {
        if(fieldList[i].startTime >= 0 && fieldList[i].resId > 0 && !fieldList[i].beOccupiedTeamUid && fieldList[i].teamUid) {
            let passHalfHour = Math.floor((now - fieldList[i].startTime) / 1000 / 1800);
            reward += this.calcExpectCash(fieldList, i, true, this.ballFan, passHalfHour);
            //logger.debug("calcOwnFieldTotalCash fieldList[i]", fieldList[i], reward);
        }
    }
    logger.debug("calcOwnFieldTotalCash: reward, fieldList", reward);
    return reward;
};

//删除个人球场的阵容
FootballGround.prototype.delGroundMatchTeam = function() {
    let fieldList = this.groundMatch.fieldList;
    let occupyFieldList = this.groundMatch.occupyFieldList;
    let teamFormations = this.player.teamFormations.allTeamFormations;//map
    for(let i in fieldList)
    {
        if(!!fieldList[i].teamUid && fieldList[i].teamUid !== "")
        {
            let teamUid = fieldList[i].teamUid;
            if(teamFormations.has(teamUid))
            {
                this.player.teamFormations.allTeamFormations.delete(teamUid);
            }
            fieldList[i].teamUid = "";                  //防守队伍uid
            fieldList[i].startTime = timeUtils.now();   //开始自产时间
            //占领用户信息
            fieldList[i].beOccupiedUid = "";            //占领的玩家uid
            fieldList[i].beOccupiedGid = "";            //占领的玩家gid
            fieldList[i].beOccupiedTeamUid = "";        //占领的阵容uid
            fieldList[i].beOccupiedTeamName = "";       //被占领
            fieldList[i].name = "";                     //占领玩家的名字
            fieldList[i].faceUrl = "";                  //占领玩家的头像
            fieldList[i].formationResId = 0;            //阵型id
            fieldList[i].attack = 0;                    //进攻值
            fieldList[i].defend = 0;                    //防守值
            fieldList[i].atkTactic = 0;                 //占领玩家的进攻战术
            fieldList[i].defTactic = 0;                 //占领玩家的防守战术
            fieldList[i].str = 0;                       //占领玩家的实力
            fieldList[i].occupyStartTime = 0;           //占领开始时间
        }
    }
    for(let i in occupyFieldList)
    {
        if(!!occupyFieldList[i].teamUid && occupyFieldList[i].teamUid !== "")
        {
            let teamUid = occupyFieldList[i].teamUid;
            if(teamFormations.has(teamUid))
            {
                this.player.teamFormations.allTeamFormations.delete(teamUid);
            }
            occupyFieldList[i].teamUid = "";            //占领的队伍uid (进攻队伍)
            occupyFieldList[i].teamName = "";           //占领队伍的名字 (进攻队伍名字)

            occupyFieldList[i].resId = 0;               //占领的训练场resId
            occupyFieldList[i].occupyUid = "";          //占领的玩家Uid
            occupyFieldList[i].occupyGid = "";          //占领的玩家的Gid
            occupyFieldList[i].occupyTeamUid = "";      //占领队伍的teamUid (防守队伍)
            occupyFieldList[i].occupyFaceUrl = "";
            occupyFieldList[i].occupyTeamIndex = 3;           //占领坑位的index (index等于3, 等于3即是没有占领)
            occupyFieldList[i].name = "";
            occupyFieldList[i].protectType = 0;         //保护类型: 0.无保护 1. 系统保护, 2.道具保护
            occupyFieldList[i].protectEndTime = 0;      //保护结束时间
            occupyFieldList[i].occupyTime = 0;          //占领开始时间

            occupyFieldList[i].ballFan = 0;             //占领时的球迷数
            occupyFieldList[i].mainGroundLevel = 0;     //占领时的球场等级
        }
    }
    for(let [k, v] of teamFormations)
    {
        if(v.Type === commonEnum.FORMATION_TYPE.GROUND_ATK || v.Type === commonEnum.FORMATION_TYPE.GROUND_DEF || v.Type === commonEnum.FORMATION_TYPE.GROUND)
        {
            teamFormations.delete(k);
        }
    }

    this.player.saveTeamFormation();
};