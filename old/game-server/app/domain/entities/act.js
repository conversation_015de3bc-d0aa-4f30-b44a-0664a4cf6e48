/**
 * Created by scott on 2019/7/08.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var commonEnum = require('../../../../shared/enum');
var dataApi = require('../../util/dataApi');
var TimeUtils = require('../../util/timeUtils');
var utils = require('../../util/utils');
var Constant = require("../../../../shared/constant");

let Act = function(player) 
{
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.globalCurrActList = new Map();            //活动列表  (可删除)
    this.globalActMgrInfo  = new Map();            //活动数据(存储)
    this.historyActList    = new Map();            //历史活动数据(埋点统计)
    this.firstChargeStatus = commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.NONE;  //首充标记
    this.openChargeStatus = commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.NONE;  //开服礼包标记
};

util.inherits(Act, EventEmitter);
module.exports = Act;

Act.prototype.initByDB = function(doc) 
{
	this.uid = doc.uid || this.uid;
    this.historyActList = this.toMap(doc.historyActList) || new Map();
    this.globalActMgrInfo = this.toMap(doc.globalActMgrInfo) || new Map();
    this.firstChargeStatus = doc.firstChargeStatus || commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.NONE;
    this.openChargeStatus = doc.openChargeStatus || commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.NONE;
    this.loadGlobalAct();
};

Act.prototype.toJSONforDB = function()
{
	var doc = {
		uid: this.uid,
        historyActList: this.toArr(this.historyActList),
        globalActMgrInfo: this.toArr(this.globalActMgrInfo),
        firstChargeStatus: this.firstChargeStatus,
        openChargeStatus: this.openChargeStatus,
	};
	return doc;
};

Act.prototype.toMap = function(arr) {
    var map =  new Map();
    if (!arr)
    {
        return map;
    }
  
    for (var i in arr)
    {
       const object = arr[i];
       var id = object["actId"];
       var actInfo = object["actInfo"];
       map.set(id, actInfo);
    }
    return map;
};

Act.prototype.toArr = function(map) {
    var arr =  [];
    if (!map)
    {
        return arr;
    }
  
    for (var [k, v] of map)
    {
       let obj = {
        actId: k,
        actInfo: v,
       };
       arr.push(obj);
    }
    return arr;
};

Act.prototype.loadGlobalAct = function()
{
    var config = dataApi.allData.data["ActiveControl"];
    if (!config) 
    {
        logger.error("loadGlobalAct: ActiveControl config not found!");
        return;
    }

    let now = TimeUtils.now();
    for (let key in config) 
    {
        const data = config[key];
        if (!data) continue;

        let resId = data.Id;
        if (resId <= 0) continue;

        let actType =  data.ActivityType;
        let startTimeStr = data.StartTime;
        let endTimeStr = data.EndTime;
        let startTime = 0;
        let endTime = 0;
        let timeType = data.TimeType;
        let periods = data.Periods;       //第几期

        if (timeType === commonEnum.ACT_TIME_TYPE.PERSISTENT) //持久
        {
            //持久性活动需要刷新周期
            //设置开始时间结束时间
            let refreshCycle = data.RefreshCycle;
            if (refreshCycle === commonEnum.ACT_REFRESH_TYPE.NONE)
            {
                logger.warn("loadGlobalAct: act config error!! actId, ActivityType,ActivityName", resId, data.ActivityType, data.ActivityName);
                continue;
            }else if (refreshCycle === commonEnum.ACT_REFRESH_TYPE.CROSS_DAY)
            {
                startTime = TimeUtils.beginningOfTodayByTime(now);
                endTime = TimeUtils.endingOfTodayByTime(now);
            }else if (refreshCycle === commonEnum.ACT_REFRESH_TYPE.CROSS_WEEK)
            {
                startTime = TimeUtils.getWeekStartTime(now);
                endTime = TimeUtils.getWeekEndTime(now);
            }else if (refreshCycle === commonEnum.ACT_REFRESH_TYPE.CROSS_MONTH)
            {
                startTime = TimeUtils.getMonthStartTime(now);
                endTime = TimeUtils.getMonthEndTime(now);
            }else 
            {  //配置表错误了
                logger.warn("loadGlobalAct: act config error!! actId, ActivityType,ActivityName", resId, data.ActivityType, data.ActivityName);
                continue;
            }
        }else if(timeType === commonEnum.ACT_TIME_TYPE.CYCLE) //周期性
        {
            //检查下活动过期没有
            startTime = this.timeToUnixTime(startTimeStr);
            endTime = this.timeToUnixTime(endTimeStr);
            //开服大礼包 不管有没有结束，调了期数就重置
            if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_OPEN_GIFT_BAG)
            {
                if (this.globalActMgrInfo.has(resId) && this.globalActMgrInfo.get(resId).periods !== periods) {
                    this.globalActMgrInfo.delete(resId);
                    this.openChargeStatus = commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.NONE //激活状态重置
                }
            }
            //活动结束 清理数据
            if(now < startTime || now > endTime) {
                if (actType !== commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE) {
                    if (this.globalCurrActList.has(resId)) {
                        this.globalCurrActList.delete(resId);
                    }

                    if (this.globalActMgrInfo.has(resId)) {
                        this.globalActMgrInfo.delete(resId);
                    }
                    continue;
                }
            }
        }

        //处理特殊类型 首充和月卡
        if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE)
        {
            if (this.firstChargeStatus === commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.ACTIVE) //激活状态，不显示首充
            {
                continue;
            }else
            {
                endTime = TimeUtils.getMaxTime();                                         //设置最大时间
            }
        }

        //处理特殊类型 开服礼包
        if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_OPEN_GIFT_BAG)
        {
            if (this.openChargeStatus === commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.ACTIVE) //激活状态，不显示
            {
                continue;
            }
        }

        //to be fixed xingtang
        if(now < startTime || now > endTime) {
            continue;
        }

        //期数不一样 清理活动数据
        if(timeType === commonEnum.ACT_TIME_TYPE.CYCLE) {
            if (this.globalCurrActList.has(resId)) {
                let oldActInfo = this.globalActMgrInfo.get(resId);
                if(oldActInfo.periods === undefined) {
                    oldActInfo.periods = 0;
                }
                logger.error("玩家uid:", this.uid, "活动信息:", "++", data.ActivityType,"++", data.ActivityName, "配置表期数:", periods, "DB期数:", oldActInfo.periods);
                if(periods !== oldActInfo.periods) {
                    logger.error("玩家uid:", this.uid, "delete 缓存ActivityName", "++", data.ActivityType,"++", data.ActivityName, "配置表期数:", periods, "DB期数:", oldActInfo.periods);
                    this.globalCurrActList.delete(resId);
                    //清理玩家数据
                    if (this.globalActMgrInfo.has(resId)) {
                        logger.error("玩家uid:", this.uid, "delete 玩家ActivityName", "++", data.ActivityType,"++", data.ActivityName, "配置表期数:", periods, "DB期数:", oldActInfo.periods);
                        this.globalActMgrInfo.delete(resId);
                    }
                }
            }
        }

        //没有数据新建
        if (!this.globalCurrActList.has(resId)) {
            let actInfo = this.makeGlobalActRecord(resId, data, startTime, endTime);
            this.globalCurrActList.set(resId, actInfo);
            if (!this.globalActMgrInfo.has(resId))
            {
                let dbActRecord = this.newActMgrRecord(resId, data.ActivityType, periods);
                //金牌默认初始为1级
                if (data.ActivityType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH)
                {
                    dbActRecord.actRecord.goldCoach.level = 1;
                }
                this.globalActMgrInfo.set(resId, dbActRecord);
            }
        }
    }
};

Act.prototype.checkAndReloadActControl = function()
{
    let delActList = [];
    let currTime = TimeUtils.now();
    for(let [k, v]  of this.globalCurrActList)
    {
        //logger.info("checkAndReloadActControl: k,v", k, v);
        var config = dataApi.allData.data["ActiveControl"][k];
        if (!config) 
        {
            //配置表中没有这个活动，删除需要删除
            logger.error("checkAndReloadActControl: ActiveControl config not found! actId", k);
            delActList.push(k);
            continue;
        }

        if (config.TimeType === commonEnum.ACT_TIME_TYPE.PERSISTENT) //持久化活动不需要删除
        {
            continue;
        }

        let actType = v.actType;
        //首充还没有领取,不能删除该活动
        if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE)
        {
            if (this.firstChargeStatus === commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.ACTIVE)
            {
                delActList.push(k); 
            }
            continue;
        }
        //开服礼包还没有领取,不能删除该活动
        if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_OPEN_GIFT_BAG)
        {
            if (this.openChargeStatus === commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.ACTIVE)
            {
                delActList.push(k);
            }
            continue;
        }

        if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD)
        {
            continue;
        }

        //活动类型
        if (currTime >= v.endTime)	
        {
            delActList.push(k);
            continue;
        }
    }

    for(let idx in delActList)
    {
        let actId = delActList[idx];

        this.historyActList.set(actId, utils.deepCopy(this.globalActMgrInfo.get(actId))); //放到历史列表中区
        this.globalCurrActList.delete(actId);
        logger.info("checkAndReloadActControl: del expired act: ", actId);
    }
};

Act.prototype.refreshActDailyOnCharge = function(actId, actType, dbActRecord)
{
    let actRecord;
    if(actId == 16) {
        actRecord = {
            battleNum: 0,                //战斗次数
            gift:[],                     //推送礼包列表
        }
    } else {
        actRecord = {
            currValue: 0,            //当前值
            awardFlag: 0,            //领取标记 0未领取 1领取
            singleTagIndexList: [],        //独立档位标记
            accumulationTagIndexList: [],  //累计领取id
            monthCard: {
                allDay: 0,                 //总天数
                cardTime: 0,               //剩余天数
                buyMonthCardTime: 0,       //购买月卡的那个时间点
                buyMonthNum: 0,            //购买月卡次数
                monthCardInfo: [],         //月卡信息
                isBuy: 0,                  //是否购买月卡   0否  1是
            },
            goldCoach: {                   //金牌教练
                isBuy: 0,                  //是否购买了礼包
                level: 0,                  //当前任务等级
                currTask: 0,               //任务进度
                taskList: [],              //任务列表
                finishTask: [],            //已完成的任务列表 [resId]
                haveTakeAwardList: [],     //领取过得奖励列表
            },
            logsign: [],                    //国庆签到记录 [{index, signDay, awarFlag, taskid, state},] 表的id， 在活动的第几天签到， 是否领取， 任务id， 签到状态
            continueCharge: {
                tagIndex: 0,               //当前奖励标签
            },
            oneFreeNum: 0,               //老虎机单抽记录次数
            moreFreeNum: 0,              //老虎机多抽记录次数
            itemList: [],                //抽奖奖励物品{itemId, num}
            allItemList: [],            //所有抽奖奖励物品
        };
    }


    dbActRecord.actId = actId;
    dbActRecord.actType = actType;
    dbActRecord.actRecord = actRecord;
    dbActRecord.taken_all= 0;
    dbActRecord.param1 = 0;
    dbActRecord.lastRefreshTime = TimeUtils.now();
    //logger.info("refreshActDailyOnCharge", dbActRecord);
};

Act.prototype.refreshAct = function(dbActRecord)
{
    let actId = dbActRecord.actId;
    let actType = dbActRecord.actType;
    //logger.error("refreshAct: dbActRecord, actId, actType", dbActRecord, actId, actType);
    switch (actType) {
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_DAILY_ON_CHARGE:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SLOTS:
            this.refreshActDailyOnCharge(actId, actType, dbActRecord);
            //logger.error("refreshAct refreshActDailyOnCharge: dbActRecord, actId, actType", dbActRecord, actId, actType);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE_1:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME_1:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_1:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_2:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_3:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_4:
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_BIG_GIFT:
        break;

        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD:
            this.updateMonthCard(actId); //更新月卡
            break;

        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE:
            this.updateContinueCharge(actId, actType);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_1:
            this.updateContinueCharge(actId, actType);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_2:
            this.updateContinueCharge(actId, actType);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_TURNTABLE:
            this.refreshActDailyOnCharge(actId, actType, dbActRecord);
            break;
        default:
            break;
    }
};
//刷新活动数据
Act.prototype.checkAndRefreshAct = function()
{
    //logger.info("checkAndRefreshAct");
    for(let [k, v]  of this.globalCurrActList)
    {
        var config = dataApi.allData.data["ActiveControl"][k];
        if (!config) 
        {
            //配置表中没有这个活动，删除需要删除
            logger.error("checkAndRefreshAct: ActiveControl config not found! actId", k);
            continue;
        }
        
        let dbActRecord = this.globalActMgrInfo.get(k);
        if (!dbActRecord) //没有找到数据,直接返回默认按钮状态
        {
            logger.error("checkAndRefreshAct:  dbActRecord not found! actId:", k);
            continue;
        }

        let lastRefreshTime = dbActRecord.lastRefreshTime;
        let refresh = false;
        //logger.info("checkAndRefreshAct: actId, RefreshCycle, lastRefreshTime", k, config.RefreshCycle, lastRefreshTime);
        switch (config.RefreshCycle) {
            case commonEnum.ACT_REFRESH_TYPE.NONE://此类型不需要刷新
                break;

            case commonEnum.ACT_REFRESH_TYPE.CROSS_DAY:
                if (!TimeUtils.isToday(lastRefreshTime))
                {   
                   
                    refresh = true;
                }
                //logger.info("checkAndRefreshAct: actId, RefreshCycle, lastRefreshTime", k, now, lastRefreshTime, has, refresh);
                break;

            case commonEnum.ACT_REFRESH_TYPE.CROSS_WEEK:
                if (!TimeUtils.isSameWeek(lastRefreshTime))
                {   
                    refresh = true;
                }
                break;

            case commonEnum.ACT_REFRESH_TYPE.CROSS_MONTH:
                if (!TimeUtils.isSameMonth(lastRefreshTime))
                {   
                    refresh = true;
                }
                break;

            default:
                break;
        }

        if (dbActRecord.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE) //首充不刷新
        {
            continue;
        }

        if (refresh)  //需要刷新
        {
            this.refreshAct(dbActRecord);
        }
    }
};

Act.prototype.getTagList = function(actId, actType)
{
    let tagList = [];
    var config = dataApi.allData.data["ActiveParam"];
    if (!config) 
    {
        logger.error("getTagList: ActiveParam config not found!");
        return tagList;
    }

    let tagIndex = 0;
    for (let idx in config) 
    {
        const data = config[idx];
        if (data.GroupId !== actType)
        {
            tagIndex = 0;
            continue;
        }

        let tagInfo = this.getTagInfo(actId, tagIndex, data);
        tagList.push(tagInfo);
        tagIndex++;
    }
    return tagList;
};

Act.prototype.getTagInfo = function(actId, tagIndex, config)
{
    let awardList = this.getActParamAwardList(config);
    let tagInfo = {
        tagName: config.Title,
        awardList: awardList,
        btnStatus: commonEnum.ACT_BTN_STATUS.NOT_TAKE,
        currValue: 0,
        totalValue: 0,
        tagIndex: tagIndex,
        coachAwardType: commonEnum.ACT_GOLD_COACH_AWARD_TYPE.FREE,
    };

    var actCtlConfig = dataApi.allData.data["ActiveControl"][actId];
    if (!actCtlConfig) 
    {
        logger.error("getTagInfo: ActiveControl config not found! actId", k);
        return tagInfo;
    }

    let btnStatus = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    let actType = actCtlConfig.ActivityType;
    let dbActRecord = this.globalActMgrInfo.get(actId);
    switch(actType)
    {
        default:
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_DAILY_ON_CHARGE:
            btnStatus = commonEnum.ACT_BTN_STATUS.BUY_GOODS;
            this.makeRspSingleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;

        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;

        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE_1:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;

        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME:
            btnStatus = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;

        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME_1:
            btnStatus = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;

        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;

        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;     
        
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH:
            btnStatus = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE:
            btnStatus = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_1:
            btnStatus = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_2:
            btnStatus = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN:
            btnStatus = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
            this.makeRsSignTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_1:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_2:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_3:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_4:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_OPEN_GIFT_BAG:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
        case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_BIG_GIFT:
            btnStatus = commonEnum.ACT_BTN_STATUS.GO_CHARGE;
            this.makeRspMultipleTagsAct(actId, tagIndex, dbActRecord, btnStatus, tagInfo, config);
            break;
    }

    return tagInfo;
};

Act.prototype.calcCountDown = function(endTime)
{
    let countDown = {
        day: 0,
        hour: 0,
        minutes: 0,
        second: 0,
    };

    let now = TimeUtils.now();
    let diffTime = endTime - now;;
    if (diffTime <= 0)
    {
        return countDown;
    }

    let DAY_MS = 24 * 60 * 60 * 1000;
    let HOUR_MS = 60 * 60 * 1000;
    let MINUTE_MS = 60 * 1000;
    let SECOND_MS = 1000;

    let leftDay = Math.floor(diffTime / DAY_MS);  

    let tmpTime = diffTime -  leftDay * DAY_MS;
    let leftHour = Math.floor(tmpTime/HOUR_MS);

    tmpTime = tmpTime - (HOUR_MS * leftHour);
    let leftMinutes = Math.floor(tmpTime / MINUTE_MS);

    tmpTime  = tmpTime - (MINUTE_MS * leftMinutes);
    let leftSecond = Math.floor(tmpTime/ SECOND_MS);

    countDown.day = leftDay;
    countDown.hour = leftHour;
    countDown.minutes = leftMinutes;
    countDown.second = leftSecond;
    //logger.info("calcCountDown: startTime, endTime, diffTime", now, endTime, diffTime);
    //logger.info("calcCountDown: leftDay, leftHour, leftMinutes, leftSecond", leftDay, leftHour, leftMinutes, leftSecond);
    return countDown;
};

Act.prototype.timeToUnixTime = function(timeStr)
{
    let maxTime = "2033/12/30 23:59:59";
    if (!timeStr)
    {
        timeStr = maxTime;
    }

    let unixTime = new Date(timeStr).getTime(); //转化为timestamp时间戳
    return unixTime;
};

Act.prototype.makeRspActRecord = function(actId, config, actInfo)   //返回活动信息
{
    let actRecord = {};
    actRecord.actId     = actId;
    actRecord.actType   = config.ActivityType;
    actRecord.startTime = actInfo.startTime;
    actRecord.endTime   = actInfo.endTime;
    actRecord.actName   = config.ActivityName;
    //推送礼包单独处理
    if(actId === 16){
        actRecord.pushGiftList = this.getPushGiftList(actId);
    } else {
        actRecord.tagList   = this.getTagList(actId, config.ActivityType);
        actRecord.countDown = this.calcCountDown(actInfo.endTime);
        actRecord.monthCard = this.getMonthCardInfo(actId);
        actRecord.goldCoach = this.getGoldCoachInfo(actId, config.ActivityType);
        actRecord.continueCharge = this.getContinueChargeInfo(actId, config.ActivityType);
        actRecord.NationalDay = this.getNationalDayInfo(actId);
    }
    return actRecord;
};

Act.prototype.checkPushGiftIsHave = function(arr, id) {
    let isHave = false;
    for(let i in arr){
        if(id === arr[i].id) {
            isHave = true;
            break;
        }
    }
    return isHave;
}
//获取推送礼包信息
Act.prototype.getPushGiftList = function(actId)
{
    let act = this.globalActMgrInfo.get(actId);
    if(!act) {
        return [];
    }

    let creatTime  = this.player.createTime;
    let config = dataApi.allData.data["PushPackage"];
    for(let n in config) {
        let establish = new Date(config[n].Establish).getTime();
        let deadline = new Date(config[n].Deadline).getTime();
        if(creatTime >= establish && creatTime <= deadline) {
            if(config[n].Condition === 0) {
                if(this.checkPushGiftIsHave(act.actRecord.gift, config[n].Id)) {
                    continue;
                };
                let gift = {};
                gift.id = config[n].Id;              //礼包id
                gift.endTime = 0;                    //获取时间
                gift.isBuy = 0;                      //是否已买
                gift.isTimeOut = 0;                  //是否超时
                act.actRecord.gift.push(gift);
                break;
            }
        }
    }

    let time = TimeUtils.now();
    for(let i = 0; i < act.actRecord.gift.length; ++i) {
        let id = act.actRecord.gift[i].id;
        let config = dataApi.allData.data["PushPackage"][id];
        if(!config) {
            continue;
        }

        //过滤默认礼包
        if(config.Condition === 0) {
            continue;
        }

        //检查物品是否过期
        if((act.actRecord.gift[i].endTime < time) && (act.actRecord.gift[i].isTimeOut !== 1)) {
            act.actRecord.gift[i].isTimeOut = 1;
        }
    }

    return act.actRecord.gift;
};

//购买推送礼包
Act.prototype.buyPushGift = function(id)
{
    let act = this.globalActMgrInfo.get(16);
    if(!act) {
        return Code.FAIL;
    }

    let time = TimeUtils.now();
    let flag = -1;
    let isHave = false;
    for(let i = 0; i < act.actRecord.gift.length; ++i) {
        if(act.actRecord.gift[i].id === id) {
            let config = dataApi.allData.data["PushPackage"][id];
            isHave = true;
            if(!config) {
                flag = 0;
                break;
            }
            //默认礼包不需要判断时间
            if(config.Condition === 0) {
                if(act.actRecord.gift[i].isBuy === 1) {
                    flag = 1;
                }
                break;
            }

            if(act.actRecord.gift[i].isBuy === 1) {
                flag = 1;
                break;
            }else if((act.actRecord.gift[i].isTimeOut === 1) || (act.actRecord.gift[i].endTime < time)) {
                flag = 2;
                break;
            }
            break;
        }
    }

    if(flag === 1) {
       return Code.BUY_FAIL;
    }else if(flag === 2) {
        return Code.TIME_FAIL;
    }else if(flag === 0) {
        return Code.CONFIG_FAIL;
    }else if(!isHave) {
        return Code.ITEM_FAIL;
    }

    let needCost = dataApi.allData.data["PushPackage"][id].Price;
    let priceType = dataApi.allData.data["PushPackage"][id].PriceType;
    let itemId = dataApi.allData.data["PushPackage"][id].ItemId;
    let itemNum = 1;
    if(priceType === 1) {
        //检查币是否足够
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, needCost)){
            return Code.CASH_FALL;
        }
        //扣币
        this.player.subtractResource(commonEnum.PLAY_INFO.cash, needCost);
        this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.cash, value: this.player.cash}]);
    }else if(priceType === 2) {
        //检查币是否足够
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, needCost)){
            return Code.GOLD_FALL;
        }
        //扣币
        this.player.subtractResource(commonEnum.PLAY_INFO.gold, needCost);
        this.player.upPlayerInfo([{type: commonEnum.PLAY_INFO.gold, value: this.player.gold}]);
    }

    this.player.bag.addItem(itemId, itemNum);
    for(let i = 0; i < act.actRecord.gift.length; ++i) {
        if (act.actRecord.gift[i].id === id) {
            act.actRecord.gift[i].isBuy = 1;
            let cfg = dataApi.allData.data["PushPackage"][id];
            if(cfg.Condition === 2) {
                act.actRecord.battleNum = 0;   //战败次数清0
                act.actRecord.gift[i].isFlag = 1;   //记录
                break;
            }
        }
    }
    return Code.OK;
};

//添加推送礼包战斗次数
Act.prototype.addPushGiftBattleNum = function(selfScore, otherScore)
{
    if(selfScore > otherScore) {
        return;
    }
    let act = this.globalActMgrInfo.get(16);
    if(!act) {
        return;
    }

    //检查是否有战败礼包未购买或者未到时间
    let flag = 0;
    let time = TimeUtils.now();
    for(let i = 0; i < act.actRecord.gift.length; ++i) {
        let id = act.actRecord.gift[i].id;
        let config = dataApi.allData.data["PushPackage"][id];
        if(!config) {
            return;
        }

        if(config.Condition === 2) {
            if(act.actRecord.gift[i].isBuy === 1) {
                flag = 1;    //已买
                break;
            }else if(act.actRecord.gift[i].endTime < time) {
                if(act.actRecord.gift[i].isFlag === 0) {
                    act.actRecord.battleNum = 0;
                    act.actRecord.gift[i].isFlag = 1;
                }
                flag = 2;    //超时
                break;
            }else {
                flag = 3;
                break;
            }
        }
    }

    //已经存在战败礼包 直接返回
    if(flag === 3) {
        return;
    }

    act.actRecord.battleNum++;
    let creatTime  = this.player.createTime;
    let config = dataApi.allData.data["PushPackage"];
    if(!config) {
        return;
    }

    for(let i in config) {
        let establish = new Date(config[i].Establish).getTime();
        let deadline = new Date(config[i].Deadline).getTime();
        if(creatTime >= establish && creatTime <= deadline) {
            if((act.actRecord.battleNum === config[i].Parameter) && (config[i].Condition === 2)) {
                //过滤已经触发过的
                if(this.checkPushGiftIsHave(act.actRecord.gift, config[i].Id)) {
                    break;
                };
                let gift = {};
                gift.id = config[i].Id;                                         //礼包id
                gift.endTime = TimeUtils.now() + (config[i].Time * 1000);       //获取时间
                gift.isBuy = 0;                                                 //是否已买
                gift.isTimeOut = 0;                                             //是否超时
                gift.isFlag = 0;                                                //是否记录过
                act.actRecord.gift.push(gift);
                //推消息给客户端
                this.player.updatePushGift(gift);
                break;
            }
        }
    }
};

//检查等级是否达到推送礼包的要求
Act.prototype.checkLevelIsStandard = function(level)
{
    let act = this.globalActMgrInfo.get(16);
    if(!act) {
        return;
    }
    let creatTime  = this.player.createTime;
    let config = dataApi.allData.data["PushPackage"];
    if(!config) {
        return;
    }

    for(let i in config) {
        let establish = new Date(config[i].Establish).getTime();
        let deadline = new Date(config[i].Deadline).getTime();
        if(creatTime >= establish && creatTime <= deadline) {
            if((level === config[i].Parameter) && (config[i].Condition === 1)) {
                let gift = {};
                gift.id = config[i].Id;                                        //礼包id
                gift.endTime = TimeUtils.now() + (config[i].Time * 1000);      //获取时间
                gift.isBuy = 0;                                                //是否已买
                gift.isTimeOut = 0;                                            //是否超时
                gift.isFlag = 0;                                                //是否记录过
                act.actRecord.gift.push(gift);
                //推消息给客户端
                this.player.updatePushGift(gift);
                break;
            }
        }
    }
};



Act.prototype.makeGlobalActRecord = function(actId, config, startTime, endTime)
{
    let actRecord = {};
    actRecord.actId     = actId;
    actRecord.actType   = config.ActivityType;
    actRecord.startTime = startTime;
    actRecord.endTime   = endTime;
    actRecord.actName   = config.ActivityName;
    actRecord.periods   = config.Periods;
    return actRecord;
};

//获取所有活动信息
Act.prototype.getActRecordList = function()
{
    this.player.processOfflineEvent(); //如果有昨天未处理的活动，先把活动领取了
    this.loadGlobalAct();
    this.checkAndRefreshAct();      //刷新活动信息
    this.checkAndReloadActControl();
    var clientList = [];
    for(let [k, v]  of this.globalCurrActList)
    {
        var config = dataApi.allData.data["ActiveControl"][k];
        if (!config) 
        {
            logger.error("getActRecordList: ActiveControl config not found! actId", k);
            continue;
        }
        if(TimeUtils.dayInterval(config.ShowStartTime) < 0|| TimeUtils.dayInterval(config.ShowEndTime) > 0)//不在显示时间内不显示
        {
            continue;
        }
        let actRecord = this.makeRspActRecord(k, config, v);
        clientList.push(actRecord); //将活动信息插入列表
    }
    //logger.debug("getActRecordList clientList", clientList);
    return clientList;
};

Act.prototype.getSatisfyConditionButNotTake =function()
{
    let offlineEventActMailList = [];
    for(let [k, v] of this.globalCurrActList)
    {
        if (v.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD) //月卡有自己的刷新机制
        {
            continue;
        }

        let offlineEventActMail = this.getNotTakeAllAward(k, v);
        for(let idx in offlineEventActMail)
        {
            let data = offlineEventActMail[idx];
            offlineEventActMailList.push(data);
        }
    }
    return offlineEventActMailList;
};

Act.prototype.getNotTakeAllAward = function(actId, act_info)
{
    let offlineEventActMail = [];
    let dbActRecord = this.globalActMgrInfo.get(actId);
    if (!dbActRecord)
    {
        logger.error("checkTakeAllAward: actId", actId);
        return offlineEventActMail;
    }

    let actType = dbActRecord.actType;
    let actName = act_info.actName;
    var config = dataApi.allData.data["ActiveParam"];
    if (!config) 
    {
        logger.error("getNotTakeAllAward: ActiveParam config not found!");
        return offlineEventActMail;
    }

    let tagIndex = 0;
    for (let idx in config) 
    {
        const data = config[idx];
        if (data.GroupId !== actType)
        {
            tagIndex = 0;
            continue;
        }

        let btnStatus = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
        switch(actType)
        {
            default:
                break;
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_DAILY_ON_CHARGE:
                btnStatus = this.getSingleSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.NOT_TAKE, data);
                break;
    
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.NOT_TAKE, data);
                break;

            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE_1:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.NOT_TAKE, data);
                break;
    
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.NOT_TAKE, data);
                break;

            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME_1:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.NOT_TAKE, data);
                break;
    
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE:
                btnStatus= this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.GO_CHARGE, data);
                break;
    
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.BUY_GOODS, data);
                break;

            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH:
                btnStatus = this.getSharedValueTagsActStatusByGoldCoach(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.NOT_TAKE, data);
                break;     

            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE:
                btnStatus = this.getSharedValueTagsActStatusByContinuedCharge(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.NOT_TAKE, data);
                break;

            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_1:
                btnStatus = this.getSharedValueTagsActStatusByContinuedCharge(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.NOT_TAKE, data);
                break;
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_2:
                btnStatus = this.getSharedValueTagsActStatusByContinuedCharge(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.NOT_TAKE, data);
                break;
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.GO_CHARGE, data);
                break;
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_1:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.GO_CHARGE, data);
                break;
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_2:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.GO_CHARGE, data);
                break;
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_3:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.GO_CHARGE, data);
                break;
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_4:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.GO_CHARGE, data);
                break;
            case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_BIG_GIFT:
                btnStatus = this.getSharedValueTagsActStatus(actId, tagIndex, dbActRecord, commonEnum.ACT_BTN_STATUS.GO_CHARGE, data);
                break;
        }

        if (btnStatus === commonEnum.ACT_BTN_STATUS.CAN_TAKE) //满足未领取
        {
            let awardList = this.getActParamAwardList(data);
            let obj = {
                actId: actId,
                actType: actType,
                actName: actName,
                tagIndex: tagIndex, 
                realAwardList: awardList,
                endTime: act_info.endTime,
            };

            if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE || actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_1
            || actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_2)
            {
                obj.endTime = TimeUtils.endingOfTodayByTime(TimeUtils.now());
            }
            logger.info("getNotTakeAllAward:", obj);
            offlineEventActMail.push(obj);
        }
        tagIndex++;
    }

    return offlineEventActMail;
};

Act.prototype.checkSatisfyConditionButNotTake = function()
{
    let offlineEventActMailList = this.getSatisfyConditionButNotTake();
    for(let idx in offlineEventActMailList)
    {
        let offlineEvent = offlineEventActMailList[idx];
        let actId = offlineEvent.actId;
        let actType = offlineEvent.actType;
        let actName = offlineEvent.actName;
        let tagIndex = offlineEvent.tagIndex;
        let realAwardList = offlineEvent.realAwardList;
        let endTime = offlineEvent.endTime;
        this.player.newActMail(actId, actType, actName, tagIndex, realAwardList, endTime);
        //logger.info("checkSatisfyConditionButNotTake: offlineEvent", offlineEvent);
    }
};

Act.prototype.newActMgrRecord = function(actId, actType, periods)
{
    let actRecord;
    if(actId == 16) {
        actRecord = {
            battleNum: 0,                //战斗次数
            gift:[],                     //推送礼包列表
        }
    } else {
        actRecord = {
            currValue: 0,            //当前值
            awardFlag: 0,                  //领取标记 0未领取 1领取
            singleTagIndexList: [],        //独立档位标记
            accumulationTagIndexList: [],  //累计领取id
            monthCard: {
                allDay: 0,                 //总天数
                cardTime: 0,               //剩余天数
                buyMonthCardTime: 0,       //购买月卡的那个时间点
                buyMonthNum: 0,            //购买月卡次数
                monthCardInfo: [],         //月卡信息
                isBuy: 0,                  //是否购买月卡   0否  1是
            },
            goldCoach: {                   //金牌教练
                isBuy: 0,                  //是否购买了礼包
                level: 0,                  //当前任务等级
                currTask: 0,               //任务进度
                taskList: [],              //任务列表
                finishTask: [],            //已完成的任务列表 [resId]
                haveTakeAwardList: [],     //领取过得奖励列表
            },
            logsign: [],                    //国庆签到记录 [{index, signDay, awarFlag, taskid, state},] 表的id， 在活动的第几天签到， 是否领取， 任务id， 签到状态
            continueCharge: {
                tagIndex: 0,               //当前奖励标签
            },
            loginSignFinishTask: [],
            oneFreeNum: 0,               //老虎机单抽记录次数
            moreFreeNum: 0,              //老虎机多抽记录次数
            itemList: [],                //抽奖奖励物品{itemId, num}
            allItemList: [],            //所有抽奖奖励物品
            //[ {idx: 0, taskId: 10001, status: 1}]
        };
    }

    let now = TimeUtils.now();
    let actMgrRecord = {
        actId: actId,
        actType: actType,
        actRecord: actRecord,
        taken_all: 0,         //是否领取了所有奖励 0未领取 1 全部领取
        param1: 0,            //参数1,
        param2: 0,            //参数2 这个参数不会每日刷新 记录老虎机累计抽奖次数 小王
        param3: 0,            //参数3 这个参数不会每日刷新 记录老虎机累计抽奖次数 大王
        lastRefreshTime: now, //最后一次刷新时间
        periods: periods,
    };

    return actMgrRecord;
};

Act.prototype.actTakeAward = function(actId, tagIndex)
{
    let awardList = [];
    let realAwardList = [];
    let retCode = {code: Code.FAIL, awardList: awardList, realAwardList: realAwardList};
    let dbActRecord = this.globalActMgrInfo.get(actId);
    if (!dbActRecord)
    {
        logger.error("actTakeAward: not dbActRecord! actId, tagIndex", actId, tagIndex);
        return retCode;
    }

    let haveTake = false;
    if (dbActRecord.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH)
    {
        haveTake = this.checkGoldCoachIsAlreadyTake(dbActRecord, tagIndex);
    }else
    {
        haveTake = this.checkIsAlreadyTake(dbActRecord, tagIndex);     
    }

    if (haveTake) //该档位已经被玩家领取过了
    {
        logger.error("actTakeAward: actId, tagIndex, this tagIndex already take!", actId, tagIndex);
        retCode.code = Code.ACT_TAKE_CODE.ALREADY_TAKE;
        return retCode;
    }

    retCode = this.takeMultipleAward(actId, dbActRecord, tagIndex);
    if (retCode.code === Code.OK)
    {   
        this.player.delActMail(actId, dbActRecord.actType, tagIndex);
        logger.info("actTakeAward delActMail", actId, dbActRecord.actType, tagIndex);
        if (dbActRecord.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE) {
            if(retCode.realAwardList.itemUidList.length > 0) {
                this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.HOF_ACT, [commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE], retCode.realAwardList.itemUidList[0]);
            }
        }else if(dbActRecord.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME_1 || dbActRecord.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME) {
            this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.CUMULATIVE_CONSUMPTION, [dbActRecord.actType, tagIndex], {});
        }
    }
    return retCode;
};

/***********************************领取奖励 ************************************************ */
Act.prototype.getActParamAwardList = function(paramConfig)
{
    let awardList = [];
    if (!paramConfig)
    {
        return awardList;
    }

    for(let i = 1; i <= 5; i++)
    {
        let type   = paramConfig["RewardType" + i ];
        let itemId = paramConfig["Reward" + i ];
        let count  = paramConfig["Number" + i ];
        if (itemId <= 0)
        {
        continue;
        }

        let item = {
            type: type,
            itemId: itemId,
            count: count,     
        };
        awardList.push(item);
    }

    return awardList;
};

Act.prototype.commonMultipleTakeAward = function(actId, dbActRecord, tagIndex, paramConfig)
{
    let awardList = [];
    let realAwardList = { itemUidList: [], heroUidList: []};
    let retCode = {code: Code.FAIL, awardList: awardList, realAwardList: realAwardList};

    logger.info("commonTakeAward: tagIndex", actId, tagIndex);
    //检查是否能够符合领取条件
    let currValue = dbActRecord.actRecord.currValue;
    logger.info("commonTakeAward: tagIndex, currValue, Parameter1", actId, tagIndex, currValue, paramConfig.Parameter1);
    if (currValue < paramConfig.Parameter1) //还不符合条件
    {
        logger.error("commonTakeAward: not reach crond_value! currValue, crond_value", currValue, paramConfig.Parameter1);
        retCode.code = Code.ACT_TAKE_CODE.NOT_REACH_TAKE_CONDITION;
        return retCode;
    }

    awardList = this.getActParamAwardList(paramConfig);

    //置位
    let awardFlag = dbActRecord.actRecord.awardFlag;
    let tmpFlag = utils.setBit(awardFlag, tagIndex);
    dbActRecord.actRecord.awardFlag = tmpFlag;

    let accumulationTagIndexList = dbActRecord.actRecord.accumulationTagIndexList;
    accumulationTagIndexList.push(tagIndex);
    retCode.awardList = awardList;
    retCode.realAwardList = this.doTakeAwardList(awardList);
    retCode.code = Code.OK;
    logger.info("commonTakeAward realAwardList", retCode.realAwardList);
    return retCode;
};

Act.prototype.commonSingleTagIndexTakeAward = function(actId, dbActRecord, tagIndex, paramConfig)
{
    let awardList = [];
    let realAwardList = { itemUidList: [], heroUidList: []};
    let retCode = {code: Code.FAIL, awardList: awardList, realAwardList: realAwardList};

    logger.info("commonTakeAward: tagIndex", actId, tagIndex);
    //检查是否能够符合领取条件
    let singleTagIndexList = dbActRecord.actRecord.singleTagIndexList;
    let currValue = this.getSingleTagIndexCurrValue(singleTagIndexList, tagIndex);

    logger.info("commonTakeAward: tagIndex, currValue, Parameter1", actId, tagIndex, currValue, paramConfig.Parameter1);
    if (currValue < paramConfig.Parameter1) //还不符合条件
    {
        logger.error("commonTakeAward: not reach crond_value! currValue, crond_value", currValue, paramConfig.Parameter1);
        retCode.code = Code.ACT_TAKE_CODE.NOT_REACH_TAKE_CONDITION;
        return retCode;
    }

    awardList = this.getActParamAwardList(paramConfig);

    //置位
    let awardFlag = dbActRecord.actRecord.awardFlag;
    let tmpFlag = utils.setBit(awardFlag, tagIndex);
    dbActRecord.actRecord.awardFlag = tmpFlag;
    retCode.awardList = awardList;
    retCode.realAwardList = this.doTakeAwardList(awardList);
    retCode.code = Code.OK;
    logger.info("commonTakeAward realAwardList", retCode.realAwardList);
    //如果是开服礼包全部领取了，就不显示了
    if(dbActRecord.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_OPEN_GIFT_BAG)
    {
        var config = dataApi.allData.data["ActiveParam"];
        let gearsNum = 0;//档位数量按表计算
        //得到档位数量
        for(let i in config)
        {
            if(config[i].GroupId === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_OPEN_GIFT_BAG)
                gearsNum++;
        }
        let flag = true;
        for(let i = 0; i < gearsNum; i++)//开服礼包有4个档位
        {
            let temp = this.checkIsAlreadyTake(dbActRecord, i);
            if(!temp)
            {
                flag = temp;
                break;
            }
        }
        if(flag)//全领过了
        {
            this.openChargeStatus = commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.ACTIVE;
        }
    }

    return retCode;
};

Act.prototype.takeMultipleAward = function(actId, dbActRecord, preTagIndex)
{
    logger.error("takeMultipleAward:", actId, dbActRecord, preTagIndex);
    let awardList = [];
    let realAwardList = { itemUidList: [], heroUidList: []};
    let retCode = {code: Code.FAIL, awardList: awardList, realAwardList: realAwardList};
    var config = dataApi.allData.data["ActiveParam"];
    if (!config) 
    {
        logger.error("takeMultipleAward: ActiveParam config not found! actId", actId);
        return retCode;
    }   

    logger.info("takeMultipleAward, actId, dbActRecord, preTagIndex", actId, dbActRecord, preTagIndex);
    let actType = dbActRecord.actType;
    let tagIndex = 0;
    for (let idx in config) 
    {
        const data = config[idx];
        if (data.GroupId !== actType)
        {
            tagIndex = 0;
            continue;
        }

        //logger.info("takeMultipleAward, data.GroupId", data.GroupId, actType);
        // logger.error("GroupId, actType, tagIndex, preTagIndex 1=>------------ ", data.GroupId, actType, tagIndex);
        if (tagIndex === preTagIndex)
        {
            switch (actType)
            {
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE_1:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    //logger.info("takeMultipleAward _commonTakeAward: actId, dbActRecord, tagIndex, retCode", actId, dbActRecord, tagIndex, retCode);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME_1:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_DAILY_ON_CHARGE:
                    retCode = this.commonSingleTagIndexTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE: 
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    this.firstChargeStatus = commonEnum.DB_SAVE_FIRST_CHARGE_STATUS.ACTIVE;
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD:
                    retCode = this.monthCardTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH:
                    retCode = this.goldCoachTakeAward(actId, dbActRecord, tagIndex, data);
                    break;

                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE:
                    retCode = this.continueChargeTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_1:
                    retCode = this.continueChargeTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_2:
                    retCode = this.continueChargeTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN:
                    retCode = this.nationalSignTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_1:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_2:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_3:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_4:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_OPEN_GIFT_BAG:
                    retCode = this.commonSingleTagIndexTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_BIG_GIFT:
                    retCode = this.commonMultipleTakeAward(actId, dbActRecord, tagIndex, data);
                    break;
                default:
                    break;
            }
            break;
        }

        tagIndex++;
    }

    return retCode;
};

//领取月卡奖励
Act.prototype.monthCardTakeAward = function(actId, dbActRecord, tagIndex, paramConfig)
{
    let awardList = [];
    let realAwardList = { itemUidList: [], heroUidList: []};
    let retCode = {code: Code.FAIL, awardList: awardList, realAwardList: realAwardList};
    let actRecord = dbActRecord.actRecord;
    let monthCard = actRecord.monthCard;
    logger.info("monthCardTakeAward: tagIndex", actId, tagIndex, monthCard);
    //检查是否能够符合领取条件
    //1.检查是否购买过月卡
    if (!monthCard.isBuy)
    {
        retCode.code = Code.ACT_TAKE_CODE.NOT_REACH_TAKE_CONDITION;
        return retCode;
    }
    //2.检查是否已经过期
    let day = TimeUtils.dayInterval(monthCard.buyMonthCardTime);
    if (day >= this.allDay) {
        retCode.code = Code.ACT_TAKE_CODE.MONTH_CARD_EXPIRED; 
        return retCode;
    }

    //3.已经领取过了
    if (this.checkIsAlreadyTake(dbActRecord, tagIndex))
    {
        retCode.code = Code.ACT_TAKE_CODE.ALREADY_TAKE; 
        return retCode;
    }

    monthCard.monthCardInfo[day].status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
    awardList = this.getActParamAwardList(paramConfig);

    logger.info("monthCardTakeAward", awardList);
    //置位
    let awardFlag = dbActRecord.actRecord.awardFlag;
    let tmpFlag = utils.setBit(awardFlag, tagIndex);
    dbActRecord.actRecord.awardFlag = tmpFlag;

    //减天数
    monthCard.cardTime--;
    if(monthCard.cardTime < 0) {
        monthCard.cardTime = 0;
    }

    retCode.awardList = awardList;
    retCode.realAwardList = this.doTakeAwardList(awardList);
    retCode.code = Code.OK;
    
    //更新懂币
    this.player.upPlayerInfo([{ type: commonEnum.PLAY_INFO.gold, value: this.player.gold}]);

    logger.info("commonTakeAward realAwardList, monthCard", retCode.realAwardList, monthCard);
    return retCode;
};

//领取金牌教练奖励
Act.prototype.goldCoachTakeAward = function(actId, dbActRecord, tagIndex, paramConfig)
{
    let awardList = [];
    let realAwardList = { itemUidList: [], heroUidList: []};
    let retCode = {code: Code.FAIL, awardList: awardList, realAwardList: realAwardList};
    let actRecord = dbActRecord.actRecord;
    let goldCoach = actRecord.goldCoach;
    let haveTakeAwardList = goldCoach.haveTakeAwardList;
    // logger.error("goldCoachTakeAward: tagIndex------------", actId, tagIndex, goldCoach, haveTakeAwardList);

    //1.已经领取过了
    if (this.checkGoldCoachIsAlreadyTake(dbActRecord, tagIndex))
    {
        retCode.code = Code.ACT_TAKE_CODE.ALREADY_TAKE; 
        return retCode;
    }

    //2.玩家是否不是付费用户
    let coachAwardType = paramConfig.Parameter1;
    if (coachAwardType === commonEnum.ACT_GOLD_COACH_AWARD_TYPE.CHARGE)
    {
        if (goldCoach.isBuy === commonEnum.ACT_GOLD_COACH_BUY_TYPE.NONE) //非付费用户领取付费档位
        {
            retCode.code = Code.ACT_TAKE_CODE.CAN_NOT_TAKE;   
            return retCode; 
        }
    }

    //3.等级检查
    let level = goldCoach.level;
    if (paramConfig.Parameter2 > level)
    {
        // logger.error("goldCoachTakeAward: not reach take level! level, needLevel", level, paramConfig.Parameter2);
        retCode.code = Code.ACT_TAKE_CODE.NOT_REACH_TAKE_CONDITION; 
        return retCode; 
    }

    //把索引添加已经领取列表上
    haveTakeAwardList.push(tagIndex);
    awardList = this.getActParamAwardList(paramConfig);

    // logger.info("goldCoachTakeAward: awardList, haveTakeAwardList", awardList, haveTakeAwardList);

    retCode.awardList = awardList;
    retCode.realAwardList = this.doTakeAwardList(awardList);
    retCode.code = Code.OK;
    
    //更新懂币
    //this.player.upPlayerInfo([{ type: commonEnum.PLAY_INFO.gold, value: this.player.gold}]);
    // logger.info("goldCoachTakeAward realAwardList, monthCard", retCode.realAwardList, goldCoach);
    return retCode;
};

//持续储值
Act.prototype.continueChargeTakeAward = function(actId, dbActRecord, tagIndex, paramConfig)
{
    let awardList = [];
    let realAwardList = { itemUidList: [], heroUidList: []};
    let retCode = {code: Code.FAIL, awardList: awardList, realAwardList: realAwardList};
    //检查是否能够符合领取条件
    let currValue = dbActRecord.actRecord.currValue;
    logger.info("continueChargeTakeAward: actId, tagIndex, currValue, Parameter1", actId, tagIndex, currValue, paramConfig.Parameter1);
    if (currValue < paramConfig.Parameter1) //还不符合条件
    {
        logger.error("continueChargeTakeAward: not reach crond_value! currValue, crond_value", currValue, paramConfig.Parameter1);
        retCode.code = Code.ACT_TAKE_CODE.NOT_REACH_TAKE_CONDITION;
        return retCode;
    }


    let actType = dbActRecord.actType;
    //最后一份礼包特殊特殊处理
    // let maxTagIndex = this.getTagConfigMaxTagIndex(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE);
    let maxTagIndex = this.getTagConfigMaxTagIndex(actType);
    let lastIndex = maxTagIndex - 1;
    if (maxTagIndex > 0 && tagIndex === lastIndex)
    {
        //倒数第二已经领取了才能够领取倒数第一个的奖励
        if (!this.checkIsAlreadyTake(dbActRecord, lastIndex - 1)) 
        {
            logger.error("continueChargeTakeAward: not reach crond_value! last but one haven't take! so not take! ", currValue, paramConfig.Parameter1, tagIndex);
            retCode.code = Code.ACT_TAKE_CODE.NOT_REACH_TAKE_CONDITION;
            return retCode;
        }
    }

    awardList = this.getActParamAwardList(paramConfig);

    //置位
    let awardFlag = dbActRecord.actRecord.awardFlag;
    let tmpFlag = utils.setBit(awardFlag, tagIndex);
    dbActRecord.actRecord.awardFlag = tmpFlag;

    let accumulationTagIndexList = dbActRecord.actRecord.accumulationTagIndexList;
    accumulationTagIndexList.push(tagIndex);
    retCode.awardList = awardList;
    retCode.realAwardList = this.doTakeAwardList(awardList);
    retCode.code = Code.OK;
    logger.info("continueChargeTakeAward realAwardList", retCode.realAwardList);
    return retCode;
};

Act.prototype.doTakeAwardList = function(awardList)
{
    let realAwardList = { itemUidList: [], heroUidList: []};
    for(let idx in awardList)
    {
        let itemObj = awardList[idx];
        let type = itemObj.type;
        let itemId = itemObj.itemId;
        let count = itemObj.count;
        switch (type) {
            case commonEnum.DROP_ITEM_TYPE.ITEM:
                var has = this.player.bag.hasCanAddItemInBag(itemId, count)
                if (!has) {
                    //发送背包满了邮件
                    this.player.email.BagIsFullMail(commonEnum.MAIL_ITEM_TYPE.ITEM, itemId, count, 0);

                    //即使满了也要添加
                    let item = {};
                    item.resId = itemId;
                    item.num = count;
                    realAwardList.itemUidList.push(item);

                    break;
                }

                var ret =this.player.bag.addItem(itemId, count);
                if (ret === Code.OK){
                    let item = {};
                    item.resId = itemId;
                    item.num = count;
                    realAwardList.itemUidList.push(item);
                }
                break;
            case commonEnum.DROP_ITEM_TYPE.HERO:
                for (let i = 0; i < count; i++) {
                    //1.检查球员是否存在
                    let exist = this.player.checkHeroResId(itemId);
                    if (exist) //转化为物品
                    {
                        let heroConfig = dataApi.allData.data["Footballer"][itemId];
                        let heroItemId = heroConfig.ItemID;
                        if (!heroItemId)
                        {
                            continue;
                        }

                        var has = this.player.bag.hasCanAddItemInBag(heroItemId, 1)
                        if (!has) {
                            //发送背包满了邮件
                            let itemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
                            this.player.email.BagIsFullMail(itemType, heroItemId, 1, 0);

                            let obj = {};
                            obj.resId = heroItemId;
                            obj.num = 1;
                            realAwardList.itemUidList.push(obj);
                            continue;
                        }

                        this.player.bag.addItem(heroItemId, 1);
                        let obj = {};
                        obj.resId = heroItemId;
                        obj.num = 1;
                        realAwardList.itemUidList.push(obj);
                    }else
                    {
                        let result = this.player.addHero(itemId);
                        let hero = result.hero;
                        let heroUid = hero.Uid;
                        realAwardList.heroUidList.push(heroUid); 
                    }
                }
                break;
            default:
                break;
        }
    }

    return realAwardList;
};

//领取国庆签到奖励
Act.prototype.nationalSignTakeAward = function(actId, dbActRecord, tagIndex, paramConfig)
{
    //logger.error("message:", actId, dbActRecord, tagIndex, paramConfig);
    let awardList = [];
    let realAwardList = { itemUidList: [], heroUidList: []};
    let retCode = {code: Code.FAIL, awardList: awardList, realAwardList: realAwardList};
    let actRecord = dbActRecord.actRecord;
    let logsign = {};
    if(!actRecord.logsign)  //如果为空初始化
    {
        logsign = this.initSignData();
    }
    else
    {
        logsign = actRecord.logsign;
    }
    logger.info("nationalSignTakeAward: tagIndex", actId, tagIndex, logsign[tagIndex]);
    //补签扣除懂球币
    if(logsign[tagIndex].state === commonEnum.ACT_BTN_STATUS.REPAIR_SIGN)
    {
        var configList = [];//7天的活动数据
        var actparconfig = dataApi.allData.data["ActiveParam"];//得到活动参数表
        for (let idx in actparconfig)//数组记录七日签到活动每天的参数
        {
            if(actparconfig[idx].GroupId === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN)
            {
                configList.push(actparconfig[idx]);
            }
        }
        var num = configList[tagIndex].Parameter2;//得到要扣除的数量
        var Player = this.player;
        //扣除懂球币
        if(Player.checkMoneyIsEnough(commonEnum.PLAY_INFO.gold, num))
        {
            Player.deductMoney(commonEnum.PLAY_INFO.gold, num);
            //更改状态
            logsign[tagIndex].signDay = tagIndex;
            logsign[tagIndex].state = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
            logsign[tagIndex].awardFlag = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
            this.nationalSignTakeAward(actId, dbActRecord, tagIndex, paramConfig);//回调
        }
        else
        {
            retCode.code = Code.ACT_TAKE_CODE.CAN_NOT_TAKE;//不能够领取
            return retCode;
        }
    }
    else if(logsign[tagIndex].state !== commonEnum.ACT_BTN_STATUS.CAN_TAKE)//不等于可领取表示没签到
    {
        if(logsign[tagIndex].state === commonEnum.ACT_BTN_STATUS.ALREADY_TAKE)//等于已领取
        {
            retCode.code = Code.ACT_TAKE_CODE.ALREADY_TAKE;//表示已经领过
            return retCode;
        }
        retCode.code = Code.ACT_TAKE_CODE.CAN_NOT_TAKE;//不能够领取
        return retCode;
    }
    else
    {
        logsign[tagIndex].awardFlag = commonEnum.ACT_BTN_STATUS.NOT_TAKE;//改领取标志为不可领取
        logsign[tagIndex].state = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;//按钮状态也改为以领取
    }
    awardList = this.getActParamAwardList(paramConfig);

    logger.info("nationalSignTakeAward", awardList);

    retCode.awardList = awardList;
    retCode.realAwardList = this.doTakeAwardList(awardList);
    retCode.code = Code.OK;

    logger.info("commonTakeAward realAwardList, nationalSignTakeAward", retCode.realAwardList, logsign[tagIndex]);
    return retCode;
};

/***********************************数据更新 ************************************************ */
Act.prototype.setMonthCardTime = function(actId)
{
    let dbActRecord = this.globalActMgrInfo.get(actId);
    if (!dbActRecord)
    {
        logger.error("setMonthCardTime: not dbActRecord! actId, tagIndex", actId);
        return;
    }

    let actType = dbActRecord.actType;
    if (actType !== commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD)
    {
        logger.error("setMonthCardTime: not month card", actId);
        return;
    }

    let actRecord = dbActRecord.actRecord;
    let monthCard = actRecord.monthCard;
    let buyMonthCardTime = monthCard.buyMonthCardTime;
    monthCard.buyMonthCardTime = buyMonthCardTime - 10 * 24 * 60 *60 * 1000;
};

Act.prototype.updateMonthCard = function(actId)
{
    let dbActRecord = this.globalActMgrInfo.get(actId);
    if (!dbActRecord)
    {
        logger.error("_updateMonthCard: not dbActRecord! actId, tagIndex", actId);
        return;
    }

    let actType = dbActRecord.actType;
    if (actType !== commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD)
    {
        logger.error("_updateMonthCard: not month card", actId);
        return;
    }
    
    var config = dataApi.allData.data["ActiveParam"];
    if (!config) 
    {
        logger.error("_updateMonthCard: ActiveParam config not found! actId", actId);
        return;
    }

    let actRecord = dbActRecord.actRecord;
    let monthCard = actRecord.monthCard;
    if (monthCard.isBuy === 0 || monthCard.buyMonthCardTime <= 0)
    {
        //logger.error("_updateMonthCard: not buy month!", actId);
        return;
    }

    let day = TimeUtils.dayInterval(monthCard.buyMonthCardTime);
    //logger.info("_updateMonthCard: actId, dbActRecord", actId);
    let awardList = [];
    for (let idx in config) 
    {
        const data = config[idx];
        if (data.GroupId !== actType)
        {
            continue;
        }

        awardList = this.getActParamAwardList(data);
        break;
    };

    let mailAwardList = this.player.offlineEvent.awardListToAttachList(awardList);
    let index = 0;
    //过期
    if(day >= monthCard.allDay) {
        day = monthCard.allDay;
    }

    for (let i = 0; i < day; ++i)
    {
        //昨日未领取就发邮件
        if (monthCard.monthCardInfo[i].status === commonEnum.ACT_BTN_STATUS.NOT_TAKE) 
        {
            monthCard.monthCardInfo[i].status = commonEnum.ACT_BTN_STATUS.ALREADY_SEND_EMAIL; //状态  0未领取   1已领取  2已发邮件
            //发邮件
            let specialAttachInfo = { roomUid: ""};
            this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.MONTH_CARD, commonEnum.MailType.SYSMAIL, utils.deepCopy(mailAwardList), specialAttachInfo, "", "", "", "");
            index++;
        }
    }

    monthCard.cardTime -= index;
    //过期
    if (day >= monthCard.allDay) {
        monthCard.monthCardInfo = [];
        monthCard.isBuy = 0;
        monthCard.cardTime = 0;
        //logger.error("_updateMonthCard: month expired!", actId);
        return;
    }

    //是否同一天
    let lastRefreshTime = dbActRecord.lastRefreshTime;
    if (!TimeUtils.isToday(lastRefreshTime)) {
        dbActRecord.lastRefreshTime = TimeUtils.now();
        dbActRecord.actRecord.awardFlag = 0;
    }
};

Act.prototype.checkUpdateGoldCoachTask = function()
{
    let needUpdate = false;
    for (let [k, v] of this.globalCurrActList) 
    {
        if ( v.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH)
        {
            let act_info = this.globalActMgrInfo.get(v.actId);
            let goldCoach = act_info.actRecord.goldCoach;
            let lastRefreshTime = act_info.lastRefreshTime;
            //跨天刷新或者长度为0
            if (!TimeUtils.isToday(lastRefreshTime) ||  goldCoach.taskList.length <= 0)
            {
                needUpdate = true;
                break;
            }
        }
    }

    return needUpdate;
};

//同步日常随机任务
Act.prototype.updateGoldCoachTask = function(systemTaskList)
{
    for (let [k, v] of this.globalCurrActList) 
    {
        if ( v.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH)
        {
            let act_info = this.globalActMgrInfo.get(v.actId);
            let goldCoach = act_info.actRecord.goldCoach;
            let lastRefreshTime = act_info.lastRefreshTime;
            //跨天刷新或者长度为0
            if (!TimeUtils.isToday(lastRefreshTime) ||  goldCoach.taskList.length <= 0)
            {
                //清空现有的任务列表
                //logger.info("updateGoldCoachTask: actId", k, v.actType);
                this.player.tasks.delAllGoldCoachTask();
                goldCoach.taskList = [];
                goldCoach.finishTask = [];
                act_info.lastRefreshTime = TimeUtils.now();

                for(let idx in systemTaskList)
                {
                    let obj = systemTaskList[idx];
                    let resId = obj.resId;
                    let taskType = obj.taskType;

                    let taskObj = 
                    {
                        resId: resId,
                        taskType: taskType,
                        isActive: 0,   //是否激活
                    }

                    if (taskType === commonEnum.ACT_GOLD_COACH_AWARD_TYPE.FREE)
                    {
                        this.player.tasks.addTask(resId);
                        taskObj.isActive = 1;
                        //logger.info("updateGoldCoachTask: add task free", resId);
                    }else if (taskType === commonEnum.ACT_GOLD_COACH_AWARD_TYPE.CHARGE)
                    {
                        this.player.tasks.addTask(resId);
                        if (goldCoach.isBuy === 1) //充值了才激活
                        {
                            taskObj.isActive = 1;
                        }
                    }
                    goldCoach.taskList.push(taskObj);
                }
            }
        }
    }
};

Act.prototype.updateTaskProgress = function(dbActRecord, resId)
{
    let goldCoach = dbActRecord.actRecord.goldCoach;
    let level = goldCoach.level;
    let currTask = goldCoach.currTask;
    let taskList = goldCoach.taskList;       
    let finishTask = goldCoach.finishTask;            //已完成的任务列表 [resId]
    let isCanAdd = true;
    let isHave = false;
    //1.检查是否是激活
    for(let idx in taskList)
    {
        let obj = taskList[idx];
        if (obj.resId === resId && obj.isActive === 0)
        {
            isCanAdd = false;
        }

        //是否有这个任务
        if(obj.resId === resId) {
            isHave = true;
        }
    }  

    if (!isCanAdd)
    {
        logger.error("updateTaskProgress: this resId not active!", resId);
        return;
    }

    //没有这个任务不能加
    if(!isHave) {
        logger.error("updateTaskProgress: not have taskId!", resId);
        return;
    }
  
    //2.检查是否重复添加
    for(let idx in finishTask)
    {
        let id = finishTask[idx];
        if (id === resId)
        {
            isCanAdd = false;
            break;
        }
    }

    if (!isCanAdd)
    {
        //logger.error("updateTaskProgress: this resId already exist finishTask, not need can add!", resId);
        return;
    }

    finishTask.push(resId);
    currTask += 1;
    if (currTask >= Constant.ACT.GOLD_COACH_MAX_TASK_COUNT)
    {
        currTask = 0;
        level += 1;
    }
    goldCoach.level = level;
    goldCoach.currTask = currTask;
    //logger.info("updateTaskProgress: level, currTask", level, currTask);
};

//更新金牌教练任务进度
Act.prototype.updateGoldCoachTaskProgress = function(resId)
{
    for (let [k, v] of this.globalCurrActList) //活动列表
    {
        if (v.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH)
        {
            let dbActRecord = this.globalActMgrInfo.get(v.actId);//活动数据
            if (!dbActRecord)
            {
                continue;
            }
            this.updateTaskProgress(dbActRecord, resId)
        }
    }
};
//更新国庆签到任务进度
Act.prototype.updateSignTaskPogress = function()
{
    var config = dataApi.allData.data["ActiveControl"][commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN];
    if(TimeUtils.dayInterval(config.StartTime) < 0|| TimeUtils.dayInterval(config.EndTime) > 0)//活动没开启
        return;
    for (let [k, v] of this.globalCurrActList) //活动列表
    {
        if (v.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN)
        {
            let actId = v.actId;
            let dbActRecord = this.globalActMgrInfo.get(v.actId);//活动数据
            if (!dbActRecord)
            {
                continue;
            }
            //更新任务进度
            let startTime = v.startTime ;
            var actRecord = dbActRecord.actRecord;
            var count = 0;
            var day = TimeUtils.dayInterval(startTime);//得到活动开始了多少天
            var configList = [];//7天的活动数据
            var actparconfig = dataApi.allData.data["ActiveParam"];//得到活动参数表
            var tasktype;
            var logsign = actRecord.logsign || [];
            if (!actparconfig)
            {
                logger.error("getTagList: ActiveParam config not found!");
                return;
            }
            for (let idx in actparconfig)//数组记录七日签到活动每天的参数
            {
                if(actparconfig[idx].GroupId === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN)
                {
                    configList.push(actparconfig[idx]);
                }
            }
            if((!logsign) || (logsign.length < configList.length))  //如果为空初始化
            {
                logsign = this.initSignData();
            }
            for(let i in logsign) {
                this.player.tasks.addTask(logsign[i].taskid);
            }
            //logger.debug("修改前logsign =====================", logsign, this.player.tasks.allTaskList[5]);
            var task = dataApi.allData.data["Task"];//得到任务表
            let id = commonEnum.TASK_INFO.nationalSign - 1;
            for(let i in logsign)
            {
                if(logsign[i].state === commonEnum.ACT_BTN_STATUS.NOT_TAKE || logsign[i].state === commonEnum.ACT_BTN_STATUS.CAN_TAKE || logsign[i].state === commonEnum.ACT_BTN_STATUS.ALREADY_TAKE)
                {
                    count++;//记录签到了几天
                }
                if(logsign[i].signDay === day)//今天已经签到过了
                    return;
            }
            for(var i in task)
            {
                if(task[i].Id === logsign[count].taskid)
                    tasktype = task[i].Type;//任务id相同得到类型
            }
            if(count === day)
            {
                logsign[count].state = commonEnum.ACT_BTN_STATUS.NOT_SIGN;//今天未签到
            }
            if((count === day) && this.player.tasks._checkIsFinish(tasktype - 1, logsign[count].taskid))//今天签到任务完成 //count ===day
            {
                logsign[count].signDay = day;
                logsign[count].state = commonEnum.ACT_BTN_STATUS.CAN_TAKE;//可领取
                logsign[count].awardFlag = 1;
                // if(logsign[count].awardFlag === 1)
                // {
                //     logsign[count].state = commonEnum.ACT_BTN_STATUS.NOT_TAKE;//已领取
                // }
            }
            if(day - count >= 1)//签到次数小于活动进行时间
            {
                logsign[count].state = commonEnum.ACT_BTN_STATUS.LEAK_SIGN;//签到的后一天为漏签
                for(let i = count + 1; i <= day; i++)
                {
                    if(i >= logsign.length)
                        break;
                    logsign[i].state = commonEnum.ACT_BTN_STATUS.REPAIR_SIGN;//补签
                }

                if(this.player.tasks._checkIsFinish(tasktype - 1, logsign[count].taskid))//判断漏签那天的任务
                {
                    logsign[count].signDay = day;
                    logsign[count].state = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
                    logsign[count].awardFlag = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
                }
            }
            //logger.debug("修改后logsign =====================", logsign);
        }
    }
};

//持续充值
//此函数只更新索引，未领取的奖励由离线邮件管理
Act.prototype.updateContinueCharge = function(actId, actType)
{
    let dbActRecord = this.globalActMgrInfo.get(actId);
    if (!dbActRecord)
    {
        logger.error("updateContinueCharge: not dbActRecord! actId, tagIndex", actId);
        return;
    }

    var config = dataApi.allData.data["ActiveParam"];
    if (!config) 
    {
        logger.error("updateContinueCharge: ActiveParam config not found! actId", actId);
        return;
    }

    let actRecord = dbActRecord.actRecord;
    let currTagIndex = actRecord.continueCharge.tagIndex;
    let currValue = actRecord.currValue;

    // let tagIndexConfig = this.getTagConfigByTagIndex(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE, currTagIndex);
    let tagIndexConfig = this.getTagConfigByTagIndex(actType, currTagIndex);
    if (!tagIndexConfig)
    {
        logger.info("updateContinueCharge: not found config!", actId, currTagIndex);
        return;
    }

    let cond_value = tagIndexConfig.Parameter1;
    logger.info("updateContinueCharge: actId, currValue, cond_value", actId, currValue, cond_value);
    if (currValue >= cond_value)
    {
        //置位
        if (!this.checkIsAlreadyTake(dbActRecord, currTagIndex))
        {
            let awardFlag = dbActRecord.actRecord.awardFlag;
            let tmpFlag = utils.setBit(awardFlag, currTagIndex);
            dbActRecord.actRecord.awardFlag = tmpFlag;
            logger.info("updateContinueCharge: currTagIndex, tmpFlag", currTagIndex, tmpFlag);
        }

        actRecord.continueCharge.tagIndex += 1;

        //处理倒数第一个奖励状态
        // let maxTagIndex = this.getTagConfigMaxTagIndex(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE);
        let maxTagIndex = this.getTagConfigMaxTagIndex(actType);
        let lastIndex = maxTagIndex - 1;
        let lastTwoIndex = maxTagIndex - 2;
        logger.info("updateContinueCharge: tagIndex, maxTagIndex, lastIndex", currTagIndex, maxTagIndex, lastIndex);
        if (maxTagIndex > 0 && currTagIndex === lastTwoIndex)
        {
            if (!this.checkIsAlreadyTake(dbActRecord, lastIndex))
            {
                awardFlag = dbActRecord.actRecord.awardFlag;
                tmpFlag = utils.setBit(awardFlag, lastIndex);
                dbActRecord.actRecord.awardFlag = tmpFlag;
              
                // let lastTagIndexConfig = this.getTagConfigByTagIndex(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE, lastIndex);
                let lastTagIndexConfig = this.getTagConfigByTagIndex(actType, lastIndex);
                let realAwardList = this.getActParamAwardList(lastTagIndexConfig);
                this.player.newActMail(actId, dbActRecord.actType, dbActRecord.actName, lastIndex, realAwardList, TimeUtils.now());
                this.player.processOfflineEvent();  //立马执行
            }
            actRecord.continueCharge.tagIndex += 1;
        }
    }

    actRecord.currValue = 0; //当前金币数值刷新
    logger.info("updateContinueCharge: actId,  dbActRecord", actId, dbActRecord);
    dbActRecord.lastRefreshTime = TimeUtils.now();
};

Act.prototype.getTagConfigByTagIndex = function(groupId, tagIndex)
{
    var config = dataApi.allData.data["ActiveParam"];
    let index = 0;
    let row = null;
    for (let idx in config) 
    {
        const data = config[idx];
        if (data.GroupId === groupId)
        {
            //logger.info("getTagConfigByTagIndex: groupId, index, tagIndex", groupId, index, tagIndex);
            if (index !== tagIndex)
            {
                index++;
            }else
            {
                row = data;     
                break;
            }
        }
    }

    return row;
};

Act.prototype.getTagConfigMaxTagIndex = function(groupId)
{
    var config = dataApi.allData.data["ActiveParam"];
    let maxTagIndex = 0;
    if (!config)
    {
        return maxTagIndex;
    }

    for (let idx in config) 
    {
        const data = config[idx];
        if (data.GroupId === groupId)
        {
            maxTagIndex++;
        }
    }

    return maxTagIndex;
};

//**********************************返回客户端数据 ************************************************** */
//持续充值
Act.prototype.getContinueChargeInfo = function(actId, actType)
{
    let rspInfo = {
        tagIndex: 0,
    };

    let dbActRecord = this.globalActMgrInfo.get(actId);
    if (!dbActRecord)
    {
        logger.error("getContinueChargeInfo: not dbActRecord! actId, tagIndex", actId);
        return rspInfo;
    }

    if (!dbActRecord.actRecord.continueCharge)
    {
        dbActRecord.actRecord.continueCharge = {};
        dbActRecord.actRecord.continueCharge.tagIndex = 0;
        return rspInfo;
    }

    let tagIndex = dbActRecord.actRecord.continueCharge.tagIndex;
    // let maxTagIndex = this.getTagConfigMaxTagIndex(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE);
    if(actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE || actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_1
        || actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_2) {
        let maxTagIndex = this.getTagConfigMaxTagIndex(actType);
        if (tagIndex >= maxTagIndex )
        {
            tagIndex = maxTagIndex - 1;
        }
    }

    if(tagIndex < 0) {
        tagIndex = 0;
    }
    rspInfo.tagIndex = tagIndex;
    return rspInfo;
};

//月卡信息
Act.prototype.getMonthCardInfo = function (actId) 
{
    let ret = 
    {
        cardTime: 0,
        isBuy: 0,
    };

    let dbActRecord = this.globalActMgrInfo.get(actId);
    if (!dbActRecord)
    {
        logger.error("getMonthCardInfo: not dbActRecord! actId, tagIndex", actId);
        return ret;
    }

    let monthCard = dbActRecord.actRecord.monthCard;
    ret.cardTime = monthCard.cardTime;
    ret.isBuy = monthCard.isBuy;
    return ret;
};

//金牌教练信息
Act.prototype.getGoldCoachInfo = function(actId, actType)
{
    let rspGoldCoach = {
        level: 0,
        currTask: 0,
        totalTask: Constant.ACT.GOLD_COACH_MAX_TASK_COUNT,
        isBuyCoach: 0,
        commonTaskList: [],
        goldTaskList: [],
    };

    let dbActRecord = this.globalActMgrInfo.get(actId);
    if (!dbActRecord)
    {
        logger.error("getGoldCoachInfo: not dbActRecord! actId, tagIndex", actId);
        return rspGoldCoach;
    }

    if (actType !== commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH)
    {
        return rspGoldCoach;
    }

    let goldCoach = dbActRecord.actRecord.goldCoach;
    rspGoldCoach.level = goldCoach.level;
    rspGoldCoach.currTask = goldCoach.currTask;
    rspGoldCoach.isBuyCoach = goldCoach.isBuy;
    this.player.tasks.checkGoldTaskIsComplete(goldCoach.taskList);
    for(let idx in goldCoach.taskList)
    {
        let obj = goldCoach.taskList[idx];
        let resId = obj.resId;
        let taskType = obj.taskType;
        let isActive = obj.isActive;
        let taskObj = null;
        // switch (isActive)
        // {
        //     case 0:   //未激活
        //         taskObj = this.player.tasks.getInitTaskById(resId);
        //         break;

            // case 1:   //已激活
        taskObj = this.player.tasks.getGoldTask(resId);
        //         break;
        //
        //     default:
        //         break;
        // }
         
        if (!taskObj)
        {
            logger.info("getGoldCoachInfo: not active resId! resId, isActive", resId, isActive);
            continue;
        }
        //logger.error("getGoldCoachInfo=----------------", taskObj)
        taskObj.isActive = isActive;
        if(taskType === commonEnum.ACT_GOLD_COACH_AWARD_TYPE.FREE)
        {

            rspGoldCoach.commonTaskList.push(taskObj);
        }
        else if (taskType === commonEnum.ACT_GOLD_COACH_AWARD_TYPE.CHARGE)
        {
            rspGoldCoach.goldTaskList.push(taskObj);
        }
    }

    //logger.info("rspGoldCoach,goldCoach: ",rspGoldCoach, goldCoach);
    return rspGoldCoach;
};

Act.prototype.checkIsAlreadyTake = function(dbActRecord, tagIndex)
{
    let awardFlag = dbActRecord.actRecord.awardFlag;
    if (utils.isBit(awardFlag, tagIndex))
    {
        return true;
    }
    return false;
};

Act.prototype.checkGoldCoachIsAlreadyTake = function(dbActRecord, tagIndex)
{
    let goldCoach = dbActRecord.actRecord.goldCoach;
    let haveTakeAwardList = goldCoach.haveTakeAwardList;
    let haveTake = false;
    if (haveTakeAwardList.length <= 0)
    {
        return haveTake;
    }

    for (let i in haveTakeAwardList) {
        const idx = haveTakeAwardList[i];
        if (idx === tagIndex)
        {
            haveTake = true;
            break;
        }
    }
    
    return haveTake;
};

//独立档位奖励信息
Act.prototype.getSingleSharedValueTagsActStatus = function(actId, tagIndex, act_info, init_btn_status, config)
{
    let btn_status = init_btn_status || commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    let cond_value = config.Parameter1;
    let actRecord = act_info.actRecord;
    let singleTagIndexList = actRecord.singleTagIndexList;
    let real_value = this.getSingleTagIndexCurrValue(singleTagIndexList, tagIndex);
    if (real_value >= cond_value) 
    {
        if (this.checkIsAlreadyTake(act_info, tagIndex))
        {
            //logger.info("makeRspSharedValueTagsAct: ", tagIndex);
            btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else
        {
            btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
        }
    }

    return btn_status;
};

Act.prototype.getSharedValueTagsActStatus = function(actId, tagIndex, act_info, init_btn_status, config)
{
	let real_value = act_info.actRecord.currValue;
    let btn_status = init_btn_status || commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    let cond_value = config.Parameter1;
    //logger.info("makeRspSharedValueTagsAct: actId, cond_value, real_value", actId, cond_value, real_value);
    if (real_value >= cond_value) 
    {
        if (this.checkIsAlreadyTake(act_info, tagIndex))
        {
            //logger.info("makeRspSharedValueTagsAct: tagIndex", tagIndex);
            btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else 
        {
            btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
        }
    }

    return btn_status;
};

Act.prototype.getSharedValueTagsActStatusByGoldCoach = function(actId, tagIndex, act_info, init_btn_status, config)
{
    let btn_status = init_btn_status || commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    let goldCoach = act_info.actRecord.goldCoach;
    let haveTake = this.checkGoldCoachIsAlreadyTake(act_info, tagIndex);
    //logger.info("getSharedValueTagsActStatusByGoldCoach: haveTake, btn_status", haveTake, btn_status);
    if (!haveTake) //没有找到
    {
        //判断当前等级是否和配置表等级一致
        let level = goldCoach.level;
        //logger.info("getSharedValueTagsActStatusByGoldCoach: level, Parameter2", level, config.Parameter2);
        if (config.Parameter2 > level ) //超过当前等级,不能领取
        {
            btn_status = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
        }else //还没有超过
        {
            let coachAwardType = config.Parameter1;
            //在判断是否为付费用户
           // logger.info("getSharedValueTagsActStatusByGoldCoach: coachAwardType", coachAwardType);
            if (coachAwardType === commonEnum.ACT_GOLD_COACH_AWARD_TYPE.CHARGE) //付费
            {
                let isBuy = goldCoach.isBuy;
                //logger.info("getSharedValueTagsActStatusByGoldCoach: isBuy", isBuy);
                if (isBuy === commonEnum.ACT_GOLD_COACH_BUY_TYPE.BUY) //充值了,可以领取
                {
                    btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE; 
                }else //没有买,不能领取
                {
                    btn_status = commonEnum.ACT_BTN_STATUS.NOT_TAKE; //不能领取了
                }
            }else 
            {
                btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE; //符合领取了
            }
        }
    }else
    {
        btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE; //已经领取了
    }

    return btn_status;
};

Act.prototype.getSharedValueTagsActStatusByContinuedCharge = function(actId, tagIndex, act_info, init_btn_status, config)
{
    let btn_status = init_btn_status || commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    let cond_value = config.Parameter1;
    let curr_value = act_info.actRecord.currValue;
    let continueCharge = act_info.actRecord.continueCharge;
    let currTagIndex = continueCharge.tagIndex;
    let actType = act_info.actType;

    // let maxTagIndex = this.getTagConfigMaxTagIndex(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE);
    let maxTagIndex = this.getTagConfigMaxTagIndex(actType);
    logger.info("getSharedValueTagsActStatusByContinuedCharge: currTagIndex, tagIndex, maxTagIndex", currTagIndex, tagIndex, maxTagIndex, curr_value, cond_value, act_info.actRecord);
    if (tagIndex <= currTagIndex)
    {
        if (curr_value >= cond_value)
        {
            if (this.checkIsAlreadyTake(act_info, tagIndex))
            {
                btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
            }else 
            {
                btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
            }
        }
    }else 
    {
        btn_status = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    }

    //处理倒数第一个奖励状态
    let lastIndex = maxTagIndex - 1;
    if (maxTagIndex > 0 && tagIndex === lastIndex)
    {
        if (this.checkIsAlreadyTake(act_info, lastIndex)) 
        {
            btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else 
        {
            //先检查最后一个档次有没有被领取
            if (this.checkIsAlreadyTake(act_info, lastIndex - 1)) //倒数第二已经领取了才能够领取倒数第一个的奖励
            {
                btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
            }else
            {
                btn_status = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
            }
        }
    }

    return btn_status;
};

//多档奖励的活动
Act.prototype.makeRspMultipleTagsAct = function(actId, tagIndex, dbActRecord, init_btn_status, tagInfo, config)
{
    let curr_value = 0;
    let actType = 0;
    if (dbActRecord) 
    {
		let actRecord = dbActRecord.actRecord;
        curr_value = actRecord.currValue;
        actType = dbActRecord.actType;
    }
    
    //金牌教练任务单独处理
    if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH)
    {
        this.makeRspSharedValueTagsActGoldCoach(actId, tagIndex, dbActRecord, curr_value, init_btn_status, tagInfo, config);
    }else if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE || actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_1
        || actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_2)
    {
        this.makeRspSharedValueTagsContinueCharge(actId, tagIndex, dbActRecord, curr_value, init_btn_status, tagInfo, config);
    }
    else
    {
        this.makeRspSharedValueTagsAct(actId, tagIndex, dbActRecord, curr_value, init_btn_status, tagInfo, config);
    }
};

Act.prototype.makeRspSharedValueTagsAct = function(actId, tagIndex, act_info, curr_value, init_btn_status, tagInfo, config)
{
    let btn_status = init_btn_status || commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    let cond_value = config.Parameter1;
    //logger.info("makeRspSharedValueTagsAct: actId, cond_value, curr_value", actId, cond_value, curr_value);
    if (curr_value >= cond_value) 
    {
        if (this.checkIsAlreadyTake(act_info, tagIndex))
        {
            //logger.info("makeRspSharedValueTagsAct: tagIndex", tagIndex);
            btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else 
        {
            btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
        }
    }

    if (curr_value > cond_value)
    {
        curr_value = cond_value;
    }

    let actType = act_info.actType;
    if (actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD) //月卡特殊处理下
    {
        let monthCard = act_info.actRecord.monthCard;
        if (monthCard.isBuy) //购买了月卡
        {
            if (this.checkIsAlreadyTake(act_info, tagIndex))
            {
                //logger.info("makeRspSharedValueTagsAct.monthCard: tagIndex, isBuy", tagIndex, monthCard.isBuy);
                btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
            }else 
            {
                btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
            }
            curr_value = cond_value;
        }
    }

    tagInfo.btnStatus = btn_status;
    tagInfo.currValue = curr_value;
    tagInfo.totalValue = cond_value;
};

//note: 金牌单独拿出来是因为奖励最多有个档位，也就是需要60位bit来标记，这里就不能使用之前的处理，直接使用index即可
Act.prototype.makeRspSharedValueTagsActGoldCoach = function(actId, tagIndex, act_info, curr_value, init_btn_status, tagInfo, config)
{
    let btn_status = this.getSharedValueTagsActStatusByGoldCoach(actId, tagIndex, act_info, init_btn_status, config);
    let coachAwardType = config.Parameter1;
    tagInfo.btnStatus = btn_status;
    tagInfo.currValue = curr_value;
    tagInfo.totalValue = curr_value;
    tagInfo.tagIndex = tagIndex;
    tagInfo.coachAwardType = coachAwardType;
};

//note: 连续储值
Act.prototype.makeRspSharedValueTagsContinueCharge = function(actId, tagIndex, act_info, curr_value, init_btn_status, tagInfo, config)
{
    let btn_status = init_btn_status || commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    let cond_value = config.Parameter1;
    let continueCharge = act_info.actRecord.continueCharge;
    let currTagIndex = continueCharge.tagIndex;

    if (tagIndex < currTagIndex)
    {
        curr_value = cond_value;
        if (this.checkIsAlreadyTake(act_info, tagIndex))
        {
            btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else 
        {
            btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
        }

    }else if (tagIndex === currTagIndex)
    {
        if (curr_value >= cond_value)
        {
            if (this.checkIsAlreadyTake(act_info, tagIndex))
            {
                btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
            }else 
            {
                btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
            }
        }
    }else 
    {
        curr_value = 0;
        btn_status = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    }

    let actType = act_info.actType;
    //处理倒数第一个奖励状态
    // let maxTagIndex = this.getTagConfigMaxTagIndex(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE);
    let maxTagIndex = this.getTagConfigMaxTagIndex(actType);
    let lastIndex = maxTagIndex - 1;
    logger.info("makeRspSharedValueTagsContinueCharge: tagIndex, maxTagIndex", tagIndex, maxTagIndex, lastIndex - 1, this.checkIsAlreadyTake(act_info, tagIndex));
    if (maxTagIndex > 0 && tagIndex === lastIndex)
    {
        if (this.checkIsAlreadyTake(act_info, lastIndex)) 
        {
            btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else 
        {
            //倒数第二已经领取了才能够领取倒数第一个的奖励
            if (this.checkIsAlreadyTake(act_info, lastIndex - 1))
            {
                btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
                logger.info("makeRspSharedValueTagsContinueCharge: tagIndex, maxTagIndex 1");
            }else
            {
                btn_status = commonEnum.ACT_BTN_STATUS.NOT_TAKE;
                logger.info("makeRspSharedValueTagsContinueCharge: tagIndex, maxTagIndex 2");
            }
        }
    }

    tagInfo.btnStatus = btn_status;
    tagInfo.currValue = curr_value;
    tagInfo.totalValue = cond_value;
};

//单档奖励活动
Act.prototype.makeRspSingleTagsAct = function(actId, tagIndex, dbActRecord, init_btn_status, tagInfo, config)
{
    this.makeRspSingleSharedValueTagsAct(actId, tagIndex, dbActRecord, init_btn_status, tagInfo, config)
};

//独立档位奖励信息
Act.prototype.makeRspSingleSharedValueTagsAct = function(actId, tagIndex, act_info, init_btn_status, tagInfo, config)
{
    let btn_status = init_btn_status || commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    let cond_value = config.Parameter1;
    if (!act_info)
    {
        tagInfo.btnStatus = btn_status;
        return;
    }

    //1.先从存储的信息去找相应的值
    let actRecord = act_info.actRecord;
    let singleTagIndexList = actRecord.singleTagIndexList;
    let real_value = this.getSingleTagIndexCurrValue(singleTagIndexList, tagIndex);
    if (real_value >= cond_value) 
    {
        if (this.checkIsAlreadyTake(act_info, tagIndex))
        {
            //logger.info("makeRspSingleSharedValueTagsAct: ", tagIndex);
            btn_status = commonEnum.ACT_BTN_STATUS.ALREADY_TAKE;
        }else
        {
            btn_status = commonEnum.ACT_BTN_STATUS.CAN_TAKE;
        }
    }

    if (real_value > cond_value)
    {
        real_value = cond_value;
    }

    tagInfo.btnStatus = btn_status;
    tagInfo.currValue = real_value;
    tagInfo.totalValue = cond_value;
};
//国庆签到界面
Act.prototype.makeRsSignTagsAct = function(actId, tagIndex, act_info, init_btn_status, tagInfo, config)
{
    let btn_status = init_btn_status || commonEnum.ACT_BTN_STATUS.NOT_TAKE;
    if (!act_info)
    {
        tagInfo.btnStatus = btn_status;
        return;
    }
    let currValue;
    //1.先从存储的信息去找相应的值
    let actRecord = act_info.actRecord;
    var signList = [];
    var actCtlConfig = dataApi.allData.data["ActiveControl"][actId];//得到活动控制表
    var actparconfig = dataApi.allData.data["ActiveParam"];//得到活动参数表
    var day = TimeUtils.dayInterval(actCtlConfig.StartTime) + 1;//得到活动开始了多少天
    var count = 0;
    for(let i in actparconfig)
    {
        if (actparconfig[i].GroupId === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN)
        {
            count++;
        }
    }
    //actRecord.logsign = [];//重新初始化一下
    if(!actRecord.logsign || actRecord.logsign.length < count)  //如果为空初始化
    {
        signList = this.initSignData();
    }
    else
    {
        signList = actRecord.logsign;
    }
    this.player.tasks.resetSignTask();//发奖励邮件
    this.updateSignTaskPogress();//更新任务进度
    // logger.error("ttttttttttttttttttttttttt", tagIndex, signList)
    btn_status = signList[tagIndex].state;
    currValue = signList[tagIndex].index;
    tagInfo.btnStatus = btn_status;
    tagInfo.currValue = currValue;
    //logger.debug("按钮状态==============", signList);
};
//获取国庆活动开始多少天
Act.prototype.getNationalDayInfo = function(actId)
{
    var day = 0;

    if (actId === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN)
    {
        var actCtlConfig = dataApi.allData.data["ActiveControl"][actId];//得到活动控制表
        day = (TimeUtils.dayInterval(actCtlConfig.StartTime) + 1) > 0 ? (TimeUtils.dayInterval(actCtlConfig.StartTime) + 1):0;//得到活动开始了多少天
    }
    return day;
};
//初始化玩家国庆活动数据
Act.prototype.initSignData = function()
{
    var actparconfig = dataApi.allData.data["ActiveParam"];//得到活动参数表
    var count = 0;
    let actRecord = {};
    for (let [k, v] of this.globalCurrActList) //活动列表
    {
        if (v.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN)
        {
            let actId = v.actId;
            let dbActRecord = this.globalActMgrInfo.get(v.actId);//活动数据
            if (!dbActRecord)
            {
                continue;
            }
            actRecord = dbActRecord.actRecord;
        }
    }
    for(let i in actparconfig)
    {
        if (actparconfig[i].GroupId === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN)
        {
            count++;
        }
    }
    var signList = [];
    if(!actRecord.logsign || actRecord.logsign.length < count)  //如果为空初始化
    {
        actRecord.logsign = [];
        for(let i in actparconfig)
        {
            if(actparconfig[i].GroupId === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SEVENDAY_SIGN)
            {
                let ret = {};
                ret.index = i;
                ret.signDay = -1;
                ret.awardFlag = 0;                  //领取标记 0不可领取 1可领取
                ret.taskid = actparconfig[i].Parameter1;
                ret.state = commonEnum.ACT_BTN_STATUS.NOT_SIGN;
                signList.push(ret);
            }
        }
        actRecord.logsign = signList;
    }
    return signList;
};


Act.prototype.getSingleTagIndexCurrValue = function(singleTagIndexList, tagIndex)
{
    let curr_value = 0;
    if (!singleTagIndexList || singleTagIndexList.length <= 0)
    {
        return curr_value;
    }

    for (const idx in singleTagIndexList) {
        let tagInfo = singleTagIndexList[idx];
        if (tagInfo.tagIndex === tagIndex)
        {
            curr_value = tagInfo.currValue;
            break;
        }
    }

    return curr_value;
};

//**********************************具体活动 ************************************************** */
Act.prototype.onCharge = function(value, rechargeId)
{
    let isAddCommonCharge = false;
    let isAddFirstCharge = false;
    switch (rechargeId) 
    {
        case commonEnum.ACT_RECHARGE_ID.ONE_RMB_GIFT: //每日充值
            this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_DAILY_ON_CHARGE, value, 0);
            break;
        case commonEnum.ACT_RECHARGE_ID.TWO_RMB_GIFT:
            this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_DAILY_ON_CHARGE, value, 1);
            break;
        case commonEnum.ACT_RECHARGE_ID.THREE_RMB_GIFT:
            this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_DAILY_ON_CHARGE, value, 2);
            break;
        case commonEnum.ACT_RECHARGE_ID.MONTH_CARD:    //月卡
            this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD, value, 0);
            isAddCommonCharge = true;
            isAddFirstCharge = true;
            break;
        case commonEnum.ACT_RECHARGE_ID.GOLD_COACH:   //金牌教练
            this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH, value, 0);
            isAddCommonCharge = true;
            isAddFirstCharge = true;
            break;
        default:
            isAddCommonCharge = true;
            isAddFirstCharge = true;
            break;
    }

    if (isAddCommonCharge) //累计充值
    {
        this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE, value, 0);           //计入累计充值
        this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE_1, value, 0);         //计入累计充值
        this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_OPEN_GIFT_BAG, value / 10, 0);      //开服大礼包，value是球币数量，除以10按累计充值多少元计算不是按球币
    }

    if (isAddFirstCharge) //首充
    {
        this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE, value, 0);
    }

    //超值大礼包   5日联储后
    if(this.isCanProcessActCharge()) {
        this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_BIG_GIFT, value, 0);                //超值大礼包
    }

    this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE, value, 0);                //计入998内马尔
    this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_1, value, 0);                //计入998内马尔
    this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE, value, 0);               //计入持续5日充值
    this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_2, value, 0);                //98充值活动
    this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_3, value, 0);                //38充值活动
    this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_4, value, 0);                //68充值活动
    this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_1, value, 0);               //计入持续4日充值
    this.processActCharge(commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_2, value, 0);               //计入持续3日充值
};

//是否启动后面活动
Act.prototype.isCanProcessActCharge = function(){
    let isCan = false;
    for (let [k, v] of this.globalCurrActList) {
        let dbActRecord = this.globalActMgrInfo.get(k);
        let actType = dbActRecord.actType;
        if(actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE) {
            let actId = dbActRecord.actId;
            //超值大礼包   5日联储后
            let act = this.getTagList(actId, actType);
            for(let i = 0; i < act.length; ++i) {
                if(act[i].tagIndex === 4 && act[i].btnStatus !== 0) {
                    isCan = true;
                    break;
                }
            }

            if(isCan) {
                break;
            }
        }
    }
    return isCan;
}

//消耗
Act.prototype.onConsume = function(value)
{
    logger.info("onConsume: value", value);
    for (let [k, v] of this.globalCurrActList) 
    {
        if ( v.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME
            || v.actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CONSUME_1)
        {
            let dbActRecord = this.globalActMgrInfo.get(v.actId);
            this.onAccumulationConsumeGold(dbActRecord, value);
        }
    }
    this.checkSatisfyConditionButNotTake();
};

//累计消耗
Act.prototype.onAccumulationConsumeGold = function(actMgrRecord, value)
{
	let actRecord = actMgrRecord.actRecord;
	let totalGold = actRecord.currValue;
    let actId = actMgrRecord.actId;
    let actType = actMgrRecord.actType;
	totalGold = totalGold + value;
    if (value >= 99999999)
    {
        logger.error("too much gold");
        return false;
    }

    actRecord.currValue = totalGold;
    logger.warn("onAccumulationConsumeGold", actRecord.currValue);

    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.COST_TOTAL, [ actType, totalGold, value], {});
	return true;
};

Act.prototype.processActCharge = function(actType, value, tagIndex)
{
    logger.info("processActCharge: actType, value, tagIndex", actType, value, tagIndex);
    for (let [k, v] of this.globalCurrActList) 
    {
        let dbActRecord = this.globalActMgrInfo.get(k);
        if (!dbActRecord)
        {
            //logger.warn("没有这个类型的活动------------", actType);
            continue;
        }

        if (v.actType === actType)
        {
            switch (actType) 
            {
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_DAILY_ON_CHARGE:
                    this.onDailyCharge(dbActRecord, value, tagIndex);
                    break;

                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE:
                    this.onAccumulationCharge(dbActRecord, value);
                    break;

                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_ACCUMULATION_CHARGE_1:
                    this.onAccumulationCharge(dbActRecord, value);
                    break;

                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_FIRST_CHARGE:
                    this.onFirstCharge(dbActRecord, value);
                    break;

                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD:
                    this.onMonthCardCharge(dbActRecord, value);
                    break;

                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH:
                    this.onGoldCoachCharge(dbActRecord, value);
                    break;

                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE:
                    this.onContinueCharge(dbActRecord, value);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_1:
                    this.onContinueCharge(dbActRecord, value);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_CONTINUE_CHARGE_2:
                    this.onContinueCharge(dbActRecord, value);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE:
                    this.onAccumulationCharge(dbActRecord, value);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_1:
                    this.onAccumulationCharge(dbActRecord, value);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_2:
                    this.onAccumulationCharge(dbActRecord, value);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_3:
                    this.onAccumulationCharge(dbActRecord, value);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SPECIAL_CHARGE_4:
                    this.onAccumulationCharge(dbActRecord, value);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_OPEN_GIFT_BAG:
                    this.onOpenCharge(dbActRecord, value);
                    break;
                case commonEnum.ACT_COMMON_TYPE.ACT_TYPE_BIG_GIFT:
                    this.onAccumulationCharge(dbActRecord, value);
                    break;
                default:
                    break;
            }

        }
    }
    this.checkSatisfyConditionButNotTake();
};

//累计充值
Act.prototype.onAccumulationCharge = function(actMgrRecord, value)
{
    let actRecord = actMgrRecord.actRecord;
    let totalCharge = actRecord.currValue;
    let actId = actMgrRecord.actId;
    let actType = actMgrRecord.actType;
    totalCharge = totalCharge + value;
    if (totalCharge >= 99999999)
    {
        logger.error("too much gold");
        return false;
    }

    actRecord.currValue = totalCharge;
    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.GET_RECHARGE_TOTAL, [ actType, totalCharge, value], {});
    return true;
};

//月卡
Act.prototype.onMonthCardCharge = function(actMgrRecord, value)
{
	 let actRecord = actMgrRecord.actRecord;
     let totalCharge = actRecord.currValue;
     let monthCard = actRecord.monthCard;
     let actId = actMgrRecord.actId;
     logger.info("onMonthCardCharge: totalCharge, value", totalCharge, value);
    if (value >= 99999999)
    {
        logger.error("too much gold");
        return false;
    }
    
    totalCharge += value;
    actRecord.currValue = totalCharge;
    // logger.error("onFirstCharge value: ", actRecord.currValue);

    let config = dataApi.allData.data["ActiveParam"];
    if (!config) {
        logger.error("onMonthCardCharge: ActiveParam config is not found");
        return false;
    }

    //找出月卡需要支付的费用
    let costMoney = 0;
    for (let i in config) {
        if ( config[i].GroupId === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_MONTH_CARD) {
            costMoney = config[i].Parameter1;
            logger.info("costMoney, param", costMoney, config[i].Parameter1);
            break;
        }
    }

    if (costMoney === 0) 
    {
        logger.error("onMonthCardCharge: not costMoney!", costMoney);
        return false;
    }

    //充钱还不够
    if (totalCharge < costMoney) 
    {
        logger.error("onMonthCardCharge: totalCharge not enough!", costMoney);
        return false;
    }

    //只记录到期购买的时间
    if (monthCard.cardTime === 0) {
        monthCard.buyMonthCardTime = TimeUtils.now();
        monthCard.allDay = 0;
    }
    let cardCount = 30;
    monthCard.cardTime += cardCount;
    monthCard.allDay += cardCount;
    monthCard.buyMonthNum++;
    if (monthCard.isBuy == 0) 
    {
        monthCard.isBuy = 1;
    }

    for (let i = 0; i < cardCount; ++i) {
        let info = {};
        info.status = commonEnum.ACT_BTN_STATUS.NOT_TAKE; //状态  0未领取   1已领取  2已发邮件
        monthCard.monthCardInfo.push(info);
    }

    actRecord.currValue -= costMoney;
    logger.error("onMonthCardCharge: ", actMgrRecord);

    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.MONTH_VIP, [ actId, costMoney ], {});
    return true;
};

//首充
Act.prototype.onFirstCharge = function(actMgrRecord, value)
{
	let actRecord = actMgrRecord.actRecord;
    let totalCharge = actRecord.currValue;
    let actId = actMgrRecord.actId;
    logger.info("onFirstCharge: totalCharge, value", totalCharge, value);
    if (value >= 99999999)
    {
        logger.error("too much gold");
        return false;
    }
	
    actRecord.currValue += value;
    // logger.error("onFirstCharge value: ", actRecord.currValue);

    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.FIRST_RECHARGE, [ value ], {});    
	return true;
};
//开服大礼包
Act.prototype.onOpenCharge = function(actMgrRecord, value)
{
    //logger.error("开服大礼包----onOpenCharge", value);
    var config = dataApi.allData.data["ActiveParam"];
    if (!config)
    {
        logger.error("onOpenCharge: ActiveControl config not found!");
        return;
    }
    let gradeNum = 0;//几个档位
    let actRecord = actMgrRecord.actRecord;
    let totalCharge = actRecord.currValue;
    let actId = actMgrRecord.actId;
    let actType = actMgrRecord.actType;
    totalCharge = totalCharge + value;//得到累计充值数
    if (totalCharge >= 99999999)
    {
        logger.error("too much gold");
        return false;
    }
    //得到档位数量
    for(let i in config)
    {
        if(config[i].GroupId === actType)
            gradeNum++;
    }
    //记录累计充值球币
    actRecord.currValue = totalCharge;
    let singleTagIndexList = actRecord.singleTagIndexList;
    //logger.info("onOpenCharge", value);
    //设置每个档位已充值球币
    if(singleTagIndexList.length !== gradeNum)
    {
        singleTagIndexList = [];
        for(let i = 0; i < gradeNum; i++)
        {
            let obj = {
                tagIndex: i,
                currValue: totalCharge,
            }
            singleTagIndexList.push(utils.deepCopy(obj));
        }
    }
    else
    {
        for(let i in singleTagIndexList)
        {
            singleTagIndexList[i].currValue = totalCharge;
        }
    }
    actRecord.singleTagIndexList = singleTagIndexList;
    actRecord.currValue = totalCharge;
    //logger.error("onOpenCharge value: ", actRecord.singleTagIndexList);
    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.OPEM_GIFT_BAG, [ actId, value ], {});
    return true;
};
//每日充值
Act.prototype.onDailyCharge = function(actMgrRecord, value, tagIndex)
{
    let actRecord = actMgrRecord.actRecord;
    let actId = actMgrRecord.actId;
    let singleTagIndexList = actRecord.singleTagIndexList;
    if (value >= 99999999)
    {
        logger.error("too much gold");
        return false;
    }

    logger.info("onDailyCharge", value, tagIndex);
    if (singleTagIndexList.length <= 0)
    {
        let obj = {
            tagIndex: tagIndex,
            currValue: value,
        }
        singleTagIndexList.push(obj);
    }else
    {
        let isFind = false;
        for(let idx in singleTagIndexList)
        {
            let data = singleTagIndexList[idx];
            if (data.tagIndex === tagIndex)
            {
                data.currValue += value;
                isFind = true;
                break;
            }
        }

        if (!isFind)
        {
            let obj = {
                tagIndex: tagIndex,
                currValue: value,
            }
            singleTagIndexList.push(obj); 
        }
    }

    // logger.error("onDailyCharge value: ", actRecord.singleTagIndexList);
    this.player.recordSlog(this.uid, commonEnum.STATIS_LOG_TYPE.EVERYDAY_RECHARGE_GIFT, [ actId, tagIndex, value ], {}); 
	return true;
};

//金牌教练
Act.prototype.onGoldCoachCharge = function(actMgrRecord, value)
{
	 let actRecord = actMgrRecord.actRecord;
     let totalCharge = actRecord.currValue;
     logger.info("onGoldCoachCharge: totalCharge, value", totalCharge, value);
    if (value >= 99999999)
    {
        logger.error("too much gold");
        return false;
    }
    
    totalCharge += value;
    actRecord.currValue = totalCharge;

    //把付费任务激活
    let goldCoach = actRecord.goldCoach;
    goldCoach.isBuy = commonEnum.ACT_GOLD_COACH_BUY_TYPE.BUY;
    let taskList = goldCoach.taskList;
    for(let idx in taskList)
    {
        let obj = taskList[idx];
        let resId = obj.resId;
        if (obj.taskType === commonEnum.ACT_GOLD_COACH_AWARD_TYPE.CHARGE && obj.isActive === 0)
        {
             obj.isActive = 1;
             let goldCoachType = commonEnum.TASK_INFO.goldCoachType - 1;
             if(this.player.tasks._checkIsFinish(goldCoachType, resId)) {
                 this.updateGoldCoachTaskProgress(resId);
             }
             logger.info("onGoldCoachCharge: active task! resId", resId);
        }
    }
    
    // logger.error("onGoldCoachCharge: ", actMgrRecord);
    return true;
};

//升级任务等级
Act.prototype.upgradeGoldCoachLevel = function(actId)
{
    let retCode = Code.FAIL;
    let dbActRecord = this.globalActMgrInfo.get(actId);
    //logger.info("upgradeGoldCoachLevel:actId", actId);
    if (!dbActRecord)
    {
        logger.error("upgradeGoldCoachLevel: not dbActRecord! actId, tagIndex", actId);
        return retCode;
    }

    let actType = dbActRecord.actType;
    if (actType !== commonEnum.ACT_COMMON_TYPE.ACT_TYPE_GOLD_COACH)
    {
        logger.error("upgradeGoldCoachLevel: not gold coach", actId);
        return retCode;
    }

    let actRecord = dbActRecord.actRecord;
    let goldCoach = actRecord.goldCoach;
    let nextLevel = goldCoach.level + 1;
    if (nextLevel > Constant.ACT.GOLD_COACH_MAX_LEVEL)
    {
        logger.error("upgradeGoldCoachLevel: level error!", actId, nextLevel);
        return retCode;
    }

    goldCoach.level = nextLevel;
    logger.info("upgradeGoldCoachLevel: actId, nextLevel", actId, nextLevel);
    this.checkSatisfyConditionButNotTake();
    retCode = Code.OK;
    return retCode;
};

//持续充值
Act.prototype.onContinueCharge = function(actMgrRecord, value)
{
	 let actRecord = actMgrRecord.actRecord;
     logger.info("onContinueCharge: value", value);
    if (value >= 99999999)
    {
        logger.error("onContinueCharge: too much gold", value);
        return false;
    }
    
    actRecord.currValue += value;
    logger.error("持续充值----------------", actMgrRecord);
    return true;
};

//活动--最佳11人
Act.prototype.buyBestFootball = function(index) {
    let ret = {
       code: Code.FAIL,
       itemList: []
    }
    let actStartTime = dataApi.allData.data['ActivityConfig'][5].StartTime;
    if(actStartTime > this.player.bestFootballerTime) {
        this.player.bestFootballerTime = actStartTime;
        this.player.buyBestFootballerNum = 0;
    }

    let needGold = 0;
    if(index === 1) {
        needGold = dataApi.allData.data["SystemParam"][9011].Param;
    }else {
        needGold = dataApi.allData.data["SystemParam"][9011].Param * 10;
    }

    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, needGold)){
        ret.code = Code.GOLD_FALL;
        return ret;
    }

    let needBuyNum = 0;
    if(typeof dataApi.allData.data["SystemParam"][9017] === "undefined") {
        needBuyNum = 55;
    }else {
        needBuyNum = dataApi.allData.data["SystemParam"][9017].Param;
    }

    let itemList = [];
    let randomList = [];  //随机物品列表
    let nextBuyNum = 0;
    let itemNameList = "";
    if(index === 1) {   //单次抽奖
        let type = 1;
        nextBuyNum = this.player.buyBestFootballerNum + 1;
        if(nextBuyNum > needBuyNum) {
            type = 2;
            this.player.buyBestFootballerNum = 0;
        }

        randomList = this.randomBestFootball(type, 1, "BestFootball");
        itemNameList = dataApi.allData.data["BestFootball"][randomList[0]].Name;
        if(type !== 2) {
            this.player.buyBestFootballerNum ++;
        }
        itemList.push(randomList[0]);
    }else {
        //10连抽  买10送1
        nextBuyNum = this.player.buyBestFootballerNum + 10;
        let addNum = nextBuyNum - needBuyNum;
        let mustDrop = false;
        if(nextBuyNum > needBuyNum) {
            mustDrop = true;
            this.player.buyBestFootballerNum = 0;
        }

        let mustList = [];  //必出奖励
        //有必出
        if(mustDrop) {
            randomList = this.randomBestFootball(1, 10, "BestFootball");
            //必出奖励
            mustList = this.randomBestFootball(2, 1, "BestFootball");
            let itemName = dataApi.allData.data["BestFootball"][mustList[0]].Name;
            itemNameList = itemNameList + itemName + "，";
            itemList.push.apply(itemList, mustList);
            this.player.buyBestFootballerNum = this.player.buyBestFootballerNum + addNum - 1;
        }else {
            randomList = this.randomBestFootball(1, 11, "BestFootball");
            this.player.buyBestFootballerNum += 10;
        }

        for(let i = 0; i < randomList.length; ++i) {
            let itemId = randomList[i];
            let itemName = dataApi.allData.data["BestFootball"][itemId].Name;
            itemNameList = itemNameList + itemName + "，";
        }

        itemList.push.apply(itemList, randomList);
    }

    let specialAttachInfo = {
        roomUid: ""
    };
    let title = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.BEST_FOOTBALL].Title;
    //发邮件
    this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.BEST_FOOTBALL, commonEnum.MailType.SYSMAIL, [], specialAttachInfo, itemNameList, "", "", "");

    // let msg = "<font color=0x83db43>" + this.player.name + "</font>" + "在"+ title + "活动中喜获" + "<font color=0x83db43>" + itemNameList + "</font>";
    // let sendMsg = {
    //     senderName: "系统",
    //     type: commonEnum.CHAT_TYPE.HORN,  // -1系统 0普通 1物品超链接 2球员超链接 3喇叭
    //     msg: msg,
    //     channel: commonEnum.CHAT_CHANNEL.SYSTEM_CHANNEL,
    // };
    // //发跑马灯
    // this.player.updateScrolling(sendMsg);
    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, needGold);
    for(let i = 0; i < itemList.length; ++i) {
        this.player.bag.addItem(itemList[i], 1);
    }

    ret.itemList = itemList;
    ret.code = Code.OK;
    return ret;
};


//活动--周末返场
Act.prototype.weekDayEncore = function() {
    let ret = {
        code: Code.FAIL,
        itemId: 0
    }
    // let actStartTime = dataApi.allData.data['ActivityConfig'][9].StartTime;
    let needGold = dataApi.allData.data["SystemParam"][9018].Param;
    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, needGold)){
        ret.code = Code.GOLD_FALL;
        return ret;
    }

    let randomList = this.randomBestFootball(1, 1, "BackWeekend");
    let item = dataApi.allData.data["BackWeekend"][randomList[0]];

    this.player.deductMoney(commonEnum.PLAY_INFO.gold, needGold);
    this.player.bag.addItem(item.Id, item.Num);

    ret.itemId = item.Id;
    ret.code = Code.OK;
    return ret;
}

/**
 *
 * @param type    奖励组
 * @param randomNum   随机数量
 * @param tableName   表名
 * @returns {Array}
 */
Act.prototype.randomBestFootball = function(type, randomNum, tableName) {
    let config = dataApi.allData.data[tableName];
    let randomConfig = [];
    let index = 0;
    for (let i in config) {
         if(type === config[i].Team) {
            randomConfig[index] = {};
            randomConfig[index].resId = config[i].Id;
            randomConfig[index].weight = config[i].Rate;
            index++;
         }
    }

    let randomList = [];
    for (let i in randomConfig) {
        for (let j = 0; j < randomConfig[i].weight; j++) {
            randomList.push(randomConfig[i].resId);
        }
    }

    let itemList = [];
    for(let i = 0; i < randomNum; ++i) {
        let itemId = randomList[Math.floor(Math.random() * randomList.length)];
        itemList.push(itemId);
    }
    return itemList;
};


//活动--赛季反场
Act.prototype.buyRegression = function() {
    let ret = {
        code: Code.FAIL,
        itemIdList: 0
    }

    let needGold = dataApi.allData.data["SystemParam"][9012].Param;
    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, needGold)){
        ret.code = Code.GOLD_FALL;
        return ret;
    }

    let itemIdList = this.randomRegression();
    let itemNum1 = dataApi.allData.data["Regression"][itemIdList[0]].Num;
    let itemName1 = dataApi.allData.data["Regression"][itemIdList[0]].Name;
    let itemNum2 = dataApi.allData.data["Regression"][itemIdList[1]].Num;
    let itemName2 = dataApi.allData.data["Regression"][itemIdList[1]].Name;
    let itemNum3 = dataApi.allData.data["Regression"][itemIdList[2]].Num;
    let itemName3 = dataApi.allData.data["Regression"][itemIdList[2]].Name;

    //发邮件
    this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.REGRESSION, commonEnum.MailType.SYSMAIL, [], {roomUid: ""}, itemName1 + "*" + itemNum1, itemName2 + "*" + itemNum2, itemName3 + "*" + itemNum3, "");

    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, needGold);
    for(let i = 0; i < itemIdList.length; ++i) {
        let itemId = dataApi.allData.data["Regression"][itemIdList[i]].ItemId;
        let itemNum = dataApi.allData.data["Regression"][itemIdList[i]].Num;
        if(itemNum > 0) {
            this.player.bag.addItem(itemId, itemNum);
        }
    }

    ret.code = Code.OK;
    ret.itemIdList = itemIdList;
    return ret;
};

Act.prototype.randomRegression = function() {
    let config = dataApi.allData.data["Regression"];
    let itemList_1 = [];
    let itemList_2 = [];
    let itemList_3 = [];
    let index = 0;
    let index_1 = 0;
    let index_2 = 0;
    for (let i in config) {
        if(config[i].Type === 1) {
            itemList_1[index] = {};
            itemList_1[index].resId = config[i].Id;
            itemList_1[index].weight = config[i].Weight;
            index++;
        }else if(config[i].Type === 2) {
            itemList_2[index_1] = {};
            itemList_2[index_1].resId = config[i].Id;
            itemList_2[index_1].weight = config[i].Weight;
            index_1++;
        }else if(config[i].Type === 3) {
            itemList_3[index_2] = {};
            itemList_3[index_2].resId = config[i].Id;
            itemList_3[index_2].weight = config[i].Weight;
            index_2++;
        }
    }

    let randomList = [];
    let randomList_1 = [];
    let randomList_2 = [];
    for (let i in itemList_1) {
        for (let j = 0; j < itemList_1[i].weight; j++) {
            randomList.push(itemList_1[i].resId);
        }
    }

    for (let i in itemList_2) {
        for (let j = 0; j < itemList_2[i].weight; j++) {
            randomList_1.push(itemList_2[i].resId);
        }
    }

    for (let i in itemList_3) {
        for (let j = 0; j < itemList_3[i].weight; j++) {
            randomList_2.push(itemList_3[i].resId);
        }
    }

    let arr = [];
    let itemId = randomList[Math.floor(Math.random() * randomList.length)];
    let itemId_1 = randomList_1[Math.floor(Math.random() * randomList_1.length)];
    let itemId_2 = randomList_2[Math.floor(Math.random() * randomList_2.length)];
    arr.push(itemId, itemId_1, itemId_2);

    return arr;
};
//返回老虎机玩家免费次数
Act.prototype.getTurntable = function() {
    logger.error("返回老虎机玩家免费次数----getTurntable");
    let ret = {code: Code.FAIL, oneFreeNum: 0, moreFreeNum: 0};
    let config = dataApi.allData.data["ActiveControl"];
    let actId = 0;
    //获取活动数据
    let actType = commonEnum.ACT_COMMON_TYPE.ACT_TYPE_TURNTABLE;
    for(let i in config)
    {
        if(config[i].ActivityType === actType)
        {
            actId = config[i].Id;
            // if(TimeUtils.dayInterval(config[i].StartTime) < 0|| TimeUtils.dayInterval(config[i].EndTime) > 0)//活动没开启
            // {
            //     ret.code = Code.TIME_FAIL;
            //     return ret;
            // }
        }

    }

    let dbActRecord = this.globalActMgrInfo.get(actId);
    let oneFreeNum = dbActRecord.actRecord.oneFreeNum;
    let moreFreeNum = dbActRecord.actRecord.moreFreeNum;
    let Control = dataApi.allData.data["TurntableControl"];
    if(Control[1].ItFree === 1 && Control[1].FreeNum > oneFreeNum)
    {
        ret.oneFreeNum = Control[1].FreeNum - oneFreeNum;
    }
    if(Control[2].ItFree === 1 && Control[2].FreeNum > moreFreeNum)
    {
        ret.moreFreeNum = Control[2].FreeNum - moreFreeNum;
    }
    ret.code = Code.OK;
    return ret;
};
//活动--老虎机抽奖       传参为 单抽 1 连抽 2
Act.prototype.buyTurntable = function(FrequencyType) {
    logger.error("活动--老虎机抽奖---- buyTurntable", FrequencyType);
    let ret = {
        code: Code.FAIL,
        itemIdList: 0,
        status: 0
    }
    let config = dataApi.allData.data["ActiveControl"];
    let actId = 0;
    let status = 0;//0普通 1小王 2大王；
    let openList = [];
    //获取活动数据
    let actType = commonEnum.ACT_COMMON_TYPE.ACT_TYPE_TURNTABLE;
    for(let i in config)
    {
        if(config[i].ActivityType === actType)
        {
            openList.push({startTime: config[i].StartTime, endTime: config[i].EndTime})

            actId = config[i].Id;
        }
    }

    let dbActRecord = this.globalActMgrInfo.get(actId);
    let Num = dbActRecord.actRecord.oneFreeNum;
    if(FrequencyType === 2)
    {
        Num = dbActRecord.actRecord.moreFreeNum;
    }

    if(!dbActRecord.param2)
    {
        dbActRecord.param2 = 0;
    }
    if(!dbActRecord.param3)
    {
        dbActRecord.param3 = 0;
    }

    let Control = dataApi.allData.data["TurntableControl"];
    let fileName = "TurntableReward";
    let ItemConfig = dataApi.allData.data["Item"];
    let ItemName;
    let flag = false;
    let itemIdList = [];

    //检查是否有免费次数
    if(Control[FrequencyType].ItFree === 1 && Num < Control[FrequencyType].FreeNum)//有免费次数
    {
        flag = true;
    }
    if(!flag)
    {
        //检查钱是否足够
        if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, Control[FrequencyType].Money)){
            ret.code = Code.GOLD_FALL;
            return ret;
        }
    }
    switch (FrequencyType) {
        case 1://单抽
            dbActRecord.param2 += 1;//小王累抽次数加1
            dbActRecord.param3 += 1;//大王累抽次数加1
            let grepId = this.randomTurntable(actId);
            if(grepId === 4)//大王
            {
                status = 2;
            }
            if(grepId === 3)//小王
            {
                status = 1;
            }
            let awardObj = this.getWeightGrepIdAward(fileName, grepId);
            itemIdList.push(utils.deepCopy(awardObj));
            if(grepId === 4 || grepId === 3)
            {
                if(grepId === 4 || grepId === 3 || awardObj.ItemId === 29994)//单抽大小王黑百搭有跑马灯
                {
                    ItemName = ItemConfig[awardObj.ItemId].Name;
                }
            }
            //logger.error("!!!!!!!!!!!!单抽!!!!!!!!!!!!!!", grepId, dbActRecord.param2, dbActRecord.param3);
            break;
        case 2://连抽
            let tempList = [];
            let mark = true;
            let temp = 0;
            for(let i = 0; i < 10; i++)
            {
                dbActRecord.param2 += 1;//小王累抽次数加10
                dbActRecord.param3 += 1;//大王累抽次数加10
                let grepId = this.randomTurntable(actId);
                if(grepId === 4)//大王
                {
                    tempList = [4,2,1,1,1,1,1,1,1,1];
                    status = 2;
                    temp = 4;
                    mark = false;
                    break;
                }
                else if(grepId === 3)//小王
                {
                    tempList = [3,2,1,1,1,1,1,1,1,1];
                    status = 1;
                    temp = 3;
                    mark = false;
                    break;
                }
                else if(grepId === 2)
                {
                    tempList = [2,1,1,1,1,1,1,1,1,1];
                    mark = false;
                    break;
                }
                tempList.push(grepId);
            }
            if(mark)
            {
                tempList = [2,1,1,1,1,1,1,1,1,1];
            }
            tempList.sort(_random_sort_func);
            for(let i in tempList)
            {
                let awardObj = this.getWeightGrepIdAward(fileName, tempList[i]);
                itemIdList.push(utils.deepCopy(awardObj));
                //跑马灯
                if(tempList[i] === temp || awardObj.ItemId === 29994)//大小王或者黑百搭
                {
                    ItemName = ItemConfig[awardObj.ItemId].Name;
                }
            }
            //logger.error("!!!!!!!!!!!!连抽!!!!!!!!!!!!!!", tempList, dbActRecord.param2, dbActRecord.param3);
            break;
    }


    let rewardList = [];
    for(let i = 0; i < itemIdList.length; i++)
    {
        let temp = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
            ResId: itemIdList[i].ItemId,
            Num: itemIdList[i].Num,
        };
        rewardList.push(utils.deepCopy(temp));
    }
    //发邮件
    let specialAttachInfo = {
        roomUid: ""
    };
    this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.TURNTABLE, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
    if(!flag)
    {
        //扣钱
        this.player.deductMoney(commonEnum.PLAY_INFO.gold, Control[FrequencyType].Money);
    }
    if(FrequencyType === 1)
    {
        dbActRecord.actRecord.oneFreeNum += 1;
    }
    else
    {
        dbActRecord.actRecord.moreFreeNum += 1;
    }
    let title = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.TURNTABLE].Title;
    if(!!ItemName)
    {
        let msg = "恭喜" + "<font color=0x83db43>" + this.player.name + "</font>" + "在" + title + "中获得" + "<font color=0x83db43>" + ItemName + "</font>";
        var sendMsg = {
            senderName: "系统",
            type: commonEnum.CHAT_TYPE.HORN,// -1系统 0普通 1物品超链接 2球员超链接 3喇叭
            msg: msg,
            channel: commonEnum.CHAT_CHANNEL.SYSTEM_CHANNEL,
        };
        //十连抽跑马灯
        this.player.updateScrolling(sendMsg);
    }
    this.globalActMgrInfo.set(actId, dbActRecord);
    //记录本次抽奖 抽奖类型 扣除金币 奖励列表
    this.historyActListAdd(actId, actType, {FrequencyType: FrequencyType, coin: Control[FrequencyType].Money, itemIdList: itemIdList});
    ret.code = Code.OK;
    ret.itemIdList = itemIdList;
    ret.status = status;
    return ret;
};
//得到对应权重组奖励
Act.prototype.getWeightGrepIdAward = function(filename, GroupId)
{
    let config = dataApi.allData.data[filename];
    let itemList = [];
    let index = 0;
    for(let i in config)
    {
        if(config[i].GroupId === GroupId || config[i].Group === GroupId)
        {
            itemList[index] = {};
            itemList[index].resId = config[i].Id;
            if(filename === "PullerAward")
            {
                itemList[index].weight = config[i].GroupWeight;
            }
            else {
                itemList[index].weight = config[i].Weight;
            }
            itemList[index].ItemId = config[i].ItemId;
            itemList[index].Num = config[i].Num;
            index++;
        }
    }
    let randomList = [];
    for (let i in itemList) {
        for (let j = 0; j < itemList[i].weight; j++) {
            randomList.push(itemList[i]);
        }
    }
    let temp = randomList[Math.floor(Math.random() * randomList.length)];
    let award = {
        ItemId: temp.ItemId,
        Num: temp.Num,
        Id: temp.resId,
    };
    return award;
};
//老虎机得到权重组
Act.prototype.randomTurntable = function(actId) {
    let dbActRecord = this.globalActMgrInfo.get(actId);
    let queen = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.Turntable_queen].Param;
    let king = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.Turntable_king].Param;
    //老虎机总抽奖次数，判断是否能抽到大小王
    if(dbActRecord.param3 >= king)//大王
    {
       dbActRecord.param3 = 0;
       return 4;

    }
    else if(dbActRecord.param2 >= queen)//小王
    {
        dbActRecord.param2 = 0;
        return 3;
    }
    else
    {
        return 1;
    }
};
//随机排序
function _random_sort_func(Obj1, obj2)
{
    return Math.random() > 0.5 ? -1 : 1;
};
//
Act.prototype.historyActListAdd = function (actId, actType, date) {
    let limit = 50;
    let temp = [];
    date.actType = actType;
    if(this.historyActList.has(actId))
    {
        temp = this.historyActList.get(actId);
        if(temp.length >= limit)
        {
            temp.pop();
        }
        temp.unshift(date);
    }
    else
    {
        temp.unshift(date);
    }
    this.historyActList.set(actId, temp);
};

Act.prototype.testGetConfig = function() {
    logger.error("Act::::::::::::::", dataApi.allData.data["BestFootball"]);
}
//返回拉霸免费次数
Act.prototype.getSlots = function()
{
    let ret = {code: Code.FAIL, num: 0};
    let config = dataApi.allData.data["ActiveControl"];
    let actId = 0;
    //获取活动数据
    let actType = commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SLOTS;
    for(let i in config)
    {
        if(config[i].ActivityType === actType)
        {
            actId = config[i].Id;
        }

    }
    let freeNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.SLOTS_FREE_NUM].Param;//免费次数
    let dbActRecord = this.globalActMgrInfo.get(actId);

    if(dbActRecord.param1 < freeNum)
    {
       ret.num = freeNum - dbActRecord.param1;
    }
    ret.code = Code.OK;
    return ret;
};
//拉霸    1 单抽 2 十连抽
Act.prototype.buySlots = function(FrequencyType, securityMoney) {
    logger.error("活动--拉霸抽奖---- buySlots", FrequencyType);
    let ret = {
        code: Code.FAIL,
        itemIdList: 0,
        status: 0
    }
    let config = dataApi.allData.data["ActiveControl"];
    let actId = 0;
    let openList = [];
    let ItemName = ""; //滚屏物品名字

    //获取活动数据
    let actType = commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SLOTS;
    for(let i in config)
    {
        if(config[i].ActivityType === actType)
        {
            openList.push({startTime: config[i].StartTime, endTime: config[i].EndTime});

            actId = config[i].Id;
        }
    }
    //获取活动记录数据
    let data = this.getSlotsHistoryActData(actId, actType);

    let fileName = "PullerAward";
    //得到玩家的活动数据
    let dbActRecord = this.globalActMgrInfo.get(actId);
    let periods = dbActRecord.periods;//活动期数
    //保存的抽奖次数
    if(!dbActRecord.param1)//这个参数记录今天抽的次数，这个参数是每日刷新的
    {
        dbActRecord.param1 = 0;
    }
    if(!dbActRecord.param2)
    {
        dbActRecord.param2 = 0;
    }
    logger.error("抽前次数", dbActRecord.param2);
    let oneMoney = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.SLOTS_ONE_MONEY].Param;//拉1次消耗
    let MuchMoney = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.SLOTS_MUCH_MONEY].Param;//拉11次消耗
    let freeNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.SLOTS_FREE_NUM].Param;//免费次数
    let clearMoney = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.SLOTS_GUARANTEE_MONEY].Param;//拉霸获得保底金额

    let money = 0;//Control[FrequencyType].Money;
    if(FrequencyType === 1)
    {
        money = oneMoney;
    }
    else if(FrequencyType === 2)
    {
        money = MuchMoney;
    }
    else
    {
        ret.code = Code.PARAM_FAIL;
        return {ret: ret, money: money, periods: periods};
    }
    if(dbActRecord.param1 < freeNum && FrequencyType === 1)
    {
        money = 0;
    }

    let itemIdList = [];

    //检查钱是否足够
    if(!this.player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, money)){
        ret.code = Code.GOLD_FALL;
        return {ret: ret, money: money, periods: periods};
    }
    securityMoney += money;

    switch (FrequencyType) {
        case 1://单抽
            dbActRecord.param1 += 1;//当日累抽次数加1
            dbActRecord.param2 += 1;//累抽次数加1
            data.buyNum += 1;//记录次数增加
            let result = this.randomSlots(actId, data, securityMoney);
            let groupId = result.groupId;
            securityMoney = result.securityMoney;
            let awardObj = this.getWeightGrepIdAward(fileName, groupId);
            itemIdList.push(utils.deepCopy(awardObj));
            break;
        case 2://连抽
            let tempList = [];
            for(let i = 0; i < 11; i++)//10连抽送一次
            {
                dbActRecord.param2 += 1;//累抽次数加11
                data.buyNum += 1;//记录次数增加
                let result = this.randomSlots(actId, data, securityMoney);
                let groupId = result.groupId;
                securityMoney = result.securityMoney;
                tempList.push(groupId);
            }
            tempList.sort(_random_sort_func);
            for(let i in tempList)
            {
                let awardObj = this.getWeightGrepIdAward(fileName, tempList[i]);
                itemIdList.push(utils.deepCopy(awardObj));
            }
            break;
    }

    let pullerConfig = dataApi.allData.data["PullerAward"];

    let rewardList = [];
    for(let i = 0; i < itemIdList.length; i++)
    {
        //记录到奖池
        if(!dbActRecord.itemList)
        {
            dbActRecord.itemList = [];
            dbActRecord.itemList.push({itemId: itemIdList[i].ItemId, num: itemIdList[i].Num});//放入奖励池
        }
        else
        {
            let index = this._checkRewardById(dbActRecord.itemList, itemIdList[i].ItemId);
            if(index !== -1)
            {
                dbActRecord.itemList[index].num += itemIdList[i].Num;
            }
            else {
                dbActRecord.itemList.push({itemId: itemIdList[i].ItemId, num: itemIdList[i].Num});//放入奖励池
            }
        }
        //记录所有奖励
        if(!dbActRecord.allItemList)
        {
            dbActRecord.allItemList = [];
            dbActRecord.allItemList.push({itemId: itemIdList[i].ItemId, num: itemIdList[i].Num});//放入奖励池
        }
        else
        {
            let index = this._checkRewardById(dbActRecord.allItemList, itemIdList[i].ItemId);
            if(index !== -1)
            {
                dbActRecord.allItemList[index].num += itemIdList[i].Num;
            }
            else {
                dbActRecord.allItemList.push({itemId: itemIdList[i].ItemId, num: itemIdList[i].Num});//放入奖励池
            }
        }
        let temp = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
            ResId: itemIdList[i].ItemId,
            Num: itemIdList[i].Num,
        };
        rewardList.push(utils.deepCopy(temp));
        if(pullerConfig[itemIdList[i].Id].Broadcast === 1)
        {
            ItemName = pullerConfig[itemIdList[i].Id].Item;
        }
    }
    // //发邮件
    // let specialAttachInfo = {
    //     roomUid: ""
    // };
    // this.player.email.sendMailReward("Sys", commonEnum.MAIL_TRANSLATE_CONTENT.SLOTS_REWARD, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
    //扣钱
    this.player.deductMoney(commonEnum.PLAY_INFO.gold, money);
    //滚屏
    let title = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.SLOTS_REWARD].Title;
    if(!!ItemName && ItemName !== "")
    {
        let msg = "恭喜" + "<font color=0x83db43>" + this.player.name + "</font>" + "在" + title + "中获得" + "<font color=0x83db43>" + ItemName + "</font>";
        var sendMsg = {
            senderName: "系统",
            type: commonEnum.CHAT_TYPE.HORN,// -1系统 0普通 1物品超链接 2球员超链接 3喇叭
            msg: msg,
            channel: commonEnum.CHAT_CHANNEL.SYSTEM_CHANNEL,
        };
        this.player.updateScrolling(sendMsg);
    }

    this.globalActMgrInfo.set(actId, dbActRecord);
    data.itemList = itemIdList;//记录本次获得物品
    //做记录  记录每人拉霸次数和时间 个人获奖 保底触发次数 中奖组2次数
    this.historyActListAdd(actId, actType, data);

    ret.code = Code.OK;
    ret.itemIdList = itemIdList;
    return {ret: ret, money: money, periods: periods, securityMoney: securityMoney};
};
//拉霸得到权重组
Act.prototype.randomSlots = function(actId, data, securityMoney) {
    let result = {groupId: 1, securityMoney: securityMoney};
    let dbActRecord = this.globalActMgrInfo.get(actId);
    let guaranteeNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.SLOTS_GUARANTEE_NUM].Param;//保底次数
    let clearMoney = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.SLOTS_GUARANTEE_MONEY].Param;//拉霸获得保底金额
    if(dbActRecord.param2 >= guaranteeNum)
    {
        dbActRecord.param2 = 0;
        result.groupId = 3;
        result.securityMoney = securityMoney;
        data.security += 1;//记录触发保底次数增加
        return result
    }
    if(securityMoney >= clearMoney && dbActRecord.param2 > (guaranteeNum / 3) && (dbActRecord.param2 < (guaranteeNum - guaranteeNum / 3)))
    {
        result.securityMoney = 0;
        result.groupId = 3;
        data.security += 1;//记录触发保底次数增加
        return result
    }

    let Control = dataApi.allData.data["PullerAward"];
    let map = new Map();
    let groupList = [];//{groupId, weight}
    let sum = 0;
    for(let i in Control)
    {
        if(Control[i].Group === 3)
            continue;
        let typeId = Control[i].Group;
        let weight = Control[i].Weight;
       if(!map.has(typeId))
       {
           map.set(typeId, weight);
       }
    }

    for(let [k,v] of map)
    {
        for(let i = 0; i < v; i++)
        {
            groupList.push(k);
        }
        sum += v;
    }
    groupList.sort(function(){ return 0.5 - Math.random() });
    result.groupId = groupList[Math.floor(Math.random() * sum)];
    result.securityMoney = securityMoney;
    if(result.groupId === 2)
        data.winning += 1;//记录中奖2组次数增加

    return result;
};
//得到拉霸记录的数据
Act.prototype.getSlotsHistoryActData = function(actId, actType)
{
    let data = {};
    if(this.historyActList.has(actId))
    {
        let list = this.historyActList.get(actId);
        return list[0];
    }
    else
    {
        data.actType = actType;
        data.buyNum = 0;//个人抽奖的总次数
        data.security = 0;//触发保底次数
        data.winning = 0;//中奖组2次数
        data.time = Date.now();//时间
        data.itemList = [];//获得物品
        return data;
    }
};
Act.prototype._checkRewardById = function (rewardList, rewardId) {
    if (rewardList.length < 1) {
        return -1;
    }
    for (let i in rewardList) {
        if (rewardList[i].itemId === rewardId) {
            return i;
        }
    }
    return -1;
};
//获取奖励信息
Act.prototype.getLotteryPrizeInfo = function (actType)
{
    let config = dataApi.allData.data["ActiveControl"];
    let actId = 0;
    let openList = [];

    //获取活动数据
    // let actType = commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SLOTS;
    for(let i in config)
    {
        if(config[i].ActivityType === actType)
        {
            openList.push({startTime: config[i].StartTime, endTime: config[i].EndTime});

            actId = config[i].Id;
        }
    }

    //得到玩家的活动数据
    let dbActRecord = this.globalActMgrInfo.get(actId);
    if(dbActRecord && dbActRecord.itemList)
    {
        let itemIdList = [];
        for(let i in dbActRecord.itemList)
        {
            itemIdList.push({ItemId: dbActRecord.itemList[i].itemId, Num: dbActRecord.itemList[i].num});
        }
        return itemIdList;
    }
    else
    {
        return [];
    }
};
//获取奖励
Act.prototype.getLotteryPrizeReward = function (actType)
{
    let config = dataApi.allData.data["ActiveControl"];
    let actId = 0;
    let openList = [];

    //获取活动数据
    // let actType = commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SLOTS;
    for(let i in config)
    {
        if(config[i].ActivityType === actType)
        {
            openList.push({startTime: config[i].StartTime, endTime: config[i].EndTime});

            actId = config[i].Id;
        }
    }
    //得到玩家的活动数据
    let dbActRecord = this.globalActMgrInfo.get(actId);
    if(!dbActRecord || !dbActRecord.itemList)
    {
        return Code.REWARD_FAIL;
    }
    let mailType;
    if(actType === commonEnum.ACT_COMMON_TYPE.ACT_TYPE_SLOTS)
    {
        mailType = commonEnum.MAIL_TRANSLATE_CONTENT.SLOTS_REWARD;
    }
    else
    {
        return Code.FAIL;
    }
    //发邮件
    let specialAttachInfo = {
        roomUid: ""
    };
    let itemList = dbActRecord.itemList;
    let rewardList = [];
    for(let i in itemList)
    {
        let temp = {
            ItemType: commonEnum.MAIL_ITEM_TYPE.ITEM,
            ResId: itemList[i].itemId,
            Num: itemList[i].num,
        };
        rewardList.push(temp);
    }
    let code = Code.OK;
    code = this.player.email.sendMailReward("Sys", mailType, commonEnum.MailType.SYSMAIL, rewardList, specialAttachInfo, "", "", "", "");
    dbActRecord.itemList = [];
    return code;
};