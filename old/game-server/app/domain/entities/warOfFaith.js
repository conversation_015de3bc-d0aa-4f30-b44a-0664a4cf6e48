/**
 * Created by sea on 2020/4/10.
 */
let logger = require('pomelo-logger').getLogger(__filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let utils = require('../../util/utils');
let timeUtils = require("../../util/timeUtils");
let dataApi = require('../../util/dataApi');
let commonEnum = require('../../../../shared/enum');

let WarOfFaith = function (beliefId) {
    this.beliefId = beliefId;             //信仰id
    this.beliefTeam = [];                 //当前信仰参赛选手
    this.beforeBattleTeam = {};           //上一轮比赛
    this.nextBattleTeam = {};             //下一轮比赛
    this.attackerList = [];               //被谁攻击   攻击者信仰id [{beliefId, attackTime}]
    this.battleInfoMap = new Map();       //比赛录像  key:1，2，3   value： [{battleInfo}];
    this.holderId = 0;                    //当前信仰占领者
    this.group = 1;                       //第几轮
    this.isFinish = [],                   //当前轮是否已打完  0未开始  1一开始  2已结束
    this.battleTeam = new Map();          //对阵信息  key:1,2,3 value: {attackTeam:{beliefId: 0, team: []}, defendTeam: {beliefId: 0, team: []}};
    this.isChallenge = 0;                 //是否被攻打或者攻打别人
    this.attackNum = 0;                   //挑战次数 (挑战别的信仰)
    this.allPlayerStrength = new Map();   //当前信仰俱乐部报名得实力值   //锁阵容时候锁定实力值
    this.challengeList = [];              //挑战列表 {atkId:, defId:}
    this.notifyList = new Map();          //报名或者放弃列表
    this.getRewardList = new Map();       //领取奖励列表
    this.atkNum = 0;   //被占领
    this.defNum = 0;   //成功防守
    this.level = 1;    //信仰等级    用于发奖
};

util.inherits(WarOfFaith, EventEmitter);

module.exports = WarOfFaith;

WarOfFaith.prototype.initByDB = function(doc) {
    this.beliefId = doc.beliefId;                              //信仰id
    this.beliefTeam = doc.beliefTeam || [];                    //当前信仰参赛选手
    this.beforeBattleTeam = doc.beforeBattleTeam || {};        //上一轮比赛
    this.nextBattleTeam = doc.nextBattleTeam || {};            //下一轮比赛
    this.attackerList = doc.attackerList || [];                //被谁攻击   攻击者信仰id [{beliefId, attackTime}]
    this.battleInfoMap = this._toMap(doc.battleInfoMap || []);       //比赛录像  key:1，2，3   value： [{battleInfo}];
    this.holderId = doc.holderId || 0;                   //当前信仰占领者
    this.group = doc.group || 1;                         //第几轮
    this.isFinish = doc.isFinish || [],               //当轮是否全部比赛都打完了
    this.battleTeam = this._toMap(doc.battleTeam || []);         //对阵信息  key:1,2,3 value: {attackTeam:{beliefId: 0, team: []}, defendTeam: {beliefId: 0, team: []}};
    this.attackNum = doc.attackNum || 0;                  //挑战次数 (挑战别的信仰)
    this.allPlayerStrength = this.allPlayerStrengthToMap(doc.allPlayerStrength || []);   //当前信仰俱乐部报名得实力值   //锁阵容时候锁定实力值
    this.challengeList = doc.challengeList || [];
    this.notifyList = this.notifyListToMap(doc.notifyList || []);
    this.isChallenge = doc.isChallenge || 0;
    this.getRewardList = this.getRewardListToMap(doc.getRewardList || []);
    this.atkNum = doc.atkNum || 0;   //被占领
    this.defNum = doc.defNum || 0;   //成功防守
    this.level = doc.level || 1;
}

WarOfFaith.prototype.toJSONforDB = function() {
    let belief = {
        beliefId: this.beliefId,
        beliefTeam: this.beliefTeam,
        beforeBattleTeam: this.beforeBattleTeam,
        nextBattleTeam: this.nextBattleTeam,
        attackerList: this.attackerList,
        holderId: this.holderId,
        group: this.group,
        isFinish: this.isFinish,
        attackNum: this.attackNum,
        battleInfoMap: this._toArray(this.battleInfoMap),
        battleTeam: this._toArray(this.battleTeam),
        allPlayerStrength: this.allPlayerStrengthToArray(this.allPlayerStrength),
        challengeList: this.challengeList,
        notifyList: this.notifyListToArray(this.notifyList),
        isChallenge: this.isChallenge,
        getRewardList: this.getRewardListToArray(this.getRewardList),
        atkNum: this.atkNum,
        defNum: this.defNum,
        level: this.level
    }

    return belief;
}

WarOfFaith.prototype.getRewardListToMap = function(arr) {
    let map = new Map();
    if(arr.length < 1) {
        return map;
    }

    for(let i = 0; i < arr.length; ++i) {
        map.set(arr[i], arr[i]);
    }
    return map;
}

WarOfFaith.prototype.getRewardListToArray = function(map) {
    let arr = [];
    if(!map) {
        return arr;
    }

    for(let [k, v] of map) {
        arr.push(v);
    }
    return arr;
}

WarOfFaith.prototype.allPlayerStrengthToArray = function(map) {
    let arr = [];
    if(!map) {
        return arr;
    }

    for(let [k, v] of map) {
        arr.push(v);
    }
    return arr;
}

WarOfFaith.prototype.allPlayerStrengthToMap = function(arr) {
    let map = new Map();
    if(arr.length < 1) {
        return map;
    }

    for(let i = 0; i < arr.length; ++i) {
        map.set(arr[i].playerId, arr[i]);
    }
    return map;
}

WarOfFaith.prototype.notifyListToMap = function(arr) {
    let map = new Map();
    if(arr.length < 1) {
        return map;
    }

    for(let i = 0; i < arr.length; ++i) {
        map.set(arr[i], arr[i]);
    }
    return map;
}

WarOfFaith.prototype.notifyListToArray = function(map) {
    let arr = [];
    if(!map) {
        return arr;
    }

    for(let [k, v] of map) {
        arr.push(v);
    }
    return arr;
}

WarOfFaith.prototype._toArray = function(map) {
    let arr = [];
    if(!map) {
        return arr;
    }

    for(let [k, v] of map) {
        arr.push(v);
    }
    return arr;
}

WarOfFaith.prototype._toMap = function(arr) {
    let map = new Map();
    if(arr.length < 1) {
       return map;
    }

    for(let i = 0; i < arr.length; ++i) {
        let id = i + 1;
        map.set(id, arr[i]);
    }
    return map;
}


/**
 * 新增挑战者
 * @param beliefId
 * @returns {number}
 */
WarOfFaith.prototype.addAttacker = function (beliefId) {
    let time = timeUtils.now();
    let info = {
        beliefId: beliefId,
        attackTime: time
    }
    let isHave = false;
    for(let i in this.attackerList) {
        if(beliefId === this.attackerList[i].beliefId) {
            isHave = true;
            break;
        }
    }
    if(!isHave) {
        this.attackerList.push(info);
    }
}

/**
 * 获取挑战数量
 * @param beliefId
 * @returns {number}
 */
WarOfFaith.prototype.getAttackerNum = function () {
    return this.attackerList.length;
}

/**
 * 添加参赛人员
 * @param beliefId
 * @param playerId
 * @param gid
 * @param name
 * @param faceUrl
 * @param strength
 */
WarOfFaith.prototype.addBeliefTeamPlayer = function (beliefId, playerId, gid, name, faceUrl, strength) {
    let isHave = false;
    for(let i = 0; i < this.beliefTeam.length; ++i) {
        if(this.beliefTeam[i].playerId === playerId) {
            isHave = true;
            break;
        }
    }

    //不存在直接添加
    if(!isHave) {
        let playerInfo = {
            beliefId: beliefId,
            playerId: playerId,
            strength: strength,
            gid: gid,
            faceUrl: faceUrl,
            playerName: name,
            battleNum: 0,        //比赛场数
            inspireNum: 0,       //别人鼓舞次数
            myInspireNum: 0,     //自己鼓舞次数
        }
        this.beliefTeam.push(playerInfo);
    }
}

/**
 * 检查是否报名
 * @param playerId
 * @returns {boolean}
 */
WarOfFaith.prototype.checkPlayerIsEnroll = function (playerId) {
    let isEnroll = false;
    for(let i in this.beliefTeam) {
        if(this.beliefTeam[i].playerId === playerId) {
            isEnroll = true;
            break;
        }
    }
    return isEnroll;
}

/**
 * 获取参赛选手
 * @returns {{outList: Array, selected: Array}}
 */
WarOfFaith.prototype.getBeliefTeamPlayer = function () {
    let ret = {
        selected: [],
        outList: []
    };
    //排序
    this.beliefTeam.sort(this.comp_func("strength"));
    //实力前50入选
    ret.selected = this.beliefTeam.slice(0, 50);
    ret.outList = this.beliefTeam.slice(50);
    return ret;
}

//从大到小排序
WarOfFaith.prototype.comp_func = function(param) {
    return (obj1, obj2) => {
        let val1 = obj1[param];
        let val2 = obj2[param];
        if(val1 !== val2) {
            if(val1 < val2) {
                return 1;
            } else if(val1 > val2) {
                return -1;
            }
        }
        return 0;
    }
}
//对阵信息  key:1,2,3 value: {attackTeam:{beliefId: 0, team: [], attackNum: 0, defendNum: 0}, defendTeam: {beliefId: 0, team: [], attackNum: 0, defendNum: 0}};
WarOfFaith.prototype.setBattleTeam = function (index, attackTeam, defendTeam) {
    if(!this.battleTeam.has(index)) {
        let obj = {
            attackBeliefId: attackTeam.beliefId,
            attackTeam: attackTeam.team,
            defendBeliefId: defendTeam.beliefId,
            defendTeam: defendTeam.team,
            attackNum: attackTeam.team.length,
            defendNum: defendTeam.team.length
        }
        this.battleTeam.set(index, obj);
    }else {
        let belief = this.battleTeam.get(index);
        if(!belief) {
            return;
        }
        belief.attackTeam = attackTeam.team;
        belief.defendTeam = defendTeam.team;
    }
}

/**
 * 获取所有对阵信息
 */
WarOfFaith.prototype.getAllBattleTeam = function () {
    let allBattleInfo = [];
    let index = 0;
    for(let [k, v] of this.battleTeam) {
        allBattleInfo[index] = {};
        allBattleInfo[index].group = k;
        allBattleInfo[index].attackBeliefId = v.attackBeliefId;
        allBattleInfo[index].attackTeam = v. attackTeam;
        allBattleInfo[index].defendBeliefId = v.defendBeliefId;
        allBattleInfo[index].defendTeam = v.defendTeam;
        allBattleInfo[index].attackNum = v.attackNum;
        allBattleInfo[index].defendNum = v.defendNum;
        index ++;
    }
    return allBattleInfo;
}

/**
 * 获取当前对阵人数
 */
WarOfFaith.prototype.getNowBattleTeamPlayerNum = function () {
    let ret = {attackNum: 0, defendNum: 0};
    let battleTeam = this.battleTeam.get(this.group);
    if(!battleTeam) {
        return ret;
    }
    ret.attackNum = battleTeam.attackNum;
    ret.defendNum = battleTeam.defendNum;
    return ret;
}

/**
 * 失败者人数扣除
 * @param index
 * @param beliefId
 */
WarOfFaith.prototype.deleteBattleTeamCount = function (index, beliefId) {
    let battleTeam = this.battleTeam.get(index);
    if(!battleTeam) {
        return;
    }

    if(battleTeam.attackBeliefId === beliefId) {
        if(battleTeam.attackNum > 0) {
            battleTeam.attackNum --;
        }
    }else if(battleTeam.defendBeliefId === beliefId){
        if(battleTeam.defendNum > 0) {
            battleTeam.defendNum --;
        }
    }
}

/**
 * 设置上一轮比赛数据
 * @param team
 */
WarOfFaith.prototype.setBeforeBattleTeam = function (team) {
    this.beforeBattleTeam = team;
}

/**设置下一轮比赛数据
 *
 * @param team
 */
WarOfFaith.prototype.setNextBattleTeam = function (team) {
    this.nextBattleTeam = team;
}

/**
 * 获取下一轮比赛数据
 *
 * @param team
 */
WarOfFaith.prototype.getNextBattleTeam = function () {
    return this.nextBattleTeam;
}

/**
 * 获取上一轮比赛数据
 *
 * @param team
 */
WarOfFaith.prototype.getBeforeBattleTeam = function () {
    return this.beforeBattleTeam;
}

/**
 * 增加战斗场数
 * @param index
 * @param beliefId
 * @param battlerId
 */
WarOfFaith.prototype.addBattleNum = function (index, beliefId, battlerId) {
    let battleTeam = this.battleTeam.get(index);
    if(!battleTeam) {
        return {};
    }

    let idx = -1;
    if(battleTeam.attackBeliefId === beliefId) {
        for(let i = 0; i < battleTeam.attackTeam.length; ++i) {
            if(battlerId === battleTeam.attackTeam[i].playerId) {
                idx = i;
                break;
            }
        }
        if(idx === -1) {
            return;
        }
        battleTeam.attackTeam[idx].battleNum ++;
    }else if(battleTeam.defendBeliefId === beliefId){
        for(let i = 0; i < battleTeam.defendTeam.length; ++i) {
            if(battlerId === battleTeam.defendTeam[i].playerId) {
                idx = i;
                break;
            }
        }
        if(idx === -1) {
            return;
        }
        battleTeam.defendTeam[idx].battleNum ++;
    }
}

WarOfFaith.prototype.checkBattleNum = function (index, beliefId, battlerId) {
    let battleTeam = this.battleTeam.get(index);
    if(!battleTeam) {
        return 0;
    }

    let idx = -1;
    let battleNum = 0;
    if(battleTeam.attackBeliefId === beliefId) {
        for(let i = 0; i < battleTeam.attackTeam.length; ++i) {
            if(battlerId === battleTeam.attackTeam[i].playerId) {
                idx = i;
                break;
            }
        }
        if(idx === -1) {
            return battleNum;
        }
        battleNum = battleTeam.attackTeam[idx].battleNum;
    }else if(battleTeam.defendBeliefId === beliefId){
        for(let i = 0; i < battleTeam.defendTeam.length; ++i) {
            if(battlerId === battleTeam.defendTeam[i].playerId) {
                idx = i;
                break;
            }
        }
        if(idx === -1) {
            return battleNum;
        }
        battleNum = battleTeam.defendTeam[idx].battleNum;
    }
    return battleNum;
}

/**获取下一轮对手
 *
 * @param index
 * @param beliefId
 * @param battlerId
 */
WarOfFaith.prototype.getBattleTeam = function (index, beliefId, battlerId) {
    let battleTeam = this.battleTeam.get(index);
    if(!battleTeam) {
        return {};
    }


    let team = [];
    if(battleTeam.attackBeliefId === beliefId) {
        team = battleTeam.attackTeam;
    }else if(battleTeam.defendBeliefId === beliefId){
        team = battleTeam.defendTeam;
    }
    let idx = -1;
    for(let i = 0; i < team.length; ++i) {
        if(battlerId === team[i].playerId) {
            idx = i;
            break;
        }
    }

    let nextId = idx + 1;
    //说明这一轮已经打完了
    if(idx === -1 || nextId === team.length) {
        return {};
    }

    let info = {
        id: beliefId,
        playerId: team[nextId].playerId,
        playerName: team[nextId].playerName,
        gid: team[nextId].gid,
        faceUrl: team[nextId].faceUrl,
        inspireNum: team[nextId].inspireNum,
    }
    return info;
}

/**
 * 设置玩家阵容实力
 * @param playerId
 * @param value
 */
WarOfFaith.prototype.setPlayerStrength = function (playerId, value) {
    if(!this.allPlayerStrength.has(playerId)) {
        let info = {
            playerId: playerId,
            strength: value
        }
        this.allPlayerStrength.set(playerId, info);
    }else {
        let player = this.allPlayerStrength.get(playerId);
        player.strength = value;
    }
}

/**
 * 获取玩家阵容实力
 * @param playerId
 */
WarOfFaith.prototype.getPlayerStrength = function (playerId) {
    if(this.allPlayerStrength.has(playerId)) {
       return this.allPlayerStrength.get(playerId).strength;
    }
    return 0;
}

/**
 * 新增比赛录像
 * @param group
 * @param obj
 */
WarOfFaith.prototype.addBattleInfo = function (group, obj) {
    if(!this.battleInfoMap.has(group)) {
        let arr = new Array(obj);
        this.battleInfoMap.set(group, arr);
    }else {
        let battle = this.battleInfoMap.get(group);
        battle.push(obj);
    }
}

/**
 * 获取比赛录像
 * @param group
 * @param obj
 */
WarOfFaith.prototype.getBattleResultInfo = function (group) {
    let battleInfo = this.battleInfoMap.get(group);
    if(!battleInfo) {
        return [];
    }
    return battleInfo;
}

WarOfFaith.prototype.addRound = function () {
    this.round ++;
}

WarOfFaith.prototype.setMatchFinishStatus = function (index, status) {
    if(index >= this.isFinish.length) {
        return;
    }
    this.isFinish[index] = status;
}

WarOfFaith.prototype.getMatchFinishStatus = function (index) {
    if(typeof index === "undefined") {
        return this.isFinish;
    }
    if(index >= this.isFinish.length) {
        return 0
    }
    return this.isFinish[index];
}

/**
 * 占领者
 * @param holderId
 */
WarOfFaith.prototype.setHolder = function (holderId) {
    this.holderId = holderId;
}

/**
 * 设置组
 * @param holderId
 */
WarOfFaith.prototype.setGroup = function (group) {
    this.group = group;
}

WarOfFaith.prototype.getBattleTeamByGroup = function (group) {
    let battle = this.battleTeam.get(group);
    if(!battle) {
        return {};
    }
    return battle;
}

WarOfFaith.prototype.getNextAttacker = function (beliefId) {
    let index = -1;
    for(let i = 0; i < this.attackerList.length; ++i) {
        if(beliefId === this.attackerList[i].beliefId) {
            index = i + 1;
            break;
        }
    }

    //说明当前挑战者是最后一个挑战者了
    if(index >= this.attackerList.length || index === -1) {
        return -1;
    }
    return this.attackerList[index].beliefId;
}

//新增通知过的人员
WarOfFaith.prototype.addNotify = function (playerId) {
    if(!this.notifyList.has(playerId)) {
        this.notifyList.set(playerId, playerId);
    }
}

//是否已经通知过了
WarOfFaith.prototype.checkIsNotify = function (playerId) {
    return this.notifyList.has(playerId);
}

/**
 * 初始化下一轮对阵数据
 * @param index
 * @param attackTeam
 * @param defendTeam
 * @param beginBattleTime
 */
WarOfFaith.prototype.initNextBattleTeam = function (index, attackTeam, defendTeam) {
    let belief = this.battleTeam.get(index);
    if(!belief) {
        return;
    }
    belief.attackBeliefId = attackTeam.beliefId;
    belief.defendBeliefId = defendTeam.beliefId;
    belief.attackTeam = attackTeam.team;
    belief.defendTeam = defendTeam.team;
    belief.attackNum = attackTeam.team.length;
    belief.defendNum = defendTeam.team.length;
}

WarOfFaith.prototype.makeClientMessage = function () {
    let beliefInfo = {};
    beliefInfo.beliefId = this.beliefId;
    // let attackerList = this.attackerList;
    // for(let i in this.attackerList) {
    //     attackerList.push(this.attackerList[i].beliefId);
    // }
    beliefInfo.attackerList = this.attackerList;
    beliefInfo.holderId = this.holderId;
    beliefInfo.atkNum = this.atkNum;
    beliefInfo.defNum = this.defNum;
    return beliefInfo;
}

//新增挑战者
WarOfFaith.prototype.addChallenger = function(atkId, defId) {
    let time = timeUtils.now();
    let info = {
        atkId: atkId,
        defId: defId,
        time: time
    }
    let isHave = false;
    for(let i in this.challengeList) {
        if(atkId === this.challengeList[i].atkId && defId === this.challengeList[i].defId) {
            isHave = true;
            break;
        }
    }
    if(!isHave) {
        this.challengeList.push(info);
    }
}

//清除数据
WarOfFaith.prototype.clearData = function() {
    this.beliefTeam = [];                 //当前信仰参赛选手
    this.beforeBattleTeam = {};           //上一轮比赛
    this.nextBattleTeam = {};             //下一轮比赛
    this.attackerList = [];               //被谁攻击   攻击者信仰id [{beliefId, attackTime}]
    this.battleInfoMap.clear();           //比赛录像  key:1，2，3   value： [{battleInfo}];
    this.holderId = 0;                    //当前信仰占领者
    this.group = 1;                       //第几轮
    this.isFinish = [],                   //当前轮是否已打完  0未开始  1一开始  2已结束
    this.battleTeam.clear();              //对阵信息  key:1,2,3 value: {attackTeam:{beliefId: 0, team: []}, defendTeam: {beliefId: 0, team: []}};
    this.isChallenge = 0;                 //是否被攻打或者攻打别人
    this.attackNum = 0;                   //挑战次数 (挑战别的信仰)
    this.allPlayerStrength.clear();       //当前信仰俱乐部报名得实力值   //锁阵容时候锁定实力值
    this.challengeList = [];              //挑战列表 {atkId:, defId:}
    this.notifyList.clear();              //报名或者放弃列表
    this.getRewardList.clear();           //清空领奖
}

//异常数据清理
WarOfFaith.prototype.clearDataToFix = function() {
    // this.beliefTeam = [];                 //当前信仰参赛选手
    for(let i = 0; i < this.isFinish.length; i++) {
        this.isFinish[i] = 0;
    }
    this.beforeBattleTeam = {};           //上一轮比赛
    this.nextBattleTeam = {};             //下一轮比赛
    this.battleInfoMap.clear();           //比赛录像  key:1，2，3   value： [{battleInfo}];
    this.holderId = 0;                    //当前信仰占领者
    this.group = 1;                       //第几轮
    this.battleTeam.clear();              //对阵信息  key:1,2,3 value: {attackTeam:{beliefId: 0, team: []}, defendTeam: {beliefId: 0, team: []}};
    this.atkNum = 0;
    this.defNum = 0;
}


//新增领奖记录
WarOfFaith.prototype.addGetRewardPLayer = function (playerId) {
    if(!this.getRewardList.has(playerId)) {
        this.getRewardList.set(playerId, playerId);
    }
}

//是否已经领取过奖励
WarOfFaith.prototype.checkIsGetReward = function (playerId) {
    return this.getRewardList.has(playerId);
}

WarOfFaith.prototype.setLevel = function (level) {
    this.level = level;
}

WarOfFaith.prototype.getLevel = function () {
    return this.level;
}