var logger = require("pomelo-logger").getLogger('pomelo', __filename);
var EventEmitter = require("events").EventEmitter;
var util = require("util");
var dataApi = require("../../util/dataApi");
var TimeUtils = require("../../util/timeUtils");
var commonEnum = require('../../../../shared/enum');

var Follow = function(player) 
{      
    this.player = player;
    this.uid = player.playerId; //玩家UID
    this.followCache = new Map(); //关注数 //playerId => followTime giveEnergy giveTime
    this.fansCache = new Map();   //粉丝数 //playerId => followTime giveEnergy giveTime
    this.giveEnergyNum = 0;//今日赠送的次数
    this.getEnergyNum = 0;//今日领取的次数
    this.refreshTime = 0;//刷新次数 用这个时间来判断
    this.cancelList = [];//保存今天取关的玩家避免重复送精力
    this.giveEnergyMAX = 30;//今日赠送的次数上限
    this.getEnergyMAX = 30;//今日领取的次数上限
};

util.inherits(Follow, EventEmitter);
module.exports = Follow;

Follow.prototype.test = function()
{
    
};

Follow.prototype.initByConfig = function()
{

};

Follow.prototype.initByDB = function(doc) {
	this.uid = doc.uid;
    this.followCache = this.toMap(doc.followCache) || new Map();
    this.fansCache = this.toMap(doc.fansCache) || new Map();
    this.giveEnergyNum = doc.giveEnergyNum || 0;//今日赠送的次数
    this.getEnergyNum = doc.getEnergyNum || 0;//今日领取的次数
    this.refreshTime = doc.refreshTime || 0;
    this.cancelList = doc.cancelList || [];
};

Follow.prototype.toJSONforDB = function() {
	var doc = {
        uid: this.uid,
        followCache: this.toArray(this.followCache),
        fansCache: this.toArray(this.fansCache),
        giveEnergyNum: this.giveEnergyNum,
        getEnergyNum: this.getEnergyNum,
        refreshTime: this.refreshTime,
        cancelList: this.cancelList
	};
	return doc;
};

Follow.prototype.toMap = function(arr) {
    var map =  new Map();
    if (!arr)
    {
        return map;
    }

    for (var i in arr)
    {
        let object = arr[i];
        //logger.info("to map", object);
        var uid = object.uid;
        var giveEnergy = 0;
        var giveTime = 0;
        if(!!object.giveEnergy)
        {
            giveEnergy = object.giveEnergy;
        }
        if(!!object.giveTime)
        {
            giveTime = object.giveTime;
        }
        var followTime = object.followTime;
        let tmp = {
            followTime: followTime,
            giveEnergy: giveEnergy,
            giveTime: giveTime,
        }
        map.set(uid, tmp);
    }

    return map;
  };
  
  Follow.prototype.toArray = function(map) {
    var arr = [];
    if (!map)
    {
        return arr;
    }

    for (const [k, v] of map)
    {
        let obj = {
            uid: k,
            followTime: v.followTime,
            giveEnergy: v.giveEnergy,
            giveTime: v.giveTime,
        }
        arr.push(obj);
    }

    return arr;
};

Follow.prototype.follow = function(uid)
{
    let followTime = TimeUtils.now();
    let data = {
        followTime: followTime,
        giveEnergy: 0,      //赠送的精力     自己送给该对象的记录
        giveTime: 0         //赠送的时间
    };
    this.followCache.set(uid, data);
    this.pFollow();
    //触发任务
    this.player.tasks.triggerTask(commonEnum.TARGET_TYPE.THIRTY);
};

Follow.prototype.unFollow = function(uid)
{
    if (this.followCache.has(uid))
    {
        this.cancelList.push(uid);
        this.followCache.delete(uid);
    }
    //logger.error("取关之后~！！！！！！！！！", this.cancelList);
    this.pFollow();
};

Follow.prototype.addFan = function(uid)
{
    let followTime = TimeUtils.now();
    let data = {
        followTime: followTime,
        giveEnergy: 0,      //赠送的精力     该对象送给自己的记录
        giveTime: 0         //赠送的时间
    };
    this.fansCache.set(uid, data);
    logger.info("addFan uid, followTime", uid, followTime);
    this.pFans();
};

Follow.prototype.delFan = function(uid)
{
    if (this.fansCache.has(uid))
    {
        this.fansCache.delete(uid);
    }

    this.pFans();
};   

Follow.prototype.getFollowTime = function(uid)
{
    if (this.followCache.has(uid))
    {
        return this.followCache.get(uid).followTime;
    }
    return 0;
};

Follow.prototype.getFansTime = function(uid)
{
    if (this.fansCache.has(uid))
    {
        return this.fansCache.get(uid).followTime;
    }
    return 0;
};
Follow.prototype.getFollowGiveEnergy = function(uid)
{
    if (this.followCache.has(uid))
    {
        return this.followCache.get(uid).giveEnergy;
    }
    return 0;
};

Follow.prototype.getFollowGiveTime = function(uid)
{
    if (this.followCache.has(uid))
    {
        return this.followCache.get(uid).giveTime;
    }
    return 0;
};

Follow.prototype.getFansGiveEnergy = function(uid)
{
    if (this.fansCache.has(uid))
    {
        return this.fansCache.get(uid).giveEnergy;
    }
    return 0;
};

Follow.prototype.getFansGiveTime = function(uid)
{
    if (this.fansCache.has(uid))
    {
        return this.fansCache.get(uid).giveTime;
    }
    return 0;
};
//领取粉丝赠送的精力
Follow.prototype.getFollowEnergy = function(uid)
{
    let player = this.player;
    if(this.getEnergyNum >= this.getEnergyMAX)//已达今日领取上限
    {
        //logger.error("已达今日领取上限", this.getEnergyNum);
         return false;
    }
    if (!this.fansCache.has(uid))
    {
        //logger.error("粉丝里没有该玩家", uid);
        return false;
    }
    let tmp = this.fansCache.get(uid);
    if(tmp.giveEnergy > 0 && !this.isToDaySevenHours(tmp.giveTime))
    {
        player.addResource(commonEnum.PLAY_INFO.energy, tmp.giveEnergy);//增加1点精力
        player.upPlayerInfo([{type: commonEnum.PLAY_INFO.energy, value: player.energy}]);
        tmp.giveEnergy = 0;
        // tmp.giveTime = 0;
        this.fansCache.set(uid, tmp);
        this.refreshNumTime();
        player.follow.getEnergyNum += 1;
        return true;
    }
    return false;
};
//检查可领取精力是否过期    每天早上7：00
Follow.prototype.getEnergyIsPast = function()
{
    for(let [k, v] of this.followCache)
    {
        // if((v.giveTime!== 0 && !TimeUtils.isToday(v.giveTime)) && TimeUtils.isToday(TimeUtils.now() - 1000 * 60 * 60 * 7))//不是今天重置
        if((v.giveTime!== 0 && this.isToDaySevenHours(v.giveTime)))//不是今天重置
        {
            v.giveEnergy = 0;
            v.giveTime = 0;
        }
    }
    for(let [k, v] of this.fansCache)
    {
        // if((v.giveTime!== 0 && !TimeUtils.isToday(v.giveTime)) && TimeUtils.isToday(TimeUtils.now() - 1000 * 60 * 60 * 7))//不是今天重置
        if((v.giveTime!== 0 && this.isToDaySevenHours(v.giveTime)))//不是今天重置
        {
            v.giveEnergy = 0;
            v.giveTime = 0;
        }
    }
};
//刷新次数时间
Follow.prototype.refreshNumTime = function()
{
    let date = new Date();
    date.setHours(7, 0, 0, 0);
    if(this.refreshTime === 0)
    {
        let time = date.getTime();
        if(TimeUtils.now() > date.getTime())
        {
            this.refreshTime = time;
        }
        else
        {
            this.refreshTime = date.getTime() - 1000 * 60 * 60 * 24;//昨天七点;
        }
    }
    if(this.isToDaySevenHours(this.refreshTime))
    {
        this.giveEnergyNum = 0;
        this.getEnergyNum = 0;
        this.cancelList = [];
        this.refreshTime = date.getTime();
    }
};
//是否第二天七点之后
Follow.prototype.isToDaySevenHours = function (time)
{
    if(time === 0)
    {
        time = this.getDaySevenHours();
    }
    //超过一天直接刷新
    if(TimeUtils.now() > time + 1000 * 60 * 60 * 24)
    {
        return true;
    }
    return false;
};
//获得记录时间
Follow.prototype.getDaySevenHours = function ()
{
    let date = new Date();
    date.setHours(7, 0, 0, 0);
    let time = date.getTime();
    if(TimeUtils.now() > date.getTime())
    {
        return time;
    }
    else {
        return date.getTime() - 1000 * 60 * 60 * 24;//昨天七点;
    }
};
function __follow_time_compare_func(rankObj1, rankObj2)
{
    let followTime1 = rankObj1.followTime;
    let followTime2 = rankObj2.followTime;
    if (followTime1 !== followTime2)
    {
        if (followTime1 < followTime2)
        {
            return 1;
        }else if(followTime1 > followTime2)
        {
            return -1;
        }
    }

    return 0;
}

Follow.prototype.getLastFollowList = function()
{
    let lastRecord = [];
    for(let [k,v] of this.followCache)
    {
        let obj = {
            uid: k, 
            followTime: v,
        }

        lastRecord.push(obj);
    }   

    lastRecord.sort(__follow_time_compare_func);
    return lastRecord;
};

Follow.prototype.getLastFansList = function()
{
    let lastRecord = [];
    for(let [k,v] of this.fansCache)
    {
        let obj = {
            uid: k, 
            followTime: v,
        }

        lastRecord.push(obj);
    }

    lastRecord.sort(__follow_time_compare_func);
    return lastRecord;
};

Follow.prototype.getLastFollowUidList = function()
{
    let lastRecord = [];
    for(let [k,v] of this.followCache)
    {
        lastRecord.push(k);
    }
    return lastRecord;
};

Follow.prototype.getLastFansUidList = function()
{
    let lastRecord = [];
    for(let [k,v] of this.fansCache)
    {
        lastRecord.push(k);
    }
    return lastRecord;
};

Follow.prototype.pFollow = function()
{
    for(let [k,v] of this.followCache)
    {
        logger.info("pFollow: k,v", k, v);
    }
};

Follow.prototype.pFans = function()
{
    //logger.info("pFans");
    for(let [k,v] of this.fansCache)
    {
        logger.info("pFans: k,v", k, v);
    }
};