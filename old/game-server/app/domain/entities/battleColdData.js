var logger = require("pomelo-logger").getLogger('pomelo', __filename);
var EventEmitter = require("events").EventEmitter;
var util = require("util");
var dataApi = require("../../util/dataApi");
var utils = require("../../util/utils");
var commonEnum = require("../../../../shared/enum");
var Calc = require("../../util/calc");
var Code = require("../../../../shared/code");
var Player = require("./player");
var Constant = require("../../../../shared/constant");
var TimeUtils = require("../../util/timeUtils");
var async = require("async");

var BattleColdData = function(league, app) {      
    this.app = app;
    this.league = league;
    this.battleColdDataMap = new Map(); //uid <=> battleData
    this.waitSendUidList = [];
};

util.inherits(BattleColdData, EventEmitter);

module.exports = BattleColdData;

BattleColdData.prototype.test = function()
{
};

BattleColdData.prototype.initByDB = function(doc) 
{

};

BattleColdData.prototype.setBattleColdData = function(playerUid, battleData)
{
    //logger.info("setBattleColdData", playerUid);
    this.battleColdDataMap.set(playerUid, battleData);
};

BattleColdData.prototype.getBattleColdData = function(playerUid)
{
    return this.battleColdDataMap.get(playerUid);
};

BattleColdData.prototype.delBattleColdData = function(playerUid)
{
    if (this.hasBattleColdData(playerUid))
    {
        this.battleColdDataMap.delete(playerUid);
    }
};

BattleColdData.prototype.hasBattleColdData = function(playerUid)
{
    return this.battleColdDataMap.has(playerUid);
};

BattleColdData.prototype.reset = function()
{
    this.restWaitUidList();
    this.battleColdDataMap.clear();
};

BattleColdData.prototype.addWaitUid = function(uid)
{
    if (utils.hasUidInList(this.waitSendUidList, uid))
    {
        logger.warn("addWaitUid: add uid repeat", uid);
    }else
    {
        this.waitSendUidList.push(uid);
    }
};

BattleColdData.prototype.restWaitUidList = function()
{
    this.waitSendUidList = [];
};

BattleColdData.prototype.getBattleDataToSvr = function(cb)
{
    let self = this;
    async.eachSeries(this.waitSendUidList, function(uid, callback) {
        self.sendGetLeagueBattleReq(uid, function(ret){
            //logger.info("getBattleDataToSvr: err, ret", uid, ret);
            if (!ret)
            {
                logger.error("getBattleDataToSvr: err, ret", ret, uid);
                callback("getBattleDataToSvr is null");
                return;
            }
            self.setBattleColdData(uid, ret.battleData);
            callback(null);
        });
    }, function(err) {
        if (!!err) 
        {
            logger.error("error", err);
            cb(err, Code.FAIL)
            return;
        }

        self.restWaitUidList();
        cb(null, Code.OK);
        return;
    });
};

BattleColdData.prototype.sendGetLeagueBattleReq = function(uid , cb)
{
	let self = this;
    let tmpSession = {frontendId: this.app.getServerId()};
    let msg = {};
    msg.playerId = uid;   
    msg.funcType = commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_GET_BATTLE_DATA;
    self.app.rpc.datanode.dataNodeRemote.getLeagueBattleData(tmpSession, msg, function(code, battleData) {
        if (code !== Code.OK)
        {
            cb(null);
            return;
        }

        cb(battleData);
        return;
    });
};

BattleColdData.prototype.getBattlePlayerActualStrength = function(uid)
{
    if (!uid)
    {
        logger.error("getBattlePlayerData: uid error!", uid);
        return 0;
    }
    if(!this.hasBattleColdData(uid))
    {
        console.trace();
        logger.error("hasBattleColdData: not found battleData", uid);
        return 0;
    }
    let battleData = this.getBattleColdData(uid);
    if (!battleData)
    {
        console.trace();
        logger.error("getBattleData: not found battleData", uid);
        return 0;
    }

    if (!battleData.teamFormations)
    {
        console.trace();
        logger.error("getBattleData: not found teamFormations", uid, battleData);
        //logger.error("getBattleData: not found teamFormations", uid, battleData.teamFormations);
        return 0;
    }

    let currTeamFormationId = battleData.teamFormations.currTeamFormationId;
    if (!currTeamFormationId)
    {
        console.trace();
        logger.error("getBattleData: not found currTeamFormationId", uid, currTeamFormationId, battleData);
        return 0;
    }
    
    let teamFormations = utils.toMap(battleData.teamFormations.teamFormations);
    let team = teamFormations.get(currTeamFormationId);
    if(!team)
    {
        logger.error("getBattlePlayerData: not found team!", uid);
        return 0;
    }
    
    let actualStrength = team.ActualStrength;
    return actualStrength;
};

BattleColdData.prototype.getBattlePlayerName = function(uid)
{
    if (!uid)
    {
        logger.error("getBattlePlayerData: uid error!", uid);
        return "";
    }

    let battleData = this.getBattleColdData(uid);
    if (!battleData)
    {
        console.trace();
        logger.error("getBattleData: not found battleData", uid);
        return "";
    }
    
    return battleData.teamName;
};

BattleColdData.prototype.getTeamTotalValue = function(uid)
{
    if (!uid)
    {
        logger.error("_getTeamTotalValue: uid error!", uid);
        return 0;
    }

    if (!this.hasBattleColdData(uid))
    {
        //logger.error("_getTeamTotalValue.hasBattleColdData: not found battleData", uid);
        return 0; 
    }

    let battleData = this.getBattleColdData(uid);
    if (!battleData)
    {
        //console.trace();
        //logger.error("_getTeamTotalValue.getBattleColdData: not found battleData", uid);
        return 0;
    }
    
    return battleData.totalValue;
};

