/**
 * Created by sea on 2019/9/03.
 */
let logger = require('pomelo-logger').getLogger(__filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let utils = require('../../util/utils');
let timeUtils = require("../../util/timeUtils");
let dataApi = require('../../util/dataApi');
let commonEnum = require('../../../../shared/enum');
let async = require('async');

let Belief = function (beliefId) {
    this.beliefId = beliefId;           //信仰id
    this.notice = "";                   //信仰公告
    this.playerList = [];               //信仰成员列表
    this.beliefRank = 0;                //信仰排名
    this.beliefGold = 0;                //信仰资金
    this.leader= [];                    //领导者
    this.notify = [];                   //信仰动态
    this.level = 1;                     //信仰等级
    this.beliefExp = 0;                 //信仰经验
    this.clearTime = timeUtils.now();   //清空当日球币捐赠
    this.todayGoldNum = 0;              //当日捐献球币数量
    this.weekExp = 0;                   //周贡献
};
  
util.inherits(Belief, EventEmitter);

module.exports = Belief;

Belief.prototype.initByDB = function(doc) {
    this.beliefId = doc.beliefId;
    this.notice = doc.notice || "";
    this.playerList = doc.playerList || [];
    this.beliefRank = doc.beliefRank || 0;
    this.beliefGold = doc.beliefGold || 0;
    this.leader = doc.leader || [];
    this.notify = doc.notify || [];
    this.level = doc.level || 1;
    this.beliefExp = doc.beliefExp || 0;
    this.todayGoldNum = doc.todayGoldNum || 0;
    this.weekExp = doc.weekExp || 0;
    this.clearTime = doc.clearTime || 0;
}

Belief.prototype.toJSONforDB = function() {
    let belief = {
        beliefId: this.beliefId,
        notice: this.notice,
        playerList: this.playerList,
        beliefRank: this.beliefRank,
        beliefGold: this.beliefGold,
        leader: this.leader,
        notify: this.notify,
        level: this.level,
        beliefExp: this.beliefExp,
        weekExp: this.weekExp,
        todayGoldNum: this.todayGoldNum,
        clearTime: this.clearTime
    }
    return belief;
}

/**
 * 信仰捐赠
 * @param type  捐赠类型  1欧元捐赠  2球币捐赠
 * @param num   次数
 */
Belief.prototype.donateBeliefGold = function(type, num) {
    let ret = {code: Code.OK, beliefGold: 0};
    let addNum = 0;
    if(type === 1) {
        if(typeof (dataApi.allData.data["SystemParam"][commonEnum.BELIEF.CASH_BELIEF_GOLD_NUM]) === "undefined") {
            addNum = 100 * num;
        }else {
            let configNum = dataApi.allData.data["SystemParam"][commonEnum.BELIEF.CASH_BELIEF_GOLD_NUM].Param;
            addNum = configNum * num;
        }
    }else {
        if(typeof (dataApi.allData.data["SystemParam"][commonEnum.BELIEF.GOLD_BELIEF_GOLD_NUM]) === "undefined") {
            addNum = 1000 * num;
        }else {
            addNum = dataApi.allData.data["SystemParam"][commonEnum.BELIEF.GOLD_BELIEF_GOLD_NUM].Param * num;
        }
    }
    this.beliefGold += addNum;
    ret.beliefGold = this.beliefGold;
    return ret;
}

/**
 * 修改信仰公告
 * @param str
 * @returns {number}
 */
Belief.prototype.modifyBeliefNotice = function(str) {
    this.notice = str;
}

Belief.prototype.setBeliefLeader = function (leader) {
    if(this.leader.length <= 0)
    {
        this.leader.push(leader);
        return;
    }
    let flag = false;
    for(let i in this.leader)
    {
        if(this.leader[i].pos === leader.pos)//pos: 1 董事长 2 副董事长 3 总经理 4 总监
        {
            this.leader[i].uid = leader.uid;
            this.leader[i].gid = leader.gid;
            this.leader[i].name = leader.name;
            this.leader[i].faceUrl = leader.faceUrl;
            flag = true;
        }
        // if(this.leader[i].uid === leader.uid && this.leader[i].pos > leader.pos)//pos: 1 董事长 2 副董事长 3 总经理 4 总监
        // {
        //     this.leader[i].uid = leader.uid;
        //     this.leader[i].gid = leader.gid;
        //     this.leader[i].name = leader.name;
        //     this.leader[i].faceUrl = leader.faceUrl;
        //     flag = true;
        // }
    }
    if(!flag)
    {
        this.leader.push(leader);
    }
    for(let i = 0; i < this.leader.length; i++)
    {
        //如果已有职位删除低的
        if(this.leader[i].uid === leader.uid && this.leader[i].pos > leader.pos)//pos: 1 董事长 2 副董事长 3 总经理 4 总监
        {
            this.leader.splice(i, 1);
        }
    }
};

Belief.prototype.getBeliefLeaderByPos = function (pos) {
    let ret = {name:"", uid:""};
    for(let i in this.leader) {
        if(pos === this.leader[i].pos) {
            ret.uid = this.leader[i].uid;
            ret.name = this.leader[i].name;
            break;
        }
    }
    return ret;
}

/**
 * 获取信仰领导着
 * @returns {Array|*}
 */
Belief.prototype.getBeliefLeader = function () {
    return this.leader;
}

/**
 * 获取玩家职位
 * @returns {Array|*}
 */
Belief.prototype.getBeliefLeaderPosByPlayerId = function (playerId) {
    let pos = -1;
    for(let i in this.leader) {
        if(this.leader[i].uid === playerId) {
           pos = this.leader[i].pos;
           break;
        }
    }
    return pos;
}

//增加信仰活跃度
Belief.prototype.addBeliefLiveness = function (value) {
    let ret = {level: 0, isBroadcast: false, beliefExp: 0};
    if(this.level >= 20) {
        ret.isBroadcast = false;
        ret.level = this.level;
        ret.beliefExp = this.beliefExp;
        return ret;
    }
    let levelData = dataApi.allData.data["BeliefExp"];
    let upLevel = false;
    let remainExp = this.beliefExp + value;
    for(let level in levelData) {
        if(Number(level) > 20) {
            continue;
        }
        let id = levelData[level].ID;
        if(this.level === id && remainExp >= levelData[level]["Exp"]) {
            remainExp = remainExp - levelData[level]["Exp"];
            this.level += 1;
            upLevel = true;
            //满级
            if(this.level >= 20) {
                remainExp = levelData[level]["Exp"];
                break;
            }
        }
    }

    this.beliefExp = remainExp;
    if(upLevel) {
        ret.isBroadcast = true;
    }
    ret.level = this.level;
    ret.beliefExp = this.beliefExp;
    return ret;
}

//获取当日捐献球币数量
Belief.prototype.getTodayDonateGoldNum = function () {
    return this.todayGoldNum;
}


Belief.prototype.addTodayDonateGoldNum = function (num) {
    this.todayGoldNum = this.todayGoldNum + num;
}

Belief.prototype.getBeliefLevel = function () {
   return this.level;
}

Belief.prototype.clearDonateGoldNum = function () {
    this.todayGoldNum = 0;
    this.clearTime = timeUtils.now();
}

Belief.prototype.addNotify = function (msg) {
    if(this.notify.length >= 50) {
        this.notify.shift();
    }
    let info = {
        time: timeUtils.now(),
        msg: msg
    }
    this.notify.push(info);
}

/**********************************************************************************************************************/
/**
 * 发给客户端的数据结构
 */
Belief.prototype.makeClientBelief = function() {
    let belief = {};
    belief.beliefId = this.beliefId;                   //信仰id
    belief.notice = this.notice;                       //信仰公告
    belief.playerList = this.playerList;               //信仰成员列表
    belief.beliefRank = this.beliefRank;               //信仰排名
    belief.beliefGold = this.beliefGold;               //信仰资金
    belief.leader = this.leader;                       //领导者
    belief.notify = this.notify;                       //信仰动态
    belief.level = this.level;                         //信仰等级
    belief.beliefExp = this.beliefExp;                 //信仰经验
    belief.todayGoldNum = this.todayGoldNum;           //信仰捐赠球币数量
    return belief;
}
