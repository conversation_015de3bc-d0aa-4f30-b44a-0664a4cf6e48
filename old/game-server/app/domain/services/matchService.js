/**
 * Idea and Persist
 * Created by <PERSON> on 2019/7/9.
 */
var logger = require('pomelo-logger').getLogger('pomelo', __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var TimeUtils = require('../../util/timeUtils');
var leagueEvent = require('../event/leagueEvent');
var async = require('async');
var dataApi = require('../../util/dataApi');
var Constant = require("../../../../shared/constant");
var commonEnum = require('../../../../shared/enum');
var GlobalRankMgr = require("../entities/globalRankMgr");

var debugConfig = require('../../../config/debugConfig');
let clusterConfig = require('../../../config/cluster');

module.exports.create = function(app, dbclient)
{
    return new MatchService(app, dbclient);
};

let MatchService = function(app, dbclient)
{
    EventEmitter.call(this);
	this.app = app;
	//this.time = 600 * 1000; //10分钟更新最新的全局数据

	this.intervalTime = 1 * 1000;	//timer设置为1秒一次
	this.timeCount = 0;
	this.maxIntervalTime = 60 * 60 * 1000;		//定时器最大时间间隔1小时

	this.oneMinite = 60 * 1000;
	this.twoMinite = 2 * 60 * 1000;
	this.tenMinite = 10 * 60 * 1000;
	this.twoSec = 2 * 1000;       //2秒一次刷新 世界boss排行榜
	this.oneSec = 1 * 1000;       //1秒一次

	if(debugConfig.isForTest) {
		this.twoMinite = 10 * 1000;
		this.oneMinite = 10 * 1000;
		//this.tenMinite = 20 * 1000;
	}

    //Db init
	this.matchDao = require("../../dao/matchDao").create(dbclient);
	this.globalRankMgr = new GlobalRankMgr(this);
	//this.init(cb);
	this.weekTime = 0;
	this.weekNum = 0;

};

util.inherits(MatchService, EventEmitter);

MatchService.prototype.init = function()
{
	logger.info('----------MatchService init-------------');
	//this.app.event.on("start_all", afterAllServerStartUp.bind(null, this, cb));
	let self = this;
	async.waterfall([
		//初始化懂球杯赛数据
		function (callback) {
			let dqCupMatchService = self.app.get("dqCupMatchService");
			dqCupMatchService.initByDB(function (err) {
				if (!!err) {
					return callback(err);
				}
				logger.debug("init dqCupMatchService initByDB, finished.");
				callback(null);
			});
		},
		//初始化世界boss数据
		function (callback) {
			if(clusterConfig.isOpenWorldBoss) {
				let worldBossService = self.app.get("worldBossService");
				worldBossService.initByDB(function (err) {
					if (!!err) {
						return callback(err);
					}
					logger.debug("init worldBossService initByDB, finished.");
					callback(null);
				});
			}else {
				callback(null);
			}
		},
		//初始化主席争夺战数据
		function (callback) {
			let chairmanMatchService = self.app.get("chairmanMatchService");
			chairmanMatchService.initByDB(function (err) {
				if (!!err) {
					return callback(err);
				}
				logger.debug("init chairmanMatchService initByDB, finished.");
				callback(null);
			});
		},
		//初始化信仰巅峰战数据
		function (callback) {
			let peakMatchService = self.app.get("peakMatchService");
			peakMatchService.initByDB(function (err) {
				if (!!err) {
					return callback(err);
				}
				logger.debug("init peakMatchService initByDB, finished.");
				callback(null);
			});
		},
		//初始化信仰之战  //暂时关闭
		function (callback) {
			let beliefMatchService = self.app.get("beliefMatchService");
			beliefMatchService.initBeliefMatch((err) => {
				callback(null);
			});
		},
		//定时器启动
		function(callback) {
			self.matchServerTimerStart();
			callback(null);
			/*
			self.setTimerGetAccount(function(err) {
				if (!!err)
				{
					callback(err);
					return;
				}
				callback(null);
			});
			*/
		},
		//初始化联赛数据
		function(callback){
			if(!clusterConfig.isStartOldBusinessMatch) {
				return callback(null, []);
			}
			let time1 = TimeUtils.now();
			self.fetchAllAccountFromGlobalAccount(true, function(code, accountList) {
				if (code !== Code.OK) {
					let errMsg = "fetchAllAccountFromGlobalAccount failed! not accountList";
					logger.error(errMsg);
					return callback(errMsg);
				}
				//载入全局rank
				self.initGlobalRankData(accountList);
				logger.debug("fetchAllAccountFromGlobalAccount cost time: ", TimeUtils.now() - time1);
				callback(null, accountList);
			});
		},
		function(accountList, callback) {
			if(!clusterConfig.isStartOldBusinessMatch) {
				return callback(null);
			}
			let time2 = TimeUtils.now();
			self.loadGlobalRankData(accountList, function(err) {
				if (!!err) {
					return callback(err);
				}
				logger.debug("loadGlobalRankData cost time: ", TimeUtils.now() - time2);
				callback(null);
			});
		}
	], function(err){
		if(err){
			logger.error("afterAllServerStartUp error!", err);
		}
		logger.info("matchService afterAllServerStartUp success!");
	});
};

MatchService.prototype.matchServerTimerStart = function() {
	let self = this;
	if(self.intervalTime > 0) {
		setInterval(self.matchServerTimerProcess, self.intervalTime, self);
	}
};

MatchService.prototype.matchServerTimerProcess = function(self) {
	self.timeCount++;
	let maxCount = Math.floor(self.maxIntervalTime/self.intervalTime);
	//logger.debug("matchServerTimerProcess timeCount, maxCount: ", self.timeCount, maxCount);
	if(self.timeCount >= maxCount) {
		self.timeCount = 0;
	}
	let passTime = self.timeCount * self.intervalTime;
	if(passTime % self.oneMinite === 0) {
		self.matchServerOneMiniteLoop();
	}
	if(passTime % self.twoMinite === 0) {
		self.matchServerTwoMiniteLoop();
	}
	if(passTime % self.tenMinite === 0) {
		self.matchServerTenMiniteLoop();
	}
	if(passTime % self.twoSec === 0) {
		self.matchServerTwoSecLoop();
	}

	//信仰之战
	if(passTime % self.oneSec === 0) {
		self.matchServerOneSecLoop();
	}
};

MatchService.prototype.matchServerOneSecLoop = function() {
	logger.debug("matchServerOneSecLoop ---- twoSec");
	//信仰之战  //暂时关闭
	let beliefMatchService = this.app.get("beliefMatchService");
	beliefMatchService.loopProcess();
};

MatchService.prototype.matchServerTwoSecLoop = function() {
	logger.debug("matchServerTwoSecLoop ---- twoSec");
	if(clusterConfig.isOpenWorldBoss) {
		let worldBossService = this.app.get("worldBossService");
		worldBossService.loopRefreshWorldBossRank();
	}
};

MatchService.prototype.matchServerOneMiniteLoop = function() {
	logger.debug("matchwServerOneMiniteLoop ---- oneMinite");
	if(clusterConfig.isOpenWorldBoss) {
		let worldBossService = this.app.get("worldBossService");
		worldBossService.loopActivity();
	}
	this.clearMvpHeroId();

	};

//每两分钟处理一次
MatchService.prototype.matchServerTwoMiniteLoop = function() {
	logger.debug("matchServerTwoMiniteLoop ---- twoMinite");
	//懂球杯轮询
	let dqCupMatchService = this.app.get("dqCupMatchService");
	dqCupMatchService.loopProcess(dqCupMatchService);
	//主席争夺战轮询
	let chairmanMatchService = this.app.get("chairmanMatchService");
	chairmanMatchService.loopProcess(chairmanMatchService);
	//信仰巅峰赛轮询
	let peakMatchService = this.app.get("peakMatchService");
	peakMatchService.loopProcess(peakMatchService);
	this.sendMvpHeroReward();
};

//10分钟轮询一次
MatchService.prototype.matchServerTenMiniteLoop = function() {
	logger.debug("matchServerTenMiniteLoop ---- tenMinite");
	//商业赛开关
	if(clusterConfig.isStartOldBusinessMatch) {
		logger.debug("not open business match");
		this.intervalGetAccount();
	}
	if(clusterConfig.isOpenWorldBoss) {
		let worldBossService = this.app.get("worldBossService");
		worldBossService.loopSaveProcess();
	}

	//信仰之战
	let beliefMatchService = this.app.get("beliefMatchService");
	beliefMatchService.loopSaveAllDataProcess();
};

//清除MVP球员ID
MatchService.prototype.clearMvpHeroId = function() {
	let startConfig = dataApi.allData.data["ActiveControl"][20];
	let endConfig = dataApi.allData.data["ActiveControl"][21];
	//判断是否在清除时间
	let time = new Date();
	let startTime = new Date(startConfig.StartTime).getTime();
	let endTime = new Date(endConfig.EndTime).getTime();

	let self = this;
	if(time >= startTime && time < endTime) {
		let msg = { mvpHeroId: 0 };
		let session = {frontendId: this.app.getServerId()};
		self.app.rpc.datanode.dataNodeRemote.getMvpHeroId(session, {}, function(err, result) {
			if(result.mvpHeroId !== 0) {
				self.app.rpc.datanode.dataNodeRemote.setMvpHeroId(session, msg, (code)=> {});
			}
		});
	}
}

//MVp返还50%金币
MatchService.prototype.sendMvpHeroReward = function() {
	let self = this;
	let session = {frontendId: this.app.getServerId()};
	async.waterfall([
		function(callback){
			let config = dataApi.allData.data["ActiveControl"][22];
			//判断是否在发奖时间
			let time = new Date();
			let startTime = new Date(config.StartTime).getTime();
			let endTime = new Date(config.EndTime).getTime();
			//发邮件时间
			if(time >= startTime  && time < endTime) {
				callback(null);
			}else {
				callback("Code.TIME_FAIL");
				return;
			}
		},function(callback){
			self.app.rpc.datanode.dataNodeRemote.getBuyMvpHeroPlayer(session, {}, function(err, result) {
				if (result.code !== Code.OK) {
					callback("not find playerList");
					return;
				}

				callback(null, result.playerList);
			});
		}, function(playerList, callback) {
			let newPlayerList = [];
			let meMap = new Map();
			for(let i in playerList) {
				if(meMap.has(playerList[i].playerId)) {
					meMap.get(playerList[i].playerId).num += playerList[i].num;
				}else {
					meMap.set(playerList[i].playerId, playerList[i]);
				}
			}

			for(let [k, v] of meMap) {
				newPlayerList.push(v);
			}

			//发奖奖励
			let sendList = [];
			for(let i = 0, len = newPlayerList.length; i < len; ++i) {
				let rewardList = [];
				let mailInfo = {};
				mailInfo.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
				mailInfo.ResId = 2;
				mailInfo.Num = newPlayerList[i].num;
				rewardList.push(mailInfo);

				let info = {};
				info.sendPlayerId = "系统";
				info.recvPlayerId = newPlayerList[i].playerId;
				info.title = "MVP球员返利";
				info.content = "亲爱的玩家，以下你购买MVP球员的返利金币，请及时领取!";
				info.attachList = rewardList;
				info.gid = newPlayerList[i].gid;
				sendList.push(info);
			}

			async.eachSeries(sendList, function (msg, cb) {
				let session = {frontendId: self.app.getServerId(), toServerId: msg.gid};
				//发送邮件
				self.app.rpc.game.entryRemote.sendEditMail(session, msg, function (err) {
					if (err != Code.OK) {
						cb("send mail fail");
						return;
					}
					cb(null);
				});
			},function (err) {
				if(!!err) {
					callback(err);
					return;
				}

				let session = { frontendId: self.app.getServerId() }
					let sMsg = {
					playerList: playerList
				}
				//更新状态
				self.app.rpc.datanode.dataNodeRemote.updateMvpSendRewardStatus(session, sMsg, function(err, result) {
					if (result.code !== Code.OK) {
						callback("updateMvpRewardStatus fail");
						return;
					}
					callback(null);
				});
			});
		}], function(err) {
			logger.warn("sendMvpHeroReward------result---------", err)
	});
}

/*
let afterAllServerStartUp = function(self, cb) 
{
	//logger.info('---------- MatchService afterAllServerStartUp -------------', );

};
*/

MatchService.prototype.fetchAllAccountFromGlobalAccount = function(isStartUp, cb)
{
	let self = this;
	let tmpSession = {frontendId: this.app.getServerId()};
    let msg = {};
    msg.isStartUp = isStartUp;   
	self.app.rpc.auth.authRemote.getAllAccount(tmpSession, msg, function(err, result) {
		if (!!err)
		{
			cb(Code.FAIL);
			return;
		}
        if (result.code !== Code.OK)
        {
            cb(result.code, result.accountList);
            return;
        }
        cb(Code.OK, result.accountList);
	});
};

MatchService.prototype.initGlobalRankData = function(accountList) 
{
	//logger.info('---------- initGlobalRankData -------------', );
	this.globalRankMgr.initByConfig();
	this.globalRankMgr.init(accountList);
	this.initWeekTime();
	this.initWeekNum();
};

/*
MatchService.prototype.setTimerGetAccount = function(cb)
{
	let self = this;
	let time = this.time;
	if(time){
	 	setInterval(function(){
	 	    self.intervalGetAccount();
 		}, time);
	}
	cb(null);
};
*/

MatchService.prototype.intervalGetAccount = function(cb)
{
	let self = this;
	async.waterfall([
        function(callback){
			self.fetchAllAccountFromGlobalAccount(false, function(code, accountList) {
				if (code !== Code.OK)
				{
					let errMsg = "intervalGetAccount failed! not accountList";
					logger.error(errMsg);
					callback(errMsg);
					return;
				}
				//全局排行榜更新
				self.globalRankMgr.timeOverUpdateRank(accountList);
				callback(null);
			});
		},function(callback){
			self.saveMatchRank(function(err){
				if (!!err)
				{
					callback(err);
					return;
				}
				callback(null);
			});
		}, function(callback) {	
			self.resetMatchRank(function(err) {
				if (!!err)
				{
					callback(err);
					return;
				}
				callback(null);
			});
		},
		function(callback) {
			self.initWeekNum();
			let nexttime = self.weekTime + ((self.weekNum + 1) * 7 * 24 * 60 * 60 * 1000);//下一次领取的时间
			let interval = nexttime - TimeUtils.now();//得到执行周期
			if(interval < 0)
				interval = 60 * 1000;
			if(interval){
				setTimeout(function(){
					self.rankAwarding();//发排名奖励();
					self.globalRankMgr.matchRank.resetWeekFansRank();//重置每周最高排名
				}, interval);
			}
			callback(null);
		}
	], function(err)
	{
		if (!!err) {
			logger.error("intervalGetAccount error!", err);
		}
		logger.info("intervalGetAccount is ok!");
		if(cb) { cb(); }
	});
};

MatchService.prototype.updateNowAccountList = function(accountList, cb)
{
	this.globalRankMgr.timeOverUpdateRank(accountList);
	cb(Code.OK);
};

MatchService.prototype.loadGlobalRankData = function(accountList, cb)
{
	logger.info('---------- loadGlobalRankData -------------');
	let self = this;
	async.waterfall([
        function(callback){
            self.loadMatchRank(accountList, function(err){
            	//估计uid为1
				if(!!err){
					logger.error("loadGlobalRankData error");
					callback(err);
					return;
				}
				self.globalRankMgr.matchRank.checkFightTimesList();
                callback(null);
            });
        }
    ], function(err){
        if(err){
            return cb(err);
        }
		cb(null);
	});
};

MatchService.prototype.loadMatchRank = function(accountList, cb)
{
	let self = this;
	async.eachSeries(accountList, function(info, callback) {
		self.readMatchRank(info.uid, function(err){
			if (!!err)
			{
				callback(err);
				return;
			}
			callback(null);
		});  
    }, function(err) {
        if (!!err) 
        {
            logger.error("error", err);
            cb(err);
            return;
		}
		logger.info('---------- loadMatchRank  success!-------------');
		cb(null);
	});
};

MatchService.prototype.readMatchRank = function(uid, cb)
{
	let self = this;
	//logger.info('---------- readMatchRank -------------', uid);
	async.waterfall([
        function(callback){
            self.matchDao.readWholeMatch(uid, function(res){ //估计uid为1
				if(!res){
					callback(null);
					return;
				}

				self.globalRankMgr.matchRank.initPlayerByDB(res.matchRank);
                callback(null);
            });
        }
    ], function(err){
        if(err){
			logger.error("loadMatchRank error", err);
            return cb(err);
		}
		cb(null);
	});
};

MatchService.prototype.saveMatchRank = function(cb)
{
	let self = this;
	let doc = self.globalRankMgr.matchRank.toJSONforDB();
	//logger.info("saveMatchRank", doc.fightTimesList.length);
	async.eachSeries(doc.fightTimesList, function(info, callback) {
		self.matchDao.updateMatchRank(info.uid, info, function(err) {
			if (!!err)
			{
				callback(err);
				return;
			}
			callback(null);
		});  
    }, function(err) {
        if (!!err) 
        {
            logger.error("error", err);
            cb(err);
            return;
		}
		cb(null);
	});
};

MatchService.prototype.resetMatchRank = function(cb)
{
	this.globalRankMgr.matchRank.resetLastTimesList();
	cb(null);
};

//得到周最高排名
MatchService.prototype.getWeekFansRank = function(playerId, cb)
{
	if (!playerId)
	{
		logger.info("MatchService.getFansRank: not playerId");
		cb(Code.FAIL, 0);
		return;
	}
	let weekFansRank = this.globalRankMgr.matchRank.getWeekFansRank(playerId);
	//logger.error("得到周最高排名============", weekFansRank);
	cb(Code.OK, weekFansRank);
};

//设置周最高排名
MatchService.prototype.flashWeekFansRank = function(playerId, cb)
{
	if (!playerId)
	{
		logger.info("MatchService.getFansRank: not playerId");
		cb(Code.FAIL);
		return;
	}
	let weekFansRank = this.globalRankMgr.matchRank.getWeekFansRank(playerId);//最高排名
	let nowRank = this.globalRankMgr.fansCountRank.getRank(playerId);//当前排名
	if(weekFansRank === 0 || !weekFansRank)
	{
		this.globalRankMgr.matchRank.setWeekFansRank(playerId, nowRank);
	}
	if(nowRank > 0 && nowRank < weekFansRank)
	{
		this.globalRankMgr.matchRank.setWeekFansRank(playerId, nowRank);
	}
	//logger.error("原来的最高排名：%d, 当前排名：%d, 设置以后的最高排名：%d", weekFansRank, nowRank, this.globalRankMgr.matchRank.getWeekFansRank(playerId));
	cb(Code.OK);
};

MatchService.prototype.getFansRank = function(playerId, msg, cb)
{
	if (!playerId)
	{
		logger.info("MatchService.getFansRank: not playerId");
		cb(Code.FAIL, 0, 0);
		return;
	}
	let rank = this.globalRankMgr.fansCountRank.getRank(playerId);
	let fansCount = 0;
	if (rank > 0)
	{
		fansCount = this.globalRankMgr.fansCountRank.getFansCount(playerId);
	}
	//logger.info("MatchService.getFansRank: rank, fansCount", fansCount, rank);
	cb(Code.OK, fansCount, rank);
};
//得到排名前100的玩家球迷数量和排名
MatchService.prototype.getGlobalFansRank = function(playerId, cb)
{
	let rankList = this.getNumFansRank(100);
	//this.rankAwarding();
	cb(Code.OK, rankList);
};
//得到排名前num个玩家数据
MatchService.prototype.getNumFansRank = function(num)
{
	let rankList = [];
	let uid;
	let name;
	let rank;
	let fansCount;
	for(let i = 0; i < this.globalRankMgr.fansCountRank.rank.length; i++)
	{
		if(i >= num)
			break;
		let fansRankObj = this.globalRankMgr.fansCountRank.rank[i];
		uid = fansRankObj.uid;
		name = this.globalRankMgr.accountList.get(uid).name;
		rank = fansRankObj.rank;//得到排名
		fansCount = Math.round(fansRankObj.fansCount);//粉丝数量
		rankList.push({name: name, rank: rank, fansCount: fansCount, uid: uid});
	}
	//logger.error("排名前 %d 信息：：：：：：：：：：：：：：：：：", num, rankList);
	return rankList;
};
//给排名前10颁奖
MatchService.prototype.rankAwarding = function()
{
	let self = this;
	let num = this.globalRankMgr.fansCountRank.rank.length;
	let rankList = self.getNumFansRank(num);//前10名发奖励
	var rewardList = [];
	var Awarding = [];
	let index = 0;
	let day = TimeUtils.dayInterval(self.weekTime);
	let Week = Math.floor(day / 7); //现在是第几周
	if(Week > 0 && Week > self.weekNum)//过了一周
	{
		var mailTextConfig = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.BUSSINESS_RANK_AWARD];
		let j = 1;
		//发送奖励
		var config = dataApi.allData.data["BusinessMatchReward"];//商业奖励表
		for (let i = 0; i < rankList.length; i++)
		{
			if (rankList[i].rank >= config[j].RankingMin && rankList[i].rank <= config[j].RankingMax)//符合的名次，可以发奖励
			{
				for (let k = 1; k <= 4; ++k)
				{
					let rewardItem = config[j]["Item" + k];
					let rewardNum = config[j]["Num" + k];
					if (rewardItem <= 0 || rewardNum <= 0) {
						continue;
					}
					rewardList[index] = {};
					rewardList[index].ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
					rewardList[index].ResId = rewardItem;
					rewardList[index].Num = rewardNum;
					index++;
				}
				var msg = {};
				msg.sendPlayerId = "系统哥";
				msg.recvPlayerId = rankList[i].uid;
				msg.title = mailTextConfig.Title;
				msg.content = mailTextConfig.Result1.replace("#0#", rankList[i].rank);
				msg.attachList = utils.cloneArray(rewardList);
				Awarding.push(msg);
				index = 0;
			}
			else
			{
				if (!config[j + 1] || !rankList[i])
					break;
				j++;
				i--;
			}
		}
		async.eachSeries(Awarding, function (msg, callback) {
			self.matchDao.findByPlayerId(msg.recvPlayerId, function (err, cb) {
				if(err != Code.OK){
					logger.error("rankAwarding findByPlayerId failed! err: ", err);
					callback(err);
					return;
				}
				let session = {frontendId: self.app.getServerId(), toServerId: cb.gid};
				logger.error("async.eachSeries-----msg", msg);
				//发送邮件
				self.app.rpc.game.entryRemote.sendEditMail(session, msg, function (cb) {
					if (cb != Code.OK) {
						logger.error("sendEditMail: ", cb);
						return callback(err);
					}
					callback(null);
				});
			});
		});
		//记录发放奖励的周数
		self.matchDao.updateWeekTimeCollection(Week, function (err) {
			if (!!err) {
				logger.error("updateWeekTimeCollection err");
			}
		});
		self.weekNum = Week;
	}
};

MatchService.prototype.initWeekNum = function()
{
	let self = this;
	this.matchDao.loadWeekTimeCollection(function (err, result) {
		if (!result)
		{
			let day = TimeUtils.dayInterval(self.weekTime);
			let weekNum = Math.floor(day / 7); //现在是第几周
			self.matchDao.updateWeekTimeCollection(weekNum, function (err) {
				if(!!err)
				{
					logger.error("updateWeekTimeCollection err");
					return;
				}
				self.weekNum = weekNum;
			});
		}
		else if(result)
		{
			self.weekNum = result.weekNum;
		}
	});
};

MatchService.prototype.initWeekTime = function()
{
	// let config = dataApi.allData.data["businessFansRank"];
	this.weekTime = new Date("2019/11/04 00:00:00").getTime();//test, 2019/11/04 00:00:00
};

MatchService.prototype.getPlayerFightTimeInfo = function(playerId, msg, cb)
{
	this.globalRankMgr.matchRank.checkUpdateFightTimes(playerId);  //检查更新挑战次数
	let fightTimes = this.globalRankMgr.matchRank.getFightTimesByUid(playerId, true);//主动挑战次数
	let passiveTimes = this.globalRankMgr.matchRank.getFightTimesByUid(playerId, false);//被动挑战次数
	let buyTimes = this.globalRankMgr.matchRank.getBuyTimes(playerId);
	let totalTimes =  this.globalRankMgr.matchRank.getTotalFightTimes();
	let totalpassTimes =  this.globalRankMgr.matchRank.gettotalpassTimes();
	let totalbuyTimes =  this.globalRankMgr.matchRank.gettotalbuyTimes();
	let leftTimes = totalTimes - fightTimes;
	let rightTimes = totalpassTimes - passiveTimes;
	buyTimes = totalbuyTimes - buyTimes;
	if (leftTimes < 0)
	{
		leftTimes = 0;
	}
	if(rightTimes < 0)
	 	rightTimes = 0;
	if(buyTimes < 0)
		buyTimes = 0;
	var info = {
		totalTimes: totalTimes,
		leftTimes: leftTimes,
		rightTimes: rightTimes,
		buyTimes: buyTimes,
	};

	logger.info("getPlayerFightTimeInfo", playerId, info);
	cb(Code.OK, info);
};
//购买挑战次数
MatchService.prototype.buyPlayerFightTimes = function(playerId)
{
	return  this.globalRankMgr.matchRank.BuyFightTimes(playerId);
};
//返回各项剩余次数
MatchService.prototype.getAllTimes = function(playerId)
{
	return this.globalRankMgr.matchRank.getAllTimes(playerId);
};

//检查是否以达到限购次数
MatchService.prototype.checkBuyPlayerFightTimes = function(playerId, cb)
{
	let obj = this.globalRankMgr.matchRank.getFightTimesList(playerId);
	let totalbuyTimes =  this.globalRankMgr.matchRank.gettotalbuyTimes();
	if(obj.buyTimes >= totalbuyTimes)
	{
		 cb(false);
	}
	cb(true);
};

MatchService.prototype.getMatchUid = function(playerId, msg, cb)
{
	if (!playerId)
	{
		logger.info("MatchService.getMatchUid: not playerId");
		cb(Code.FAIL, "");
		return;
	}
	let matchUid = this.globalRankMgr.matchRank.getMatchPlayerUid(playerId, msg.actualStrength);

	cb(Code.OK, matchUid);
};

MatchService.prototype.matchBattleReq = function(msg, cb)
{
	if (!msg.playerId) {
		logger.error("MatchService.matchBattleReq: playerId is null");
		return cb(Code.FAIL);
	}
	if (!msg.enemyUid || msg.enemyUid === "")
	{
		logger.error("MatchService.matchBattleReq: enemyUid is null", msg.playerId);
		return cb(Code.MATCH.NOT_FOUND_MATCH_PLAYER);
	}
	let businessRewardInfo = msg.businessRewardInfo;
	if (!businessRewardInfo) {
		businessRewardInfo = {
			totalCash: 0,
			winCash: 0,
			loseCash: 0,
			drawCash: 0,
			matchWinRatio: 0,
			matchLoseRatio: 0,
			matchDrawRatio: 0,
			enemyFansCount: 0,
			myFansCount: 0,
			FansQ: 0,
		};
	}

	//logger.info("MatchService.matchBattleReq: businessRewardInfo", businessRewardInfo);
	
	//战斗
	let self = this;
	let ssMsg = {};
	ssMsg.home = msg.playerId;
	ssMsg.homeGid = msg.playerGid;
	ssMsg.away = msg.enemyUid;
	ssMsg.awayGid = msg.enemyGid;
	ssMsg.businessRewardInfo = businessRewardInfo;
	let roomUid = "";
	let tmpSession = {frontendId: this.app.getServerId()};
	async.waterfall([
		function (callback) {
			self.app.rpc.battle.battleRemote.pvpMatchBattle(tmpSession, ssMsg, function (code, result) {
				if(code !== Code.OK) {
					logger.error("rpc pvpMatchBattle fail, ret code: ", code);
					callback("rpc pvpMatchBattle fail, ret code:" + code);
					return;
				}
				callback(null, result);
			});
		},
		function (result, callback) {
			self.updateMatchRecord(result, function(err) {
				if(!!err) 
				{
					callback("updateMatchRecord failed!");
					return;
				}
				roomUid = result.roomUid;
				callback(null);
			});
		}
	], function(err) {
		if(!!err){
			logger.debug("water fall fail: error msg: ", err);
			cb(Code.FAIL, roomUid);
			return;
		}
		cb(Code.OK, roomUid);
	});
};

MatchService.prototype.updateMatchRecord = function(result, cb)
{
	let homeScore = result.homeScore;
	let awayScore = result.awayScore;
	let resultA = 0;
	let resultB = 0;
	if (homeScore !== awayScore)
	{
		if (homeScore > awayScore) //A赢
		{
			resultA = 2;
			resultB = 1;
		}else
		{
			resultA = 1;
			resultB = 2;
		}
	}
	let matchRecordA = {
		playerId: result.home,
		roomUid: result.roomUid,
		teamA: result.home,
		teamB: result.away,
		result: resultA,
		teamAScore: result.homeScore,
		teamBScore: result.awayScore,
		beginTime: result.beginTime,
		teamARank: this.globalRankMgr.fansCountRank.getRank(result.home),
		teamBRank: this.globalRankMgr.fansCountRank.getRank(result.away),
		FansChangeNum: result.homeFansChangeNum
	  };

	  let matchRecordB = {
		playerId: result.away,
		roomUid: result.roomUid,
		teamA: result.home,
		teamB: result.away,
		result: resultB,
		teamAScore: result.homeScore,
		teamBScore: result.awayScore,
		beginTime: result.beginTime,
		teamARank: this.globalRankMgr.fansCountRank.getRank(result.home),
		teamBRank: this.globalRankMgr.fansCountRank.getRank(result.away),
		FansChangeNum: result.awayFansChangeNum
	  };

	  let arr = [];
	  arr.push(matchRecordA);
	  arr.push(matchRecordB);

	  this.globalRankMgr.matchRank.incrementFightTime(result.home);//已经主动挑战次数+1
	  this.globalRankMgr.matchRank.incrementPassiveTimes(result.away);//已经被动挑战次数+1
	  //logger.info("matchRecordA", matchRecordA);
	  //logger.info("matchRecordB", matchRecordB);

	  let self = this;
	  let funcType = commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_MATCH_UPDATE_BATTLE_RECORD;
	  async.eachSeries(arr, function(matchRecord, callback) {
		  self.clusterToGameFuncService(matchRecord, funcType, function(err){
			  if (!!err)
			  {
				  logger.error("clusterToGameFuncService: err, ret", err);
				  callback(err);
				  return;
			  }
  
			  callback(null);
		  });
	  }, function(err) {
		  if (!!err) 
		  {
			  logger.error("error", err);
			  cb(err);
			  return;
		  }
		  cb(null);
	});
};

MatchService.prototype.makeMessageBody = function(obj, funcType)
{
	let msg = {};
	switch (funcType) 
	{
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_MATCH_UPDATE_BATTLE_RECORD:
			msg.playerId 	= obj.playerId;
			msg.roomUid  	= obj.roomUid;
			msg.teamA 		= obj.teamA;
			msg.teamB 		= obj.teamB;
			msg.result 		= obj.result;
			msg.teamAScore  = obj.teamAScore;
			msg.teamBScore 	= obj.teamBScore;
			msg.beginTime   = obj.beginTime;
			msg.teamARank   = obj.teamARank;
			msg.teamBRank   = obj.teamBRank;
			msg.FansChangeNum = obj.FansChangeNum;
			break;
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_MATCH_GET_ALL_PLAYER_FIGHT_TIMES:
			break;
		default:
			logger.error("not case hint ", msg.funcType);
			msg.playerId   = obj.playerId;
			break;
	}

	msg.funcType = funcType;
	return msg;
};

MatchService.prototype.clusterToGameFuncService = function(obj, funcType, cb)
{
	let msg = this.makeMessageBody(obj, funcType);
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.datanode.dataNodeRemote.clusterToGameFunc(session, msg, function(code) {
		if (code !== Code.OK)
		{
			logger.error("clusterToGameFuncService return failed!", code);
            return cb("clusterToGameFuncService return failed!");
		}
		cb(null);
	});
};

MatchService.prototype.updateActConfig = function(msg, cb)
{
	let id = msg.key;
	let data = msg.data;
	dataApi.allData.data[id] = data;
	cb(Code.OK);
};
