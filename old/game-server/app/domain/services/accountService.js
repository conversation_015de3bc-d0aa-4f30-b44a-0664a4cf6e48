/**
 * Created by aaa on 2015/5/13.
 */

var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var Account =  require("../entities/account");

module.exports.create = function(app, dbclient){
    return new AccountService(app, dbclient);
};

//Cluster集群 处理全局账号相关的服务
let AccountService = function(app, dbclient) {
    EventEmitter.call(this);
    this.app = app;
    this.accountDao = require('../../dao/accountDao').create(dbclient);
    this.account = new Account(this.accountDao); 
    this.init();
};

util.inherits(AccountService, EventEmitter);

AccountService.prototype.getAccountDao = function() {
    return this.accountDao;
};

AccountService.prototype.getAccountInfo = function (msg, cb) {
    this.accountDao.findOrCreate(msg, function (code, info) {
        if(code !== Code.OK) {
            cb(null, code, 0);
        }else {
            //new tourist return new username
            logger.debug('-------- login ok ----------- playerId',info.uid);
            cb(null, Code.OK, info.uid);
        }
    });
};

AccountService.prototype.init = function() {
    this.afterAllServerStartUp(function(err) {
        if (!!err)
        {
        	logger.error('----------afterAllServerStartUp error-------------', err);
        }
    });
};

AccountService.prototype.afterAllServerStartUp = function(cb)
{
	logger.info('----------afterAllServerStartUp -------------');
	this.app.event.on("start_all", loadAllAccount.bind(null, this, cb));
};

let loadAllAccount = function(self, cb)
{
	self.accountDao.getAllAccount(function(err, doc) {
		if(!!err) {
            logger.error('======= loadAllAccount err ======', err);
            cb(err);
            return;
		}

        let accountCount = 0;
		for(var i in doc) {
            let uid = doc[i]["uid"];
			if(!!uid) {
                let accountObj = self.account.makeAccountObj(uid, doc[i]["oid"], doc[i]["cl"], doc[i]["pf"], doc[i]["nm"], doc[i]["gid"], doc[i]["name"], doc[i]["level"], doc[i]["fansCount"], doc[i]["actualStrength"], doc[i]["groundOpenStatus"]);
                self.account.updateAccount(uid, accountObj);
                //logger.info("accountObj", accountObj);
                accountCount++;
			}
        }

        logger.warn("loadAllAccount success! accountCount:", accountCount);
        cb(null);
        return;
    });
};

AccountService.prototype.updateGlobalAccount = function(msg, cb)
{
    let playerId = msg.playerId;
    let name = msg.name;
    let level= msg.level;
    let fansCount = msg.fansCount;
    let actualStrength = msg.actualStrength;
    let updateNow = msg.updateNow;
    let groundOpenStatus =  msg.groundOpenStatus;
    if (!playerId)
    {
        logger.error("updateGlobalAccount: not playerId", playerId);
        cb(Code.FAIL);
        return;
    }

    //logger.info("message", msg);
    //logger.info("name, level, fansCount, actualStrength", playerId, name, level, fansCount, actualStrength, groundOpenStatus);

    let self = this;
    let tmpSession = {frontendId: this.app.getServerId()};
    this.account.updateGlobalAccount(playerId, name, level, fansCount, actualStrength, groundOpenStatus, function(code) {
        if (updateNow === 0)
        {
            cb(code);
            return;
        }

        let ssMsg = {};
        let accountList = [];
        let obj = self.account.getPlayerObj(playerId); 
        accountList.push(obj);
        ssMsg.accountList = accountList;
        self.app.rpc.match.matchRemote.updateNowAccountList(tmpSession, ssMsg, function(err, result) {
            if (!!err)
            {
                cb(Code.FAIL);
                return;
            }
    
            if (result.code !== Code.OK)
            {
                cb(Code.FAIL);
                return;
            }
    
            cb(Code.OK);
            return;
        });
    });
};

AccountService.prototype.getAllAccount = function(msg, cb)
{
    if (!msg.isStartUp && msg.isStartUp !== false)
    {
        logger.error("getAllAccount: not isStartUp");
        cb(Code.FAIL, []);
        return;
    }   

    if (!msg.isStartUp) //不是起服
    {
        let accountList = this.account.getAllAccount();
        cb(Code.OK, accountList);
        return;
    }

    if (msg.isStartUp) //起服拉取数据
    {
        let self = this;
        let accountArr =[];
        self.accountDao.getAllAccount(function (err, doc) {
            if(!!err) {
                logger.error('======= loadAllAccount err ======', err);
                cb(err);
                return;
            }
    
            let accountCount = 0;
            for(var i in doc) {
                let uid = doc[i]["uid"];
                if(!!uid) {
                    let accountObj = self.account.makeAccountObj(uid, doc[i]["oid"], doc[i]["cl"], doc[i]["pf"], doc[i]["nm"], doc[i]["gid"], doc[i]["name"], doc[i]["level"], doc[i]["fansCount"], doc[i]["actualStrength"], doc[i]["groundOpenStatus"]);
                    let obj = {
                        uid: uid,
                        name: accountObj.name,
                        level: accountObj.level,
                        fansCount: accountObj.fansCount,
                        actualStrength: accountObj.actualStrength,
                        groundOpenStatus: accountObj.groundOpenStatus,
                    };

                    accountArr.push(obj);
                    accountCount++;
                }
            }

            //logger.warn("isStartUp loadAllAccount success! accountCount:", accountCount);
            cb(Code.OK, accountArr);
            return;
        });
    }
};

AccountService.prototype.searchPlayerName = function(msg, cb)
{
    let playerId = "";
    if (!msg.name && msg.name === "")
    {
        logger.error("searchPlayerName: not isStartUp");
        cb(Code.FAIL, playerId);
        return;
    }

    playerId = this.account.getPlayerUid(msg.name);
    logger.info("searchPlayerName: playerId", playerId);
    cb(Code.OK, playerId);
    return;
};