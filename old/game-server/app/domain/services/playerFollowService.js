/**
 * Idea and Persist
 * Created by <PERSON> on 2019/7/15.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var calc = require('../../util/calc');
var TimeUtils = require('../../util/timeUtils');
var commonEnum = require('../../../../shared/enum');
var dataApi = require('../../util/dataApi');
var http = require('http');
var clusterConfig = require('../../../config/cluster');

module.exports.create = function(app, dbclient) {
	return new PlayerFollowService(app, dbclient);
};

var PlayerFollowService = function(app, dbclient){
	EventEmitter.call(this);
	this.app = app;
};

util.inherits(PlayerFollowService, EventEmitter);

//分页算法
PlayerFollowService.prototype.getTotalPageCount = function(maxResult, totalRecord)
{
	let totalPage = 0
	if (totalRecord > 0)
	{
		totalPage = Math.floor((totalRecord + maxResult -1) / maxResult);
	}

	logger.info("getTotalPageCount", maxResult, totalRecord, totalPage);
	return totalPage;
};

//把指定页的数据拿出来
PlayerFollowService.prototype.getUidListByPage = function(currPage, maxResult, totalUidList)
{
	let uidList = [];
	if (totalUidList.length <= 0){
		return uidList;
	}
	if (currPage <= 0) {
		return uidList;
	}
	//指定页数的范围求出来
	let pageEndIdx = currPage * maxResult - 1;
	let pageStartIdx = pageEndIdx - (maxResult -1);
	logger.info("getUidListByPage", currPage, maxResult, totalUidList, pageEndIdx, pageStartIdx);
	for (let idx in totalUidList) 
	{
		if (idx > pageEndIdx)
		{
			break;
		}

		if (idx < pageStartIdx)
		{
			continue;
		}
		let uid =totalUidList[idx];
		uidList.push(uid);
	}

	logger.info("getUidListByPage uidList", uidList);
	return uidList;
};

//获取界面数据
PlayerFollowService.prototype.getFollowList = function(playerId, msg, cb) 
{
	let followPageInfo = {};
	let fansPageInfo = {};
	
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerFollowService.getTrophy: player not exist !', playerId);
		cb(Code.FAIL, followPageInfo, fansPageInfo);
		return;
	}

	let followList = player.follow.getLastFollowUidList();
	let fansList = player.follow.getLastFansUidList();
	//1.分页
	let currPage = 1;  //默认取第一页的数据
	//每页最大记录数量
	let maxResult = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.FollowMaxResult].Param;
	followPageInfo.totalRecord = followList.length;
	followPageInfo.totalPage = this.getTotalPageCount(maxResult, followList.length);
	followPageInfo.maxResult = maxResult;
	followPageInfo.currPage = currPage;
	followPageInfo.followComRoleList = [];

	fansPageInfo.totalRecord = fansList.length;
	fansPageInfo.totalPage = this.getTotalPageCount(maxResult, fansList.length);
	fansPageInfo.maxResult = maxResult;
	fansPageInfo.currPage = currPage;
	fansPageInfo.followComRoleList = [];

	//2.把第页的玩家数据都放出来
	let currPageFollowList = this.getUidListByPage(currPage, maxResult, followList);
	let currPageFanList = this.getUidListByPage(currPage, maxResult, fansList);

	//3.取数据，然后填充相关字段
	let followComRoleList = [];
	let fansComRoleList = [];
	let self = this;
	let ssMsg ={};
	let session = {frontendId: this.app.getServerId()};
	async.waterfall([
		function (callback) {
			if (currPageFollowList.length <= 0)
			{
				callback(null, []);
				return;
			}
			ssMsg.playerIdList = currPageFollowList;
			self.app.rpc.datanode.dataNodeRemote.getOnlineStateByList(session, ssMsg, function(err, result) {
				if (!!err)
				{
					logger.error("getOnlineStateByList: catch a error!", err);
					callback(err);
					return;
				}

				if(result.code !== Code.OK) {
					logger.error("getOnlineStateByList failed!", result.code);
					callback("getOnlineStateByList failed!");
					return;
				}

				let followOnlineStateList = result.onlineStateList;
				callback(null, followOnlineStateList);
			});
		},
		function (onlineStateList, callback) {
			if (onlineStateList.length <= 0)
			{
				callback(null);
				return;
			}
			self.makeFollowPageList(playerId, onlineStateList, commonEnum.FOLLOW_DETAIL_TYPE.FOLLOW, function(err, list) {
				if (!!err)
				{
					logger.error("makeFollowPageList FOLLOW error!", err);
					callback(err);
					return;
				}
				followComRoleList = list;
				callback(null);
			});
		},
		function (callback) {
			ssMsg.playerIdList = currPageFanList;
			if (currPageFanList.length <= 0)
			{
				callback(null, []);
				return;
			}
			self.app.rpc.datanode.dataNodeRemote.getOnlineStateByList(session, ssMsg, function(err, result) {
				if (!!err)
				{
					logger.error("getOnlineStateByList failed!", err);
					callback(err);
					return;
				}

				if(result.code !== Code.OK) {
					logger.error("getOnlineStateByList failed!", result.code);
					callback("getOnlineStateByList failed!");
					return;
				}
			
				let fansOnlineStateList = result.onlineStateList;
				callback(null, fansOnlineStateList);
			});
		},
		function (onlineStateList, callback) {
			if (onlineStateList.length <= 0)
			{
				callback(null);
				return;
			}
			self.makeFollowPageList(playerId, onlineStateList, commonEnum.FOLLOW_DETAIL_TYPE.FANS, function(err, list) {
				if (!!err)
				{
					logger.error("makeFollowPageList FOLLOW error!", err);
					callback(err);
					return;
				}

				fansComRoleList = list;
				callback(null);
			});
		}
	], function(err){
		if (!!err)
		{
			logger.info("waterfull error msg", err);
			cb(Code.FAIL, followPageInfo, fansPageInfo);
			return;
		}

		//重新赋值
		followPageInfo.followComRoleList = followComRoleList;
		fansPageInfo.followComRoleList = fansComRoleList;
		cb(Code.OK, followPageInfo, fansPageInfo, player.follow.giveEnergyNum, player.follow.getEnergyNum);
	});
};
//赠送精力		赠送对象uid
PlayerFollowService.prototype.giveFollowEnergy = function(playerId, msg, cb)
{
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player) {
		logger.error('PlayerFollowService.follow: player not exist !', playerId);
		cb(Code.FAIL);
		return;
	}

	let code = this.giveCheckFollowEnergy(playerId, msg);
	if(code !== Code.OK)
	{
		cb(code);
	}
	let uidList = [];
	uidList.push(msg);

	let session = {frontendId: this.app.getServerId()};
	let ssMsg = {
		followUidList: uidList,
		wantFollowUid: playerId,
		wantFollowOpenId: player.openId,
	};
	//logger.error("111111111111111111", msg, ssMsg);
	this.app.rpc.datanode.dataNodeRemote.onlineGiveFollowEnergy(session, ssMsg, function(err, result) {
		if (!!err)
		{
			logger.error("giveFollowEnergy failed!", err);
			cb(Code.FAIL);
			return;
		}
		if(result.code !== Code.OK) {
			logger.error("giveFollowEnergy failed!", result.code);
			cb(result.code);
			return;
		}
		cb(result.code);
	});
};
//检查
PlayerFollowService.prototype.giveCheckFollowEnergy = function(playerId, uid)
{
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	//是否已达赠送次数上限
	if(player.follow.giveEnergyNum >= player.follow.giveEnergyMAX)
	{
		//logger.error("已达今日赠送上限", self.follow.giveEnergyNum);
		return Code.GIVE_ENERGY.GIVE_ENERGY_MAX;//已达今日赠送上限
	}
	//是否以关注对象
	if(!player.follow.followCache.has(uid))
	{
		//logger.error("没有关注这个玩家", uid, self.follow.followCache.has(uid), self.follow.followCache);
		return Code.GIVE_ENERGY.NOT_FOLLOW_PLAYER;//没有关注这个玩家
	}
	else
	{
		for(let [k, v] of player.follow.followCache)
		{
			if(k === uid && !player.follow.isToDaySevenHours(v.giveTime) && v.giveTime !== 0)
			{
				logger.error("今天已经给该玩家送过了", k, v);
				return Code.GIVE_ENERGY.TODAY_GIVE_PLAYER;//今天已经给该玩家送过了
			}
		}
		//logger.error("赠送精力之时", self.follow.cancelList);
		for(let i in player.follow.cancelList)
		{
			if(player.follow.cancelList[i] === uid)
			{
				//logger.error("今天已经给该玩家送过了", uid);
				return Code.GIVE_ENERGY.TODAY_GIVE_PLAYER;//今天已经给该玩家送过了
			}
		}
		//记录下自己赠送的关注者
		player.follow.followCache.get(uid).giveEnergy = 1;
		player.follow.followCache.get(uid).giveTime = player.follow.getDaySevenHours();//TimeUtils.now();
	}
	player.follow.giveEnergyNum += 1;
	//设置任务完成
	player.tasks.triggerTask(commonEnum.TARGET_TYPE.GIVE_ENERGY);
	return Code.OK;
};
//一键赠送精力
PlayerFollowService.prototype.giveAllFollowEnergy = function(playerId, cb)
{
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	let self = this;
	let uidList = [];
	let date = new Date();
	date.setHours(7, 0, 0, 0);

	for(let [k, v] of player.follow.followCache)
	{
		if((!player.follow.isToDaySevenHours(v.giveTime) && v.giveEnergy !== 0) || v.giveTime === 0)
		{
			let code = this.giveCheckFollowEnergy(playerId, k);
			if(code !== Code.OK)
			{
				continue;
			}
			uidList.push(k);
		}
	}
	let session = {frontendId: this.app.getServerId()};
	let ssMsg = {
		followUidList: uidList,
		wantFollowUid: playerId,
		wantFollowOpenId: player.openId,
	};
	self.app.rpc.datanode.dataNodeRemote.onlineGiveFollowEnergy(session, ssMsg, function(err, result) {
		if (!!err)
		{
			logger.error("giveAllFollowEnergy failed!", err);
			cb(null);
			return;
		}
		if(result.code !== Code.OK) {
			logger.error("giveAllFollowEnergy failed!", result.code);
			cb(null);
			return;
		}

		cb(result.code, result.num);
	});

};

//online转发回来实现
PlayerFollowService.prototype.giveEnergy = function(msg, cb)
{
	let uid = msg.followUid;
	let playerId = msg.wantFollowUid;
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(uid);

	if(player)//在线
	{
		let tmp = player.follow.fansCache.get(playerId);
		tmp.giveEnergy = 1;
		tmp.giveTime = player.follow.getDaySevenHours();//TimeUtils.now();
		player.follow.fansCache.set(playerId, tmp);
		player.updateRedDotHintState(commonEnum.REDDOT_HINT.FOLLOW);//推送红点
		cb(Code.OK);
	}
	else//不在线
	{
		let findDbName = [commonEnum.DB_NAME.player, commonEnum.DB_NAME.follow, commonEnum.DB_NAME.offlineEvent];
		playerService.getOtherPlayer(uid, findDbName, function (otherPlayer) {

			let tmp = {
				playerId: playerId,
				giveEnergy: 1,
				giveTime: otherPlayer.follow.getDaySevenHours(),//TimeUtils.now(),
			};

			let offlineEventList = otherPlayer.offlineEvent.getOfflineEventList();//获取离线事件列表
			let obj;
			let isHave = false;
			for(let i in offlineEventList)
			{
				obj = offlineEventList[i];
				if(obj.id === commonEnum.OFFLINE_EVENT_TYPE.FOLLOW_GIVE_ENERGY)
				{
					obj.offlineEventList.push(tmp);
					isHave = true
				}
			}

			if(!isHave)
			{
				obj = {};
				obj.id = commonEnum.OFFLINE_EVENT_TYPE.FOLLOW_GIVE_ENERGY;
				obj.offlineEventList = [];
				obj.offlineEventList.push(tmp);
			}

			offlineEventList.push(utils.deepCopy(obj));
			//更新离线事件
			playerService.playerDao.updateOfflineEvent(uid, offlineEventList, function(err) {
				if (!!err) {
					logger.error("updateOfflineEvent failed!", playerId, uid, offlineEventList);
				}
				cb(Code.OK);
			});
		});
	}
};
//领取精力		领取粉丝的		领取的粉丝的uid
PlayerFollowService.prototype.getFollowEnergy = function(playerId, msg, cb)
{
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	let fansCache = player.follow.fansCache;

	if(player.follow.getEnergyNum >= player.follow.getEnergyMAX)
	{
		return cb(Code.GIVE_ENERGY.GET_ENERGY_MAX);//今日无可领取次数
	}

	if(!fansCache.has(msg))
	{
		return cb(Code.FAIL);//没有可领取的精力
	}

	if(player.follow.getFollowEnergy(msg))//领取
	{
		return cb(Code.OK);
	}

	//否则该玩家没有赠送精力
	return cb(Code.FAIL);
};
//一键领取精力			领取粉丝的
PlayerFollowService.prototype.getAllFollowEnergy = function(playerId, cb)
{
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	let fansCache = player.follow.fansCache;
	let num = 0;//记录领取的数量

	if(player.follow.getEnergyNum >= player.follow.getEnergyMAX)
	{
		return cb(Code.GIVE_ENERGY.GET_ENERGY_MAX, 0);//今日无可领取次数
	}

	for(let [k, v] of fansCache)
	{
		if(player.follow.getFollowEnergy(k))//领取
		{
			num += 1;
		}
	}
	cb(Code.OK, num);
};
//排序
function _record_compare_func(rankObj1, rankObj2)
{
	let messageCount1 = rankObj1.messageCount;
	let messageCount2 = rankObj2.messageCount;

    let onlineStatus1 = rankObj1.onlineStatus;
	let onlineStatus2 = rankObj2.onlineStatus;
	
	let followTime1 = rankObj1.followTime;
	let followTime2 = rankObj2.followTime;

	if (messageCount1 !== messageCount2)
    {
        if (messageCount1 < messageCount2)
        {
            return 1;
        }else if(messageCount1 > messageCount2)
        {
            return -1;
        }
	}
	
    if (onlineStatus1 !== onlineStatus2)
    {
        if (onlineStatus1 > onlineStatus2)
        {
            return 1;
        }else if(onlineStatus1 < onlineStatus2)
        {
            return -1;
        }
    }

	if (followTime1 !== followTime2)
    {
        if (followTime1 < followTime2)
        {
            return 1;
        }else if(followTime1 > followTime2)
        {
            return -1;
        }
	}
	
    return 0;
}

//组装数据
PlayerFollowService.prototype.makeFollowPageList = function(playerId, followOnlineStateList, type, cb)
{
	let onlineStatusMap = new Map();
	for (let index in followOnlineStateList) {
		let data = followOnlineStateList[index];
		onlineStatusMap.set(data.uid, data.status);
	}

	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player)
	{
		logger.error('PlayerFollowService.makeFollowPageList: player not exist !', playerId);
		cb(null, []);
		return;
	}
	player.follow.getEnergyIsPast();
	player.follow.refreshNumTime();
	let self = this;
	let followComRoleList = [];
	let findDbName = [commonEnum.DB_NAME.player, commonEnum.DB_NAME.follow];
	async.eachSeries(followOnlineStateList, function(obj, callback) {
		logger.debug("makeFollowPageList - getOtherPlayer now:", obj.uid);
		playerService.getOtherPlayer(obj.uid, findDbName, function(otherPlayer){
			if(!otherPlayer) {
				player.follow.followCache.delete(obj.uid);
				player.follow.fansCache.delete(obj.uid);
				logger.error("makeFollowPageList.getOtherPlayer: get otherPlayer failed!", obj.uid);
				callback("makeFollowPageList.getOtherPlayer: get otherPlayer failed!");
				return;
			}

			let followTime = 0;
			let giveEnergy = 0;
			let giveTime = 0;
			if (type === commonEnum.FOLLOW_DETAIL_TYPE.FOLLOW)
			{
				followTime = player.follow.getFollowTime(obj.uid);
				giveEnergy = player.follow.getFollowGiveEnergy(obj.uid);
				giveTime = player.follow.getFollowGiveTime(obj.uid);
			}else
			{
				followTime = player.follow.getFansTime(obj.uid);
				giveEnergy = player.follow.getFansGiveEnergy(obj.uid);
				giveTime = player.follow.getFansGiveTime(obj.uid);
			}

			let onlineStatus = onlineStatusMap.get(obj.uid);
			let followComRoleInfo = {
				playerUid: obj.uid,
				name: otherPlayer.name,
				level: otherPlayer.level,
				faceUrl: otherPlayer.faceUrl,
				vip: otherPlayer.vip,
				followTime: followTime,
				giveEnergy: giveEnergy,
				giveTime: giveTime,
				onlineStatus: onlineStatus,
				messageCount: 0,
			};
			followComRoleList.push(followComRoleInfo);
			callback(null);
		});
    }, function(err) {
        if (!!err) 
        {
			logger.error("foreachFuncService failed");
			cb(err, followComRoleList);
            return;
		}

		//排序
		followComRoleList.sort(_record_compare_func);
		cb(null, followComRoleList);
    });
};

//获取指定页数数据
PlayerFollowService.prototype.getFollowDetail = function(playerId, msg, cb)
{
	let comPageInfo = {};
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player)
	{
		logger.error('PlayerFollowService.getFollowDetail: player not exist !', playerId);
		cb(Code.FAIL, 0, comPageInfo);
		return;
	}

	let typeId = msg.typeId;
	if (!typeId)
	{
		logger.error('PlayerFollowService.getFollowDetail: typeId not exist !', playerId, msg);
		cb(Code.FAIL, 0, comPageInfo);
		return;
	}

	let page = msg.page;
	if (!page)
	{
		logger.error('PlayerFollowService.getFollowDetail: page not exist !', playerId, msg);
		cb(Code.FAIL, 0, comPageInfo);
		return;
	}

	//每页最大记录数量
	let maxResult = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.FollowMaxResult].Param;
	let playerUidList = [];
	if (typeId === commonEnum.FOLLOW_DETAIL_TYPE.FOLLOW)
	{
		playerUidList = player.follow.getLastFollowUidList();
	}else if(typeId === commonEnum.FOLLOW_DETAIL_TYPE.FANS)
	{
		playerUidList = player.follow.getLastFansUidList();
	}

	//1.分页
	let totalRecord = playerUidList.length;
	let totalPage = this.getTotalPageCount(maxResult, totalRecord);
	if (page > totalPage) //超过当前最大页数
	{
		logger.error('PlayerFollowService.getFollowDetail: page more than totalPage !', playerId, msg);
		cb(Code.FAIL, typeId, comPageInfo);
		return;
	}

	comPageInfo.followComRoleList = [];
	let currPageList = this.getUidListByPage(page, maxResult, playerUidList);
	let self = this;
	let ssMsg ={};
	let session = {frontendId: this.app.getServerId()};
	async.waterfall([
		function (callback) {
			ssMsg.playerIdList = currPageList;
			self.app.rpc.datanode.dataNodeRemote.getOnlineStateByList(session, ssMsg, function(err, result) {
				if (!!err)
				{
					logger.error("getOnlineStateByList: catch a error!", err);
					callback(err);
					return;
				}

				if(result.code !== Code.OK) {
					logger.error("getOnlineStateByList failed!", result.code);
					callback("getOnlineStateByList failed!");
					return;
				}

				let followOnlineStateList = result.onlineStateList;
				callback(null, followOnlineStateList);
			});
		},
		function (onlineStateList, callback) {
			self.makeFollowPageList(playerId, onlineStateList, typeId, function(err, list) {
				if (!!err)
				{
					logger.error("makeFollowPageList FOLLOW error!", err);
					callback(err);
					return;
				}
				comPageInfo.followComRoleList = list;
				callback(null);
			});
		},
	], function(err){
		if (!!err)
		{
			logger.info("waterfull error msg", err);
			cb(Code.FAIL);
			return;
		}
		comPageInfo.totalRecord = totalRecord;
		comPageInfo.totalPage = totalPage;
		comPageInfo.maxResult = maxResult;
		comPageInfo.currPage = page;
		cb(Code.OK, typeId, comPageInfo);
	});
};

PlayerFollowService.prototype.follow = function(playerId, msg, cb) 
{
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player) {
		logger.error('PlayerFollowService.follow: player not exist !', playerId);
		cb(Code.FAIL);
		return;
	}
	let followUid = msg.uid;
	if (!followUid) {
		logger.error("PlayerFollowService.follow: followUid is error", playerId, msg);
		cb(Code.FAIL);
		return;
	}
	//不允许关注自己
	if (followUid === playerId) {
		logger.error("PlayerFollowService.follow: followUid and playerId is some", playerId, msg);
		cb(Code.FOLLOW_ERROR_CODE.NOT_ALLOW_FOLLOW_SELF);
		return;
	}
	//检查对方是否已经被关注过了
	if (player.follow.getFollowTime(followUid)) {
		logger.error("PlayerFollowService.follow: followUid is already follow, not allow repeated follow!", playerId, msg);
		cb(Code.FOLLOW_ERROR_CODE.PLAYER_ALREADY_EXIST_FOLLOW_LIST);
		return;
	}
	let followList = player.follow.getLastFollowUidList();
	let followMaxCount = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.FollowMaxCount].Param;
	let fansMaxCount = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.FansMaxCount].Param;
	//followMaxCount = 2;
	//fansMaxCount = 1;
	//检查是否达到关注上限
	logger.info("PlayerFollowService follow", followMaxCount, fansMaxCount,followList.length );
	if (followList.length > followMaxCount)
	{
		logger.error("PlayerFollowService.follow: the follow count is reach max, not allow add!", playerId, msg);
		cb(Code.FOLLOW_ERROR_CODE.FOLLOW_COUNT_REACH_MAX_NOT_FOLLOW);
		return;
	}

	let self = this;
	let ssMsg = {};
	let session = {frontendId: this.app.getServerId()};
	//通知online服务器处理关注
	ssMsg.followUid = followUid;
	ssMsg.wantFollowUid = playerId;
	ssMsg.wantFollowOpenId = player.openId;
	let findDbName = [commonEnum.DB_NAME.player, commonEnum.DB_NAME.follow];
	async.waterfall([
		function(callback) {
			logger.debug("follow - getOtherPlayer now:", followUid);
			playerService.getOtherPlayer(followUid, findDbName, function(otherPlayer){
				if(!otherPlayer)
				{
					logger.error("makeFollowPageList.getOtherPlayer: get otherPlayer failed!", followUid);
					callback("makeFollowPageList.getOtherPlayer: get otherPlayer failed!");
					return;
				}
				let followTime = otherPlayer.follow.getFansTime(playerId);//对方被关注列表有他
				let selfFollowTime = player.follow.getFollowTime(followUid);//自己的关注列表也有对方
				if ((followTime && followTime > 0) && (selfFollowTime && selfFollowTime > 0))
				{
					logger.info("the player alreay in other fans list, please check again!", playerId, followUid);
					callback(Code.FOLLOW_ERROR_CODE.PLAYER_ALREADY_EXIST_FOLLOW_LIST);
					return;
				}
				//对方粉丝达到上限
				let fansCount = otherPlayer.follow.getLastFansUidList();
				if (fansCount > fansMaxCount)
				{
					logger.info("fanscount reach max, not allow follow!", playerId, followUid, fansCount, fansMaxCount);
					callback(Code.FOLLOW_ERROR_CODE.FANS_COUNT_REACH_MAX_NOT_FOLLOW);
					return;
				}
				logger.info("follow", playerId, followUid, followTime);
				ssMsg.followOpenId = otherPlayer.openId;
				callback(null);
			});
		},
		function(callback) {
			self.app.rpc.datanode.dataNodeRemote.onlineFollow(session, ssMsg, function(err, result) {
				if (!!err)
				{
					logger.error("onlineFollow failed!", err);
					callback(Code.FAIL);
					return;
				}
				if(result.code !== Code.OK) {
					logger.error("onlineFollow failed!", result.code);
					callback(result.code);
					return;
				}
				callback(null);
			});
		}
	], function(err) {
		if (!!err)
		{
			logger.error("follow: error!", err);
			let retCode = Code.FAIL;
			switch (err) {
				case Code.FOLLOW_ERROR_CODE.PLAYER_ALREADY_EXIST_FOLLOW_LIST:
					retCode = Code.FOLLOW_ERROR_CODE.PLAYER_ALREADY_EXIST_FOLLOW_LIST;
					break;
				case Code.FOLLOW_ERROR_CODE.FOLLOW_COUNT_REACH_MAX_NOT_FOLLOW:
					retCode = Code.FOLLOW_ERROR_CODE.FOLLOW_COUNT_REACH_MAX_NOT_FOLLOW;
					break;
				case Code.FOLLOW_ERROR_CODE.FANS_COUNT_REACH_MAX_NOT_FOLLOW:
					retCode = Code.FOLLOW_ERROR_CODE.FANS_COUNT_REACH_MAX_NOT_FOLLOW;
					break;
				default:
					break;
			}
			cb(retCode);
			return;
		}
		//添加关注信息
		player.follow.follow(followUid);
		self.updateFollowDetail(playerId, commonEnum.FOLLOW_DETAIL_TYPE.FOLLOW, 1);//发送通知
		player.saveFollow();
		//self.followHttpRequestToDqd(ssMsg.wantFollowOpenId, ssMsg.followOpenId, 1, function (code) {});
		cb(Code.OK);
	});
};

PlayerFollowService.prototype.unFollow = function(playerId, msg, cb) 
{
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!player) {
		logger.error('PlayerFollowService.unFollow: player not exist !');
		cb(Code.FAIL);
		return;
	}
	let unFollowUid = msg.uid;
	if (!unFollowUid) {
		logger.error("PlayerFollowService.unFollow: unFollowUid is error", playerId, msg);
		cb(Code.FAIL);
		return;
	}
	//不允许取消关注自己
	if (unFollowUid === playerId) {
		logger.error("PlayerFollowService.unFollow: followUid and playerId is some", playerId, msg);
		cb(Code.FOLLOW_ERROR_CODE.NOT_ALLOW_UNFOLLOW_SELF);
		return;
	}
	//检查取消关注的玩家是否存在
	if (!player.follow.getFollowTime(unFollowUid)) {
		logger.error("PlayerFollowService.unFollow: unFollowUid is not found!", playerId, unFollowUid, msg);
		cb(Code.FOLLOW_ERROR_CODE.UNFOLLOW_PLAYER_NOT_FOUND);
		return;
	}
	let self = this;
	let ssMsg = {};
	ssMsg.unFollowUid = unFollowUid;
	ssMsg.wantUnFollowUid = playerId;
	ssMsg.wantUnFollowOpenId = player.openId;
	let findDbName = [commonEnum.DB_NAME.player, commonEnum.DB_NAME.follow];
	async.waterfall([
		function(callback) {
			logger.debug("unFollow - getOtherPlayer now:", unFollowUid);
			playerService.getOtherPlayer(unFollowUid, findDbName, function(otherPlayer){
				if(!otherPlayer)
				{
					logger.error("unFollow getOtherPlayer fail !", unFollowUid);
					return callback({msg: "unFollow getOtherPlayer fail", code: Code.FAIL});
				}
				logger.info("unFollow: ", playerId, unFollowUid);
				ssMsg.unFollowOpenId = otherPlayer.openId;
				callback(null);
			});
		},
		function(callback) {
			let session = {frontendId: self.app.getServerId()};
			//通知online服务器处理关注
			self.app.rpc.datanode.dataNodeRemote.onlineUnFollow(session, ssMsg, function(err, result) {
				if (!!err) {
					logger.error("onlineUnFollow 111 failed!", err);
					return callback({msg: err, code: Code.FAIL});
				}
				if(result.code !== Code.OK) {
					logger.error("onlineUnFollow 222 failed!", result.code);
					return callback({msg: err, code: result.code});
				}
				callback(null);
			});
		}
	], function(err) {
		if (!!err)
		{
			logger.error("unFollow: error!", err);
			return cb(err.code);
		}
		//to do 这里需要计算 followUid所在的页数
		//取消关注玩家
		player.follow.unFollow(unFollowUid);
		self.updateFollowDetail(playerId, commonEnum.FOLLOW_DETAIL_TYPE.FOLLOW, 1);
		player.saveFollow();
		//self.followHttpRequestToDqd(ssMsg.wantUnFollowOpenId, ssMsg.unFollowOpenId, 0, function (code) {});
		cb(Code.OK);
	});
};

//由于架构设计的原因，这里代码是用来处理online发过来的消息
//消息的结构 follow(game) ->onlineFollow(online)（找出玩家所在的游戏服）->onlineProcessFollow(game)对方游戏进行处理
//关注只会影响到粉丝的数量
PlayerFollowService.prototype.onlineProcessFollow = function(msg, cb) 
{
	let playerId = msg.followUid;
	let wantFollowUid = msg.wantFollowUid;
	if (!playerId)
	{
		logger.error("PlayerFollowService.onlineProcessFollow: not playerId", msg);
		cb(Code.FAIL);
		return;
	}

	if (!wantFollowUid)
	{
		logger.error("PlayerFollowService.onlineProcessFollow: not wantFollowUid", msg);
		cb(Code.FAIL);
		return;
	}

	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!!player) //玩家在线
	{
		logger.info('PlayerFollowService.onlineProcessFollow: player online!', playerId);
		//添加粉丝数
		player.follow.addFan(wantFollowUid);//被关注的玩家添加粉丝数量
		//被取消关注玩家不需要发送消息
		if (player.isOnline()) 
		{
			this.updateFollowDetail(playerId, commonEnum.FOLLOW_DETAIL_TYPE.FANS, 1);
			let self = this;
			logger.debug("onlineProcessFollow - getOtherPlayer now:", wantFollowUid);
			let findDbName = [commonEnum.DB_NAME.player, commonEnum.DB_NAME.follow];
			playerService.getOtherPlayer(wantFollowUid, findDbName, function (otherPlayer) {
				if (!otherPlayer) {
					logger.error("makeFollowPageList.getOtherPlayer: get otherPlayer failed!", wantFollowUid);
					return;
				}

				let chatMsg = {};
				chatMsg.msg = otherPlayer.name + "已经关注了您!";
				self.broadcastChatSystemMsg(chatMsg, player);
				player.saveFollow();
				cb(Code.OK);
			});
		} else {
			player.saveFollow();
			cb(Code.OK);
		}
	}else {
		let record = {
			uid: wantFollowUid,
			followTime: TimeUtils.now(),
			giveEnergy: 0,
			giveTime: 0
		};
		playerService.playerDao.addOneFollowRecord(playerId, record, function(err) {
			if (!!err)
			{
				logger.error("addOneFollowRecord failed!", playerId, record);
				cb(Code.FAIL);
				return;
			}

			logger.info("addOneFollowRecord", playerId, record);
			cb(Code.OK);
		});
	}
};

PlayerFollowService.prototype.onlineProcessUnFollow = function(msg, cb) 
{
	let playerId = msg.unFollowUid;
	let wantUnFollowUid = msg.wantUnFollowUid;
	if (!playerId)
	{
		logger.error("PlayerFollowService.onlineProcessFollow: not playerId", msg);
		cb(Code.FAIL);
		return;
	}
	if (!wantUnFollowUid)
	{
		logger.error("PlayerFollowService.onlineProcessFollow: not wantUnFollowUid", msg);
		cb(Code.FAIL);
		return;
	}
	let playerService = this.app.get("playerService");
	var player = playerService.getPlayer(playerId);
	if(!!player) //玩家在线
	{
		logger.info('PlayerFollowService.onlineProcessFollow: player online!', playerId);
		//添加粉丝数
		player.follow.delFan(wantUnFollowUid);//被关注的玩家添加粉丝数量
		if (player.isOnline())
		{
			this.updateFollowDetail(playerId, commonEnum.FOLLOW_DETAIL_TYPE.FANS, 1);	
		}
		player.saveFollow();
		cb(Code.OK);
	}else {   //离线
		let record = {
			uid: wantUnFollowUid,
			followTime: TimeUtils.now(),
		};
		playerService.playerDao.deleteOneFollowRecord(playerId, record, function(err) {
			if (!!err)
			{
				logger.error("deleteOneFollowRecord failed!", playerId, record);
				cb(Code.FAIL);
				return;
			}

			logger.info("deleteOneFollowRecord", playerId, record);
			cb(Code.OK);
		});
	}
};

//更新关注或者粉丝数据
PlayerFollowService.prototype.updateFollowDetail = function(playerId, typeId, page)
{
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	//这里只需要给在线的玩家发送信息
	if (!!player && player.isOnline())  
	{
		let comPageInfo = {};
		//每页最大记录数量
		let maxResult = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.FollowMaxResult].Param;
		let playerUidList = [];
		if (typeId === commonEnum.FOLLOW_DETAIL_TYPE.FOLLOW)
		{
			playerUidList = player.follow.getLastFollowUidList();
		}else if(typeId === commonEnum.FOLLOW_DETAIL_TYPE.FANS)
		{
			playerUidList = player.follow.getLastFansUidList();
			logger.info("updateFollowDetail getLastFansUidList", playerUidList);
		}
	
		//1.分页
		let totalRecord = playerUidList.length;
		logger.info("updateFollowDetail", playerUidList, totalRecord);
		let totalPage = this.getTotalPageCount(maxResult, totalRecord);
		comPageInfo.totalRecord = totalRecord;
		comPageInfo.totalPage = totalPage;
		comPageInfo.maxResult = maxResult;
		comPageInfo.currPage = page;
		comPageInfo.followComRoleList = [];
		if (totalPage === 0)
		{
			comPageInfo.currPage = 1; //没有数据默认发第一页
			this.app.get("pushMessageService").unicast("game.playerHandler.updateFollowDetail", { 
				typeId: typeId, comPageInfo: comPageInfo}, player);
			logger.info("updateFollowDetail");
			return;
		}

		if (page > totalPage) //超过当前最大页数
		{
			logger.error('PlayerFollowService.getFollowDetail: page more than totalPage !', playerId, typeId, page, totalPage);
			return;
		}

		let currPageList = this.getUidListByPage(page, maxResult, playerUidList);
		let self = this;
		let ssMsg ={};
		let session = {frontendId: this.app.getServerId()};
		async.waterfall([
			function (callback) {
				ssMsg.playerIdList = currPageList;
				self.app.rpc.datanode.dataNodeRemote.getOnlineStateByList(session, ssMsg, function(err, result) {
					if (!!err)
					{
						logger.error("getOnlineStateByList: catch a error!", err);
						callback(err);
						return;
					}
	
					if(result.code !== Code.OK) {
						logger.error("getOnlineStateByList failed!", result.code);
						callback("getOnlineStateByList failed!");
						return;
					}
	
					let followOnlineStateList = result.onlineStateList;
					callback(null, followOnlineStateList);
				});
			},
			function (onlineStateList, callback) {
				self.makeFollowPageList(playerId, onlineStateList, typeId, function(err, list) {
					if (!!err)
					{
						logger.error("makeFollowPageList FOLLOW error!", playerId, err);
						callback(err);
						return;
					}
					comPageInfo.followComRoleList = list;
					callback(null);
				});
			},
		], function(err){
			if (!!err)
			{
				logger.info("PlayerFollowService.updateFollowDetail: waterfull error msg", err);
				return;
			}

			self.app.get("pushMessageService").unicast("game.playerHandler.updateFollowDetail", { 
				typeId: typeId, comPageInfo: comPageInfo}, player);
		});
	}
};

PlayerFollowService.prototype.followHttpRequestToDqd = function (wantFollowOpenId, followOpenId, action, cb) 
{
	//"?app_id=20044&open_id=253ca24692402d4e251bfef81ee9d15c&favour_open_id=k01&action=1&sign=4rhfqqoqdjljd";
	let getUrl = clusterConfig.dqd_http_url + "/api/gameFavor" + "?app_id=" + clusterConfig.app_id
		+ "&open_id=" + wantFollowOpenId + "&favour_open_id=" + followOpenId + "&action=" + action + "&sign=";
	let signObj = {app_id: clusterConfig.app_id, open_id: wantFollowOpenId, favour_open_id: followOpenId, action: action};
	let sign = utils.getCommonMd5Sign(signObj, clusterConfig.api_secret_key);
	getUrl += sign;
	logger.debug("followHttpRequestToDqd getUrl: ", getUrl);
	let req = http.get(getUrl, function(res){
		res.setEncoding("utf-8");
		res.on("data",function(data){
			let retObj = JSON.parse(data.toString());
			logger.debug('data: ', data.toString(), retObj);
			if(retObj.code === "0") {
				cb(Code.OK);
			}
			else {
				logger.error("followHttpRequestToDqd fail. wantFollowOpenId, followOpenId, action, sign, errMsg: ",
					wantFollowOpenId, followOpenId, action, sign, retObj.message);
				cb(Code.FAIL);
			}
		});
	});

	req.on("error",function(err){
		logger.error("followHttpRequestToDqd, http get err: ", err.message);
		cb(Code.FAIL);
	});
	req.end();
};

//这里没有使用playerService接口(使用了广播)，只是单播
PlayerFollowService.prototype.broadcastChatSystemMsg = function (msg, player) 
{
	msg.channel = commonEnum.CHAT_CHANNEL.SYSTEM_CHANNEL;
	msg.senderName = "系统";
	msg.type = 0;
	let oneMsg = this.app.get("playerService").makeChatMsgToClient(msg);
	this.app.get("pushMessageService").unicast("game.playerHandler.updateChat", {oneMsg: oneMsg}, player);
};