/**
 * Idea and Persist
 * Created by <PERSON> on 2019/4/9.
 */
var logger = require('pomelo-logger').getLogger('pomelo', __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var TimeUtils = require('../../util/timeUtils');
var leagueEvent = require('../event/leagueEvent');
var LeagueFsm = require("../entities/leagueFsm");
var async = require('async');
var dataApi = require('../../util/dataApi');
var Constant = require("../../../../shared/constant");
var commonEnum = require('../../../../shared/enum');

module.exports.create = function(app, dbclient) {
    return new LeagueService(app, dbclient);
};

let LeagueService = function(app, dbclient, cb){
    EventEmitter.call(this);
    this.app = app;
	this.leagueFsm = {};
	this.onlineLeague = new Map(); //uid => league
    //Db init
	this.matchDao = require("../../dao/matchDao").create(dbclient);
	//this.init(this, cb);
};

util.inherits(LeagueService, EventEmitter);

LeagueService.prototype.init = function()
{
	const mode = 0;
	let seasonId = 0;
	let self = this;
	async.waterfall([
		function(callback){
			if (mode <= 0)
			{
				callback(null);
				return;
			}
			self.matchDao.clearDB(function(err) { //估计uid为1
				callback(null);
			});
		},
		function(callback) {
			self.matchDao.getLeagueSeasonId(function(err, id) {
				if(!!err){
					logger.error("readLeague:", id, err);
					return callback(err);
				}
				//logger.error("xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", id);
				seasonId = id;
				if (!seasonId ) {//查找不到数据的话,默认赛季从1开始
					seasonId = 1;
					logger.info("afterAllServerStartUp: not found season data, default season 1 begin!");
				}
				callback(null);
			});
		},
		function(callback) {
			self.loadLeagueData(seasonId, function(err, league){
				if (!!err)
				{
					logger.error("load league data failed", seasonId);
					return callback("load league data failed " + seasonId);
				}

				self.onlineLeague.set(seasonId, league);
				//logger.error("afterAllServerStartUp loadLeagueData ok:-----------", seasonId);
				callback(null);
			});
		}
	], function(error) {
		if(error){
			logger.error("afterAllServerStartUp", error);
			return;
		}
		self.leagueFsm.start();
		self.app.set("initLeagueDBState", true);
		logger.info("afterAllServerStartUp all finished!");
	});
};

/*
let afterAllServerStartUp = function() {

};
*/

LeagueService.prototype.getProfession = function(seasonId, cb)
{
	logger.info("getProfession _____________", seasonId);
	this.matchDao.find("profession", seasonId, function(err, doc) {
		if(!!err)
		{
			return cb("find profession error");
		}

		logger.info("find", seasonId);
		return cb(null, doc);
	});
};

LeagueService.prototype.loadLeagueData = function(seasonId, cb) {
	let self = this;
	logger.info('---------- loadLeagueData -------------seasonId', seasonId);
	async.waterfall([
        function(callback){
            self.matchDao.readWholeSeasonFsm(1, function(res, isNewRegister){ //估计uid为1
				if(!res)
				{
					return callback(null);
				}

				let leagueFsm = new LeagueFsm(1 *1000, self.app, self);
				if(isNewRegister) 
				{
					//logger.debug('initByConfig');
					leagueFsm.init(); //数据初始化
				}
				else
				{
					leagueFsm.initByDB(res.leagueFsm);
				}

				self.leagueFsm = leagueFsm;
                callback(null);
            });
        },
        function(callback){
			self.matchDao.readWholeSeasonNoFsm(seasonId, function(res, isNewRegister){
				if(!res)
				{
					return callback(null);
				}

				if(isNewRegister) {
					//logger.debug('initByConfig ........', isNewRegister);
				}else {
					//logger.debug('initByDB ____');
					//logger.debug("res.community", res);
					self.leagueFsm.league.community.initByDB(res.community);
					self.leagueFsm.league.normal.initByDB(res.normal);
					self.leagueFsm.league.knockout.initByDB(res.knockout);
					self.leagueFsm.league.profession.initByDB(res.profession);
					self.leagueFsm.enrollTime.initByDB(res.enrollTime);
				}
		
				//logger.debug('---------- load league ------------- event', seasonId, isNewRegister);
				//添加事件
				leagueEvent.addEvent(self.app, self.leagueFsm);
				callback(null);
			});
        },
		function (callback) {
			self.matchDao.getLeagueChampion(seasonId, function(res, playerUidList){
				if(!res)
				{
					return callback(null);
				}
				//logger.error("历届冠军名单：", res, playerUidList);
				callback(null, res, playerUidList);
			});
		},
		function (res, playerUidList, callback) {
			self.app.rpc.datanode.dataNodeRemote.getOtherPlayerListSimpleDB({frontendId: self.app.getServerId()}, {uidList: playerUidList, myGid: self.app.getServerId()}, function (result) {
				callback(null, res, result);
			})
		},
		function (res, result, callback) {
			let i = 0;
			for(let [k, v] of res)
			{
				let tmpList = [];
				for(let p in v)
				{
					if(v[p].championUid !== result[i].uid)
						continue;
					let tmp = {typeId: v[p].typeId, championUid: v[p].championUid, Name: result[i].Name, FaceUrl: result[i].FaceUrl};
					tmpList.push(utils.deepCopy(tmp));
					i++;
				}
				self.leagueFsm.leagueChampionRecord.set(k, utils.cloneArray(tmpList));
			}
			//logger.error("历届冠军名单", self.leagueFsm.league.uid, self.leagueFsm.leagueChampionRecord);
			callback(null);
		}
    ], function(error){
        if(error){
            return cb(error);
        }
		cb(null, self.leagueFsm.league);
	});
};

//读取新的赛季信息
LeagueService.prototype.reLoadLeagueData =  function(seasonId, cb)
{
	let self = this;
	//logger.info('---------- reLoadLeagueData -------------seasonId', seasonId);
	this.matchDao.readWholeSeasonNoFsm(seasonId, function(res, isNewRegister){
		if(!res){
			return cb(null);
		}
		self.leagueFsm.loadLeague(seasonId);
		leagueEvent.addEvent(self.app, self.leagueFsm); //重新加载league时间
		logger.debug('---------- load league ------------- event', seasonId, isNewRegister);
		return cb(null);
	});
};

//获取冠军的name faceUrl
LeagueService.prototype.getLeagueChampionSimpleInfo =  function(uidList, cb)
{
	//logger.error("获取冠军的name faceUrl", uidList);
	let self = this;
	self.app.rpc.datanode.dataNodeRemote.getOtherPlayerListSimpleDB({frontendId: self.app.getServerId()}, {uidList: uidList, myGid: self.app.getServerId()}, function (result) {
		cb(result);
	})
};

//得到联赛历届冠军的信息
LeagueService.prototype.getLeagueChampion = function(typeId, cb)
{
	//logger.error("获取冠军的name faceUrl", uidList);
	let self = this;
	//[{Name faceUrl Num}]
	let ChampionList = [];
	let ChampionRecordList = self.leagueFsm.leagueChampionRecord;
	//logger.error("!!!!!!!!!!!!!!!!!!!!!!!", self.leagueFsm.leagueChampionRecord);
	for(let [k, v] of ChampionRecordList)
	{
		for(let i in v)
		{
			if(v[i].typeId === typeId)
			{
				let tmp = {seasonId: k, playerUid: v[i].championUid, Name: v[i].Name, faceUrl: v[i].FaceUrl};
				ChampionList.push(utils.deepCopy(tmp));
			}
		}

	}
	//logger.error("111111111111111", ChampionList);
    cb(Code.OK, ChampionList);
};

LeagueService.prototype.getLeague = function(uid) {
	return this.onlineLeague.get(uid);
};

LeagueService.prototype.SSGetLeagueSeasonId = function(cb) {
	let seasonId = 0;
	this.matchDao.getLeagueSeasonId(function(err, id) {
		if(!!err){
			logger.error("readLeague:", id, err);
			return cb(err);
		}
		seasonId = id;
		if (!seasonId ) {//查找不到数据的话,默认赛季从1开始
			seasonId = 1;
			logger.info("afterAllServerStartUp: not found season data, default season 1 begin!");
		}
		cb(seasonId);
		return;
	});
};

//获取报名信息
LeagueService.prototype.SSGetLeagueEnroll = function(playerId, msg, cb)
{
	//logger.info("SSGetLeagueEnroll",playerId, msg);
	let status = commonEnum.LEAGUE_ENROLL_STATUS.ENROLL_BEGIN;
	let beginTime = 0;
	let endTime = 0;
	let fsmData = this.leagueFsm.getGlobalSysData();
	let global_sys_data = fsmData.global_sys_data;
	let enroll_begin_time = global_sys_data.enroll_begin_time;
	//let enroll_end_time = global_sys_data.enroll_end_time;
	let curr_status = global_sys_data.curr_status;
	let curr_begintime = global_sys_data.next_game_init_time;
	let status_obj = this.leagueFsm.getStatus(curr_status);
	let enroll_end_time = curr_begintime + status_obj.next_time;

	if (curr_status <= Constant.STATE.ENROLL_BEGIN)
	{
		status = commonEnum.LEAGUE_ENROLL_STATUS.ENROLL_BEGIN;
		//赛程详细时间 note: 这是只是为了取报名时间 和LeagueFsm的变量没有关系
		var config = dataApi.allData.data["LeagueSwitch"][1];
		if (config) 
		{
			let openTime = new Date(config.OpenTime).getTime();
			let enrollTimeBegin = this.leagueFsm.scheduleDetail.getEnrollStart();
			let enrollTimeEnd = this.leagueFsm.scheduleDetail.getEnrollEnd();
			//enroll_begin_time = openTime + enrollTimeBegin;
			enroll_end_time = openTime + enrollTimeEnd;
			enroll_begin_time = curr_begintime + status_obj.next_time;
			//logger.error("报名时间：一个赛季多少天：%d天, 赛季：第%d赛季, 时差：%d, 第一场开服时间：%d", day, this.leagueFsm.seasonId, timeLag, enroll_begin_time, enrollTimeBegin, curr_begintime + status_obj.next_time);
		}
	}else if (curr_status <= Constant.STATE.ENROLL_OVER)
	{
		status = commonEnum.LEAGUE_ENROLL_STATUS.ENROLL_RUNNING;
	}else
	{
		status = commonEnum.LEAGUE_ENROLL_STATUS.ENROLL_OVER;
	}

	let isEnroll = 0;
	if (this.leagueFsm.league.checkEnroll(playerId))
	{
		isEnroll = 1;
		beginTime = curr_begintime + this.leagueFsm.scheduleDetail.scheduleDetailMap.get(Constant.STATE.COMMUNITY_ROUND1_RUNNING);//第一场比赛时间的弹窗通知
		endTime = enroll_end_time;
		cb(Code.OK, status, beginTime, endTime, isEnroll);
		return;
	}
	if(enroll_begin_time === 0)
	{
		let leagueSwitch = dataApi.allData.data["LeagueSwitch"]['1'].OpenTime;
		let leagueSchedule = dataApi.allData.data["LeagueSchedule"]['1'];
		let leagueTime = dataApi.allData.data["LeagueTime"];
		let Hour = 0;
		let Minutes = 0;
		let Seconds = 0;
		for(let i in leagueTime)
		{
			if(leagueTime[i].GroupId === leagueSchedule.GroupId)
			{
				Hour = leagueTime[i].Hour;
				Minutes = leagueTime[i].Minutes;
				Seconds = leagueTime[i].Seconds;
			}
		}
		leagueSwitch = leagueSwitch + " " + Hour + ":" + Minutes + ":" + Seconds;
		enroll_begin_time = new Date(leagueSwitch).getTime();
	}

	beginTime = enroll_begin_time;
	endTime = enroll_end_time;
	cb(Code.OK, status, beginTime, endTime, isEnroll);
	return;
};

//玩家报名
LeagueService.prototype.SSLeagueEnroll = function(playerId, msg, cb)
{
	let playerObj = msg.playerObj;
	if (!playerObj)
	{
		logger.error("SSLeagueEnroll: not playerObj", playerId);
		cb(Code.FAIL);
		return;
	}

	let code = this.leagueFsm.league.Enroll(playerObj);
	if (code != Code.OK)
	{
		cb(code);
		return;
	}

	logger.info("SSLeagueEnroll: playerId", playerId);
	cb(Code.OK);
	return;
};

//检查玩家是否已经存在专业联赛，存在专业联赛不用报名
LeagueService.prototype.checkIsProfessionJoiner = function(playerId, cb)
{
	cb(this.leagueFsm.league.isProfessionJoiner(playerId));
};

//检查玩家是否已经参加联赛
LeagueService.prototype.checkIsLeagueJoiner = function(playerId, cb)
{
	if(this.leagueFsm.league.isProfessionJoiner(playerId) || this.leagueFsm.league.isCommunityJoiner(playerId) || this.leagueFsm.league.isNormalJoiner(playerId) || this.leagueFsm.league.checkEnroll(playerId))
	{
		cb(true);
		return
	}
	cb(false);
};

//玩家玩家是否有资格加入比赛
LeagueService.prototype.checkPlayerCanJoin = function(openId)
{
	let canJoin = false;
	if (!openId)
	{
		return canJoin;
	}

	let accountConfig = dataApi.allData.data["Account"];
	for(let idx in accountConfig)
	{
		let row = accountConfig[idx];
		if (openId === row.OpenId && row.Level > 0 && row.ActualStrength > 0)
		{
			canJoin = true;
			break;
		}
	}

	return canJoin;
};

//开关状态
LeagueService.prototype.SSGetFuncSwitch = function(playerId, msg, cb)
{
	let status = commonEnum.LEAGUE_STATUS.CLOSE;
	let openTime = 0;
	var config = dataApi.allData.data["LeagueSwitch"][1];
	if (!config) 
	{
        logger.error("SSGetFuncSwitch: leagueSwitch Config not found!");
        return cb(Code.FAIL, status, openTime);
	}
	
	let openId = msg.openId;
	if (!openId)
	{
		logger.error("SSGetFuncSwitch: openId not found!", playerId, msg);
        return cb(Code.FAIL, status, openTime);
	}

    let curr_time = TimeUtils.now();
	openTime = new Date(config.OpenTime).getTime(); //转化为timestamp时间戳
	//logger.info("SSGetFuncSwitch", config.OpenTime, openTime);
	if (curr_time >= openTime)
	{
		status = commonEnum.LEAGUE_STATUS.OPEN; //系统开始
	}else
	{
		//玩家删档测试玩家	
		if (this.checkPlayerCanJoin(openId))
		{
			if (!this.leagueFsm.hasFirstSeasonJoinPlayer(playerId))
			{
				let obj = 
				{
					playerId: playerId,
					openId: openId,
					joinTime: TimeUtils.now(),
				};

				this.leagueFsm.setFirstSeasonJoinPlayer(playerId, obj);
				this.leagueFsm.saveWhole(); //保存数据
				logger.warn("SSGetFuncSwitch: new add season join player! playerId, openId ", playerId, openId);
			}else
			{
				logger.warn("SSGetFuncSwitch: already add season join player! playerId, openId ", playerId, openId);
			}
		}else 
		{
			logger.warn("SSGetFuncSwitch: not condition! playerId, openId", playerId, openId);
		}
	}

	cb(Code.OK, status, openTime);
	return;
};

//界面展示数据
LeagueService.prototype.SSGetCurrLeagueData = function(playerId, msg, cb)
{
	logger.info("SSGetCurrLeagueData______________", playerId, msg);
	let failedRet = {
		typeId: 0,
		roundId: 0,
		name: "",
		groupId: 0,
		finalRound: 0,
		maxGroupCount: 0,
		scheduleTime: 0,
		maxRound: 0,
		scheduleList: [],
	};

	let seasonId = this.leagueFsm.getSeasonId();
	if (!playerId)
	{
		logger.error("SSGetCurrLeagueData: not playerId", playerId);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.SUPER;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.SUPER);
		cb(Code.FAIL, seasonId, failedRet, 0, 0);
		return;
	}

	let curr_status = this.leagueFsm.getCurrStatus();
    if (curr_status <= Constant.STATE.NOT_USE_2)
    {
		//比赛开始前
		logger.info("SSGetCurrLeagueData: system isn't can access!",  curr_status);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.AMATEUR;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.AMATEUR);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet, 0, 0);
		return;
    }

    //先确定当前比赛进程，在确定玩家身份
    let typeId = this.leagueFsm.league.getTypeIdByStatus(curr_status);
    if (typeId <= 0)
    {
		logger.info("SSGetCurrLeagueData: system isn't can access!",  typeId);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.AMATEUR;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.AMATEUR);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet, 0, typeId);
		return;
	}
	let isJoin = 0;
    if(this.leagueFsm.league.isCommunityJoiner(playerId))
    {
		isJoin = 1;
    }
	let roundScoreRankData = this.leagueFsm.league.getCurrLeague(playerId);
	cb(Code.OK, seasonId, roundScoreRankData, isJoin, typeId);
	return;
};

//查询赛程
LeagueService.prototype.SSGetSchedule = function(playerId, msg, cb)
{
	let seasonId = this.leagueFsm.getSeasonId();
	let failedRoundScheduleData = {
		typeId: 0,
		roundId: 0,
		name: "",
		groupId: 0,
		finalRound: 0,
		maxGroupCount: 0,
		scheduleTime: 0,
		scheduleList: [],
	};

	let failedRet = {seasonId: seasonId, roundScheduleData: failedRoundScheduleData};
	if (!playerId)
	{
		logger.error("SSGetSchedule: not playerId", playerId);
		cb(Code.FAIL, failedRet.seasonId, failedRet.roundScheduleData);
		return;
	}

	let typeId = msg.typeId;
	let roundId = msg.roundId;
	let groupId = msg.groupId;
	if (!typeId || !roundId || !groupId)
	{
		logger.error("SSGetSchedule: typeId, roundId, groupId error!", playerId, typeId, roundId, groupId);
		cb(Code.FAIL, failedRet.seasonId, failedRet.roundScheduleData);
		return;
	}

	
	logger.info("SSGetSchedule", playerId, typeId, roundId, groupId);

	let curr_status = this.leagueFsm.getCurrStatus();
    if (curr_status <= Constant.STATE.NOT_USE_2)
    {
		//比赛开始前
		logger.info("SSGetSchedule: system isn't can access!",  curr_status);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.COMMUNITY;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.COMMUNITY);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet);
		return;
    }

    let currTypeId = this.leagueFsm.league.getTypeIdByStatus(curr_status);
    if (currTypeId <= 0)
    {
		logger.info("SSGetSchedule: system isn't can access!",  currTypeId);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.COMMUNITY;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.COMMUNITY);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet);
		return;
	}

	if (typeId < currTypeId && currTypeId !== commonEnum.LEAGUE_TYPE_ID.AMATEUR && curr_status < Constant.STATE.SHOW_PROFESSION_ROUND1_INFO)
	{
		logger.info("SSGetSchedule: system  not reach typeId!", currTypeId, typeId);
		failedRet.typeId = typeId;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(typeId);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet);
		return;
	}

	let roundScheduleData = this.leagueFsm.league.getSchedule(typeId, roundId, groupId);
	cb(Code.OK, seasonId, roundScheduleData);
	return;
};

//查询积分榜
LeagueService.prototype.SSGetScoreRank = function(playerId, msg, cb)
{
	logger.info("SSGetScoreRank", playerId, msg);
	let failedRet = {
		typeId: 0,
		roundId: 0,
		name: "",
		groupId: 0,
		finalRound: 0,
		maxGroupCount: 0,
		scheduleTime: 0,
		maxRound: 0,
		teamInfoList: [],
	};

	let seasonId = this.leagueFsm.getSeasonId();
	if (!playerId)
	{
		logger.error("SSGetScoreRank: not playerId", playerId);
		cb(Code.FAIL, seasonId, failedRet);
		return;
	}

	let typeId = msg.typeId;
	let roundId = msg.roundId;
	let groupId = msg.groupId;
	if (!typeId || !roundId || !groupId)
	{
		logger.error("SSGetScoreRank: typeId, roundId, groupId error!", playerId, typeId, roundId, groupId);
		cb(Code.FAIL, seasonId, failedRet);
		return;
	}

	logger.info("SSGetScoreRank", playerId, typeId, roundId, groupId);

	let curr_status = this.leagueFsm.getCurrStatus();
    if (curr_status <= Constant.STATE.NOT_USE_2)
    {
		logger.info("SSGetScoreRank: system isn't can access!",  curr_status);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.COMMUNITY;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.COMMUNITY);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet);
		return;
    }

    let currTypeId = this.leagueFsm.league.getTypeIdByStatus(curr_status);
    if (currTypeId <= 0)
    {
		logger.info("SSGetScoreRank: system isn't can access!",  currTypeId);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.COMMUNITY;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.COMMUNITY);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet);
		return;
	}

	if (typeId === commonEnum.LEAGUE_TYPE_ID.COMMUNITY
		|| typeId === commonEnum.LEAGUE_TYPE_ID.NORMAL
		|| typeId === commonEnum.LEAGUE_TYPE_ID.KNOCKOUT) 
	{
		if (typeId > currTypeId)
		{
			logger.info("SSGetCurrLeagueData: system  not reach typeId!",  currTypeId, typeId);
			failedRet.typeId = typeId;
			failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(typeId);
			cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet);
			return;
		}
    }
	if(typeId > 5)
		typeId = commonEnum.LEAGUE_TYPE_ID.PREPARE;

	let roundScoreRankData = this.leagueFsm.league.getScoreRank(typeId, roundId, groupId);
	cb(Code.OK, seasonId, roundScoreRankData);
	return;
};

//查询默认赛程
LeagueService.prototype.SSSwitchSchedule = function(playerId, msg, cb)
{
	logger.info("SSSwitchSchedule", playerId, msg);
	let seasonId = this.leagueFsm.getSeasonId();
	let failedRet = {
		typeId: 0,
		roundId: 0,
		name: "",
		groupId: 0,
		finalRound: 0,
		maxGroupCount: 0,
		scheduleTime: 0,
		scheduleList: [],
	};

	if (!playerId)
	{
		logger.error("SSSwitchSchedule: not playerId", playerId);
		cb(Code.FAIL, seasonId, failedRet);
		return;
	}

	let typeId = msg.typeId;
	if (!typeId)
	{
		logger.error("SSSwitchSchedule: typeId, roundId, groupId error!", playerId, typeId);
		cb(Code.FAIL, seasonId, failedRet);
		return;
	}

	let curr_status = this.leagueFsm.getCurrStatus();
    if (curr_status <= Constant.STATE.NOT_USE_2)
    {
		logger.info("SSSwitchSchedule: system isn't can access!",  curr_status);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.COMMUNITY;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.COMMUNITY);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet);
		return;
    }

    let currTypeId = this.leagueFsm.league.getScheduleTypeIdByStatus(curr_status);
    if (currTypeId <= 0)
    {
		logger.info("SSSwitchSchedule: system isn't can access!",  currTypeId);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.COMMUNITY;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.COMMUNITY);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet);
		return;
	}

	let retData = this.leagueFsm.league.getDefaultSchedule(playerId, typeId, msg.groupId);
	if (retData.code !==  Code.OK)
	{
		cb(retData.code, seasonId, retData.roundScheduleData);
		return;
	}

	cb(Code.OK, seasonId, retData.roundScheduleData);
	return;
};

//查询默认积分榜
LeagueService.prototype.SSSwitchScore = function(playerId, msg, cb)
{
	logger.info("SSSwitchScore", playerId, msg);
	let failedRet = {
		typeId: 0,
		roundId: 0,
		name: "",
		groupId: 0,
		finalRound: 0,
		maxGroupCount: 0,
		scheduleTime: 0,
		maxRound: 0,
		teamInfoList: [],
	};

	let seasonId = this.leagueFsm.getSeasonId();
	if (!playerId)
	{
		logger.error("SSSwitchScore: not playerId", playerId);
		failedRet.typeId = commonEnum.LEAGUE_TYPE_ID.COMMUNITY;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(commonEnum.LEAGUE_TYPE_ID.COMMUNITY);
		cb(Code.FAIL, seasonId, failedRet);
		return;
	}

	let typeId = msg.typeId;
	if (!typeId)
	{
		logger.error("SSSwitchScore: typeId, roundId, groupId error!", playerId, typeId);
		cb(Code.FAIL, seasonId, failedRet);
		return;
	}

	let round = 0;
	switch (typeId)//查看的联赛还没开打显示公布赛程时间
	{
		case commonEnum.LEAGUE_TYPE_ID.COMMUNITY:
			round = this.leagueFsm.league.community.currRound;
			break;
		case commonEnum.LEAGUE_TYPE_ID.NORMAL:
			round = this.leagueFsm.league.normal.currRound;
			break;
		case commonEnum.LEAGUE_TYPE_ID.KNOCKOUT:
			round = this.leagueFsm.league.knockout.currRound;
			break;
		// case commonEnum.LEAGUE_TYPE_ID.AMATEUR:
		// case commonEnum.LEAGUE_TYPE_ID.LEAGUE_SECOND:
		// case commonEnum.LEAGUE_TYPE_ID.LEAGUE_FIRST:
		// case commonEnum.LEAGUE_TYPE_ID.CHAMPIONS:
		// case commonEnum.LEAGUE_TYPE_ID.SUPER:
		// case commonEnum.LEAGUE_TYPE_ID.PROFESSION:
		// 	round = this.leagueFsm.league.profession.currRound;
	}

	if(round <= 0 && typeId > commonEnum.LEAGUE_TYPE_ID.PREPARE)//没开始显示开始时间
	{
		logger.info("SSSwitchScore: round isn't 0!",  round);
		failedRet.typeId = typeId;
		failedRet.scheduleTime = this.leagueFsm.league.getBattleTime(typeId);
		cb(Code.LEAGUE.CURR_NOT_DATA, seasonId, failedRet);
		return;
	}

	logger.info("SSSwitchScore", playerId, typeId);
	
	let retData = this.leagueFsm.league.getDefaultScoreRank(playerId, typeId, msg.groupId);
	if (retData.code !==  Code.OK)
	{
		cb(retData.code, seasonId, retData.roundScoreRankData);
		return;
	}

	cb(Code.OK, seasonId, retData.roundScoreRankData);
	return;
};

//查询个人赛事信息
LeagueService.prototype.SSGetPersonalHistory = function(playerId, msg, cb)
{
	logger.info("SSGetPersonalHistory", playerId, msg);
	let failedRet = {
	};

	if (!playerId)
	{
		logger.error("SSGetPersonalHistory: not playerId", playerId);
		cb(Code.FAIL, failedRet);
		return;
	}

	let destUid = msg.uid;
	if (!destUid)
	{
		logger.error("SSGetPersonalHistory: not playerId destUid", destUid);
		cb(Code.FAIL, failedRet);
		return;
	}

	let personalHistoryInfo = this.leagueFsm.league.getPersonalHistory(destUid);
	cb(Code.OK, personalHistoryInfo);
	return;
};
//查询个人预选赛信息
LeagueService.prototype.SSGetPrimaryHistory = function(playerId, msg, cb)
{
	logger.info("SSGetPrimaryHistory", playerId, msg);
	let failedRet = {
	};

	if (!playerId)
	{
		logger.error("SSGetPrimaryHistory: not playerId", playerId);
		cb(Code.FAIL, failedRet);
		return;
	}

	let destUid = msg.uid;
	if (!destUid)
	{
		logger.error("SSGetPrimaryHistory: not playerId destUid", destUid);
		cb(Code.FAIL, failedRet);
		return;
	}

	let personalHistoryInfo = this.leagueFsm.league.getPrimaryHistory(destUid);
	cb(Code.OK, personalHistoryInfo);
	return;
};

//查询个人全部赛程信息
LeagueService.prototype.SSGetPersonalAllSchedule = function(playerId, msg, cb)
{
	logger.info("SSGetPersonalAllSchedule", playerId, msg);
	let failedRet = {
	};

	if (!playerId)
	{
		logger.error("SSGetPersonalAllSchedule: not playerId", playerId);
		cb(Code.FAIL, failedRet);
		return;
	}

	let destUid = msg.uid;
	if (!destUid)
	{
		logger.error("SSGetPersonalAllSchedule: not playerId destUid", destUid);
		cb(Code.FAIL, failedRet);
		return;
	}

	let PersonalAllSchedule = this.leagueFsm.league.getPersonalAllSchedule(destUid);
	let seasonId = this.leagueFsm.league.uid;
	let grepId = this.leagueFsm.league.getProfessionGroupIdByUid(destUid);
	if(grepId === 0)
	{
		grepId = 6;
	}
	switch (grepId) {
		case 5:
			grepId = 1;
			break;
		case 4:
			grepId = 2;
			break;
		case 3:
			grepId = 3;
			break;
		case 2:
			grepId = 4;
			break;
		case 1:
			grepId = 5;
			break;
		default:
			grepId = 6;
			break;
	}
	if(PersonalAllSchedule.length === 0)
	{
		let MaxRound = 0;
		switch (grepId) {
			case 1:
			case 2:
			case 3:
			case 4:
			case 5:
				MaxRound = 39;
				break;
			case 7:
				MaxRound = 6;
				break;
			case 8:
				MaxRound = 5;
				break;
		}
		for(let i = 1; i < MaxRound; i++)
		{
			let roundScheduleData = this.leagueFsm.league.getSchedule(grepId, i, grepId);
			for(let k in roundScheduleData.scheduleList)
			{
				if(roundScheduleData.scheduleList[k].teamA === destUid || roundScheduleData.scheduleList[k].teamB === destUid)
				{
					let ScheduleGroupInfo = roundScheduleData.scheduleList[k];
					let msg = {
						typeId: grepId,
						grepId: grepId,
						roundId: i,
						beginTime: ScheduleGroupInfo.beginTime,
						teamA: {
							playerUid: ScheduleGroupInfo.teamA,
							name: "",
							faceIcon: 0,
							actualStrength: 0,
						},
						teamB: {
							playerUid: ScheduleGroupInfo.teamB,
							name: "",
							faceIcon: 0,
							actualStrength: 0,
						},
						teamAScore: 0,
						teamBScore: 0,
						status: 0,
						isNull: 0,
						seasonId: seasonId,
						state: 0,
					}
					PersonalAllSchedule.push(utils.deepCopy(msg));
				}
			}
		}
	}

	cb(Code.OK, PersonalAllSchedule, grepId, seasonId);
	return;
};

LeagueService.prototype.SSCheckSetTeamFormation = function(playerId, msg, cb)
{
	let code = this.leagueFsm.league.checkSetTeam();
	cb(code);
	return;
};

LeagueService.prototype.makeMessageBody = function(obj, funcType)
{
	let msg = {};
	switch (funcType) 
	{
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_NOTIFY_ENROLL_RESULT_MAIL:
			msg.playerId 	= obj.uid;
			msg.success  	= obj.success;
			msg.profession = obj.profession;
			break;
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_PREPARE:
			msg.playerId 	   = obj.uid;
			msg.roomUid 	   = obj.roomUid;
			msg.competitorUid  = obj.competitorUid;
			msg.teamA          = obj.teamA;
			msg.teamB          = obj.teamB;
			msg.beginTime      = obj.beginTime;
			msg.leagueName     = obj.leagueName;
			msg.groupId        = obj.groupId;
			msg.round          = obj.round;
			break;	
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_START:
			msg.typeId         = obj.typeId;
			msg.leagueName 	   = obj.leagueName;
			msg.round  		   = obj.round;
			break;
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_FINAL_REWARD:
			msg.playerId   = obj.uid;
			msg.groupId    = obj.groupId;
			msg.rank       = obj.rank;
			msg.typeId     = obj.typeId;
			break;
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_NOTIFY_WIN_AND_LOSE_MAIL:
			msg.playerId   = obj.uid;
			msg.selfScore  = obj.selfScore;
			msg.enemyScore = obj.enemyScore;
			msg.enemyUid   = obj.enemyUid;
			msg.enemyName  = obj.enemyName;
			msg.isNull 	   = obj.isNull;
			msg.roomUid    = obj.roomUid;
			msg.typeId     = obj.typeId;
			msg.notifyType = obj.notifyType;
			msg.leagueName = obj.leagueName;
			msg.round      = obj.round;
			break;
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_NOTIFY_FINAL_WIN_MAIL:
			msg.playerId   = obj.uid;
			msg.name       = obj.name;
			msg.typeId     = obj.typeId;
			msg.notifyType = obj.notifyType;
			break;
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_GET_BATTLE_DATA:
			msg.playerId   = obj.uid;
			break;
		case commonEnum.CLUSTER_2_GAME_FUNC_TYPE.PVP_LEAGUE_PUBLIC_NOTIFY_PLAYER:
			msg.publicMailType = obj.publicMailType;
			msg.uidList 	   = obj.uidList;
			msg.time		   = obj.time;
			break;
		default:
			logger.error("not case hint ", msg.funcType);
			msg.playerId   = obj.uid;
			break;
	}
	msg.funcType = funcType;
	return msg;
};

LeagueService.prototype.clusterToGameFuncService = function(obj, funcType, cb)
{
	let msg = this.makeMessageBody(obj, funcType);
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.datanode.dataNodeRemote.clusterToGameFunc(session, msg, function(code) {
		if (code !== Code.OK)
		{
			logger.error("clusterToGameFuncService return failed!", code);
            return cb("clusterToGameFuncService return failed!");
		}

		cb(null);
		return;
	});
};

LeagueService.prototype.finalupDataToGameFuncService = function(allRankData, cb)
{
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.datanode.dataNodeRemote.finalupData(session, allRankData, function(code) {
		if (code !== Code.OK)
		{
			logger.error("finalupDataToGameFuncService return failed!", code);
			return cb("finalupDataToGameFuncService return failed!");
		}
		cb(null);
	});
};

LeagueService.prototype.foreachFuncService = function(data, funcType, cb)
{
	let self = this;
	async.eachSeries(data, function(obj, callback) {
		self.clusterToGameFuncService(obj, funcType, function(err){
			if (!!err)
			{	
				callback(err);
				return;
			}

			callback(null);
		});
    }, function(err) {
        if (!!err) 
        {
			logger.error("foreachFuncService failed");
			cb(err);
            return;
		}

		cb(null);
		return;
    });
};

LeagueService.prototype.clusterToAllGameFuncService = function(obj, funcType, cb)
{
	let msg = this.makeMessageBody(obj, funcType);
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.datanode.dataNodeRemote.cluster2AllGameServerMsg(session, msg, function(code) {
		if (code !== Code.OK)
		{
			logger.error("_clusterToAllGameFuncService failed!", code);
            return cb("_clusterToAllGameFuncService return failed!");
		}

		cb(null);
		return;
	});
};

LeagueService.prototype.makeClusterToGameTaskArray = function(data, funcType)
{
	//logger.info("data, funcType", funcType);
	let self = this;
	var arr = [];
	let index = 0;
	for(let idx in data)
	{
		let obj = data[idx];
		arr[index] = function(obj, funcType) {
			return function(callback) {
				self.clusterToGameFuncService(obj, funcType, function(err){
					if (!!err)
					{	
						callback(err, obj.uid);
						return;
					}
		
					callback(null, obj.uid);
				});
			}
		} (obj, funcType)
		//logger.info("index, obj, funcType 1: ", index, obj, funcType)
		index++;
	}
	return arr;
};

LeagueService.prototype.asyncSomeTaskClusterToGameFuncService = function(leagueName, round, data, funcType, cb)
{
	let self = this;
	var clusterToGameArray = this.makeClusterToGameTaskArray(data, funcType);
	async.parallel(clusterToGameArray, function (err, results) {
		if (!!err)
		{
			logger.info("asyncSomeTaskClusterToGameFuncService error!", err);
			cb(err);
			return;
		}

		logger.info("parallel clusterToGameFuncService results success!");
		//self.outputResult(leagueName, round, results);
		cb(null);
		return;
	});
};

LeagueService.prototype.outputResult = function(leagueName, round, results)
{
	for(let idx in results)
	{
		let uid = results[idx];
		logger.info("parallel uid: ", leagueName, round, uid);
	}
};

//直接保存联赛数据
LeagueService.prototype.saveWholeLeagueData = function (cb)
{
	let self = this;
	self.matchDao.saveLeagueData(self.leagueFsm, cb);
};
//获取gid
LeagueService.prototype.getUidListGid = function (uidList, cb)
{
	let self = this;
	let session = {frontendId: self.app.getServerId()};
	self.app.rpc.datanode.dataNodeRemote.getUidListGid(session, uidList, function (playerList) {
		cb(playerList);
	});
};
//联赛更新到荣誉墙数据组装
LeagueService.prototype.getLeaguePlayerHonorData = function (list, cb)
{
	// let playerList = [];
	let self = this;
	let session = {frontendId: self.app.getServerId()};
	let uidList = [];
	for(let i in list)
	{
		uidList.push(list[i].uid);
	}
	async.waterfall([
		function (callback) {
			self.app.rpc.datanode.dataNodeRemote.getUidListGid(session, uidList, function (playerList) {
				callback(null, playerList);
			});
		},function (playerList, callback) {
			async.eachSeries(list, function(obj, cb1)
			{
				let gid = "";
				for(let i = playerList.length - 1; i >= 0; i--)
				{
					if(playerList[i].uid === obj.uid)
					{
						gid = playerList[i].gid;
						playerList.splice(i, 1);
						break;
					}
				}
				if(gid !== "")
				{
					//赛季 联赛等级(枚举：LEAGUE_TYPE_ID) 排名
					playerList.push({uid: obj.uid, gid: gid, seasonId: obj.seasonId, typeId: obj.typeId, rank: obj.rank});
				}
				cb1(null);
			}, function (err) {
				cb(playerList);
				return;
			});
		}
	], function (err) {
		cb([]);
	});
};

//更新玩家荣誉墙
LeagueService.prototype.updataHonorData = function (playerList, type, cb)
{
	//按gid分
	let map = new Map();
	let gidPlayerList = [];
	for(let i in playerList)
	{
		if(map.has(playerList[i].gid))
		{
			let list = map.get(playerList[i].gid);
			list.push(playerList[i]);
		}
		else
		{
			let list = [];
			list.push(playerList[i]);
			map.set(playerList[i].gid, list);
		}
	}
	for(let [k, v] of map)
	{
		gidPlayerList.push(v);
	}
	let self = this;
	async.eachSeries(gidPlayerList, function (sendList, callback) {
		if(sendList.length > 0)
		{
			let gid = sendList[0].gid;
			let session = {frontendId: self.app.getServerId(), toServerId: gid};
			if(type === commonEnum.HONOR_DISPOSE_TYPE.JOIN_NUM)//加参赛次数
			{
				//sendList[i] = {uid, gid}
				let msg = {playerList: sendList, type: commonEnum.HONOR_WALL_TYPE.LEAGUE};
				self.app.rpc.game.entryRemote.updateHonorWallJoinNum(session, msg, function (err) {
					if (err !== Code.OK) {
						logger.error("updateHonorWallJoinNum err: ", err, commonEnum.HONOR_WALL_TYPE.LEAGUE);
						callback(null);
						return;
					}
					callback(null);
				});
			}
			else if(type === commonEnum.HONOR_DISPOSE_TYPE.DATA)//更新赛季数据
			{
				//sendList[i] = {uid, gid, seasonId, rank, pos}
				self.app.rpc.game.entryRemote.updateHonorWallData(session, sendList, commonEnum.HONOR_WALL_TYPE.LEAGUE, function (err) {
					if (err !== Code.OK) {
						logger.error("updateHonorWallData err: ", err, commonEnum.HONOR_WALL_TYPE.LEAGUE);
						callback(null);
						return;
					}
					callback(null);
					return;
				});
			}
		}
		else
		{
			callbcak(null);
		}
	}, function (err) {
		cb()
	});
};