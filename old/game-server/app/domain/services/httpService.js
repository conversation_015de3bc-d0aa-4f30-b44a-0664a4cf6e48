/**
 * Created by aaa on 2015/7/30.
 */


var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var http = require('http');
var qs = require('querystring');
var clusterConfig = require('../../../config/cluster.json');
var xml2js = require('xml2js');
var utils = require('../../util/utils');

module.exports.create = function(app, db){
    return new HttpService(app, db);
};

var HttpService = function(app, db) {
    EventEmitter.call(this);
    this.app = app;
    this.server = initHttpServer(this);
    this.db = db;
    return this.server;
};

util.inherits(HttpService, EventEmitter);

var initHttpServer = function (self) {
    var port = clusterConfig.authHttpPort;
    var server = http.createServer(function (req, res) {
        if(req.method === "GET") {
            logger.debug('-------- httpService ---------- GET method start! url:', req.url);
        }
        if(req.method === "POST") {
            logger.debug('-------- httpService ---------- POST method start! url:', req.url);
            var postData = "";
            req.on('data', function (data) {
                postData += data;
            });
            req.on('end', function () {
                logger.debug('-------- httpService ----------- POST method, postData',postData);
                self.postDispatch(req.url, postData, res);
                //var params = qs.parse(postData);
            })
        }
    });
    server.listen(port);
    logger.debug('auth http server start, listen port:', port);
    return server;
};

HttpService.prototype.postDispatch = function (reqUrl, data, res) {
    if(reqUrl === '/mail/sendSystemMail') {
        this.sendSystemMail(data, res);
    }else if(reqUrl === '/broadcast/rollingMsg') {
        this.sendRollingMsg(data, res);
    }
};

HttpService.prototype.sendSystemMail = function (data, res) {
    logger.debug('rpc start sendSystemMail.', JSON.parse(data));
    let dataObj = JSON.parse(data);
    let msg = {
        uidArray: dataObj.uidArray,
        mailInfo: dataObj.mail
    };
    this.app.rpc.datanode.dataNodeRemote.sendSystemMailByUidList({frontendId: this.app.getServerId()}, msg, function (code) {
        res.end(JSON.stringify({code: code}));
    });
};

//跑马灯信息(滚动信息)
HttpService.prototype.sendRollingMsg = function (data, res) {
    logger.debug('rpc start sendRollingMsg.', JSON.parse(data));
    let dataObj = JSON.parse(data);
    let msg = dataObj.sendMsg;
    this.app.rpc.datanode.dataNodeRemote.chatBroadcast({frontendId: this.app.getServerId()}, msg, function (code) {
        res.end(JSON.stringify({code: code}));
    });
};


/*
HttpService.prototype.getGoodsList = function (data, res, self) {
    //unicode转码
    data=utils.hexToDecode(data);
    var xml = data.toString("utf-8");
    logger.debug('xml',xml);
    var parser = new xml2js.Parser();
    parser.parseString(xml, function (err, data) {
        logger.debug(data, data.xml.login_id);
        //获取角色id
        self.db.collection("account", function (err, collection) {
            logger.debug('loginId', Number(data.xml.login_id[0]));
            collection.findOne({
                loginId: Number(data.xml.login_id[0])
            }, {
                fields: {
                    _id: 1,
                    sid: 1
                }
            }, function (err, doc) {
                if(doc) {
                    //获取可上架物品列表
                    var playerId=doc._id+"_"+doc.sid;
                    logger.debug('playerId', playerId, doc, doc._id);
                    self.app.rpc.logic.entryRemote.yxbGetGoodsList(null, playerId, function (err, result) {
                        var httpRet;
                        if(result.code != Code.OK) {
                            httpRet = JSON.stringify({data: "FAIL"});
                            logger.debug('httpRet',httpRet);
                            res.end(httpRet);
                        }
                        else {
                            httpRet = JSON.stringify({data: result.dataList, notifyUrl: gameConfig.youxibi_url+"/lock_goods/"});
                            logger.debug('httpRet',httpRet);
                            res.end(httpRet);
                        }
                    });
                }else{
                    logger.debug('can not find login Id',Number(data.xml.login_id[0]));
                    res.end(JSON.stringify({data: "FAIL"}));
                }
            })
        });
    });
};

HttpService.prototype.lockGoods = function (data, res, self) {
    //unicode转码
    data = utils.hexToDecode(data);
    var xml = data.toString("utf-8");
    logger.debug('------------- xml ----------',xml);
    var parser = new xml2js.Parser();
    parser.parseString(xml, function (err, data) {
        logger.debug(data.xml, data.xml.login_id, data.xml.identify);
        var loginId = Number(data.xml.login_id);
        var entityId = Number(data.xml.identify);
        var resId = Number(data.xml.goods_id);
        var num = Number(data.xml.goods_num);
        var tradeNo = data.xml.trade_no.toString();
        logger.debug("--------- parser -----------",loginId, entityId, resId, num, tradeNo);
        self.db.collection("account", function (err, collection) {
            collection.findOne({
                loginId: loginId
            }, {
                fields: {
                    _id: 1,
                    sid: 1
                }
            }, function (err, doc) {
                if(doc) {
                    //锁定或解锁物品
                    var msg = {};
                    msg.playerId = doc._id+"_"+doc.sid;
                    msg.entityId = entityId;
                    msg.resId = resId;
                    msg.num = num;
                    msg.tradeNo = tradeNo;
                    self.app.rpc.logic.entryRemote.yxbLockGoods(null, msg, function (err, result) {
                        var httpRet;
                        if(result.code != Code.OK) {
                            httpRet = JSON.stringify({data: "FAIL"});
                            logger.debug('httpRet', httpRet);
                        }else {
                            httpRet = JSON.stringify({data: "SUCCESS", notifyUrl: gameConfig.youxibi_url+"/goods_operation/"});
                            logger.debug('httpRet', httpRet);
                        }
                        res.end(httpRet);
                    });
                }else{
                    logger.debug('can not find login Id',loginId);
                    res.end(JSON.stringify({data: "FAIL"}));
                }
            })
        });
    })
};

HttpService.prototype.goodsOperation = function (data, res, self) {
    //unicode转码
    data = utils.hexToDecode(data);
    var xml = data.toString("utf-8");
    logger.debug('------------- goodsOperation xml ----------',xml);
    var parser = new xml2js.Parser();
    var httpRet;
    parser.parseString(xml, function (err, data) {
        logger.debug("goodsOperation data",data.xml);
        var loginId;
        var tradeNo;
        if(data.xml.login_id) {
            loginId = Number(data.xml.login_id[0]);
        }
        if(data.xml.trade_no) {
            tradeNo = data.xml.trade_no[0];
        }
        if(data.xml.ret == 'CHECK') {
            //校验用户是否为游戏账户
            self.app.rpc.logic.entryRemote.yxbCheckPlayer(null, loginId, function (err, code) {
                if(code == Code.OK) {
                    httpRet = JSON.stringify({data: "SUCCESS"});
                    logger.debug('goodsOperation yxbCheckPlayer httpRet:',httpRet);
                    return res.end(httpRet);
                }else {
                    httpRet = JSON.stringify({data: "USER_NOT_EXSIST"});
                    logger.debug('goodsOperation yxbCheckPlayer httpRet:',httpRet);
                    return res.end(httpRet);
                }
            })
        //}else if(data.xml.ret == "SUCCESS" || data.xml.ret == "CANCEL") {
        }else if(data.xml.ret == "SUCCESS") {
            //被其他用户购买走道具
            //loginId = 14535;
            self.app.rpc.logic.entryRemote.yxbBuyGoods(null, loginId, tradeNo, function (err, code) {
                if(code == Code.OK) {
                    httpRet = JSON.stringify({data: "SUCCESS"});
                    logger.debug('goodsOperation yxbBuyGoods httpRet:',httpRet);
                    return res.end(httpRet);
                }else {
                    httpRet = JSON.stringify({data: "FAIL"});
                    logger.debug('goodsOperation yxbBuyGoods httpRet:',httpRet);
                    return res.end(httpRet);
                }
            })
        }else if(data.xml.ret == "CANCEL") {
            //道具下架
            self.app.rpc.logic.entryRemote.yxbUnlockGoods(null, tradeNo, function (err, code) {
                if(code == Code.OK) {
                    httpRet = JSON.stringify({data: "SUCCESS"});
                    logger.debug('goodsOperation yxbUnlockGoods httpRet:',httpRet);
                    return res.end(httpRet);
                }else {
                    httpRet = JSON.stringify({data: "FAIL"});
                    logger.debug('goodsOperation yxbUnlockGoods httpRet:',httpRet);
                    return res.end(httpRet);
                }
            })
        }
    })
};
*/
