/**
 * Idea and Persist
 * Created by June on 2019/4/9.
 */

let logger = require('pomelo-logger').getLogger(__filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let async = require('async');

let utils = require('../../util/utils');
let Room = require('../entities/room');

module.exports.create = function(app, dbClient){
    return new BrecordService(app, dbClient);
};

let BrecordService = function(app, dbclient){
    EventEmitter.call(this);
    this.app = app;
    //初始化数据库操作层
    this.brecordDao = require('../../dao/brecordDao').create(dbclient);
};

util.inherits(BrecordService, EventEmitter);

BrecordService.prototype.insertBattleRecord = function(msg, cb) {
    let self = this;
    //新增战报数据并存入数据库
    self.brecordDao.insertBattleRecord(msg, function (err) {
        if(!!err) {
            return cb(err);
        }
        return cb(null);
    });
};

BrecordService.prototype.getBattleRecord = function(msg, cb) {
    //获取战报数据
    let self = this;
    self.brecordDao.getBattleRecord(msg, function (err, doc) {
        if(!!err) {
            return cb(err);
        }
        return cb(null, doc);
    });
};
