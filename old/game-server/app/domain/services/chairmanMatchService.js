/**
 * Idea and Persist
 * Created by <PERSON> on 2020/3/31.
 */
var logger = require('pomelo-logger').getLogger('pomelo', __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var TimeUtils = require('../../util/timeUtils');
var calcUtils = require('../../util/calc');

var async = require('async');
var dataApi = require('../../util/dataApi');
var Constant = require("../../../../shared/constant");
var commonEnum = require('../../../../shared/enum');
var debugConfig = require('../../../config/debugConfig');
var clusterConfig = require('../../../config/cluster');

module.exports.create = function(app, dbclient)
{
    return new chairmanMatchService(app, dbclient);
};
//主席争夺战
let chairmanMatchService = function(app, dbclient, cb)
{
    EventEmitter.call(this);
    this.app = app;

    this.preMatchMaxNum = 200;			//1轮最多200场战斗
    this.preBattleOutTimes = 3;			//预选赛3次出局
    if(debugConfig.isForTest) {
        this.preMatchMaxNum = 100;
    }

    this.match64Num = 64;
    this.matchDao = require("../../dao/matchDao").create(dbclient);


    this.allCompetition = new Map();//各信仰的比赛数据 信仰id => 每个比赛的所有数据{}
    // this.initCompetitionDate();

    this.noneUid = "noneUid";				//轮空Uid
    this.noneRoomUid = "noEnemy";			//轮空战斗

    this.isStat = false;                    //是否统计过了

    this.honorMap = new Map();		//每一期的主席 副主席 总经理 教练 记录
    // 信仰id => [{time,
    // chairman：{Uid, name, faceUrl, gid},      //主席
    // coChairman：{Uid, name, faceUrl, gid},    //副主席
    // secondPlace：{Uid, name, faceUrl, gid},   //总经理
    // thirdPlace：{Uid, name, faceUrl, gid},    //主教练
    // },{},...]
};

util.inherits(chairmanMatchService, EventEmitter);

//初始化数据
chairmanMatchService.prototype.initCompetitionDate = function()
{
    let config = dataApi.allData.data["Belief"];
    for(let i in config)
    {
        let data = {
            //preBattleInfoMap: new Map(),	//预选赛个人比赛信息表
            preBattleRoundCursor: 0,		//预选赛每一轮战斗的游标数据 (每轮最多1000次战斗)
            //对战信息表
            preBattleList: [],				//预选赛对战列表 [{uid1, uid2}, ...]
            preBattleOutUidList: [],		//预选赛淘汰列表
            dqCup64BattleList: [],			//64强对战信息
            dqCup32BattleList: [],			//32强对战信息
            dqCup16BattleList: [],			//16强对战信息
            dqCup8BattleList: [],			//8强对战信息
            dqCup4BattleList: [],			//4强对战信息
            ch3BattleList: [],              //争三对战信息
            dqCup2BattleList: [],			//2强对战信息
            thirdPlace: {},                 //主教练   比赛第三名
            secondPlace: {},                //总经理   比赛第二名
            coChairman: {},                 //副主席   竞价
            chairman: {},		    		//主席    比赛第一名   {uid, gid, name, faceUrl}
            outBattleRound: 0,				//第二阶段淘汰赛(32强以后比赛，每次要打两轮, 分主客场)
            campaignList: [],               //竞选列表
            agoDqCup64BattleList: [],		//上一季64强信息
            // matchChampionRecord: [],		//每一期的懂球杯的冠军记录 {time, chairman, coChairman, secondPlace, thirdPlace}
            matchPeriod: commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_INIT,	//该阶段完成后设置
            PeriodWait: false,
            finishPeriod: commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_INIT,
            honorSeasonId: 0,               //荣誉墙赛季
        }
        this.allCompetition.set(config[i].ID, utils.deepCopy(data));
        this.finishNumMax += 1;
    }
    for(let [k, v] of this.allCompetition)
    {
        v.preBattleInfoMap = new Map();
    }

   // logger.error("主席争夺战初始化数据~~~~~~~~~~~~", this.allCompetition);
};

//RPC处理
//报名
chairmanMatchService.prototype.playerEnroll = function(beliefId, playerId, gid, name, faceUrl, cb) {
    //获得对应信仰的比赛数据
    let chairmanData = this.allCompetition.get(beliefId);
    if(chairmanData.matchPeriod !== commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START) {
        return cb(Code.DQ_CUP_CODE.NOT_ENROLL_PERIOD);
    }

    // if(this.isAgo64BattleList(beliefId, playerId))
    // {
    //     //上期64强不用重新报名
    //     return cb(Code.DQ_CUP_CODE.ENROLL_REPEAT);
    // }

    //检查是否已经报名
    if(chairmanData.preBattleInfoMap.has(playerId)) {
        return cb(Code.DQ_CUP_CODE.ENROLL_REPEAT);
    }else {
        chairmanData.preBattleInfoMap.set(playerId, {win: 0, lose: 0, draw: 0, gid: gid, roomUidList: [], faceUrl: faceUrl, name: name});
        let playerList = [{uid: playerId, gid: gid}];
        this.updataHonorData(playerList, commonEnum.HONOR_DISPOSE_TYPE.JOIN_NUM, function () {
            return cb(Code.OK);
        });
    }
};

chairmanMatchService.prototype.getChairmanMatchInfo = function(beliefId, playerId, cb) {
    let chairmanData = this.allCompetition.get(beliefId);
    //没报名, 也可以看
    let nowTimePeriod = this.checkMatchPeriod();
    let nextPeriod = this.calcNextPeriod(nowTimePeriod, chairmanData.matchPeriod, chairmanData.finishPeriod);
    logger.debug("getChairmanMatchInfo nowTimePeriod, nextPeriod", nowTimePeriod, nextPeriod);
    let endTimeHM = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.ENROLL_END);
    let nowDate = new Date();
    let endTimeDate = TimeUtils.newDateByParam(nowDate.getFullYear(), nowDate.getMonth()+1, nowDate.getDate(), endTimeHM.hour, endTimeHM.min, 0);
    let enrollEndTime = endTimeDate.getTime();
    if(nowDate.getDay() === 5) {
        enrollEndTime += 3600 * 24 * 1000 * 2;
    }else if(nowDate.getDay() === 6) {
        enrollEndTime += 3600 * 24 * 1000;
    }
    logger.debug("endTimeDate: ", endTimeDate);
    let playerInfo = chairmanData.preBattleInfoMap.get(playerId);
    let result = {isEnroll: 1, matchPeriod: nowTimePeriod, enrollEndTime: enrollEndTime, preBattle: this.getPreBattleForClient(beliefId, playerInfo),
        match64List: this.getOutBattleListForClient(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_64),
        match32List: this.getOutBattleListForClient(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_32),
        match16List: this.getOutBattleListForClient(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_16),
        match8List: this.getOutBattleListForClient(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_8),
        match4List: this.getOutBattleListForClient(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_4),
        match3List: this.getCh3BattleListForClient(beliefId),
        match2List: this.getOutBattleListForClient(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_2),
        campaignList: chairmanData.campaignList, chairman: chairmanData.chairman, coChairman: chairmanData.coChairman, secondPlace: chairmanData.secondPlace, thirdPlace: chairmanData.thirdPlace};
    // //决赛阶段特殊处理
    // let lastChampion = this.getLastChampion();
    // if(result.match2List.length === 1 && !!lastChampion.uid && !result.match2List[1]) {
    //     logger.debug("the final period: ", chairmanData.matchPeriod);
    //     result.match2List[1] = {awayUid: lastChampion.uid, awayFaceUrl: lastChampion.faceUrl, awayName: lastChampion.name};
    // }
    if(!playerInfo) {
        result.isEnroll = 0;
    }
    // if(this.isAgo64BattleList(beliefId, playerId))
    // {
    //     result.isEnroll = 2;	//上期64强无需报名
    // }
    logger.debug("getChMatchInfo result: ", result);
    cb(Code.OK, result);
};

chairmanMatchService.prototype.getPreBattleForClient = function(beliefId, playerInfo) {
    let chairmanData = this.allCompetition.get(beliefId);
    if(!playerInfo) {
        return {recordList: [], winNum: 0, loseNum: 0, drawNum: 0};
    }
    let preBattle = {recordList: [], winNum: playerInfo.win, loseNum: playerInfo.lose, drawNum: playerInfo.draw};
    for(let i=0, lens=playerInfo.roomUidList.length; i<lens; i++) {
        let oneRecord = {};
        if(playerInfo.roomUidList[i].home !== this.noneUid) {
            oneRecord.homeUid = playerInfo.roomUidList[i].home;
            let homeInfo = chairmanData.preBattleInfoMap.get(oneRecord.homeUid);
            oneRecord.homeFaceUrl = homeInfo.faceUrl || "";
            oneRecord.homeName = homeInfo.name;
            oneRecord.homeHomeScore = playerInfo.roomUidList[i].homeScore;
        }
        if(playerInfo.roomUidList[i].away !== this.noneUid) {
            oneRecord.awayUid = playerInfo.roomUidList[i].away;
            let awayInfo = chairmanData.preBattleInfoMap.get(oneRecord.awayUid);
            oneRecord.awayFaceUrl = awayInfo.faceUrl || "";
            oneRecord.awayName = awayInfo.name;
            oneRecord.awayHomeScore = playerInfo.roomUidList[i].awayScore;
        }
        /*
        if(playerInfo.roomUidList[i].roomUid1 !== this.noneRoomUid) {
            oneRecord.roomUid1 = playerInfo.roomUidList[i].roomUid1;
        }
        if(playerInfo.roomUidList[i].roomUid2 !== this.noneRoomUid) {
            oneRecord.roomUid2 = playerInfo.roomUidList[i].roomUid2;
        }
        */
        oneRecord.roomUid1 = playerInfo.roomUidList[i].roomUid;
        oneRecord.time1 = playerInfo.roomUidList[i].time;
        preBattle.recordList.push(oneRecord);
    }
    return preBattle;
};
//竞选
chairmanMatchService.prototype.coChairmanCampaign = function(msg, cb) {
    let charimanData = this.allCompetition.get(msg.beliefId);
    //是否在竞选阶段
    if(charimanData.matchPeriod !== commonEnum.CHAIRMAN_MATCH_SCHEDULE.CAMPAIGN_START)
    {
        return cb(Code.CHAIRMAN_CODE.MATCHPERIOD_ERROR, charimanData.campaignList);
    }
    //除了主席都可以竞选
    if(msg.playerId === charimanData.chairman.uid)
    {
        return cb(Code.CHAIRMAN_CODE.ON_THE_JOB, charimanData.campaignList);
    }
    let flag = false;
    let time = 0;
    for(let i in charimanData.campaignList)
    {
        if(charimanData.campaignList[i].uid === msg.playerId)
        {
            time = charimanData.campaignList[i].time;
            flag = true;
        }
    }
    if(!flag && msg.num < 1000) //第一次竞选金额是否大于1000
    {
        return cb(Code.CHAIRMAN_CODE.AMOUNT_ERROR, charimanData.campaignList);
    }
    //判断是否超过30秒
    if(flag && time !== 0 && time + 30000 > TimeUtils.now())
    {
        return cb(Code.CHAIRMAN_CODE.NOT_30_SECONDS, charimanData.campaignList);//超过30秒才能再次竞选
    }

    let data = {};
    data.uid = msg.playerId;
    data.gid = msg.gid;
    data.name = msg.name;
    data.faceUrl = msg.faceUrl;
    data.time = TimeUtils.now();
    data.num = msg.num;
    data.history = [];
    data.history.push(msg.num);
    for(let i in charimanData.campaignList)
    {
        if(charimanData.campaignList[i].uid === data.uid)
        {
            charimanData.campaignList[i].num += data.num;
            charimanData.campaignList[i].time = data.time;
            charimanData.campaignList[i].history.push(data.num);
            charimanData.campaignList.sort(__campaign_rank_func);
            return cb(Code.OK, charimanData.campaignList);
        }
    }
    charimanData.campaignList.push(data);
    charimanData.campaignList.sort(__campaign_rank_func);
    cb(Code.OK, charimanData.campaignList);

};

chairmanMatchService.prototype.getOutBattleListForClient = function(beliefId, period) {
    let listForClient = [];
    let battleList = this.getBattleListByPyPeriod(beliefId, period);
    let chairmanData = this.allCompetition.get(beliefId);
    logger.debug("getOutBattleListForClient battleList, period:", battleList, period);
    for(let i=0, lens=battleList.length; i<lens; i++) {
        let oneInfo = {};
        if(battleList[i].home !== this.noneUid) {
            oneInfo.homeUid = battleList[i].home;
            let homeInfo = chairmanData.preBattleInfoMap.get(oneInfo.homeUid);
            if(!!homeInfo) {
                oneInfo.homeFaceUrl = homeInfo.faceUrl || "";
                if(!homeInfo.faceUrl) {
                    logger.error("wrong faceUrl, homeInfo:", homeInfo);
                }
                oneInfo.homeName = homeInfo.name;
                oneInfo.homeHomeScore = battleList[i].homeHomeScore;
                oneInfo.homeAwayScore = battleList[i].homeAwayScore;
            }
        }
        if(battleList[i].away !== this.noneUid) {
            oneInfo.awayUid = battleList[i].away;
            let awayInfo = chairmanData.preBattleInfoMap.get(oneInfo.awayUid);
            if(!!awayInfo) {
                // if(oneInfo.awayUid === chairmanData.chairman.uid) {
                //     awayInfo = chairmanData.chairman;
                // }
                if(!awayInfo.faceUrl) {
                    logger.error("getOutBattleListForClient awayInfo error", awayInfo, battleList[i]);
                }
                oneInfo.awayFaceUrl = awayInfo.faceUrl || "";
                if(!awayInfo.faceUrl) {
                    logger.debug("wrong faceUrl, awayInfo:", awayInfo);
                }
                oneInfo.awayName = awayInfo.name;
                oneInfo.awayHomeScore = battleList[i].awayHomeScore;
                oneInfo.awayAwayScore = battleList[i].awayAwayScore;
                oneInfo.winnerUid = battleList[i].winnerUid;
            }
        }
        if(battleList[i].roomUid1 !== this.noneRoomUid) {
            oneInfo.roomUid1 = battleList[i].roomUid1;
            oneInfo.time1 = battleList[i].time1;
        }
        if(battleList[i].roomUid2 !== this.noneRoomUid) {
            oneInfo.roomUid2 = battleList[i].roomUid2;
            oneInfo.time2 = battleList[i].time2;
        }
        listForClient.push(oneInfo);
    }
    return listForClient;
};

chairmanMatchService.prototype.getCh3BattleListForClient = function(beliefId) {
    let listForClient = [];
    //let battleList = this.getBattleListByPyPeriod(beliefId, period);
    let chairmanData = this.allCompetition.get(beliefId);
    let battleList = chairmanData.ch3BattleList;
    logger.debug("getCh3BattleListForClient battleList, period:", battleList);
    for(let i=0, lens=battleList.length; i<lens; i++) {
        let oneInfo = {};
        if(battleList[i].home !== this.noneUid) {
            oneInfo.homeUid = battleList[i].home;
            let homeInfo = chairmanData.preBattleInfoMap.get(oneInfo.homeUid);
            if(!!homeInfo) {
                oneInfo.homeFaceUrl = homeInfo.faceUrl || "";
                if(!homeInfo.faceUrl) {
                    logger.error("wrong faceUrl, homeInfo:", homeInfo);
                }
                oneInfo.homeName = homeInfo.name;
                oneInfo.homeHomeScore = battleList[i].homeHomeScore;
                oneInfo.homeAwayScore = battleList[i].homeAwayScore;
            }
        }
        if(battleList[i].away !== this.noneUid) {
            oneInfo.awayUid = battleList[i].away;
            let awayInfo = chairmanData.preBattleInfoMap.get(oneInfo.awayUid);
            if(!!awayInfo) {
                // if(oneInfo.awayUid === chairmanData.chairman.uid) {
                //     awayInfo = chairmanData.chairman;
                // }
                if(!awayInfo.faceUrl) {
                    logger.error("getCh3BattleListForClient awayInfo error", awayInfo, battleList[i]);
                }
                oneInfo.awayFaceUrl = awayInfo.faceUrl || "";
                if(!awayInfo.faceUrl) {
                    logger.debug("wrong faceUrl, awayInfo:", awayInfo);
                }
                oneInfo.awayName = awayInfo.name;
                oneInfo.awayHomeScore = battleList[i].awayHomeScore;
                oneInfo.awayAwayScore = battleList[i].awayAwayScore;
                oneInfo.winnerUid = battleList[i].winnerUid;
            }
        }
        if(battleList[i].roomUid1 !== this.noneRoomUid) {
            oneInfo.roomUid1 = battleList[i].roomUid1;
            oneInfo.time1 = battleList[i].time1;
        }
        if(battleList[i].roomUid2 !== this.noneRoomUid) {
            oneInfo.roomUid2 = battleList[i].roomUid2;
            oneInfo.time2 = battleList[i].time2;
        }
        listForClient.push(oneInfo);
    }
    return listForClient;
};

chairmanMatchService.prototype.getChairmanMatchTopList = function(beliefId, playerId, cb) {
    if(!this.honorMap.has(beliefId))
    {
        return cb(Code.OK, []);
    }
    let honorList = this.honorMap.get(beliefId);
    //统计历届冠军数据
    logger.debug("getChairmanMatchTopList honorList: ", beliefId, honorList);

    if(honorList.length > 0)
    {
        //排序
        honorList.sort(__rating_compare_func);
    }

    cb(Code.OK, honorList);
};

function __rating_compare_func(rankObj1, rankObj2) {

    if(rankObj1.time > rankObj2.time) {
        return 1;
    }else if(rankObj1.time < rankObj2.time) {
        return -1;
    }
    return 0;
}
function __campaign_rank_func(rankObj1, rankObj2) {

    if(rankObj1.num > rankObj2.num) {
        return -1;
    }else if(rankObj1.num < rankObj2.num) {
        return 1;
    }
    else
    {
        if(rankObj1.time > rankObj2.time) {
            return 1;
        }else if(rankObj1.time < rankObj2.time) {
            return -1;
        }
    }
    return 0;
}
//rpc 设置联赛职位 pos: 1 主席 2 副主席 3 总经理 4 主教练
chairmanMatchService.prototype.setBeliefLeader = function(beliefId, pos){
    let chairmanData = this.allCompetition.get(beliefId);
    let playerId = "";
    let gid = "";
    let name = "";
    let faceUrl = "";
    switch (pos) {
        case 1:
            playerId = chairmanData.chairman.uid;
            gid = chairmanData.chairman.gid;
            name = chairmanData.chairman.name;
            faceUrl = chairmanData.chairman.faceUrl;
            break;
        case 2:
            playerId = chairmanData.coChairman.uid;
            gid = chairmanData.coChairman.gid;
            name = chairmanData.coChairman.name;
            faceUrl = chairmanData.coChairman.faceUrl;
            break;
        case 3:
            playerId = chairmanData.secondPlace.uid;
            gid = chairmanData.secondPlace.gid;
            name = chairmanData.secondPlace.name;
            faceUrl = chairmanData.secondPlace.faceUrl;
            break;
        case 4:
            playerId = chairmanData.thirdPlace.uid;
            gid = chairmanData.thirdPlace.gid;
            name = chairmanData.thirdPlace.name;
            faceUrl = chairmanData.thirdPlace.faceUrl;
            break;
    }
    let session = {frontendId: this.app.getServerId()};
    let rpcMsg= {
        beliefId:beliefId,
        leader:{uid: playerId, gid: gid, name: name, faceUrl: faceUrl, pos: pos}
    };
    this.app.rpc.datanode.dataNodeRemote.setBeliefLeader(session, rpcMsg, function(err, result) {
        logger.error("setBeliefLeader", beliefId, pos, playerId);
    });
};

chairmanMatchService.prototype.initByDB = function(cb)
{
    this.initCompetitionDate();
    let nowTimePeriod = this.checkMatchPeriod();
    //获取数据库数据，初始化
    let self = this;
    this.matchDao.findChMatchInfo(function (err, data) {
        logger.debug("findChMatchInfo initByDB data:", data);
        if(!!data) {
            for(let i in data.matchInfo.allCompetition)
            {
                let tmp = {};
                tmp.preBattleInfoMap = self.preBattleInfoArrayToMap(data.matchInfo.allCompetition[i].preBattleInfoMap) || new Map();
                tmp.preBattleRoundCursor = data.matchInfo.allCompetition[i].preBattleRoundCursor || 0;
                tmp.preBattleList = utils.cloneArray(data.matchInfo.allCompetition[i].preBattleList) || [];
                tmp.preBattleOutUidList = utils.cloneArray(data.matchInfo.allCompetition[i].preBattleOutUidList) || [];
                tmp.dqCup64BattleList = utils.cloneArray(data.matchInfo.allCompetition[i].dqCup64BattleList) || [];
                tmp.dqCup32BattleList = utils.cloneArray(data.matchInfo.allCompetition[i].dqCup32BattleList) || [];
                tmp.dqCup16BattleList = utils.cloneArray(data.matchInfo.allCompetition[i].dqCup16BattleList) || [];
                tmp.dqCup8BattleList = utils.cloneArray(data.matchInfo.allCompetition[i].dqCup8BattleList) || [];
                tmp.dqCup4BattleList = utils.cloneArray(data.matchInfo.allCompetition[i].dqCup4BattleList) || [];
                tmp.ch3BattleList = utils.cloneArray(data.matchInfo.allCompetition[i].ch3BattleList) || [];         //争三对战信息
                tmp.dqCup2BattleList = utils.cloneArray(data.matchInfo.allCompetition[i].dqCup2BattleList) || [];
                tmp.thirdPlace = utils.deepCopy(data.matchInfo.allCompetition[i].thirdPlace) || {};
                tmp.secondPlace = utils.deepCopy(data.matchInfo.allCompetition[i].secondPlace) || {};
                tmp.coChairman = utils.deepCopy(data.matchInfo.allCompetition[i].coChairman) || {};
                tmp.chairman = utils.deepCopy(data.matchInfo.allCompetition[i].chairman) || {};
                tmp.outBattleRound = data.matchInfo.allCompetition[i].outBattleRound || 0;
                tmp.agoDqCup64BattleList = utils.cloneArray(data.matchInfo.allCompetition[i].agoDqCup64BattleList) || [];
                tmp.campaignList = utils.cloneArray(data.matchInfo.allCompetition[i].campaignList) || [];
                tmp.matchPeriod = data.matchInfo.allCompetition[i].matchPeriod || commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_INIT;	//该阶段完成后设置
                tmp.finishPeriod = data.matchInfo.allCompetition[i].finishPeriod || commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_INIT;
                tmp.PeriodWait = data.matchInfo.allCompetition[i].PeriodWait || false;
                tmp.honorSeasonId = data.matchInfo.allCompetition[i].honorSeasonId || 0;            //荣誉墙赛季
                if(data.matchInfo.allCompetition[i].matchPeriod < nowTimePeriod)
                {
                    tmp.PeriodWait = false;
                }
                self.allCompetition.set(data.matchInfo.allCompetition[i].beliefId, tmp);
            }
            self.honorMap = self.honorMapInfoArrayToMap(data.matchInfo.honorMap) || new Map();
            self.isStat = data.matchInfo.isStat || false;
        }
        //logger.debug("initByDB self.matchChampionRecord: ", self.matchChampionRecord);
        //return cb(null);
    });

    //测试模式下，自动报名
    // if (debugConfig.isForTest) {
    //     //let championUid = self.getLastChampion().uid;
    //     self.matchDao.getAccountInfoLimit(1200, function (doc) {
    //         //let arr = [];
    //         for (let i=0,lens=doc.length;i<lens;i++) {
    //             // if(doc[i].uid === championUid) {
    //             //     continue;
    //             // }
    //             let enrollData = {
    //                 gid: doc[i].gid,
    //                 win: 0,
    //                 lose: 0,
    //                 draw: 0,
    //                 roomUidList: [],
    //                 faceUrl: 'test',
    //                 name: doc[i].name
    //             };
    //             //arr.push(enrollData);
    //             let beliefId = Math.floor(Math.random()*16) + 1;
    //             let chairman = self.allCompetition.get(beliefId);
    //             //logger.error("!!!!!!!!!!!!!!", chairman, beliefId);
    //             chairman.preBattleInfoMap.set(doc[i].uid, enrollData);
    //         }
    //         return cb(null);
    //     });
    // }
    //logger.error("initByDB:::::::::::::", this.allCompetition);
    cb(null);
};

chairmanMatchService.prototype.updateDB = function (cb) {

    let data = {};
    let tmpList = [];
    let num = 0;
    let length = 0;
    for(let [k, v] of this.allCompetition)
    {
        let list = this.preBattleInfoMapToArray(k);
        if(list === [])
        {
            num++;
        }
        length++;
    }
    if(num === length)
        return;

    for(let [k, v] of this.allCompetition)
    {
        let tmp = {};
        tmp.beliefId = k;
        tmp.preBattleInfoMap = this.preBattleInfoMapToArray(k);
        tmp.preBattleRoundCursor = v.preBattleRoundCursor;
        tmp.preBattleList = utils.cloneArray(v.preBattleList);
        tmp.preBattleOutUidList = utils.cloneArray(v.preBattleOutUidList);
        tmp.dqCup64BattleList = utils.cloneArray(v.dqCup64BattleList);
        tmp.dqCup32BattleList = utils.cloneArray(v.dqCup32BattleList);
        tmp.dqCup16BattleList = utils.cloneArray(v.dqCup16BattleList);
        tmp.dqCup8BattleList = utils.cloneArray(v.dqCup8BattleList);
        tmp.dqCup4BattleList = utils.cloneArray(v.dqCup4BattleList);
        tmp.ch3BattleList = utils.cloneArray(v.ch3BattleList);
        tmp.dqCup2BattleList = utils.cloneArray(v.dqCup2BattleList);
        tmp.thirdPlace = utils.deepCopy(v.thirdPlace);
        tmp.secondPlace = utils.deepCopy(v.secondPlace);
        tmp.coChairman = utils.deepCopy(v.coChairman);
        tmp.chairman = utils.deepCopy(v.chairman);
        tmp.outBattleRound = v.outBattleRound;
        tmp.agoDqCup64BattleList = utils.cloneArray(v.agoDqCup64BattleList);
        tmp.campaignList = utils.cloneArray(v.campaignList);
        tmp.matchPeriod = v.matchPeriod;	//该阶段完成后设置
        tmp.finishPeriod = v.finishPeriod;
        tmp.PeriodWait = v.PeriodWait;
        tmp.honorSeasonId = v.honorSeasonId;
        tmpList.push(tmp);
    }

    // data.matchPeriod = this.matchPeriod;
    // data.finishPeriod = this.finishPeriod;
    // data.finishNum = utils.cloneArray(this.finishNum);
    data.allCompetition = tmpList;
    data.honorMap = this.honorMapInfoMapToArray(this.honorMap);
    data.isStat = this.isStat;
    this.matchDao.updateChMatchInfoToDB(data, function (err) {
        if(cb) { cb(null); }
    });
};

chairmanMatchService.prototype.preBattleInfoMapToArray = function(beliefId) {
    // (playerId, {win: 0, lose: 0, draw: 0, gid: gid, roomUidList: [], faceUrl: faceUrl, name: name});
    let chairman = this.allCompetition.get(beliefId);
    let arr = [];
    for(let [k, v] of chairman.preBattleInfoMap) {
        let info = {};
        info.uid = k;
        info.gid = v.gid;
        info.win = v.win;
        info.lose = v.lose;
        info.draw = v.draw;
        info.roomUidList = utils.cloneArray(v.roomUidList);
        info.faceUrl = v.faceUrl;
        info.name = v.name;
        arr.push(info);
    }
    return arr;
};

chairmanMatchService.prototype.honorMapInfoMapToArray = function() {
    let arr = [];
    for(let [k, v] of this.honorMap) {
        let info = {};
      for(let i in v)
      {
          info.beliefId = k;
          info.time = v[i].time;
          info.chairman = v[i].chairman;
          info.coChairman = v[i].coChairman;
          info.secondPlace = v[i].secondPlace;
          info.thirdPlace = v[i].thirdPlace;
          arr.push(utils.deepCopy(info));
      }
    }
    // arr = [{beliefId, time, chairman, cochairman, secondPlace, thirdPlace}, ...]
    return arr;
};

chairmanMatchService.prototype.preBattleInfoArrayToMap = function(arr) {
    let map = new Map();
    for(let i=0,lens=arr.length; i<lens; i++) {
        let info = {};
        info.gid = arr[i].gid;
        info.win = arr[i].win;
        info.lose = arr[i].lose;
        info.draw = arr[i].draw;
        info.roomUidList = utils.cloneArray(arr[i].roomUidList);
        info.faceUrl = arr[i].faceUrl;
        info.name = arr[i].name;
        map.set(arr[i].uid, info);
    }
    logger.debug("preBattleInfoArrayToMap arr: ", arr.length);
    return map;
};

chairmanMatchService.prototype.honorMapInfoArrayToMap = function(arr) {
    let map = new Map();
    if(!arr || arr.length <= 0)
        return map;
    for(let i in arr)
    {
       let data = {};
       data.time = arr[i].time;
       data.chairman = arr[i].chairman;
       data.coChairman = arr[i].coChairman;
       data.secondPlace = arr[i].secondPlace;
       data.thirdPlace = arr[i].thirdPlace;
       //logger.error("###########", data);
       if(map.has(arr[i].beliefId))
       {
            let list = map.get(arr[i].beliefId);
            list.push(utils.deepCopy(data));
       }
       else
       {
            let list = [];
           list.push(utils.deepCopy(data));
            map.set(arr[i].beliefId, utils.cloneArray(list));
       }
    }
    //logger.debug("honorMapInfoArrayToMap arr: ", arr.length);
    return map;
};


chairmanMatchService.prototype.loopProcess = function() {
    //计算出当前的时间所属的阶段
    let nowTimePeriod = this.checkMatchPeriod();
    let ready = 0;
    for(let [k, v] of this.allCompetition)
    {
        //根据已进行的状态和当前状态判定进行什么逻辑处理, 下一次状态
        let nextPeriod = this.calcNextPeriod(nowTimePeriod, v.matchPeriod, v.finishPeriod);
        logger.debug("chairmanMatchService loopProcess in ...", k, v.matchPeriod, nextPeriod, nowTimePeriod);
        if(v.finishPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_REWARD)
            ready++;
        this.dispatchProcess(k, nextPeriod);
    }
    if(ready === this.allCompetition.size && !this.isStat)
    {
        this.isStat = true;
        //统计数据
        this.statisticalData(function () {
        });
        this.announcement(function () {
        });
    }
    //存储数据
    this.updateDB();
};

chairmanMatchService.prototype.getSchduleConfigHourAndMinute = function (configId) {
    let timeArr = dataApi.allData.data["ChiefBattleSchedule"][configId].Time.split(":");
    return {hour: parseInt(timeArr[0], 10), min: parseInt(timeArr[1], 10)};
};
//这里设置赛程时间
chairmanMatchService.prototype.checkMatchPeriod = function () {
    //检查状态
    let date = new Date();
    logger.debug("checkMatchPeriod date: ", date, date.getDay());

    //首日特殊处理
    let matchStartDay = clusterConfig.dq_match_start_day.split('-');
    logger.debug("matchStartDay: ", matchStartDay, date.getFullYear(), date.getMonth(), date.getDate());
    if(date.getFullYear() === parseInt(matchStartDay[0]) && (date.getMonth()+1) === parseInt(matchStartDay[1]) && date.getDate() === parseInt(matchStartDay[2])) {
        for(let [k, v] of this.allCompetition)
        {
            v.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
            v.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
        }
        return commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
    }

    let period = commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_INIT;
    //0. 休赛期(周五晚上比赛结束到周一下一轮比赛开始)
    let enrollStartTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.ENROLL_START);
    let enrollEndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.ENROLL_END);
    let perMatchStartTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.PRE_MATCH_START);
    let perMatchEndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.PRE_MATCH_END);

    let match64EndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_64_END);
    let match32EndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_32_END);
    let match16EndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_16_END);
    let match8EndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_8_END);
    let match4EndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_4_END);
    let match2EndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_2_END);
    let campaignStartTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.CAMPAIGN_START);
    let campaignEndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.CAMPAIGN_END);
    let sendRewardTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.SEND_REWARD);
    let sendRewardEndTime = this.getSchduleConfigHourAndMinute(commonEnum.CHAIRMAN_MATCH_SCHEDULE.SEND_FINISHED);

    if((date.getDay()===4 && (date.getHours()>enrollStartTime.hour || (date.getHours()===enrollStartTime.hour && date.getMinutes()>=enrollStartTime.min))) ||
        date.getDay()===5 ||
        date.getDay()===6 ||
        (date.getDay()===0 && ((date.getHours()<enrollEndTime.hour || (date.getHours()===enrollEndTime.hour && date.getMinutes()<enrollEndTime.min)))))
    {
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
        for(let [k, v] of this.allCompetition)
        {
            v.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
            v.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
        }
    }else if(this.timeInPeriod(date, enrollEndTime, perMatchStartTime) && date.getDay()===0) {
        //2. 报名结束期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_END;
    }else if(this.timeInPeriod(date, perMatchStartTime, perMatchEndTime) && date.getDay()===0) {
        //2. 预选赛期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.PRE_MATCH;
    }else if(this.timeInPeriod(date, perMatchEndTime, match64EndTime) && date.getDay()===0) {
        //3. 64强比赛期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_64;
    }else if(this.timeInPeriod(date, match64EndTime, match32EndTime) && date.getDay()===0) {
        //3. 32强比赛期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_32;
    }else if(this.timeInPeriod(date, match32EndTime, match16EndTime) && date.getDay()===0) {
        //4. 16强比赛期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_16;
    }else if(this.timeInPeriod(date, match16EndTime, match8EndTime) && date.getDay()===0) {
        //5. 8强比赛期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_8;
    }else if(this.timeInPeriod(date, match8EndTime, match4EndTime) && date.getDay()===0) {
        //6. 4强比赛期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_4;
    }else if(this.timeInPeriod(date, match4EndTime, match2EndTime) && date.getDay()===0) {
        //7. 2强比赛期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_2;
    }else if(this.timeInPeriod(date, match2EndTime, campaignStartTime) && date.getDay()===0) {
        //7. 决赛结束竞选等待期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.WAIT_REWARD;
    }else if(this.timeInPeriod(date, campaignStartTime, campaignEndTime) && date.getDay()===0) {
        //8. 竞选开始
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.CAMPAIGN_START;
    }else if(this.timeInPeriod(date, campaignEndTime, sendRewardTime) && date.getDay()===0) {
        //9. 竞选结束
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.CAMPAIGN_END;
    }else if(this.timeInPeriod(date, sendRewardTime, sendRewardEndTime) && date.getDay()===0) {
        //10. 奖励发放期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_REWARD;
        // for(let [k, v] of this.allCompetition)
        // {
        //     //状态循环
        //     if(v.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_INIT
        //         || v.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START) {
        //         period = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
        //         v.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
        //         v.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
        //     }
        // }
    }else if((date.getHours() > sendRewardEndTime || (date.getHours()===sendRewardEndTime.hour && date.getMinutes()>=sendRewardEndTime.min)) && date.getDay()===0) {
        //发奖励结束
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_FINISHED;
    }else {
        //其他时间就为休止期
        period = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_INIT;
        this.isStat = false;
        // this.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL;
        // this.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL;
        for(let [k, v] of this.allCompetition)
        {
            v.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_INIT;
            v.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_INIT;
        }
    }
    logger.debug("checkMatchPeriod period", period);
    return period;
};
//各状态具体执行函数
chairmanMatchService.prototype.dispatchProcess = function (beliefId, nextPeriod) {
    //logger.debug("dispatchProcess nextPeriod: ", nextPeriod);
    let chairmanData = this.allCompetition.get(beliefId);
    switch (nextPeriod) {
        case commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START:
            this.matchEnrollProcess(beliefId);
            break;
        case commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_END:
            this.matchEnrollEndProcess(beliefId);
            break;
        case commonEnum.CHAIRMAN_MATCH_PERIOD.PRE_MATCH:
            this.preMatchProcess(beliefId, chairmanData);//第一阶段淘汰赛
            break;
        case commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_64:
        case commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_32:
        case commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_16:
        case commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_8:
        case commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_4:
        case commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_2:
            this.outBattleProcess(beliefId);//第二阶段淘汰赛
            break;
        case commonEnum.CHAIRMAN_MATCH_PERIOD.WAIT_REWARD://竞选准备阶段
            chairmanData.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.WAIT_REWARD;
            chairmanData.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.WAIT_REWARD;
            break;
        case commonEnum.CHAIRMAN_MATCH_PERIOD.CAMPAIGN_START://竞选开始
            chairmanData.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.CAMPAIGN_START;
            chairmanData.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.CAMPAIGN_START;
            break;
        case commonEnum.CHAIRMAN_MATCH_PERIOD.CAMPAIGN_END://竞选结束
            if(chairmanData.finishPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.CAMPAIGN_END)
                break;
            chairmanData.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.CAMPAIGN_END;
            if(!!chairmanData.campaignList[0])
            {
                let data = {};
                let campaignReturn = chairmanData.campaignList;
                data.uid = chairmanData.campaignList[0].uid;
                data.name = chairmanData.campaignList[0].name;
                data.gid = chairmanData.campaignList[0].gid;
                data.faceUrl = chairmanData.campaignList[0].faceUrl;
                chairmanData.coChairman = data;//竞选第一名当选副主席
                if(chairmanData.thirdPlace.uid === data.uid)
                {
                    chairmanData.thirdPlace = {};
                }
                if(chairmanData.secondPlace.uid === data.uid)
                {
                    chairmanData.secondPlace = {};
                }
                campaignReturn.splice(0, 1);
                if(campaignReturn.length > 0)
                {
                    let uidList = [];
                    for(let i in campaignReturn)
                    {
                        let tmp = {};
                        tmp.uid = campaignReturn[i].uid;
                        tmp.gid = campaignReturn[i].gid;
                        tmp.num = campaignReturn[i].num;
                        uidList.push(utils.deepCopy(tmp));
                    }
                    this.campaignReturnMail(beliefId, uidList);
                }
                //chairmanData.campaignList = [];
                //设置副主席
                this.setBeliefLeader(beliefId, 2);
            }
            chairmanData.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.CAMPAIGN_END;
            break;
        case commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_REWARD:
            this.sendReward(beliefId);
            break;
        case commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_FINISHED:
            this.chairmanMatchReset(beliefId);
            break;
        default:
            //logger.error("chairmanMatchService dispatchProcess error nextPeriod:", nextPeriod);
            break;
    }
};

chairmanMatchService.prototype.timeInPeriod = function(nowDate, startTimeObj, endTimeObj) {
    //是否在某个时间区间
    let result = false;
    //logger.debug("timeInPeriod: ", nowDate.getHours(), nowDate.getMinutes(), startTimeObj, endTimeObj);
    if((nowDate.getHours()>startTimeObj.hour || (nowDate.getHours()===startTimeObj.hour && nowDate.getMinutes()>=startTimeObj.min))
        && (nowDate.getHours()<endTimeObj.hour || (nowDate.getHours()===endTimeObj.hour && nowDate.getMinutes()<endTimeObj.min))) {
        result = true;
    }
    return result;
};
//根据已进行的状态和当前状态计算下一次阶段
chairmanMatchService.prototype.calcNextPeriod = function(nowPeriod, matchPeriod, finishPeriod) {
    let date = new Date();
    let nextPeriod = nowPeriod;
    //logger.error("calcNextPeriod nowPeriod, matchPeriod: ", nowPeriod, matchPeriod);
    if(nowPeriod === commonEnum.CHAIRMAN_MATCH_SCHEDULE.ENROLL_START &&
        matchPeriod ===  commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_INIT){
        matchPeriod = commonEnum.CHAIRMAN_MATCH_SCHEDULE.ENROLL_START;
        finishPeriod = matchPeriod;
        return nextPeriod = matchPeriod+1;
    }

    if(finishPeriod === commonEnum.CHAIRMAN_MATCH_SCHEDULE.MATCH_INIT) {
        finishPeriod = matchPeriod;
    }
    if(matchPeriod !== nowPeriod) {
        if(finishPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_FINISHED) {
            nextPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_INIT;
        }else {
            if(finishPeriod === matchPeriod) {
                nextPeriod = matchPeriod+1;
            }
            else {
                nextPeriod = matchPeriod;
            }
        }
    }
    return nextPeriod;
};

//报名期的处理
chairmanMatchService.prototype.matchEnrollProcess = function (beliefId) {
    //处于报名期
    let chairmanData = this.allCompetition.get(beliefId);
    chairmanData.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
    chairmanData.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_START;
    chairmanData.chairman = {};
    chairmanData.coChairman = {};
    chairmanData.secondPlace = {};
    chairmanData.thirdPlace = {};
    chairmanData.PeriodWait = false;
};

//报名期结束处理
chairmanMatchService.prototype.matchEnrollEndProcess = function (beliefId) {
    //处于报名结束期
    let chairmanData = this.allCompetition.get(beliefId);
    //检查报名结束是否已处理过
    let nowTimePeriod = this.checkMatchPeriod();
    if(chairmanData.matchPeriod >= commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_END) {
        logger.error("matchEnrollProcess finish. wait time over. period:", beliefId, nowTimePeriod, chairmanData.matchPeriod);
        return ;
    }
    chairmanData.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_END;
    chairmanData.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.ENROLL_END;
    logger.error("清空职位！！！！！！！！！！！！！！！！！1", beliefId);
    //更新赛季
    chairmanData.honorSeasonId += 1;
    //清空职位
    let self = this;
    let session = {frontendId: self.app.getServerId()};
    //清空职位
    self.app.rpc.datanode.dataNodeRemote.resetAllBeliefLeader(session, beliefId, function (code) {
        //logger.error("清空职位！！！！！！！！！！！！！！！！！", beliefId);
        // chairmanData.PeriodWait = false;
    });
};


//预选赛区: 1. 判定预选赛是否结束, 2. 预选赛分组, 3. 战斗
chairmanMatchService.prototype.preMatchProcess = function(beliefId, chairmanData) {
    let isFinished = this.checkPreMatchfinished(beliefId);
    if(isFinished) {
        logger.error("preMatchProcess finished!");
        // if(this.whetherNext(beliefId))
        // {
        chairmanData.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.PRE_MATCH;
        // }
        return ;
    }
    if(chairmanData.PeriodWait) {
        logger.error("preMatchProcess Wait!", chairmanData.PeriodWait);
        return;
    }
    //battle
    let toBattleList = [];
    if(chairmanData.preBattleList.length-chairmanData.preBattleRoundCursor>=this.preMatchMaxNum) {
        for(let i=0; i<this.preMatchMaxNum; i++) {
            toBattleList.push(chairmanData.preBattleList[chairmanData.preBattleRoundCursor+i]);
        }
    }else {
        for(let i=chairmanData.preBattleRoundCursor, lens=chairmanData.preBattleList.length; i<lens; i++) {
            toBattleList.push(chairmanData.preBattleList[i]);
        }
    }
    logger.debug("preMatchProcess toBattleList: ", toBattleList);
    let self = this;
    chairmanData.PeriodWait = true;
    async.eachSeries(toBattleList, function (battleInfo, callback) {
        //1. 普通
        let session = {frontendId: self.app.getServerId()};
        let battleServers = self.app.getServersByType("battle");
        let index = calcUtils.randRange(0, battleServers.length-1);
        session.toServerId = battleServers[index].id;
        if(battleInfo.home !== self.noneUid && battleInfo.away !== self.noneUid) {
            self.app.rpc.battle.battleRemote.pvpChairmanBattleReq(session, battleInfo, function (code, retMsg) {
                chairmanData.preBattleRoundCursor++;
                if(code!== Code.OK) {
                    logger.error("pvpDqCupBattleReq error", code, battleInfo);
                    callback(null);
                }else {
                    //战斗结果
                    let homeObj = chairmanData.preBattleInfoMap.get(battleInfo.home);
                    let awayObj = chairmanData.preBattleInfoMap.get(battleInfo.away);
                    if(retMsg.homeScore > retMsg.awayScore) {
                        homeObj.win++;
                        awayObj.lose++;
                    }else if(retMsg.homeScore === retMsg.awayScore) {
                        homeObj.draw++;
                        awayObj.draw++;
                    }else {
                        homeObj.lose++;
                        awayObj.win++;
                    }
                    //插入战斗录像数据
                    homeObj.roomUidList.push({roomUid: retMsg.roomUid, home:retMsg.home, away: retMsg.away,
                        homeScore: retMsg.homeScore, awayScore: retMsg.awayScore, time: TimeUtils.now()});
                    awayObj.roomUidList.push({roomUid: retMsg.roomUid, home:retMsg.home, away: retMsg.away,
                        homeScore: retMsg.homeScore, awayScore: retMsg.awayScore, time: TimeUtils.now()});
                    callback(null);
                }
            })
        }else if(battleInfo.home === self.noneUid && battleInfo.away === self.noneUid) {
            logger.error("preMatchProcess grouping error. home and away is all noneUid");
            chairmanData.preBattleRoundCursor++;
            callback(null);
        }else {
            //2. 轮空
            if(battleInfo.home !== self.noneUid) {
                //home win
                let homeObj = chairmanData.preBattleInfoMap.get(battleInfo.home);
                homeObj.win++;
                homeObj.roomUidList.push({roomUid: self.noneRoomUid, home: battleInfo.home, away: self.noneUid});
            }else {
                //away win
                let awayObj = chairmanData.preBattleInfoMap.get(battleInfo.away);
                awayObj.win++;
                awayObj.roomUidList.push({roomUid: self.noneRoomUid, home: self.noneUid, away: battleInfo.away});
            }
            chairmanData.preBattleRoundCursor++;
            callback(null);
        }
    }, function(err) {
        //该轮比赛打完，重新分组
        if(chairmanData.preBattleRoundCursor >= chairmanData.preBattleList.length) {
            chairmanData.preBattleRoundCursor = 0;
            self.preMatchGrouping(beliefId);
        }
        chairmanData.PeriodWait = false;
    })
};

chairmanMatchService.prototype.checkPreMatchfinished = function (beliefId) {
    //let isFirstGrouping = false;
    let chairmanData = this.allCompetition.get(beliefId);
    if(chairmanData.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.PRE_MATCH) {
        logger.debug("checkPreMatchfinished matchPeriod: ", chairmanData.matchPeriod);
        return true;
    }
    logger.debug("checkPreMatchfinished preBattleList 111: ", chairmanData.preBattleList);
    //预选赛开始, 进行第一次分组
    if(chairmanData.preBattleList.length === 0){
        this.preMatchGrouping(beliefId);
        // this.champion = this.getLastChampion(beliefId);
    }
    logger.debug("checkPreMatchfinished preBattleList: ", chairmanData.preBattleList);
    //比赛人数少于64人
    let leftPlayerList = [];
    for(let i=0, lens=chairmanData.preBattleList.length; i<lens; i++) {
        let uid1 = chairmanData.preBattleList[i].home;
        let uid2 = chairmanData.preBattleList[i].away;
        if(uid1 !== this.noneUid && chairmanData.preBattleInfoMap.get(uid1).lose < this.preBattleOutTimes) {
            leftPlayerList.push({uid: uid1, gid: chairmanData.preBattleInfoMap.get(uid1).gid});
        }
        if(uid2 !== this.noneUid && chairmanData.preBattleInfoMap.get(uid2).lose < this.preBattleOutTimes) {
            leftPlayerList.push({uid: uid2, gid: chairmanData.preBattleInfoMap.get(uid2).gid});
        }
        // if(uid1 !== this.noneUid && chairmanData.preBattleInfoMap.get(uid1).lose >= this.preBattleOutTimes) {
        //     chairmanData.preBattleOutUidList.push(uid1);
        // }
        // if(uid2 !== this.noneUid && chairmanData.preBattleInfoMap.get(uid2).lose >= this.preBattleOutTimes) {
        //     chairmanData.preBattleOutUidList.push(uid2);
        // }
    }
    logger.debug("checkPreMatchfinished pre battle num: ", leftPlayerList.length);
    //已决出64强
    if(leftPlayerList.length === this.match64Num) {
        //let battleList = this.getBattleListByPyPeriod(this.matchPeriod);
        let roundNum = this.getNumByMatchPeriod(commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_64);
        //预选赛进64强
        if(roundNum === this.match64Num) {
            this.preBattleTo64Grouping(beliefId, leftPlayerList, roundNum);
            // if(this.whetherNext(beliefId))
            // {
                chairmanData.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.PRE_MATCH;
            // }
        }
        return true;
    }else if(leftPlayerList.length < this.match64Num) {
        //取人数补充
        let num = this.match64Num - leftPlayerList.length;
        let needAddNum = num;
        logger.debug("this.preBattleOutUidList , num: ", chairmanData.preBattleOutUidList.length, num);
        for(let i=0; i<needAddNum; i++) {
            if(chairmanData.preBattleOutUidList.length <= 0) {
                break;
            }
            let uid1 = chairmanData.preBattleOutUidList.pop();
            logger.debug("this.preBattleOutUidList: uid1 ", uid1);
            leftPlayerList.push({uid: uid1, gid: chairmanData.preBattleInfoMap.get(uid1).gid});
            num--;
            if(num <= 0) {
                break;
            }
            let uid2 = chairmanData.preBattleOutUidList.pop();
            leftPlayerList.push({uid: uid2, gid: chairmanData.preBattleInfoMap.get(uid2).gid});
            num--;
            if(num <= 0) {
                break;
            }
        }
        let roundNum = this.getNumByMatchPeriod(commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_64);
        //预选赛进64强
        if(roundNum === this.match64Num) {
            this.preBattleTo64Grouping(beliefId, leftPlayerList, roundNum);
            // if(this.whetherNext(beliefId))
            // {
                chairmanData.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.PRE_MATCH;
            // }
        }
        return true;
    }else {
        return false;
    }
};

chairmanMatchService.prototype.preMatchGrouping = function (beliefId) {
    let CompetitionDate = this.allCompetition.get(beliefId);
    //判断是否需要重新分组
    if(CompetitionDate.preBattleRoundCursor !== 0) {
        logger.debug("this.preBattleRoundCursor round not finished.", CompetitionDate.preBattleRoundCursor);
        return ;
    }
    let list = [];
    //符合条件的加入列表
    for(let [k,v] of CompetitionDate.preBattleInfoMap) {
        if(v.lose < this.preBattleOutTimes) {
            list.push({uid: k, gid: v.gid});
        }else if(CompetitionDate.preBattleOutUidList.indexOf(k) === -1) {
            CompetitionDate.preBattleOutUidList.push(k);
        }
    }
    //随机排序
    list = this.randomSort(list);
    //清空之前数据
    CompetitionDate.preBattleList = [];
    //分组
    for(let i=0, lens=Math.ceil(list.length/2); i<lens; i++) {
        let homeInfo = list[2*i];
        let awayInfo = list[2*i+1];
        if(!homeInfo) {
            homeInfo = {uid: this.noneUid};
        }
        if(!awayInfo) {
            awayInfo = {uid: this.noneUid};
        }
        CompetitionDate.preBattleList.push({home: homeInfo.uid, homeGid: homeInfo.gid,
            away: awayInfo.uid, awayGid: awayInfo.gid});
    }
};

chairmanMatchService.prototype.randomSort = function(list) {
    let clonelist = list.concat();
    let len = clonelist.length;
    for(let i=0;i<len;i++) {
        let index = Math.floor(Math.random() * clonelist.length);
        let tmp = clonelist[index];
        clonelist[index] = clonelist[i];
        clonelist[i] = tmp;
    }
    return clonelist;
};

//淘汰赛处理
chairmanMatchService.prototype.outBattleProcess = function(beliefId) {
    let chairmanData = this.allCompetition.get(beliefId);
    //检查该轮淘汰赛是否已经打完
    let nowTimePeriod = this.checkMatchPeriod();
    //logger.error("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!淘汰赛处理", beliefId, nowTimePeriod, chairmanData.matchPeriod);
    if(nowTimePeriod <= chairmanData.matchPeriod || chairmanData.PeriodWait) {
        logger.error("outBattleProcess finish. wait time over. period:", beliefId, nowTimePeriod, chairmanData.matchPeriod);
        return ;
    }

    let battleList = this.getBattleListByPyPeriod(beliefId, chairmanData.matchPeriod + 1);
    //没有人打的情况, 直接跳过
    if(battleList.length === 0) {
        chairmanData.matchPeriod++;
        chairmanData.finishPeriod = chairmanData.matchPeriod;
        chairmanData.PeriodWait = false;
        return ;
    }
    //let roundNum = this.getNumByMatchPeriod(this.matchPeriod);
    chairmanData.PeriodWait = true;
    //战斗
    let self = this;
    async.eachSeries(battleList, function (battleInfo, callback) {
        //logger.debug("outBattle start, battleInfo: ", battleInfo);
        if(battleInfo.home === self.noneUid && battleInfo.away === self.noneUid) {
            battleInfo.homeHomeScore += 1;
            callback(null);
        }else if(battleInfo.home === self.noneUid) {
            battleInfo.awayHomeScore += 1;
            callback(null);
        }else if(battleInfo.away === self.noneUid) {
            battleInfo.homeHomeScore += 1;
            callback(null);
        }else {
            let session = {frontendId: self.app.getServerId()};
            let battleServers = self.app.getServersByType("battle");
            let index = calcUtils.randRange(0, battleServers.length-1);
            session.toServerId = battleServers[index].id;
            self.app.rpc.battle.battleRemote.pvpChairmanBattleReq(session, battleInfo, function (code, retMsg) {
                if (code !== Code.OK) {
                    logger.error("pvpChairmanBattleReq error", code, battleInfo);
                    callback(null);
                } else {
                    //战斗结果
                    if(chairmanData.outBattleRound === 1) {
                        //主客场轮换
                        battleInfo.homeAwayScore = retMsg.homeScore;
                        battleInfo.awayHomeScore = retMsg.awayScore;
                    }else {
                        battleInfo.homeHomeScore = retMsg.homeScore;
                        battleInfo.awayAwayScore = retMsg.awayScore;
                    }
                    //战斗录像
                    if(!!battleInfo.roomUid1) {
                        battleInfo.roomUid2 = retMsg.roomUid;
                        battleInfo.time2 = TimeUtils.now();
                    }else {
                        battleInfo.roomUid1 = retMsg.roomUid;
                        battleInfo.time1 = TimeUtils.now();
                    }
                    callback(null);
                }
            });
        }
    }, function (err) {
        //轮换标志增加
        chairmanData.outBattleRound++;
        chairmanData.PeriodWait = false;
        //修改64强,32强,16强比赛只用打1轮淘汰赛
        if(chairmanData.outBattleRound === 2 || chairmanData.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.PRE_MATCH || chairmanData.matchPeriod
            === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_64 || chairmanData.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_32) {
            //logger.error("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!打完", beliefId, nowTimePeriod, chairmanData.matchPeriod);
            chairmanData.PeriodWait = false;
            if(chairmanData.matchPeriod < nowTimePeriod)
            {
                chairmanData.outBattleRound = 0;
                chairmanData.matchPeriod++;
                chairmanData.finishPeriod = chairmanData.matchPeriod;
            }
            else if(chairmanData.matchPeriod >= nowTimePeriod)
            {
                chairmanData.outBattleRound = 0;
                chairmanData.matchPeriod = nowTimePeriod;
                chairmanData.finishPeriod = chairmanData.matchPeriod;
                return;
            }
            let nextBattleList = self.getBattleListByPyPeriod(beliefId, chairmanData.matchPeriod+1);

            let index = 0;
            //晋级下一轮结算
            for(let i=0,lens=battleList.length; i<lens; i++) {
                //logger.debug("compare battleInfo: ", battleList[i]);
                //取胜者
                let winner = "home";
                let loser = "away";
                //1. 总比分
                let homeAllScore = battleList[i].homeHomeScore + battleList[i].homeAwayScore;
                let awayAllScore = battleList[i].awayHomeScore + battleList[i].awayAwayScore;
                if(homeAllScore < awayAllScore) {
                    winner = "away";
                    loser = "home";
                }else if(homeAllScore > awayAllScore) {
                    winner = "home";
                    loser = "away";
                }else {
                    //2. 客场进球数
                    if(battleList[i].homeAwayScore > battleList[i].awayAwayScore) {
                        winner = "home";
                        loser = "away";
                    }else if(battleList[i].homeAwayScore < battleList[i].awayAwayScore) {
                        winner = "away";
                        loser = "home";
                    }else {
                        //3. 等级 to do
                        //4. 随机选一个
                        let randNum = utils.random(1, 100);
                        if(randNum <= 50) {
                            winner = "home";
                            loser = "away";
                        }else {
                            winner = "away";
                            loser = "home";
                        }
                    }
                }
                //记录胜者
                battleList[i].winnerUid = battleList[i][winner];
                logger.debug("index[%d] winner: %s", index, winner, i, battleList[i]);
                let groupIndex = Math.floor(index/2);
                if(!nextBattleList[groupIndex]) {
                    nextBattleList[groupIndex] = {};
                }
                if(index%2 === 0) {
                    if(!battleList[i][winner]) {
                        nextBattleList[groupIndex].home = self.noneUid;
                        nextBattleList[groupIndex].homeGid = "";
                    }else {
                        nextBattleList[groupIndex].home = battleList[i][winner];
                        nextBattleList[groupIndex].homeGid = battleList[i][winner+"Gid"];
                    }
                    nextBattleList[groupIndex].homeHomeScore = 0;
                    nextBattleList[groupIndex].homeAwayScore = 0;
                }else {
                    if(!battleList[i][winner]) {
                        nextBattleList[groupIndex].away = self.noneUid;
                        nextBattleList[groupIndex].awayGid = "";
                    }else {
                        nextBattleList[groupIndex].away = battleList[i][winner];
                        nextBattleList[groupIndex].awayGid = battleList[i][winner+"Gid"];
                    }
                    nextBattleList[groupIndex].awayHomeScore = 0;
                    nextBattleList[groupIndex].awayAwayScore = 0;
                }
                if(battleList.length === 2 && chairmanData.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_4)
                {
                    //4强结束把两个败者加入争3组争第三
                    let chairmanData = self.allCompetition.get(beliefId);
                    let ch3BattleList = chairmanData.ch3BattleList;
                    let data = {home: self.noneUid, homeGid: "", homeHomeScore: 0, homeAwayScore: 0,
                        away: self.noneUid, awayGid: "", awayHomeScore: 0, awayAwayScore: 0};
                    if(ch3BattleList[groupIndex])
                    {
                        data = ch3BattleList[groupIndex];
                    }
                    if(index%2 === 0) {
                        if(!battleList[i][loser]) {
                            data.home = self.noneUid;
                            data.homeGid = "";
                        }else {
                            data.home = battleList[i][loser];
                            data.homeGid = battleList[i][loser+"Gid"];
                        }
                        data.homeHomeScore = 0;
                        data.homeAwayScore = 0;
                    }else {
                        if(!battleList[i][loser]) {
                            data.away = self.noneUid;
                            data.awayGid = "";
                        }else {
                            data.away = battleList[i][loser];
                            data.awayGid = battleList[i][loser+"Gid"];
                        }
                        data.awayHomeScore = 0;
                        data.awayAwayScore = 0;
                    }
                    ch3BattleList[groupIndex] = data;
                    //chairmanData.ch3BattleList = ch3BattleList;
                }
                if(nextBattleList.length === 1 && chairmanData.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_2)
                {
                    let data = {};
                    //最后一轮设置主席和总经理
                   if(battleList[0][winner] !== self.noneUid)
                   {
                       data.uid = battleList[0][winner];
                       data.gid = battleList[0][winner+'Gid'];
                       let obj = chairmanData.preBattleInfoMap.get(data.uid);
                       data.name = obj.name;
                       data.faceUrl = obj.faceUrl;
                       chairmanData.chairman = data;
                       //设置主席
                       self.setBeliefLeader(beliefId, 1);
                   }
                   data = {};
                   if(battleList[0][loser] !== self.noneUid)
                   {
                       data.uid = battleList[0][loser];
                       data.gid = battleList[0][loser+'Gid'];
                       let obj = chairmanData.preBattleInfoMap.get(data.uid);
                       data.name = obj.name;
                       data.faceUrl = obj.faceUrl;
                       chairmanData.secondPlace = data;
                       //设置总经理
                       self.setBeliefLeader(beliefId, 3);
                   }
                }
                index++;
            }
            logger.debug("nextBattleList: ", beliefId, nextBattleList);
            if(battleList.length === 2 && chairmanData.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_4) {
               //4强的两个失败者对战得到第三名
                self.finalBattleProcess(beliefId);
            }
            // if(nextBattleList.length === 1 && self.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_2) {
            //     logger.debug("the champion uid: ", nextBattleList[0].home);
            //     //冠军赛 -- 改为决赛后再挑战擂主
            //
            // }
        }
    });
};
//这里争三
chairmanMatchService.prototype.finalBattleProcess = function(beliefId) {
    let self = this;
    let session = {frontendId: self.app.getServerId()};
    let battleServers = self.app.getServersByType("battle");
    let chairmanData = this.allCompetition.get(beliefId);
    let ch3BattleList = chairmanData.ch3BattleList;
    if(ch3BattleList.length <= 0)
        return;
    //打两场
    let battleMembers = {};
    async.waterfall([
        function (callback) {
            let index = calcUtils.randRange(0, battleServers.length-1);
            session.toServerId = battleServers[index].id;
            battleMembers = {home: ch3BattleList[0].home, homeGid: ch3BattleList[0].homeGid,
                away: ch3BattleList[0].away, awayGid: ch3BattleList[0].awayGid};
            if(ch3BattleList[0].home === self.noneUid)
            {
                ch3BattleList[0].awayAwayScore += 1;
                callback(null);
            }
            else if(ch3BattleList[0].away === self.noneUid)
            {
                ch3BattleList[0].homeHomeScore += 1;
                callback(null);
            }
            else
            {
                self.app.rpc.battle.battleRemote.pvpDqCupBattleReq(session, battleMembers, function (code, retMsg) {
                    if (code !== Code.OK) {
                        logger.error("finalBattleProcess error", code, battleMembers);
                        callback(null);
                    }
                    //战斗结果
                    ch3BattleList[0].homeHomeScore = retMsg.homeScore;
                    ch3BattleList[0].awayAwayScore = retMsg.awayScore;
                    //战斗录像
                    ch3BattleList[0].roomUid1 = retMsg.roomUid;
                    ch3BattleList[0].time1 = TimeUtils.now();
                    callback();
                });
            }
        },
        function (callback) {
            let index = calcUtils.randRange(0, battleServers.length-1);
            session.toServerId = battleServers[index].id;
            battleMembers = {home: ch3BattleList[0].away, homeGid: ch3BattleList[0].awayGid,
                away: ch3BattleList[0].home, awayGid: ch3BattleList[0].homeGid};
            if(ch3BattleList[0].away === self.noneUid)
            {
                ch3BattleList[0].homeAwayScore += 1;
                callback(null);
            }
            else if(ch3BattleList[0].home === self.noneUid)
            {
                ch3BattleList[0].awayHomeScore += 1;
                callback(null);
            }
            else
            {
                self.app.rpc.battle.battleRemote.pvpDqCupBattleReq(session, battleMembers, function (code, retMsg) {
                    if (code !== Code.OK) {
                        logger.error("finalBattleProcess error", code, battleMembers);
                        callback(null);
                    }
                    //战斗结果
                    ch3BattleList[0].homeAwayScore = retMsg.awayScore;
                    ch3BattleList[0].awayHomeScore = retMsg.homeScore;
                    //战斗录像
                    ch3BattleList[0].roomUid2 = retMsg.roomUid;
                    ch3BattleList[0].time2 = TimeUtils.now();
                    callback(null);
                });
            }
        }
    ], function (err) {
        //胜者判定
        //1. 总比分
        let winner = "home";
        let homeAllScore = ch3BattleList[0].homeHomeScore + ch3BattleList[0].homeAwayScore;
        let awayAllScore = ch3BattleList[0].awayHomeScore + ch3BattleList[0].awayAwayScore;
        if(homeAllScore < awayAllScore) {
            winner = "away";
        }else if(homeAllScore > awayAllScore) {
            winner = "home";
        }else {
            //2. 客场进球数
            if(ch3BattleList[0].homeAwayScore > ch3BattleList[0].awayAwayScore) {
                winner = "home";
            }else if(ch3BattleList[0].homeAwayScore < ch3BattleList[0].awayAwayScore) {
                winner = "away";
            }else {
                //3. 等级 to do
                //4. 随机选一个
                let randNum = utils.random(1, 100);
                if(randNum <= 50) {
                    winner = "home";
                }else {
                    winner = "away";
                }
            }
        }
        ch3BattleList[0].winnerUid = ch3BattleList[0][winner];
        //记录第三名
        if(ch3BattleList[0].winnerUid !== self.noneUid) {
            chairmanData.thirdPlace.uid = ch3BattleList[0][winner];
            chairmanData.thirdPlace.gid = ch3BattleList[0][winner+'Gid'];
            let obj = chairmanData.preBattleInfoMap.get(chairmanData.thirdPlace.uid);
            chairmanData.thirdPlace.name = obj.name;
            chairmanData.thirdPlace.faceUrl = obj.faceUrl;
            //设置主教练
            self.setBeliefLeader(beliefId, 4);
        }
    });
};

chairmanMatchService.prototype.preBattleTo64Grouping = function (beliefId, leftPlayerList, roundNum) {
    let chairmanData = this.allCompetition.get(beliefId);
    //一个人都没有参加
    if(leftPlayerList.length <= 0) {
        return;
    }
    //清空分组
    chairmanData.dqCup64BattleList = [];
    chairmanData.agoDqCup64BattleList = [];
    //let lastRoundMemberList = this.getLastRoundMemberList(roundNum);
    //人数不足, 用轮空补全 (应该只有64强会出现, 其他情况下轮空已经在上一轮补全了)
    let noneNum = 0;
    if(leftPlayerList.length < roundNum) {
        noneNum = roundNum - leftPlayerList.length;
        logger.debug("outBattleGrouping roundNum 111 roundNum, noneNum:", roundNum, noneNum);
    }
    for(let i = 0; i < roundNum/2; i++)
    {
        let data = {home: this.noneUid, homeGid: "", homeHomeScore: 0, homeAwayScore: 0,
            away: this.noneUid, awayGid: "", awayHomeScore: 0, awayAwayScore: 0};
        chairmanData.dqCup64BattleList.push(utils.deepCopy(data));
    }
    let interval = Math.floor(32/leftPlayerList.length);//间隔
    let k = 0;
    let p = roundNum / 2 - 1;
    let rectify1 = k;
    let rectify2 = p;
    for(let i in leftPlayerList)
    {
        if(k <= 0 || k >= roundNum / 2 - 1 )
            k = rectify1;
        if(p >= roundNum / 2 - 1 || p <= 0)
            p = rectify2;
        if(i % 2 === 0)
        {
            chairmanData.dqCup64BattleList[k].home = leftPlayerList[i].uid;
            chairmanData.dqCup64BattleList[k].homeGid = leftPlayerList[i].gid;
            chairmanData.dqCup64BattleList[k].homeHomeScore = 0;
            chairmanData.dqCup64BattleList[k].homeAwayScore = 0;
            rectify1 = k + 1;
            if(interval - 1 > 0)
            {
                k+=interval-1;
            }
            else
            {
                k+=1;
            }
        }
        else
        {
            chairmanData.dqCup64BattleList[p].away = leftPlayerList[i].uid;
            chairmanData.dqCup64BattleList[p].awayGid = leftPlayerList[i].gid;
            chairmanData.dqCup64BattleList[p].awayHomeScore = 0;
            chairmanData.dqCup64BattleList[p].awayAwayScore = 0;
            rectify2 = p - 1;
            if(interval - 1 > 0)
            {
                p-=interval-1;
            }
            else
            {
                p-=1;
            }
        }
    }
};

chairmanMatchService.prototype.getLastRoundMemberList = function(roundNum) {
    let battleList = [];
    switch (roundNum) {
        case 64:
            battleList = this.preBattleList;
            break;
        case 32:
            battleList = this.dqCup64BattleList;
            break;
        case 16:
            battleList = this.dqCup32BattleList;
            break;
        case 8:
            battleList = this.dqCup16BattleList;
            break;
        case 4:
            battleList = this.dqCup8BattleList;
            break;
        case 2:
            battleList = this.dqCup4BattleList;
            break;
        default:
            logger.error("getLastRoundMemberList error roundNum:", roundNum);
            return [];
    }
    for(let i=0,lens=battleList.length; i<lens; i++) {
        let uid1 = battleList[i].home;
        let uid2 = battleList[i].away;
        //只有从预选赛到32强需要清除轮空信息
        if(roundNum === this.match64Num) {
            if(uid1 !== this.noneUid) {
                battleList.push({uid: uid1, gid: battleList[i].homeGid});
            }
            if(uid2 !== this.noneUid) {
                battleList.push({uid: uid2, gid: battleList[i].awayGid});
            }
        }
        else {
            battleList.push({uid: uid1, gid: battleList[i].homeGid});
            battleList.push({uid: uid2, gid: battleList[i].awayGid});
        }
    }
    return battleList;
};

//获取对战表引用
chairmanMatchService.prototype.getBattleListByPyPeriod = function(beliefId, period) {
    let chairmanData = this.allCompetition.get(beliefId);
    if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.PRE_MATCH) {
        return chairmanData.preBattleList;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_64) {
        return chairmanData.dqCup64BattleList;
    } else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_32) {
        return chairmanData.dqCup32BattleList;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_16) {
        return chairmanData.dqCup16BattleList;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_8) {
        return chairmanData.dqCup8BattleList;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_4) {
        return chairmanData.dqCup4BattleList;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_2) {
        return chairmanData.dqCup2BattleList;
    }
    return [];
};

chairmanMatchService.prototype.getNumByMatchPeriod = function (period) {
    if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_64) {
        return 64;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_32) {
        return 32;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_16) {
        return 16;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_8) {
        return 8;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_4) {
        return 4;
    }else if(period === commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_2) {
        return 2;
    }
    return 0;
};

chairmanMatchService.prototype.sendReward = function (beliefId) {
    let chairmanData = this.allCompetition.get(beliefId);
    if(chairmanData.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_REWARD)
    {
        chairmanData.finishPeriod = chairmanData.matchPeriod;
        return;
    }
    if(chairmanData.PeriodWait)
    {
        logger.error("sendReward PeriodWait", beliefId, chairmanData.PeriodWait);
        return;
    }

    //发奖励, 每周奖励不一样
    let alreadySendMap = new Map();	//不重复发奖
    let self = this;
    let oneMatchRecord = [];	//记录每场比赛的名次
    logger.debug("sendReward - champion: ", chairmanData.chairman);
    chairmanData.PeriodWait = true;
    async.waterfall([
        function (callback) {
            let uidList = [];
            //主席奖励
            if(!!chairmanData.chairman.uid && chairmanData.chairman.uid !== self.noneUid) {
                uidList = [{uid: chairmanData.chairman.uid, gid: chairmanData.chairman.gid, pos: 1}];
                oneMatchRecord.push({uid: chairmanData.chairman.uid, name: chairmanData.chairman.name, rank: 1});
            }
            //发奖励邮件
            self.sendRewardMailByPeriod(beliefId, "俱乐部董事长", uidList, function () {
                for(let i in uidList)
                {
                    alreadySendMap.set(uidList[i].uid, i);
                }
                callback(null);
            })
        },
        function (callback) {
            let uidList = [];
            //副主席奖励
            if(!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) {
                uidList.push({uid: chairmanData.coChairman.uid, gid: chairmanData.coChairman.gid, pos: 2});
            }
            self.sendRewardMailByPeriod(beliefId,"俱乐部副董事长", uidList, function () {
                for(let i in uidList)
                {
                    alreadySendMap.set(uidList[i].uid, i);
                }
                callback(null);
            })
        },
        function (callback) {
            let uidList = [];
            //总经理奖励
            if(!!chairmanData.secondPlace.uid && chairmanData.secondPlace.uid !== self.noneUid) {
                uidList.push({uid: chairmanData.secondPlace.uid, gid: chairmanData.secondPlace.gid, pos: 3});
                oneMatchRecord.push({uid: chairmanData.secondPlace.uid, name: chairmanData.secondPlace.name, rank: 2});
            }
            self.sendRewardMailByPeriod(beliefId,"俱乐部总经理", uidList, function () {
                for(let i in uidList)
                {
                    alreadySendMap.set(uidList[i].uid, i);
                }
                callback(null);
            })
        },
        function (callback) {
            let uidList = [];
            //主教练奖励
            if(!!chairmanData.thirdPlace.uid && chairmanData.thirdPlace.uid !== self.noneUid) {
                uidList.push({uid: chairmanData.thirdPlace.uid, gid: chairmanData.thirdPlace.gid, pos: 4});
                oneMatchRecord.push({uid: chairmanData.thirdPlace.uid, name: chairmanData.thirdPlace.name, rank: 3});
            }
            self.sendRewardMailByPeriod(beliefId,"俱乐部总监", uidList, function () {
                for(let i in uidList)
                {
                    alreadySendMap.set(uidList[i].uid, i);
                }
                callback(null);
            })
        },
        function (callback) {
            //4强战绩
            let playerList = self.getBattleListByPyPeriod(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_4);
            let uidList = [];
            for(let i=0,lens=playerList.length;i<lens;i++) {
                if(!!playerList[i].home && (!alreadySendMap.has(playerList[i].home)) && playerList[i].home !== self.noneUid) {
                    uidList.push({uid: playerList[i].home, gid: playerList[i].homeGid, pos: 5});
                    alreadySendMap.set(playerList[i].home, 1);
                }
                if(!!playerList[i].away && (!alreadySendMap.has(playerList[i].away)) && playerList[i].away !== self.noneUid) {
                    uidList.push({uid: playerList[i].away, gid: playerList[i].awayGid, pos: 5});
                    alreadySendMap.set(playerList[i].away, 1);
                }
                //因为副主席排名可能在其中任何一个，所以在这里得到副主席排名
                if(!!playerList[i].home && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].home === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 4});
                }
                else if(!!playerList[i].away && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].away === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 4});
                }
            }
            for(let i=0, lens=uidList.length; i<lens; i++) {
                let uid = uidList[i].uid;
                if(uid === chairmanData.thirdPlace.uid)
                    continue;
                oneMatchRecord.push({uid: uid, name: chairmanData.preBattleInfoMap.get(uid).name, rank: 4});
            }
            //4强奖励
            self.sendRewardMailByPeriod(beliefId,"4强", uidList, function () {
                callback(null);
            })
        },
        function (callback) {
            //8强战绩
            let playerList = self.getBattleListByPyPeriod(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_8);
            let uidList = [];
            for(let i=0,lens=playerList.length;i<lens;i++) {
                if(!!playerList[i].home && (!alreadySendMap.has(playerList[i].home)) && playerList[i].home !== self.noneUid) {
                    uidList.push({uid: playerList[i].home, gid: playerList[i].homeGid, pos: 5});
                    alreadySendMap.set(playerList[i].home, 1);
                }
                if(!!playerList[i].away && (!alreadySendMap.has(playerList[i].away)) && playerList[i].away !== self.noneUid) {
                    uidList.push({uid: playerList[i].away, gid: playerList[i].awayGid, pos: 5});
                    alreadySendMap.set(playerList[i].away, 1);
                }
                if(!!playerList[i].home && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].home === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 8});
                }
                else if(!!playerList[i].away && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].away === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 8});
                }
            }
            for(let i=0, lens=uidList.length; i<lens; i++) {
                let uid = uidList[i].uid;
                oneMatchRecord.push({uid: uid, name: chairmanData.preBattleInfoMap.get(uid).name, rank: 8});
            }
            //8强奖励
            self.sendRewardMailByPeriod(beliefId,"8强", uidList, function () {
                callback(null);
            })
        },
        function (callback) {
            //16强战绩
            let playerList = self.getBattleListByPyPeriod(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_16);
            let uidList = [];
            for(let i=0,lens=playerList.length;i<lens;i++) {
                if(!!playerList[i].home && (!alreadySendMap.has(playerList[i].home)) && playerList[i].home !== self.noneUid) {
                    uidList.push({uid: playerList[i].home, gid: playerList[i].homeGid, pos: 5});
                    alreadySendMap.set(playerList[i].home, 1);
                }
                if(!!playerList[i].away && (!alreadySendMap.has(playerList[i].away)) && playerList[i].away !== self.noneUid) {
                    uidList.push({uid: playerList[i].away, gid: playerList[i].awayGid, pos: 5});
                    alreadySendMap.set(playerList[i].away, 1);
                }
                if(!!playerList[i].home && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].home === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 16});
                }
                else if(!!playerList[i].away && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].away === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 16});
                }
            }
            for(let i=0, lens=uidList.length; i<lens; i++) {
                let uid = uidList[i].uid;
                oneMatchRecord.push({uid: uid, name: chairmanData.preBattleInfoMap.get(uid).name, rank: 16});
            }
            //16强奖励
            self.sendRewardMailByPeriod(beliefId,"16强", uidList, function () {
                callback(null);
            })
        },
        function (callback) {
            //32强战绩
            let playerList = self.getBattleListByPyPeriod(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_32);
            let uidList = [];
            for(let i=0,lens=playerList.length;i<lens;i++) {
                if(!!playerList[i].home && (!alreadySendMap.has(playerList[i].home)) && playerList[i].home !== self.noneUid) {
                    uidList.push({uid: playerList[i].home, gid: playerList[i].homeGid, pos: 5});
                    alreadySendMap.set(playerList[i].home, 1);
                }
                if(!!playerList[i].away && (!alreadySendMap.has(playerList[i].away)) && playerList[i].away !== self.noneUid) {
                    uidList.push({uid: playerList[i].away, gid: playerList[i].awayGid, pos: 5});
                    alreadySendMap.set(playerList[i].away, 1);
                }
                if(!!playerList[i].home && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].home === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 32});
                }
                else if(!!playerList[i].away && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].away === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 32});
                }
            }
            for(let i=0, lens=uidList.length; i<lens; i++) {
                let uid = uidList[i].uid;
                oneMatchRecord.push({uid: uid, name: chairmanData.preBattleInfoMap.get(uid).name, rank: 32});
            }
            //32强奖励
            self.sendRewardMailByPeriod(beliefId,"32强", uidList, function () {
                callback(null);
            })
        },
        function (callback) {
            //64强战绩
            let playerList = self.getBattleListByPyPeriod(beliefId, commonEnum.CHAIRMAN_MATCH_PERIOD.MATCH_64);
            let uidList = [];
            for(let i=0,lens=playerList.length;i<lens;i++) {
                if(!!playerList[i].home && (!alreadySendMap.has(playerList[i].home)) && playerList[i].home !== self.noneUid) {
                    uidList.push({uid: playerList[i].home, gid: playerList[i].homeGid, pos: 5});
                    alreadySendMap.set(playerList[i].home, 1);
                }
                if(!!playerList[i].away && (!alreadySendMap.has(playerList[i].away)) && playerList[i].away !== self.noneUid) {
                    uidList.push({uid: playerList[i].away, gid: playerList[i].awayGid, pos: 5});
                    alreadySendMap.set(playerList[i].away, 1);
                }
                if(!!playerList[i].home && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].home === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 64});
                }
                else if(!!playerList[i].away && (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) && (playerList[i].away === chairmanData.coChairman.uid))
                {
                    oneMatchRecord.push({uid: chairmanData.coChairman.uid, name: chairmanData.coChairman.name, rank: 64});
                }
            }
            for(let i=0, lens=uidList.length; i<lens; i++) {
                let uid = uidList[i].uid;
                oneMatchRecord.push({uid: uid, name: chairmanData.preBattleInfoMap.get(uid).name, rank: 64});
            }
            //64强奖励
            self.sendRewardMailByPeriod(beliefId,"64强", uidList, function () {
                callback(null);
            })
        },
        function (callback) {
            let uidList = [];
            //参与奖
            for(let [k, v] of chairmanData.preBattleInfoMap)
            {
                if(k !== chairmanData.chairman.uid && k !== chairmanData.coChairman.uid &&
                    k !== chairmanData.secondPlace.uid && k !== chairmanData.thirdPlace.uid &&
                    !alreadySendMap.has(k))
                {
                    uidList.push({uid: k, gid: v.gid, pos: 5});
                }
            }
            self.sendRewardMailByPeriod(beliefId,"预选赛", uidList, function () {
                callback(null);
            })
        },
        function (callback) {
            //记录比赛记录
            self.matchDao.updateChMatchRecord(beliefId + 1, oneMatchRecord, callback);
            callback(null);
        },
        function (callback) {
            //更新荣誉墙数据
            let playerList = [];
            let coChairmanRank = 0;//副董事长排名
            for(let i in oneMatchRecord)
            {
                let uid = oneMatchRecord[i].uid;
                let rank = oneMatchRecord[i].rank;
                let playerData = chairmanData.preBattleInfoMap.get(uid);
                let gid = playerData.gid;
                let pos = 0;//默认0就是没有职位
                if(rank === 1)
                {
                    pos = rank;
                }
                else if(rank > 1 && rank < 4)
                {
                    pos = rank + 1;
                }
                //如果副董事长有参加获得排名
                if(!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid && chairmanData.coChairman.uid === uid)
                {
                    coChairmanRank = rank;
                    continue;
                }
                playerList.push({uid: uid, gid: gid, seasonId: chairmanData.honorSeasonId, beliefId: beliefId, rank: rank, pos: pos});
            }
            //判断下有没有副董事长有的话也加进去
            if(!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid)
            {
                playerList.push({uid: chairmanData.coChairman.uid, gid: chairmanData.coChairman.gid, seasonId: chairmanData.honorSeasonId, beliefId: beliefId, rank: coChairmanRank, pos: 2});
            }
            self.updataHonorData(playerList, commonEnum.HONOR_DISPOSE_TYPE.DATA, function () {
               callback(null);
            });
        }
    ], function (err) {
        chairmanData.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_REWARD;
        chairmanData.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_REWARD;
        logger.debug("sendMail Period finished");
    });
};
chairmanMatchService.prototype.chairmanMatchReset = function (beliefId)
{
    let chairmanData = this.allCompetition.get(beliefId);
    if(chairmanData.matchPeriod === commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_FINISHED)
        return;
    let unlockList = [];//参赛者解除锁定信仰
    for(let [k, v] of chairmanData.preBattleInfoMap)
    {
        unlockList.push({uid: k, gid: v.gid});
    }
    //解除锁定信仰
    this.playerUnlockBelief(unlockList);
    //清空比赛数据
    // chairmanData.preBattleInfoMap = new Map();		//预选赛个人比赛信息表
    chairmanData.preBattleInfoMap.clear();            //预选赛个人比赛信息表
    chairmanData.preBattleRoundCursor = 0;			//预选赛每一轮战斗的游标数据 (每轮最多1000次战斗)
    //对战信息表
    chairmanData.preBattleList = [];				//预选赛对战列表 [{uid1, uid2}, ...]
    chairmanData.preBattleOutUidList = [];          //淘汰列表
    chairmanData.dqCup64BattleList = [];			//64强对战信息
    chairmanData.dqCup32BattleList = [];			//32强对战信息
    chairmanData.dqCup16BattleList = [];			//16强对战信息
    chairmanData.dqCup8BattleList = [];				//8强对战信息
    chairmanData.dqCup4BattleList = [];				//4强对战信息
    chairmanData.ch3BattleList = [];                //争3对战信息
    chairmanData.dqCup2BattleList = [];				//2强对战信息
    chairmanData.outBattleRound = 0;				//淘汰赛(32强以后比赛，每次要打两轮, 分主客场)
    chairmanData.campaignList = [];                 //竞选列表
    chairmanData.PeriodWait = false;
    //报名结束时上届主席副主席总经理主教练载入史册，清空
    this.addHonorList(beliefId);
    chairmanData.matchPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_FINISHED;
    chairmanData.finishPeriod = commonEnum.CHAIRMAN_MATCH_PERIOD.SEND_FINISHED;
};
//加入历届荣誉列表
chairmanMatchService.prototype.addHonorList = function(beliefId)
{
    let self = this;
    let chairmanData = self.allCompetition.get(beliefId);
    // logger.error("########################", JSON.stringify(chairmanData.chairman) === "{}", JSON.stringify(chairmanData.coChairman) === "{}",
    //     JSON.stringify(chairmanData.secondPlace) === "{}", JSON.stringify(chairmanData.thirdPlace) === "{}");
    if(JSON.stringify(chairmanData.chairman) === "{}" && JSON.stringify(chairmanData.coChairman) === "{}" &&
        JSON.stringify(chairmanData.secondPlace) === "{}" && JSON.stringify(chairmanData.thirdPlace) === "{}")
        return;
    if(self.honorMap.has(beliefId))
    {
        let honorList = self.honorMap.get(beliefId);
        let data = {
            time: TimeUtils.now(),
            chairman: chairmanData.chairman || {},
            coChairman: chairmanData.coChairman || {},
            secondPlace: chairmanData.secondPlace || {},
            thirdPlace: chairmanData.thirdPlace || {}
        }
        honorList.push(data);
        self.honorMap.set(beliefId, honorList);
    }
    else
    {
        let honorList = [];
        let data = {
            time: TimeUtils.now(),
            chairman: chairmanData.chairman || {},
            coChairman: chairmanData.coChairman || {},
            secondPlace: chairmanData.secondPlace || {},
            thirdPlace: chairmanData.thirdPlace || {}
        }
        honorList.push(data);
        self.honorMap.set(beliefId, honorList);
    }
    // chairmanData.chairman = {};
    // chairmanData.coChairman = {};
    // chairmanData.secondPlace = {};
    // chairmanData.thirdPlace = {};
};
//预选赛玩家没进入64强解锁信仰
chairmanMatchService.prototype.playerUnlockBelief = function(unlockList)
{
    let map = new Map();
    let list = [];
    for(let i in unlockList)
    {
        if(map.has(unlockList[i].gid))
        {
            map.get(unlockList[i].gid).push(unlockList[i].uid);
        }
        else
        {
            list.push(unlockList[i].uid);
            map.set(unlockList[i].gid, utils.cloneArray(list));
        }
    }
    let disposeList = [];
    for(let [k, v] of map)
    {
        disposeList.push(v);
    }
    let self = this;
    async.eachSeries(disposeList, function (sameGameList, callback) {
        let session = {frontendId: self.app.getServerId(), toServerId: sameGameList[0].gid};
       //处理解锁玩家信仰
        self.app.rpc.game.entryRemote.unlockBelief(session, sameGameList, function (code) {

            callback(null);
        })
    }, function (err) {
    });
};
//发奖邮件
chairmanMatchService.prototype.sendRewardMailByPeriod = function (beliefId, roundName, uidList, cb) {
    //根据不同gid区分发送到不同game服务器处理
    let list = [];
    let gid2Index = new Map();
    let index = 0;
    for(let i=0,lens=uidList.length;i<lens;i++) {
        if(gid2Index.has(uidList[i].gid)) {
            list[gid2Index.get(uidList[i].gid)].push(uidList[i]);
        }else {
            list[index] = [];
            list[index].push(uidList[i]);
            gid2Index.set(uidList[i].gid, index);
            index++;
        }
    }
    logger.debug("sendRewardMailByPeriod process gid list: ", list);
    if(uidList.length <= 0)
        return cb();
    //邮件配置
    let config = dataApi.allData.data["ChiefBattleReward"];
    let beliefConfig = dataApi.allData.data["Belief"];
    let mailTextConfig = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.CHIEF_BATTLE_REWARD];

    let beliefName = beliefConfig[beliefId].Team;//信仰名
    if(uidList[0].pos < 5)
    {
        mailTextConfig = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.CHIEF_BATTLE_ELECTION];
    }
    let self = this;
    let msg = {};
    msg.mailInfo = {};
    msg.mailInfo.title = mailTextConfig.Title;        	//标题
    //内容
    msg.mailInfo.content = mailTextConfig.Result1.replace("#0#", beliefName);
    msg.mailInfo.content = msg.mailInfo.content.replace("#0#", roundName);
    msg.mailInfo.attachList = [];						//奖励
    for(let n=1;n<=4;n++) {
        let reward = {};
        reward.ResId = config[uidList[0].pos.toString()]["Reward"+n];
        reward.Num = config[uidList[0].pos.toString()]["Num"+n];
        reward.ItemType = config[uidList[0].pos.toString()]["RewardType"+n];
        if(reward.ResId > 0 && reward.Num > 0) {
            msg.mailInfo.attachList.push(reward);
        }
    }
    //logger.error("发奖励邮件：：：：：：：：", list, msg);
    async.eachSeries(list, function (sameGameList, callback) {
        let session = {frontendId: self.app.getServerId(), toServerId: sameGameList[0].gid};
        //发送者uid列表
        msg.uidArray = [];
        for(let n=0,lens=sameGameList.length;n<lens;n++) {
            if(!!sameGameList[n].uid && !!sameGameList[n].uid !== self.noneUid) {
                msg.uidArray.push(sameGameList[n].uid);
            }
        }
        //logger.error("chairman Send Reward Debug, period, msg: ", period, msg);
        self.app.rpc.game.entryRemote.sendEditMailByList(session, msg, function (code) {
            //logger.debug("sendEditMail 1 code:", code, msg);
            callback(null);
        })
    }, function (err) {
        //logger.debug("send mail finished. period:", period);
        cb();
    });

};
//落选球币返还邮件          uidList = [{uid, gid, num}]
chairmanMatchService.prototype.campaignReturnMail = function (beliefId, uidList) {
    if(uidList.length <= 0)
        return;
    //邮件配置
    let config = dataApi.allData.data["ChiefBattleReward"];
    let beliefConfig = dataApi.allData.data["Belief"];
    let mailTextConfig = dataApi.allData.data["MailText"][commonEnum.MAIL_TRANSLATE_CONTENT.CAMPAIGN_RETURN];
    let beliefName = beliefConfig[beliefId].Team;//信仰名

    let self = this;
    let msg = {};
    msg.mailInfo = {};
    msg.mailInfo.title = mailTextConfig.Title;        	//标题
    //内容
    // msg.mailInfo.content = mailTextConfig.Result1.replace("#0#", beliefName);
    msg.mailInfo.content = mailTextConfig.Result1;
    msg.mailInfo.attachList = [];						//奖励

    async.eachSeries(uidList, function (data, callback) {
        let session = {frontendId: self.app.getServerId(), toServerId: data.gid};
        let info = {};
        let rewardList = [];
        let reward = {};
        reward.ItemType = commonEnum.MAIL_ITEM_TYPE.ITEM;
        reward.ResId = 2;
        reward.Num = data.num;
        rewardList.push(reward);

        info.sendPlayerId = "系统";
        info.recvPlayerId = data.uid;
        info.title = mailTextConfig.Title;
        info.content = mailTextConfig.Result1;
        info.attachList = utils.cloneArray(rewardList);

        //发送邮件
        self.app.rpc.game.entryRemote.sendEditMail(session, info, function (err) {
            if (err != Code.OK) {
                logger.error("seedWorldBossJoinReward: ", err);
                callback(err);
                return;
            }
            callback(null);
        });
    },function (err) {
            return;
    });
};
//是否上期64强
chairmanMatchService.prototype.isAgo64BattleList = function (beliefId, playerId) {
        let chairman = this.allCompetition.get(beliefId);
        for(let i in chairman.agoDqCup64BattleList)
        {
            if(chairman.agoDqCup64BattleList[i] === playerId)
                return true;
        }
        return false;
};
//统计数据          报名人数 竞选人数  竞选总额  当选金额  职位记录
chairmanMatchService.prototype.statisticalData = function (cb) {
    let day = TimeUtils.dayInterval("2020/5/4 00:0:00");
    let week = Math.floor(day / 7); //第几周
    let date = new Date();
    let time = date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
    //参加人数
    let enrollNum = 0;
    //竞选参加人数
    let campaignNum = 0;
    //竞选总额
    let campaignCash = 0;
    //职位和当选金额记录
    let campaignList = [];
    for(let [k, v] of this.allCompetition)
    {
        enrollNum += v.preBattleInfoMap.size;
        campaignNum += v.campaignList.length;
        for(let i in v.campaignList)
        {
            campaignCash += v.campaignList[i].num;
        }
        if(!!v.chairman.uid)
        {
            campaignList.push({beliefId: k, uid: v.chairman.uid, name: v.chairman.name, cash: 0, pos: 1});
        }
        if(!!v.coChairman.uid)
        {
            v.campaignList.sort(__campaign_rank_func);
            let cash = 0;
            if(!!v.campaignList[0])
            {
                cash = v.campaignList[0].num;
            }
            campaignList.push({beliefId: k, uid: v.coChairman.uid, name: v.coChairman.name, cash: cash, pos: 2});
        }
        if(!!v.secondPlace.uid)
        {
            campaignList.push({beliefId: k, uid: v.secondPlace.uid, name: v.secondPlace.name, cash: 0, pos: 3});
        }
        if(!!v.thirdPlace.uid)
        {
            campaignList.push({beliefId: k, uid: v.thirdPlace.uid, name: v.thirdPlace.name, cash: 0, pos: 4});
        }
    }
    let ssMsg = {
        week: week,
        time: time,
        enrollNum: enrollNum,
        campaignNum: campaignNum,
        campaignCash: campaignCash,
        campaignList: campaignList
    };
    let session = {frontendId: this.app.getServerId()};
    let self = this;
    self.app.rpc.datanode.dataNodeRemote.updateChMatchData(session, ssMsg, function (err) {
       if(!!err)
       {
           logger.error("chairmanMatchService statisticalData updateChMatchData err", err, ssMsg);
       }
       cb(null);
    });
};
//公告播报各个信仰俱乐部职位名单从董事长至总监
chairmanMatchService.prototype.announcement = function (cb) {
    let config = dataApi.allData.data["Belief"];
    let list = [];
    for(let i in config)
    {
        list.push({id: config[i].ID, team: config[i].Team});
    }
    let self = this;
    let session = {frontendId: self.app.getServerId()};
    async.eachSeries(list, function (obj, callback) {
        let chairmanData = self.allCompetition.get(obj.id);
        let team = obj.team;//信仰名
        let thirdPlace = "";//主教练
        let secondPlace = "";//总经理
        let coChairman = "";//副主席
        let chairman = "";//主席
        let msg = "";
        if (!!chairmanData.chairman.uid && chairmanData.chairman.uid !== self.noneUid) {
            chairman = chairmanData.chairman.name;
            msg += "恭喜 " + "<font color=0x83db43>" + chairman + "</font>" + "成为" + team + "信仰的董事长 ";
        }
        if (!!chairmanData.coChairman.uid && chairmanData.coChairman.uid !== self.noneUid) {
            coChairman = chairmanData.coChairman.name;
            msg += "<font color=0x83db43>" + coChairman + "</font>" + "成为" + team + "信仰的副董事长 ";
        }
        if (!!chairmanData.secondPlace.uid && chairmanData.secondPlace.uid !== self.noneUid) {
            secondPlace = chairmanData.secondPlace.name;
            msg += "<font color=0x83db43>" + secondPlace + "</font>" + "成为" + team + "信仰的总经理 ";
        }
        if (!!chairmanData.thirdPlace.uid && chairmanData.thirdPlace.uid !== self.noneUid) {
            thirdPlace = chairmanData.thirdPlace.name;
            msg += "<font color=0x83db43>" + thirdPlace + "</font>" + "成为" + team + "信仰的总监 ";
        }
        // let msg = "恭喜 " + chairman + "成为" + team + "信仰的董事长，" + coChairman + "成为" + team + "信仰的副董事长，" + secondPlace + "成为" + team + "信仰的总经理，" + thirdPlace + "成为" + team + "信仰的总监"
        if (msg !== "")
        {
            let sendMsg = {
                senderName: "系统",
                type: commonEnum.CHAT_TYPE.HORN,// -1系统 0普通 1物品超链接 2球员超链接 3喇叭
                msg: msg,
                channel: commonEnum.CHAT_CHANNEL.SYSTEM_CHANNEL,
            };
            self.app.rpc.game.entryRemote.broadcastChatMsg(session, sendMsg, function (code) {
                callback(null);
            });
        }
        else
        {
            callback(null);
        }
    }, function (err) {
        cb(null);
    });
};
//更新玩家荣誉墙
chairmanMatchService.prototype.updataHonorData = function (playerList, type, cb)
{
    //按gid分
    let map = new Map();
    let gidPlayerList = [];
    for(let i in playerList)
    {
        if(map.has(playerList[i].gid))
        {
            let list = map.get(playerList[i].gid);
            list.push(playerList[i]);
        }
        else
        {
            let list = [];
            list.push(playerList[i]);
            map.set(playerList[i].gid, list);
        }
    }
    for(let [k, v] of map)
    {
        gidPlayerList.push(v);
    }
    let self = this;
    async.eachSeries(gidPlayerList, function (playerList, callback) {
        if(playerList.length > 0)
        {
            let gid = playerList[0].gid;
            let session = {frontendId: self.app.getServerId(), toServerId: gid};
            if(type === commonEnum.HONOR_DISPOSE_TYPE.JOIN_NUM)//加参赛次数
            {
                //playerList[i] = {uid, gid}
                let msg = {playerList: playerList, type: commonEnum.HONOR_WALL_TYPE.CHAIRMAN};
                self.app.rpc.game.entryRemote.updateHonorWallJoinNum(session, msg, function (err) {
                    if (err !== Code.OK) {
                        logger.error("updateHonorWallJoinNum err: ", err, commonEnum.HONOR_WALL_TYPE.CHAIRMAN);
                        callback(null);
                        return;
                    }
                    callback(null);
                });
            }
            else if(type === commonEnum.HONOR_DISPOSE_TYPE.DATA)//更新赛季数据
            {
                //playerList[i] = {uid, gid, seasonId, rank, pos}
                self.app.rpc.game.entryRemote.updateHonorWallData(session, playerList, commonEnum.HONOR_WALL_TYPE.CHAIRMAN, function (err) {
                    if (err !== Code.OK) {
                        logger.error("updateHonorWallData err: ", err, commonEnum.HONOR_WALL_TYPE.CHAIRMAN);
                        callback(null);
                        return;
                    }
                    callback(null);
                });
            }
        }
        else
        {
            callbcak(null);
        }
    }, function (err) {
        cb()
    });
};