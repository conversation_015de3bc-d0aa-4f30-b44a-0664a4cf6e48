/**
 * Idea and Persist
 * Created by sea on 2019/12/16.
 */
let logger = require('pomelo-logger').getLogger("pomelo", __filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let async = require('async');
let Code = require('../../../../shared/code');
let utils = require('../../util/utils');
let timeUtils = require('../../util/timeUtils');
let commonEnum = require('../../../../shared/enum');
let dataApi = require('../../util/dataApi');

module.exports.create = function(app, dbclient) {
	return new PlayerAssociationService(app, dbclient);
};

let PlayerAssociationService = function (app, dbclient) 
{
	this.app = app;
};

util.inherits(PlayerAssociationService, EventEmitter);


PlayerAssociationService.prototype.createAssociation = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
		logger.debug('PlayerAssociationService createAssociation player not exist !');
		cb(Code.FAIL);
		return;
    }
    
    if(msg.name === "" || !msg.name) {
        logger.error("createAssociation----------名字不能为空----------------------");
        cb(Code.PARAM_FAIL);
        return;
    }

    //玩家已经有协会了就不能再创建了
    if(player.league !== "") {
        logger.error("createAssociation----已有协会----------------------------");
        cb(Code.RANGE_FAIL);
        return;
    }

    let teamUid = player.teamFormations.getCurrTeamFormationId();
    let teamFormation = player.teamFormations.getOneTeamFormation(teamUid);
    let teamAct = Math.floor(teamFormation.ActualStrength);
    let frontendId = player.getFrontendId();
    let sessionId = player.getSessionId();


    let self = this;
	let ssMsg = { playerName: player.name,
                  name: msg.name,
                  faceId: msg.faceId,
                  rate: teamAct,
                  gid: self.app.getServerId(),
                  faceUrl: player.faceUrl,
                  frontendId: frontendId,
                  sessionId: sessionId
	            };
	let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.createAssociation(session, playerId, ssMsg, function (err, result) {
        logger.error("createAssociation+++++++++++++++++++++", result);
        if(result.code !== Code.OK) {
            cb(result.code);
            return;
        }
        player.league = msg.name;
        player.save();
        cb(Code.OK);
    });
}

PlayerAssociationService.prototype.exitAssociation = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.debug('PlayerAssociationService exitAssociation player not exist !');
        cb(Code.FAIL);
        return;
    }

    if(player.league === "") {
        logger.error("exitAssociation-------------玩家没有协会-------------------");
        cb(Code.RANGE_FAIL);
        return;
    }

    let self = this;
    let ssMsg = {leagueName: player.league};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.exitAssociation(session, playerId, ssMsg, function (err, result) {
        logger.error("exitAssociation++++++++222+++++++++++++", result);
        if(result.code !== Code.OK) {
            cb(result.code);
            return;
        }
        player.league = "";
        player.save(true);
        cb(Code.OK);
    });
}

PlayerAssociationService.prototype.getAllAssociation = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
		logger.debug('PlayerAssociationService getAllAssociation player not exist !');
		cb(Code.FAIL, [], []);
		return;
    }

    let leagueList = player.leagueList;
    let self = this;
	let ssMsg = {playerId: playerId};
	let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.getAllAssociation(session, playerId, ssMsg, function (err, result) {
        logger.error("getAllAssociation+++++++++++++++++++++", result);
        cb(Code.OK, result.associationInfo, leagueList);
    });
}

PlayerAssociationService.prototype.getAssociationInfo = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.debug('PlayerAssociationService getAssociationInfo player not exist !');
        cb(Code.FAIL, {});
        return;
    }

    let leagueName = msg.leagueName;
    if(!!leagueName) {
        let self = this;
        let ssMsg = {playerId: playerId, leagueName: leagueName};
        let session = {frontendId: this.app.getServerId()};
        self.app.rpc.datanode.dataNodeRemote.getAssociationInfo(session, playerId, ssMsg, function (err, result) {
            logger.error("getAssociationInfo+++++++++++++++++++++", result);
            if(result.code !== Code.OK) {
                return cb(Code.FAIL, {});
            }
            return cb(Code.OK, result.associationInfo);
        });
    } else {
        return cb(Code.PARAM_FAIL, {});
    }
}


PlayerAssociationService.prototype.getAssociationPlayerList = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.debug('PlayerAssociationService getAssociationPlayerList player not exist !');
        cb(Code.FAIL);
        return;
    }

    let self = this;
    let ssMsg = {associationId: msg.associationId};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.getAssociationPlayerList(session, playerId, ssMsg, function (err, result) {
        logger.error("getAssociationPlayerList+++++++++++++++++++++",err, result);
        cb(Code.OK, result.playerList);
    });
}

PlayerAssociationService.prototype.getAssociationPlayerListByName = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let playerUid = msg.senderUid;
    let player = playerService.getPlayer(playerUid);
    if(!player){
        logger.debug('PlayerAssociationService getAssociationPlayerListByName player not exist !');
        cb(Code.FAIL, [], 0);
        return;
    }

    let self = this;
    let ssMsg = {associationName: msg.associationName};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.getAssociationPlayerListByName(session, playerUid, ssMsg, function (err, result) {
        cb(result.code, result.playerList, result.associationId);
    });
}

PlayerAssociationService.prototype.getAssociationIdByLeagueName = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let playerUid = msg.senderUid;
    let player = playerService.getPlayer(playerUid);
    if(!player){
        logger.debug('PlayerAssociationService getAssociationIdByLeagueName player not exist !');
        cb(Code.FAIL, 0);
        return;
    }

    let self = this;
    let ssMsg = {associationName: msg.associationName};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.getAssociationIdByLeagueName(session, playerUid, ssMsg, function (err, result) {
        cb(result.code, result.playerList, result.associationId);
    });
}

PlayerAssociationService.prototype.joinAssociation = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.debug('PlayerAssociationService joinAssociation player not exist !');
        cb(Code.FAIL, 0);
        return;
    }

    if(player.league !== "") {
        cb(Code.ASSOCIATION.JOIN_FAIL, 0);
        return;
    }

    //超出申请上限
    if(player.leagueList.length > 10) {
        cb(Code.ASSOCIATION.APPLY_MAX_FAIL, 0);
        return;
    }

    let teamUid = player.teamFormations.getCurrTeamFormationId();
    let teamFormation = player.teamFormations.getOneTeamFormation(teamUid);
    let teamAct = Math.floor(teamFormation.ActualStrength);
    let frontendId = player.getFrontendId();
    let sessionId = player.getSessionId();

    let self = this;
    let ssMsg = {
        associationId: msg.associationId,
        playerName: player.name,
        gid: self.app.getServerId(),
        faceUrl: player.faceUrl,
        strength: teamAct,
        frontendId: frontendId,
        sessionId: sessionId
    };
    let session = {frontendId: self.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.joinAssociation(session, playerId, ssMsg, function (err, result) {
        logger.error("joinAssociation+++++++++++++++++++++",err, result);
        if(result.code !== Code.OK) {
            cb(result.code, msg.associationId);
            return;
        }
        player.leagueList.push(msg.associationId);
        player.save(true);
        return cb(Code.OK, msg.associationId);
    });
}

PlayerAssociationService.prototype.removeJoinAssociation = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.debug('PlayerAssociationService removeJoinAssociation player not exist !');
        cb(Code.FAIL, 0);
        return;
    }

    let self = this;
    let ssMsg = { associationId: msg.associationId };
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.removeJoinAssociation(session, playerId, ssMsg, function (err, result) {
        logger.error("removeJoinAssociation+++++++++++++++++++++",err, result);
        let index = player.leagueList.indexOf(msg.associationId);
        if(index != -1) {
            player.leagueList.splice(index, 1);
        }

        if(result.code !== Code.OK) {
            cb(result.code, msg.associationId);
            return;
        }
        player.save(true);
        return cb(Code.OK, msg.associationId);
    });
}

PlayerAssociationService.prototype.agreeJoinAssociation = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.debug('PlayerAssociationService agreeJoinAssociation player not exist !');
        cb(Code.FAIL, msg.type);
        return;
    }

    let self = this;
    let ssMsg = {associationId: msg.associationId, agreeId: msg.agreeId, type: msg.type};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.agreeJoinAssociation(session, playerId, ssMsg, function (err, result) {
        logger.error("agreeJoinAssociation+++++++++++++++++++++",err, result);
        player.save(true);
        cb(Code.OK, msg.type);
    });
}

PlayerAssociationService.prototype.getApprovalList = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.debug('PlayerAssociationService getApprovalList player not exist !');
        cb(Code.FAIL);
        return;
    }

    let self = this;
    let ssMsg = {associationId: msg.associationId};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.getApprovalList(session, playerId, ssMsg, function (err, result) {
        logger.error("getApprovalList+++++++++++++++++++++",err, result);
        cb(Code.OK, result.approvalList);
    });
}

PlayerAssociationService.prototype.updatePlayerStatus = function(playerId, leagueName, type) {
    let self = this;
    let ssMsg = {leagueName: leagueName, type: type};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.updatePlayerStatus(session, playerId, ssMsg, function (err) {
        return;
    });
}

PlayerAssociationService.prototype.updatePlayerSessionId = function(playerId, leagueName, frontendId, sessionId) {
    let self = this;
    let ssMsg = {leagueName: leagueName, sessionId: sessionId, frontendId: frontendId};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.updatePlayerSessionId(session, playerId, ssMsg, function (err) {
        return;
    });
}

PlayerAssociationService.prototype.updatePlayerExp = function(playerId, leagueName, num) {
    let self = this;
    let ssMsg = {leagueName: leagueName, num: num};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.updatePlayerExp(session, playerId, ssMsg, function (err) {
        return;
    });
}

PlayerAssociationService.prototype.updateAssociationStrength = function(playerId, leagueName, value) {
    let self = this;
    let ssMsg = {value: value, leagueName: leagueName};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.updateAssociationStrength(session, playerId, ssMsg, function (err) {
        return;
    });
}

PlayerAssociationService.prototype.changeAssociationPosition = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.debug('PlayerAssociationService changeAssociationPosition player not exist !');
        cb(Code.FAIL, 0);
        return;
    }
    if(msg.changePos < 1 || msg.changePos > 3) {
        cb(Code.PARAM_FAIL, msg.changePos)
        return;
    }

    let self = this;
    let ssMsg = {associationId: msg.associationId, enforcedId: msg.enforcedId, changePos: msg.changePos};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.changeAssociationPosition(session, playerId, ssMsg, function (err, result) {
        logger.error("changeAssociationPosition+++++++++++++++++++++",err, result);
        cb(result.code, msg.changePos);
    });
}

PlayerAssociationService.prototype.modifyNotice = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        logger.debug('PlayerAssociationService modifyNotice player not exist !');
        cb(Code.FAIL);
        return;
    }

    let self = this;
    let ssMsg = {content: msg.content};
    let session = {frontendId: this.app.getServerId()};
    self.app.rpc.datanode.dataNodeRemote.modifyNotice(session, playerId, ssMsg, function (err) {
        logger.error("modifyNotice+++++++++++++++++++++",err);
        cb(Code.OK);
    });
}



//检查协会赞助   4点刷新
PlayerAssociationService.prototype.checkAssociationSponsorRewardTime = function(playerId) {
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if(!player){
        return;
    }

    //没数据就初始化
    if(player.associationSponsorReward.length < 1) {
        let info = [];
        let idx = 0;
        for (let i = 1; i < 4; ++i) {
            info[idx] = {};
            info[idx].type = i;
            info[idx].num = 0;
            info[idx].isSponsor = 0;       //是否赞助
            info[idx].reTime = 0;          //刷新时间
        }
        player.associationSponsorReward = info;
    }

    let sponsorNum = 0;
    for(let i = 0; i < player.associationSponsorReward.length; ++i) {
        if(player.associationSponsorReward[i].isSponsor === 0) {
            sponsorNum ++;
        }
    }

    //3个都是未赞助就不用刷新了
    if(sponsorNum === 3) {
        return;
    }

    let reTime = 0;
    for(let i = 0; i < player.associationSponsorReward.length; ++i) {
        if(reTime <= player.associationSponsorReward[i].reTime) {
            reTime = player.associationSponsorReward[i].reTime;
        }
    }

    let dateHour = new Date(reTime).getHours();
    let nowTime = new Date().getHours();
    //玩家是0-4点之间赞助的
    if(dateHour < 4) {
        if(!timeUtils.isToday(reTime)) {
            for(let i = 0; i < player.associationSponsorReward.length; ++i) {
                if(player.associationSponsorReward[i].isSponsor !== 0) {
                    player.associationSponsorReward[i].isSponsor = 0;
                }
            }
        } else {
            //如果已经超过4点就刷新
            if(nowTime >= 4) {
                for(let i = 0; i < player.associationSponsorReward.length; ++i) {
                    for(let i = 0; i < player.associationSponsorReward.length; ++i) {
                        if(player.associationSponsorReward[i].isSponsor !== 0) {
                            player.associationSponsorReward[i].isSponsor = 0;
                        }
                    }
                }
            }
        }
    } else {
        if(!timeUtils.isToday(reTime)) {
            //判断与当前时间相差几天
            let dayInterval = timeUtils.dayInterval(reTime)
            if(dayInterval == 1) {
                //如果已经超过4点就刷新
                if(nowTime >= 4) {
                    for(let i = 0; i < player.associationSponsorReward.length; ++i) {
                        if(player.associationSponsorReward[i].isSponsor !== 0) {
                            player.associationSponsorReward[i].isSponsor = 0;
                        }
                    }
                }
            } else {
                for(let i = 0; i < player.associationSponsorReward.length; ++i) {
                    if(player.associationSponsorReward[i].isSponsor !== 0) {
                        player.associationSponsorReward[i].isSponsor = 0;
                    }
                }
            }
        }
    }
}
