/**
 * Created by sea on 2019/3/30.
 */
let logger = require('pomelo-logger').getLogger("pomelo", __filename);
let EventEmitter = require('events').EventEmitter;
let util = require('util');
let Code = require('../../../../shared/code');
let commonEnum = require('../../../../shared/enum');
let dataApi = require('../../util/dataApi');

module.exports.create = function(app, dbclient) {
	return new PlayerBeliefService(app, dbclient);
};

let PlayerBeliefService = function(app, dbclient){
	this.app = app;
};

util.inherits(PlayerBeliefService, EventEmitter);


PlayerBeliefService.prototype.getBeliefInfo = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerBeliefService getBeliefInfo player not exist !', playerId);
		cb(Code.FAIL, {});
		return;
	}

	let rpcMsg = {beliefId: player.getBeliefId()};
	let session = {frontendId: this.app.getServerId()};
	let self = this;
	self.app.rpc.datanode.dataNodeRemote.getBeliefInfo(session, rpcMsg, function(err, result) {
		cb(result.code, result.beliefInfo);
	});
};

PlayerBeliefService.prototype.getBeliefList = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerBeliefService getBeliefList player not exist !', playerId);
		cb(Code.FAIL, []);
		return;
	}

	let rpcMsg = {};
	let session = {frontendId: this.app.getServerId()};
	let self = this;
	self.app.rpc.datanode.dataNodeRemote.getBeliefList(session, rpcMsg, function(code, result) {
		cb(code, result);
	});
};

PlayerBeliefService.prototype.donateBeliefGold = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerBeliefService donateBeliefGold player not exist !', playerId);
		cb(Code.FAIL, 0, 0);
		return;
	}
	let type = msg.type;
	let num = msg.num;
	let costNum = 0;
	if(type === 1) {
		if(typeof (dataApi.allData.data["SystemParam"][commonEnum.BELIEF.COST_CASH_NUM]) === "undefined") {
			costNum = 1000000 * num;
		}else {
			costNum = dataApi.allData.data["SystemParam"][commonEnum.BELIEF.COST_CASH_NUM].Param * num;
		}
		//检查钱是否足够
		if(!player.checkResourceIsEnough(commonEnum.PLAY_INFO.cash, costNum)){
			return cb(Code.CASH_FALL, 0, 0);
		}
	}else {
		if(typeof (dataApi.allData.data["SystemParam"][commonEnum.BELIEF.COST_GOLD_NUM]) === "undefined") {
			costNum = 100 * num;
		}else {
			costNum = dataApi.allData.data["SystemParam"][commonEnum.BELIEF.COST_GOLD_NUM].Param * num;
		}
		//检查钱是否足够
		if(!player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, costNum)){
			return cb(Code.GOLD_FALL, 0, 0);
		}
	}

	let rpcMsg = {beliefId: player.getBeliefId(), type: type, num: num};
	let session = {frontendId: this.app.getServerId()};
	let self = this;
	self.app.rpc.datanode.dataNodeRemote.donateBeliefGold(session, rpcMsg, function(err, result) {
		// logger.error("sssssssssssdonateBeliefGoldssssssssssss", err, result)
		if(result.code !== Code.OK) {
			return cb(result.code, 0, 0);
		}

		//增加个人信仰值
		let addNum = 0;
		if(type === 1) {
			//扣欧元
			player.deductMoney(commonEnum.PLAY_INFO.cash, costNum);
			if(typeof (dataApi.allData.data["SystemParam"][commonEnum.BELIEF.CASH_BELIEF_NUM]) === "undefined") {
				addNum = 1000000 * num;
			}else {
				addNum = dataApi.allData.data["SystemParam"][commonEnum.BELIEF.CASH_BELIEF_NUM].Param * num;
			}
		}else {
			//扣球币
			player.deductMoney(commonEnum.PLAY_INFO.gold, costNum);
			if(typeof (dataApi.allData.data["SystemParam"][commonEnum.BELIEF.GOLD_BELIEF_NUM]) === "undefined") {
				addNum = 100 * num;
			}else {
				addNum = dataApi.allData.data["SystemParam"][commonEnum.BELIEF.GOLD_BELIEF_NUM].Param * num;
			}
		}
		player.addResource(commonEnum.PLAY_INFO.beliefNum, addNum);
		player.save();
		cb(Code.OK, result.beliefGold, player.beliefNum);
	});
};

PlayerBeliefService.prototype.modifyBeliefNotice = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerBeliefService modifyBeliefNotice player not exist !', playerId);
		cb(Code.FAIL, "");
		return;
	}

	let str = msg.str;
	if(str.length > 100) {
		return cb(Code.PARAM_FAIL, "");
	}

	let rpcMsg = {beliefId: player.getBeliefId(), str: str, playerId: playerId};
	let session = {frontendId: this.app.getServerId()};
	let self = this;
	self.app.rpc.datanode.dataNodeRemote.modifyBeliefNotice(session, rpcMsg, function(err, result) {
		// logger.error("sssssssssssmodifyBeliefNoticessssssssssss", err, result)
		if(result.code !== Code.OK) {
			return cb(result.code, "");
		}
		cb(Code.OK, str);
	});
};

PlayerBeliefService.prototype.getBeliefLeader = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerBeliefService getBeliefLeader player not exist !', playerId);
		cb(Code.FAIL, []);
		return;
	}

	let rpcMsg = {beliefId: player.getBeliefId()};
	let session = {frontendId: this.app.getServerId()};
	let self = this;
	self.app.rpc.datanode.dataNodeRemote.getBeliefLeader(session, rpcMsg, function(code, result) {
		if(code !== Code.OK) {
			return cb(code, result);
		}
		cb(Code.OK, result);
	});
};

PlayerBeliefService.prototype.getActiveRankInfo = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerBeliefService getActiveRankInfo player not exist !', playerId);
		cb(Code.FAIL, [], 0, 0);
		return;
	}

	let rpcMsg = {beliefId: player.getBeliefId(), playerId: playerId};
	let session = {frontendId: this.app.getServerId()};
	let self = this;
	self.app.rpc.datanode.dataNodeRemote.getActiveRankInfo(session, rpcMsg, function(code, result, todayNum, historyNum) {
		if(code !== Code.OK) {
			return cb(code, result, todayNum, historyNum);
		}
		cb(Code.OK, result, todayNum, historyNum);
	});
};

PlayerBeliefService.prototype.donateGoldTask = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerBeliefService getActiveRankInfo player not exist !', playerId);
		cb(Code.FAIL,  0, 0, 0, 0, 0, []);
		return;
	}

	if(typeof msg.num !== "number" || msg.num < 10 || msg.num > 10000) {
		return cb(Code.PARAM_FAIL, 0, 0, 0, 0, 0, []);
	}
	let num = msg.num;
	//检查钱是否足够
	if(!player.checkResourceIsEnough(commonEnum.PLAY_INFO.gold, num)){
		return cb(Code.GOLD_FALL, 0, 0, 0, 0, 0, []);
	}

	let session = {frontendId: this.app.getServerId()};
	let self = this;
	self.app.rpc.datanode.dataNodeRemote.getTodayDonateGoldNum(session, {beliefId: player.beliefId}, function(code, goldNum) {
		if(code !== Code.OK) {
			return cb(code, 0, 0, 0, 0, 0, [])
		}

		let total = goldNum + num;
		let config = dataApi.allData.data["BeliefRate"];
		let rate = [];
		for(let i in config) {
			let min = config[i].Min;
			let max = config[i].Max;
			if(total >= min && total <= max) {
				let beforeIdx = (Number(i) - 1).toString();
				if(config[i].ID !== 1) {
					let beforeNum = config[beforeIdx].Max - goldNum;
					if(beforeNum > 0) {
						//之前的经验
						let srcNum = beforeNum;
						let srcRate = config[beforeIdx].Rate;
						let info1 = {
							rate: srcRate,
							num: srcNum
						}
						rate.push(info1);
						//超出的经验
						let destNum = num - srcNum;
						let info2 = {
							rate: config[i].Rate,
							num: destNum
						}
						rate.push(info2);
					}else {
						let info2 = {
							rate: config[i].Rate,
							num: num
						}
						rate.push(info2);
					}
				}else {
					let info = {};
					info.rate = config[i].Rate;
					info.num = num;
					rate.push(info);
				}
			}
		}

		let addNum = 0;
		for(let j in rate) {
			addNum += Math.round(rate[j].num * rate[j].rate);
		}
		if(addNum === 0) {
			return cb(Code.CONFIG_FAIL, 0, 0, 0, 0, 0, []);
		}

		let rpcMsg = {playerId: playerId, playerName: player.name, faceUrl: player.faceUrl, beliefId: player.beliefId, value: addNum, type: 2, goldNum: num};
		self.app.rpc.datanode.dataNodeRemote.addBeliefLiveness(session, rpcMsg, function (code, level, beliefExp) {
			if (code !== Code.OK) {
				return cb(code, 0, 0, 0, 0, 0, []);
			}

			//扣球币
			player.deductMoney(commonEnum.PLAY_INFO.gold, num);
			player.addResource(commonEnum.PLAY_INFO.beliefLiveness, addNum);
			player.addResource(commonEnum.PLAY_INFO.beliefNum, addNum * 2);
			player.upPlayerInfo([{type: commonEnum.PLAY_INFO.beliefNum, value: player.beliefNum}]);
			player.save();
			cb(Code.OK, level, beliefExp, num, total, addNum, rate);
		});
	});
};

PlayerBeliefService.prototype.getBeliefSkillList = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerBeliefService getBeliefSkillList player not exist !', playerId);
		cb(Code.FAIL, []);
		return;
	}

	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.datanode.dataNodeRemote.getBeliefLevel(session, {beliefId: player.beliefId}, function(code, level) {
		if(code !== Code.OK) {
			return cb(code, []);
		}

		let ret = player.beliefSkill.getBeliefSkillList(level);
		cb(Code.OK, ret);
	});
};

PlayerBeliefService.prototype.upgradeBeliefSkill = function(playerId, msg, cb) {
	let playerService = this.app.get("playerService");
	let player = playerService.getPlayer(playerId);
	if(!player){
		logger.error('PlayerBeliefService upgradeBeliefSkill player not exist !', playerId);
		cb(Code.FAIL, 0, 0);
		return;
	}
	let skillId = msg.skillId;
	let session = {frontendId: this.app.getServerId()};
	this.app.rpc.datanode.dataNodeRemote.getBeliefLevel(session, {beliefId: player.beliefId}, function(code, level) {
		if(code !== Code.OK) {
			return cb(code, 0, skillId);
		}
		//信仰等级没当前等级高
		if(level <= player.beliefSkill.getBeliefSkillLevel(skillId)) {
			return cb(Code.MAXLEVEL_FAIL, 0, skillId);
		}

		let ret = player.beliefSkill.upgradeBeliefSkill(skillId);
		if(ret.code !== Code.OK) {
			return cb(ret.code, ret.level, skillId);
		}
		player.save();
		player.saveBeliefSkill();
		return cb(Code.OK, ret.level, skillId);
	});
};