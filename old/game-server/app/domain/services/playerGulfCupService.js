/**
 * Created by sea on 2019/12/11.
 */
var logger = require('pomelo-logger').getLogger("pomelo", __filename);
var EventEmitter = require('events').EventEmitter;
var util = require('util');
var async = require('async');
var Code = require('../../../../shared/code');
var utils = require('../../util/utils');
var calc = require('../../util/calc');
var TimeUtils = require('../../util/timeUtils');
var commonEnum = require('../../../../shared/enum');
var dataApi = require('../../util/dataApi');

module.exports.create = function(app, dbclient) {
    return new playerGulfCupService(app, dbclient);
};

var playerGulfCupService = function (app, dbclient)
{
    this.app = app;

};

util.inherits(playerGulfCupService, EventEmitter);

//得到界面
playerGulfCupService.prototype.getGulfSelectInterface = function(playerId, msg, cb)
{
    let playerService = this.app.get("playerService");
    var player = playerService.getPlayer(playerId);
    if(!player) {
        logger.debug('PlayerGulfCupService.getSelectInterface: player not exist !');
        cb(Code.FAIL, "");
        return;
    }
    player.gulfCup.isToDayInit();//是否隔天，隔天刷新
    let teamList = player.gulfCup.getConfigTeam();//得到配置的8支队伍
    let CombatList = player.gulfCup.CombatList;
    for(let idx in CombatList)//检查对战列表有没有问题
    {
        if(!CombatList[idx].TeamA || !CombatList[idx].TeamB || CombatList.length > teamList.length / 2)
        {
            logger.warn("getGulfSelectInterface CombatList Data error repair", CombatList);
            player.gulfCup.initCombatList(true);//初始化对战列表
            break;
        }
    }
    player.gulfCup.repairData();
    //是否开始，奖励条件id，队伍列表，对战列表，挑战次数
    cb(Code.OK, player.gulfCup.isBegin, player.gulfCup.getConditionID(), teamList, player.gulfCup.CombatList, player.gulfCup.contestNum);

};
//参加比赛
playerGulfCupService.prototype.gulfCupJoinMatch = function(playerId, msg, cb) {
    let playerService = this.app.get("playerService");
    var player = playerService.getPlayer(playerId);
    if (!player) {
        logger.debug('PlayerGulfCupService.gulfCupJoinMatch: player not exist !');
        cb(Code.FAIL, 0, []);
        return;
    }
    if (player.gulfCup.isBegin !== 0 && player.gulfCup.CombatList)
    {
        cb(Code.OK, player.gulfCup.conditionID, player.gulfCup.CombatList);//返回奖励条件id，对战列表
        return;
    }
    if(player.gulfCup.contestNum >= player.gulfCup.contestNumMax)
    {
        //大于参加次数今天不能参加了
        cb(Code.COUNT_FALL, player.gulfCup.conditionID, []);
        return;
    }
    let money = 0;
    switch (player.gulfCup.contestNum) {
        case 0:
            //第一次免费
            break;
        case 1:
            //扣除球币
            money = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_round2_Gold].Param;
            break;
        default :
            //之后每次球币
            money = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_more_Gold].Param;
    }
    //logger.error("-==-=-=-=-=-=-=-=-",money, player.gulfCup.contestNum);
    if(!player.checkMoneyIsEnough(commonEnum.PLAY_INFO.gold, money))//检查钱是否足够
    {
        logger.error('gulfCup.joinMatch: money not enough !', playerId, money);
        return cb(Code.GOLD_FALL, player.gulfCup.conditionID, []);
    }

    //触发任务
    player.tasks.triggerTask(commonEnum.TARGET_TYPE.JOIN_GULF_CUP);
    player.gulfCup.initCombatList(false);//初始化对战列表
    cb(Code.OK, player.gulfCup.conditionID, player.gulfCup.CombatList);//返回奖励条件id，对战列表

};
//开始比赛
playerGulfCupService.prototype.gulfCupBattle = function(playerId, msg, cb)
{
    let playerService = this.app.get("playerService");
    var player = playerService.getPlayer(playerId);
    if(!player) {
        logger.debug('playerGulfCupService.gulfCupBattle: player not exist !');
        cb(Code.FAIL, "");
        return;
    }

    let costNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_Battle_energy].Param;//每场比赛扣除精力
    let costRet = player.checkResourceIsEnough(commonEnum.PLAY_INFO.energy, costNum);//检查体力是否足够
    if(!costRet) {
        cb(Code.ENERGY_FAIL, "");
        return;
    }

    let teamId;
    //logger.error("yyyyyyyyyyyyyyyyyyyyyyy",  player.gulfCup.CombatList.length, playerId, player.gulfCup.CombatList);
    for(let i in player.gulfCup.CombatList)//遍历海湾杯战斗列表
    {
        if(player.gulfCup.CombatList[i].TeamA.id === playerId)
        {
            if(!player.gulfCup.CombatList[i].TeamB.id)
            {
                logger.error("gulf CombatList err", playerId, player.gulfCup.CombatList);
            }
            teamId = player.gulfCup.CombatList[i].TeamB.id;//得到现阶段对战的队伍id
            break;
        }
        if(player.gulfCup.CombatList[i].TeamB.id === playerId)
        {
            if(!player.gulfCup.CombatList[i].TeamA.id)
            {
                logger.error("gulf CombatList err", playerId, player.gulfCup.CombatList);
            }
            teamId = player.gulfCup.CombatList[i].TeamA.id;//得到现阶段对战的队伍id
            break;
        }
    }
    let retCode = 200;
    if(!teamId)
    {
        retCode = Code.PARAM_FAIL;
        cb(retCode, "");
        return;
    }

    //获取比赛列表
    let rpcMsg = {};
    rpcMsg.configId = teamId;
    rpcMsg.playerId = playerId;         //玩家本人
    rpcMsg.playerLv = player.level;     //玩家等级
    let self = this;
    let rpcSession = {frontendId: playerService.app.getServerId()};
    //测试战斗结束返回奖励
    // self.updateGulfCupResult(playerId, teamId, 4, 1, function (ret) {
    //     logger.error("返回的奖励:::::::::::", ret);
    // });

    //通知BattleSvr
    playerService.app.rpc.battle.battleRemote.pveGulfCupBattleStart(rpcSession, rpcMsg, function (code, roomUid) {
        if(code !== Code.OK) {
            logger.warn("rpc pveGulfCupBattleStart fail, ret code: ", code);
            cb(code, "");
            return;
        }
        cb(code, roomUid);
        return;
    });
};

//战斗服返回的战斗结果
playerGulfCupService.prototype.updateGulfCupResult = function(playerId, reamId, selfScore, otherScore, cb)
{
    //logger.error("有没有返回战斗结果呢：：：：：：：：：：：：：：：", playerId, reamId, selfScore, otherScore);
    let playerService = this.app.get("playerService");
    let player = playerService.getPlayer(playerId);
    if (!player){
        logger.error("PlayerGulfCupService.updateGulfCupResult player not exist!", playerId);
        cb(Code.FAIL);
        return;
    }

    // logger.error("PlayerWorldCupService.updateWorldCupResult",robotId, selfScore, otherScore);
    let ret = player.gulfCup.pveGulfCupBattleResult(reamId, selfScore, otherScore);
    if (ret.code !== Code.OK) {
        logger.error("PlayerGulfCupService.updateGulfCupResult error reason!");
        return cb(ret.code);
    }

    //计算PL
    player.heros.checkHeroLeaveTime();
    //记录时间
    player.heros.recordHeroLeaveTime();
    //增加球员战斗场数
    player.heros.addHeroBattleNum(commonEnum.HERO_BATTLE_TYPE.GulfCup);
    //增加球员PL
    player.heros.addHeroFatigue(selfScore, otherScore);
    //记录战败
    player.act.addPushGiftBattleNum(selfScore, otherScore);

    let msg = {
        code: ret.code,
        award: ret.award,
        awardNum: ret.awardNum,
    };

    //推送战斗结果
    playerService.app.get("pushMessageService").unicast("game.playerHandler.updateGulfCupResult", msg, player);

    let costNum = dataApi.allData.data["SystemParam"][commonEnum.TABLE_SYSTEM_PARAM.gulfCup_Battle_energy].Param;//每场比赛扣除精力
    //扣除体力
    player.consumeEnergy(costNum);

    //player.tasks.triggerTask(commonEnum.TARGET_TYPE.JOIN_MIDDLE_EAST_CUP);
    player.saveHeros();
    player.updateHeros();
    player.saveGulfCup();
    player.saveAct();
    player.saveTasks();
    cb(ret.code);
    return;
};
