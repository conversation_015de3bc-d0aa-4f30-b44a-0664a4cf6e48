/**
 * Created by shine on 2015/3/24.
 */
var EventEmitter = require('events').EventEmitter;
var logger = require('pomelo-logger').getLogger("pomelo", __filename);;
var util = require('util');
var async = require('async');


module.exports.create = function (app, gmDao) {
	return new GMService(app, gmDao);
};

var GMService = function(app, gmDao){
	this.app = app;
	this.gmDao = gmDao;
};

GMService.prototype.insertLoginLog = function(log) {
	this.gmDao.insertLoginActivity(log);
	if (log.isNewRegister) {
		this.gmDao.insertRegisterActivity(log);
	}
};

