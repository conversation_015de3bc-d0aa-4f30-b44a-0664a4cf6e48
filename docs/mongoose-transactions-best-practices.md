# Mongoose 事务最佳实践指南

## 1. 事务的基本概念

事务是数据库操作的一个逻辑单元，具有 ACID 特性：

- **原子性(Atomicity)**: 事务中的所有操作作为一个整体执行，要么全部成功，要么全部失败回滚
- **一致性(Consistency)**: 事务执行前后数据库保持一致状态
- **隔离性(Isolation)**: 并发事务之间互不干扰
- **持久性(Durability)**: 事务一旦提交永久生效

## 2. 事务实现示例

### 2.1 基础事务模板
```typescript
async function transactionExample() {
  const session = await mongoose.startSession();
  
  try {
    session.startTransaction();
    
    // 执行数据库操作...
    await Model1.updateOne({...}, {$set: {...}}, { session });
    await Model2.updateOne({...}, {$set: {...}}, { session });
    
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
}
```

### 2.2 事务选项配置
```typescript
const transactionOptions = {
  readPreference: 'primary',
  readConcern: { level: 'local' },
  writeConcern: { w: 'majority' }
};

await session.startTransaction(transactionOptions);
```

## 3. 常见使用场景

### 3.1 多文档操作
适用于需要同时更新多个集合的场景，确保数据一致性。

```typescript
// 球员转会场景
async function transferPlayer(playerId: string, fromTeam: string, toTeam: string) {
  const session = await mongoose.startSession();
  
  try {
    session.startTransaction();
    
    // 1. 更新球员所属球队
    await PlayerModel.updateOne(
      { playerId },
      { teamId: toTeam },
      { session }
    );

    // 2. 更新原球队阵容
    await TeamModel.updateOne(
      { teamId: fromTeam },
      { $pull: { players: playerId } },
      { session }
    );

    // 3. 更新新球队阵容
    await TeamModel.updateOne(
      { teamId: toTeam },
      { $push: { players: playerId } },
      { session }
    );

    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  } finally {
    session.endSession();
  }
}
```

### 3.2 金融类操作
涉及货币、积分等敏感数据的操作。

```typescript
// 球员交易场景
async function tradePlayer(playerId: string, price: number, buyerId: string) {
  const session = await mongoose.startSession();

  try {
    session.startTransaction();
    
    // 1. 检查并扣除买家金币
    const buyer = await UserModel.findOneAndUpdate(
      { 
        userId: buyerId,
        coins: { $gte: price }
      },
      { $inc: { coins: -price } },
      { session, new: true }
    );
    
    if (!buyer) {
      throw new Error('余额不足');
    }

    // 2. 转移球员所有权
    await PlayerModel.updateOne(
      { playerId },
      { userId: buyerId },
      { session }
    );

    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  }
}
```

### 3.3 数据迁移和批量更新
需要保证大量数据更新的一致性。

```typescript
async function batchUpdatePlayers(updates: PlayerUpdate[]) {
  const session = await mongoose.startSession();
  
  try {
    session.startTransaction();
    
    for (const update of updates) {
      await PlayerModel.updateOne(
        { playerId: update.playerId },
        { $set: update.data },
        { session }
      );
    }
    
    await session.commitTransaction();
  } catch (error) {
    await session.abortTransaction();
    throw error;
  }
}
```

## 4. 最佳实践建议

### 4.1 性能优化
- 控制事务范围，避免过长事务
- 合理设置超时时间
- 避免在事务中执行查询操作
- 使用批量操作替代循环单条操作

```typescript
// 推荐的批量操作方式
const bulkOps = updates.map(update => ({
  updateOne: {
    filter: { playerId: update.playerId },
    update: { $set: update.data }
  }
}));

await PlayerModel.bulkWrite(bulkOps, { session });
```

### 4.2 错误处理
- 实现完善的回滚机制
- 记录详细的错误日志
- 添加重试策略

```typescript
async function retryTransaction(operation: Function, maxAttempts = 3) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxAttempts) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
}
```

### 4.3 资源管理
- 使用 try-catch-finally 确保资源释放
- 控制并发事务数量
- 监控事务执行时间

```typescript
class TransactionManager {
  private activeTransactions = 0;
  private readonly MAX_CONCURRENT = 10;

  async executeInTransaction(operation: Function) {
    if (this.activeTransactions >= this.MAX_CONCURRENT) {
      throw new Error('Too many concurrent transactions');
    }

    const session = await mongoose.startSession();
    this.activeTransactions++;

    try {
      session.startTransaction();
      const result = await operation(session);
      await session.commitTransaction();
      return result;
    } catch (error) {
      await session.abortTransaction();
      throw error;
    } finally {
      session.endSession();
      this.activeTransactions--;
    }
  }
}
```

## 5. 注意事项

1. **事务支持**
   - 确保 MongoDB 版本 >= 4.0
   - 确保使用复制集部署
   - 检查驱动程序版本兼容性

2. **性能影响**
   - 事务会带来额外开销
   - 长时间运行的事务可能影响其他操作
   - 合理评估是否需要使用事务

3. **调试与监控**
   - 记录事务执行时间
   - 监控事务成功率
   - 设置适当的告警阈值

## 6. 总结

MongoDB 事务是保证数据一致性的重要机制，但需要权衡使用场景和性能开销。合理使用事务可以提高应用的可靠性和数据完整性，但过度使用可能导致性能下降。建议在以下场景优先考虑使用事务：

- 多文档操作需要保证原子性
- 涉及金融、积分等敏感数据操作
- 需要保证数据一致性的复杂业务流程

在实际应用中，应结合业务需求和性能要求，合理设计事务范围和并发控制策略。
