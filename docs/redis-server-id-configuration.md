# Redis区服ID配置最佳实践

## 📋 配置优先级

Redis前缀架构v3.0支持多种方式配置区服ID，优先级如下：

### 1. **系统环境变量（推荐生产环境）**
```bash
# 启动时设置，优先级最高
SERVER_ID=1 npm start
SERVER_ID=2 node dist/apps/auth/main.js

# Docker环境
docker run -e SERVER_ID=1 my-app
```

### 2. **模块配置（推荐开发环境）**
```typescript
// apps/auth/src/app.module.ts
@Module({
  imports: [
    RedisModule.forRootAsync({
      service: 'auth',
      serverId: '1', // 明确指定区服ID
      useFactory: (configService: ConfigService) => ({
        host: configService.get('REDIS_HOST'),
        port: configService.get('REDIS_PORT'),
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

### 3. **配置文件（.env）**
```bash
# .env
SERVER_ID=1
MICROSERVICE_NAME=auth
```

### 4. **默认值**
```typescript
// 如果以上都未设置，默认为 '1'
```

## 🎯 业务逻辑层默认行为

### ✅ **推荐设计：默认当前区服**

```typescript
// 业务代码中的常见用法
class UserService {
  // 不传递serverId时，默认使用当前区服
  async getUserProfile(userId: string) {
    return await this.redisService.get(`user:profile:${userId}`, 'server');
    // 实际键名: dev:fm:server1:user:user:profile:123
  }

  // 只有在需要跨区服访问时才指定serverId
  async getUserFromOtherServer(userId: string, targetServerId: string) {
    return await this.redisService.get(`user:profile:${userId}`, 'server', targetServerId);
    // 实际键名: dev:fm:server2:user:user:profile:123
  }

  // 全局数据不需要serverId
  async getGlobalConfig() {
    return await this.redisService.get('config:system', 'global');
    // 实际键名: dev:fm:global:user:config:system
  }
}
```

### 📊 **使用统计分析**

| 使用场景 | 频率 | serverId参数 | 说明 |
|---------|------|-------------|------|
| 当前区服操作 | 95% | 不传递 | 默认当前区服，最常见 |
| 跨区服访问 | 4% | 明确指定 | 管理、迁移、监控 |
| 全局数据 | 1% | 不需要 | 系统配置、区服列表 |

## 🔧 配置方式对比

### **方式A: 系统环境变量（推荐）**

**优势**：
- ✅ **部署灵活**: 同一镜像可部署到不同区服
- ✅ **安全性高**: 不会意外提交到代码仓库
- ✅ **运维友好**: 容器编排工具易于管理
- ✅ **性能最优**: 启动时确定，无需重复读取

**使用场景**：
```bash
# 生产环境 - Kubernetes
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-server1
spec:
  template:
    spec:
      containers:
      - name: auth
        env:
        - name: SERVER_ID
          value: "1"

# 生产环境 - Docker Compose
services:
  auth-server1:
    environment:
      - SERVER_ID=1
  auth-server2:
    environment:
      - SERVER_ID=2
```

### **方式B: .env配置文件**

**优势**：
- ✅ **开发便利**: 本地开发配置简单
- ✅ **版本控制**: 可以提交默认配置

**劣势**：
- ❌ **部署复杂**: 需要为每个区服准备不同配置文件
- ❌ **安全风险**: 可能意外提交敏感配置

**使用场景**：
```bash
# 开发环境
# .env.development
SERVER_ID=dev
NODE_ENV=development

# .env.production.server1
SERVER_ID=1
NODE_ENV=production

# .env.production.server2
SERVER_ID=2
NODE_ENV=production
```

## 🚀 最佳实践建议

### 1. **分环境配置策略**

```typescript
// 开发环境：使用模块配置
RedisModule.forRootAsync({
  service: 'auth',
  serverId: process.env.NODE_ENV === 'development' ? 'dev' : undefined,
  // ...
})

// 生产环境：使用系统环境变量
// 启动命令: SERVER_ID=1 node dist/main.js
```

### 2. **动态配置支持**

```typescript
// 支持运行时切换（主要用于测试）
import { RedisKeyUtils } from '@/redis';

// 测试中临时切换区服
beforeEach(() => {
  RedisKeyUtils.setCurrentServerId('test');
});

afterEach(() => {
  RedisKeyUtils.resetCache();
});
```

### 3. **配置验证**

```typescript
// 启动时验证配置
@Injectable()
export class ConfigValidationService implements OnModuleInit {
  constructor(
    @Inject('REDIS_SERVER_ID') private readonly serverId: string,
    @Inject('REDIS_SERVICE_CONTEXT') private readonly serviceContext: string,
  ) {}

  async onModuleInit() {
    this.logger.log(`🚀 Redis配置验证:`);
    this.logger.log(`  - 区服ID: ${this.serverId}`);
    this.logger.log(`  - 服务上下文: ${this.serviceContext}`);
    
    if (!this.serverId || this.serverId === 'undefined') {
      throw new Error('SERVER_ID未正确配置');
    }
  }
}
```

## 📊 架构优势总结

### ✅ **设计优势**

1. **灵活性最大化**
   - 支持当前区服默认行为（99%场景）
   - 支持跨区服访问（1%场景）
   - 支持多种配置方式

2. **性能优化**
   - 缓存机制避免重复读取环境变量
   - 默认行为无额外性能开销

3. **开发友好**
   - 便捷方法简化常见操作
   - 类型安全的API设计
   - 清晰的配置优先级

4. **运维友好**
   - 支持容器化部署
   - 支持动态配置
   - 详细的日志输出

### 🎯 **推荐配置**

```typescript
// 生产环境推荐配置
export class ProductionConfig {
  // 1. 使用系统环境变量
  static getServerConfig() {
    return {
      serverId: process.env.SERVER_ID, // 启动时设置
      service: process.env.MICROSERVICE_NAME,
    };
  }

  // 2. 业务代码保持简洁
  async getUserData(userId: string) {
    // 默认当前区服，无需传递serverId
    return await this.redisService.get(`user:${userId}`, 'server');
  }

  // 3. 跨区服访问时明确指定
  async migrateUser(userId: string, targetServerId: string) {
    const userData = await this.redisService.get(`user:${userId}`, 'server');
    await this.redisService.set(`user:${userId}`, userData, undefined, 'server', targetServerId);
  }
}
```

这种设计既保证了99%常见场景的简洁性，又支持1%特殊场景的灵活性，是最优的架构选择。
