# 网关服务开发指南

## 🎯 **概述**

本指南为网关服务的开发团队提供了基于分层架构的开发规范、最佳实践和代码审查标准。遵循这些指南将确保代码质量、架构一致性和团队协作效率。

## 🏗️ **开发原则**

### **1. 分层架构原则**

```typescript
// ✅ 遵循分层依赖规则
应用层 → 业务层 → 核心层 → 基础设施层 → 通用层

// ✅ 正确的依赖注入
@Injectable()
export class ProxyService {
  constructor(
    private readonly routeMatcher: RouteMatcherService,  // 核心层
    private readonly authService: AuthService,          // 核心层
  ) {}
}

// ❌ 错误的跨层依赖
@Injectable()
export class CoreService {
  constructor(
    private readonly proxyService: ProxyService,  // ❌ 核心层不能依赖业务层
  ) {}
}
```

### **2. 单一职责原则**

```typescript
// ✅ 职责单一的服务
@Injectable()
export class RouteMatcherService {
  // 只负责路由匹配逻辑
  async match(path: string): Promise<RouteConfig> {
    // 路由匹配实现
  }
}

// ❌ 职责混乱的服务
@Injectable()
export class GatewayService {
  // ❌ 混合了路由、认证、缓存等多种职责
  async routeAndAuthAndCache() {}
}
```

### **3. 依赖倒置原则**

```typescript
// ✅ 依赖抽象接口
export interface LoadBalanceStrategy {
  select(targets: ServiceTarget[]): ServiceTarget;
}

@Injectable()
export class LoadBalancerService {
  constructor(
    @Inject('LOAD_BALANCE_STRATEGY') 
    private readonly strategy: LoadBalanceStrategy
  ) {}
}

// ❌ 依赖具体实现
@Injectable()
export class LoadBalancerService {
  private strategy = new RoundRobinStrategy(); // ❌ 硬编码依赖
}
```

## 📁 **文件组织规范**

### **1. 目录命名规范**

```bash
# ✅ 正确的目录命名
apps/gateway/src/
├── app/           # 应用层 - 小写单数
├── domain/        # 业务层 - 小写单数
├── core/          # 核心层 - 小写单数
├── infrastructure/ # 基础设施层 - 小写单数
└── common/        # 通用层 - 小写单数

# ❌ 错误的目录命名
├── Apps/          # ❌ 大写
├── domains/       # ❌ 复数
├── Core-Layer/    # ❌ 连字符
└── Common_Utils/  # ❌ 下划线
```

### **2. 文件命名规范**

```bash
# ✅ 正确的文件命名
user.controller.ts     # 控制器
user.service.ts        # 服务
user.module.ts         # 模块
user.interface.ts      # 接口
user.dto.ts           # 数据传输对象
user.guard.ts         # 守卫
user.interceptor.ts   # 拦截器
user.middleware.ts    # 中间件
user.filter.ts        # 过滤器
user.spec.ts          # 测试文件

# ❌ 错误的文件命名
UserController.ts     # ❌ 大驼峰
user_service.ts       # ❌ 下划线
user-Service.ts       # ❌ 混合命名
userservice.ts        # ❌ 缺少分隔符
```

### **3. 模块组织规范**

```typescript
// ✅ 正确的模块结构
domain/user/
├── user.controller.ts
├── user.service.ts
├── user.module.ts
├── dto/
│   ├── create-user.dto.ts
│   └── update-user.dto.ts
├── guards/
│   └── user-auth.guard.ts
└── interfaces/
    └── user.interface.ts

// ❌ 错误的模块结构
domain/user/
├── controller.ts      # ❌ 缺少前缀
├── service.ts         # ❌ 缺少前缀
├── module.ts          # ❌ 缺少前缀
└── all-dtos.ts        # ❌ 文件过大
```

## 🔧 **代码编写规范**

### **1. 模块定义规范**

```typescript
// ✅ 正确的模块定义
@Module({
  imports: [
    ConfigModule,           // 配置模块
    CoreModule,            // 核心模块
    MicroservicesModule,   // 基础设施模块
  ],
  controllers: [UserController],
  providers: [
    UserService,
    {
      provide: 'USER_REPOSITORY',
      useClass: UserRepository,
    },
  ],
  exports: [UserService],  // 明确导出
})
export class UserModule {}

// ❌ 错误的模块定义
@Module({
  imports: [
    // ❌ 重复导入共享模块
    ClientsModule.registerAsync([...]),
    JwtModule.registerAsync([...]),
  ],
  controllers: [UserController],
  providers: [
    UserService,
    // ❌ 直接注册外部依赖
    RouteMatcherService,
    LoadBalancerService,
  ],
  // ❌ 缺少 exports
})
export class UserModule {}
```

### **2. 服务定义规范**

```typescript
// ✅ 正确的服务定义
@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @Inject('USER_REPOSITORY')
    private readonly userRepository: UserRepository,
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  async createUser(createUserDto: CreateUserDto): Promise<User> {
    this.logger.log(`Creating user: ${createUserDto.email}`);
    
    // 业务逻辑实现
    const hashedPassword = await this.authService.hashPassword(
      createUserDto.password
    );
    
    return this.userRepository.create({
      ...createUserDto,
      password: hashedPassword,
    });
  }

  async findById(id: string): Promise<User | null> {
    return this.userRepository.findById(id);
  }
}

// ❌ 错误的服务定义
@Injectable()
export class UserService {
  // ❌ 缺少日志
  // ❌ 直接依赖具体实现
  constructor(private readonly userRepository: MongoUserRepository) {}

  // ❌ 方法过大，职责不清
  async createUserWithAuthAndNotification(data: any): Promise<any> {
    // 混合了用户创建、认证、通知等多种职责
  }

  // ❌ 缺少错误处理
  async findById(id: string): Promise<User> {
    return this.userRepository.findById(id); // 可能返回 null
  }
}
```

### **3. 控制器定义规范**

```typescript
// ✅ 正确的控制器定义
@Controller('users')
@UseGuards(AuthGuard)
@ApiTags('用户管理')
export class UserController {
  private readonly logger = new Logger(UserController.name);

  constructor(private readonly characterService: UserService) {}

  @Post()
  @ApiOperation({ summary: '创建用户' })
  @ApiResponse({ status: 201, description: '用户创建成功', type: User })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async create(
    @Body() createUserDto: CreateUserDto,
  ): Promise<ApiResponse<User>> {
    try {
      const user = await this.characterService.createUser(createUserDto);
      return {
        success: true,
        data: user,
        message: '用户创建成功',
      };
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`);
      throw new BadRequestException('用户创建失败');
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取用户信息' })
  async findOne(@Param('id') id: string): Promise<ApiResponse<User>> {
    const user = await this.characterService.findById(id);
    if (!user) {
      throw new NotFoundException('用户不存在');
    }
    
    return {
      success: true,
      data: user,
      message: '获取成功',
    };
  }
}

// ❌ 错误的控制器定义
@Controller('users')
export class UserController {
  // ❌ 缺少日志和文档
  constructor(private readonly characterService: UserService) {}

  @Post()
  async create(@Body() data: any): Promise<any> {
    // ❌ 缺少类型定义
    // ❌ 缺少错误处理
    // ❌ 缺少响应格式统一
    return this.characterService.createUser(data);
  }
}
```

## 🚀 **开发流程**

### **1. 新功能开发流程**

```bash
# 1. 创建功能分支
git checkout -b feature/user-management

# 2. 分析需求，确定所属层级
# - 是否为新的业务用例？ → 业务层
# - 是否为核心能力扩展？ → 核心层
# - 是否为技术基础设施？ → 基础设施层
# - 是否为系统级功能？ → 应用层

# 3. 创建目录结构
mkdir -p apps/gateway/src/domain/user
mkdir -p apps/gateway/src/domain/user/dto
mkdir -p apps/gateway/src/domain/user/guards

# 4. 创建文件
touch apps/gateway/src/domain/user/user.controller.ts
touch apps/gateway/src/domain/user/user.service.ts
touch apps/gateway/src/domain/user/user.module.ts

# 5. 编写测试用例
touch apps/gateway/src/domain/user/user.service.spec.ts
touch apps/gateway/src/domain/user/user.controller.spec.ts

# 6. 实现功能
# 7. 运行测试
npm run test:gateway

# 8. 运行架构测试
npx jest --config apps/gateway/jest.architecture.config.js

# 9. 代码审查
# 10. 合并代码
```

### **2. 重构流程**

```bash
# 1. 创建重构分支
git checkout -b refactor/improve-auth-module

# 2. 运行现有测试确保功能正常
npm run test:gateway

# 3. 进行重构
# - 只改变代码组织方式
# - 不改变功能实现
# - 保持API兼容性

# 4. 运行测试验证功能
npm run test:gateway

# 5. 运行架构测试验证结构
npx jest --config apps/gateway/jest.architecture.config.js

# 6. 性能测试验证无回归
npm run test:performance

# 7. 更新文档
# 8. 代码审查
# 9. 合并代码
```

## 🛠️ **开发工具和命令**

### **1. 常用开发命令**

```bash
# 启动开发服务器
npm run start:gateway:dev

# 构建项目
npm run build:gateway

# 运行测试
npm run test:gateway              # 单元测试
npm run test:gateway:e2e          # 端到端测试
npm run test:gateway:cov          # 测试覆盖率

# 运行架构测试
npx jest --config apps/gateway/jest.architecture.config.js

# 代码格式化
npm run format

# 代码检查
npm run lint

# 类型检查
npx tsc --noEmit
```

### **2. 调试配置**

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Gateway",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/@nestjs/cli/bin/nest.js",
      "args": ["start", "gateway", "--debug", "--watch"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "restart": true,
      "protocol": "inspector",
      "stopOnEntry": false,
      "outFiles": ["${workspaceFolder}/dist/**/*.js"]
    }
  ]
}
```

### **3. 代码片段模板**

```json
// .vscode/snippets/nestjs.json
{
  "NestJS Service": {
    "prefix": "nest-service",
    "body": [
      "@Injectable()",
      "export class ${1:ServiceName}Service {",
      "  private readonly logger = new Logger(${1:ServiceName}Service.name);",
      "",
      "  constructor(",
      "    ${2:// dependencies}",
      "  ) {}",
      "",
      "  ${3:// methods}",
      "}"
    ],
    "description": "创建NestJS服务"
  },
  "NestJS Controller": {
    "prefix": "nest-controller",
    "body": [
      "@Controller('${1:route}')",
      "@ApiTags('${2:tag}')",
      "export class ${3:ControllerName}Controller {",
      "  private readonly logger = new Logger(${3:ControllerName}Controller.name);",
      "",
      "  constructor(",
      "    private readonly ${4:service}: ${5:ServiceType},",
      "  ) {}",
      "",
      "  ${6:// endpoints}",
      "}"
    ],
    "description": "创建NestJS控制器"
  }
}
```

## 📊 **性能监控和优化**

### **1. 性能指标监控**

```typescript
// 关键性能指标
interface PerformanceMetrics {
  // 内存使用
  memoryUsage: {
    heapUsed: number;      // 已使用堆内存
    heapTotal: number;     // 总堆内存
    external: number;      // 外部内存
    rss: number;          // 常驻集大小
  };

  // 响应时间
  responseTime: {
    avg: number;          // 平均响应时间
    p95: number;          // 95分位响应时间
    p99: number;          // 99分位响应时间
  };

  // 吞吐量
  throughput: {
    requestsPerSecond: number;  // 每秒请求数
    connectionsActive: number;  // 活跃连接数
  };

  // 错误率
  errorRate: {
    total: number;        // 总错误率
    byType: Record<string, number>; // 按类型分组的错误率
  };
}
```

### **2. 性能优化建议**

```typescript
// ✅ 性能优化最佳实践

// 1. 使用连接池
@Module({
  imports: [
    ClientsModule.registerAsync([{
      name: 'AUTH_SERVICE',
      useFactory: () => ({
        transport: Transport.REDIS,
        options: {
          host: 'localhost',
          port: 6379,
          retryAttempts: 5,
          retryDelay: 3000,
          maxRetriesPerRequest: 3,  // ✅ 限制重试次数
        },
      }),
    }]),
  ],
})

// 2. 实现缓存策略
@Injectable()
export class UserService {
  @Cacheable('user', 300) // ✅ 缓存5分钟
  async findById(id: string): Promise<User> {
    return this.userRepository.findById(id);
  }
}

// 3. 使用批量操作
async findMultipleUsers(ids: string[]): Promise<User[]> {
  // ✅ 批量查询而不是循环单个查询
  return this.userRepository.findByIds(ids);
}

// 4. 实现限流
@UseGuards(ThrottlerGuard)
@Throttle(100, 60) // ✅ 每分钟最多100次请求
@Controller('users')
export class UserController {}

// 5. 异步处理
async processLargeDataset(data: any[]): Promise<void> {
  // ✅ 分批处理大数据集
  const batchSize = 100;
  for (let i = 0; i < data.length; i += batchSize) {
    const batch = data.slice(i, i + batchSize);
    await this.processBatch(batch);
  }
}
```

## 🔒 **安全开发规范**

### **1. 输入验证**

```typescript
// ✅ 正确的输入验证
export class CreateUserDto {
  @IsEmail()
  @ApiProperty({ description: '用户邮箱' })
  email: string;

  @IsString()
  @MinLength(8)
  @MaxLength(50)
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: '密码必须包含大小写字母和数字',
  })
  @ApiProperty({ description: '用户密码' })
  password: string;

  @IsString()
  @MinLength(2)
  @MaxLength(50)
  @ApiProperty({ description: '用户姓名' })
  name: string;
}

// ❌ 错误的输入验证
export class CreateUserDto {
  email: any;     // ❌ 缺少类型和验证
  password: any;  // ❌ 缺少密码强度验证
  name: any;      // ❌ 缺少长度限制
}
```

### **2. 权限控制**

```typescript
// ✅ 正确的权限控制
@Controller('users')
@UseGuards(AuthGuard, RolesGuard)
export class UserController {
  @Post()
  @Roles('admin', 'user-manager')
  @ApiOperation({ summary: '创建用户' })
  async create(@Body() createUserDto: CreateUserDto) {
    return this.characterService.createUser(createUserDto);
  }

  @Get(':id')
  @Permissions('user:read')
  async findOne(@Param('id') id: string, @CurrentUser() user: User) {
    // ✅ 检查用户是否有权限访问该资源
    if (id !== user.id && !user.hasRole('admin')) {
      throw new ForbiddenException('无权限访问该用户信息');
    }
    return this.characterService.findById(id);
  }
}

// ❌ 错误的权限控制
@Controller('users')
export class UserController {
  @Get(':id')
  async findOne(@Param('id') id: string) {
    // ❌ 缺少权限检查
    return this.characterService.findById(id);
  }
}
```

### **3. 敏感信息处理**

```typescript
// ✅ 正确的敏感信息处理
export class User {
  @ApiProperty()
  id: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  name: string;

  @Exclude() // ✅ 排除敏感字段
  password: string;

  @Transform(({ value }) => '***') // ✅ 脱敏处理
  @ApiProperty()
  phone: string;
}

// 日志记录时脱敏
this.logger.log(`User login: ${email.replace(/(.{2}).*(@.*)/, '$1***$2')}`);

// ❌ 错误的敏感信息处理
export class User {
  id: string;
  email: string;
  name: string;
  password: string; // ❌ 密码可能被序列化返回
  phone: string;    // ❌ 手机号未脱敏
}

// ❌ 日志中包含敏感信息
this.logger.log(`User login: ${email}, password: ${password}`);
```

## 📚 **学习资源**

### **1. 推荐阅读**

- **架构设计**: 《Clean Architecture》- Robert C. Martin
- **领域驱动设计**: 《Domain-Driven Design》- Eric Evans
- **微服务架构**: 《Building Microservices》- Sam Newman
- **NestJS官方文档**: https://docs.nestjs.com/

### **2. 团队培训计划**

```typescript
// 培训计划
const trainingPlan = {
  week1: '分层架构原理和实践',
  week2: 'NestJS依赖注入和模块系统',
  week3: '测试驱动开发和单元测试',
  week4: '性能优化和监控',
  week5: '安全开发最佳实践',
  week6: '代码审查和质量保证',
};
```

## 🎯 **总结**

遵循本开发指南将帮助团队：

1. **保持架构一致性**: 确保所有代码都符合分层架构原则
2. **提高代码质量**: 通过规范和检查清单保证代码质量
3. **提升开发效率**: 标准化的流程和工具提高开发效率
4. **增强系统安全性**: 安全开发规范保护系统安全
5. **促进团队协作**: 统一的标准便于团队协作和知识传递

请所有团队成员认真学习并严格遵循这些指南，共同维护高质量的代码库。

## 🧪 **测试规范**

### **1. 单元测试规范**

```typescript
// ✅ 正确的单元测试
describe('UserService', () => {
  let service: UserService;
  let userRepository: jest.Mocked<UserRepository>;
  let authService: jest.Mocked<AuthService>;

  beforeEach(async () => {
    const mockUserRepository = {
      create: jest.fn(),
      findById: jest.fn(),
    };

    const mockAuthService = {
      hashPassword: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        { provide: 'USER_REPOSITORY', useValue: mockUserRepository },
        { provide: AuthService, useValue: mockAuthService },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    userRepository = module.get('USER_REPOSITORY');
    authService = module.get(AuthService);
  });

  describe('createUser', () => {
    it('应该成功创建用户', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };
      const hashedPassword = 'hashed_password';
      const expectedUser = { id: '1', ...createUserDto, password: hashedPassword };

      authService.hashPassword.mockResolvedValue(hashedPassword);
      userRepository.create.mockResolvedValue(expectedUser);

      // Act
      const result = await service.createUser(createUserDto);

      // Assert
      expect(authService.hashPassword).toHaveBeenCalledWith('password123');
      expect(userRepository.create).toHaveBeenCalledWith({
        ...createUserDto,
        password: hashedPassword,
      });
      expect(result).toEqual(expectedUser);
    });

    it('当密码哈希失败时应该抛出错误', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      authService.hashPassword.mockRejectedValue(new Error('Hash failed'));

      // Act & Assert
      await expect(service.createUser(createUserDto)).rejects.toThrow('Hash failed');
    });
  });
});
```

### **2. 集成测试规范**

```typescript
// ✅ 正确的集成测试
describe('UserController (e2e)', () => {
  let app: INestApplication;
  let characterService: UserService;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [UserModule],
    })
      .overrideProvider('USER_REPOSITORY')
      .useValue(mockUserRepository)
      .compile();

    app = moduleFixture.createNestApplication();
    characterService = moduleFixture.get<UserService>(UserService);
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/users (POST)', () => {
    it('应该创建新用户', () => {
      const createUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      };

      return request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.email).toBe(createUserDto.email);
          expect(res.body.data.password).toBeUndefined(); // 密码不应该返回
        });
    });
  });
});
```

## 📋 **代码审查清单**

### **1. 架构层面检查**

```typescript
// ✅ 架构检查清单
□ 文件放在正确的层级目录下
□ 依赖方向符合分层架构规则
□ 没有跨层直接依赖
□ 模块职责单一明确
□ 接口定义清晰
□ 没有循环依赖

// ❌ 常见架构问题
□ 业务层依赖应用层
□ 核心层依赖业务层
□ 基础设施层依赖上层
□ 通用层依赖其他层
□ 模块职责混乱
□ 直接依赖具体实现
```

### **2. 代码质量检查**

```typescript
// ✅ 代码质量检查清单
□ 遵循命名规范
□ 有适当的日志记录
□ 有完整的错误处理
□ 有类型定义
□ 有API文档注释
□ 有单元测试覆盖
□ 没有硬编码值
□ 使用依赖注入

// ❌ 常见代码问题
□ 命名不规范
□ 缺少日志
□ 缺少错误处理
□ 使用any类型
□ 缺少文档
□ 缺少测试
□ 硬编码配置
□ 直接实例化依赖
```

### **3. 性能和安全检查**

```typescript
// ✅ 性能和安全检查清单
□ 没有内存泄漏风险
□ 有适当的缓存策略
□ 有限流保护
□ 有输入验证
□ 有权限检查
□ 敏感信息已脱敏
□ 使用参数化查询
□ 有超时设置

// ❌ 常见性能和安全问题
□ 未释放资源
□ 缺少缓存
□ 缺少限流
□ 缺少输入验证
□ 缺少权限检查
□ 敏感信息泄露
□ SQL注入风险
□ 无超时控制
```
