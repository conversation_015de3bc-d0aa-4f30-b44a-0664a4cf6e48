# 微服务开发快速参考卡片

## 🚀 MessagePattern命名规范

### ✅ 正确格式
```typescript
@MessagePattern('模块名.方法名')

// 示例
@MessagePattern('hero.create')
@MessagePattern('hero.getList')
@MessagePattern('formation.autoFormation')
```

### ❌ 错误格式
```typescript
@MessagePattern('create')        // 缺少模块前缀
@MessagePattern('getList')       // 缺少模块前缀
@MessagePattern('hero_create')   // 使用下划线
```

## 🔄 两种调用方式

### 1️⃣ 服务间直接调用
```typescript
// 用于：微服务之间的内部通信
await this.microserviceClient.call(
  '服务名',           // 'hero'
  'MessagePattern',   // 'hero.getList'
  参数对象
);

// 实例
const response = await this.microserviceClient.call(
  MICROSERVICE_NAMES.HERO_SERVICE,
  'hero.getList',
  { characterId: 'char_123' }
);
```

### 2️⃣ 网关WebSocket调用
```typescript
// 用于：客户端/测试脚本通过网关调用
const command = '服务名.MessagePattern';  // 'hero.hero.getList'
await this.sendMessage(command, 参数对象);

// 实例
await this.sendMessage('hero.hero.getList', {
  characterId: 'char_123'
});
```

## 📋 快速检查清单

### MessagePattern检查
- [ ] 使用`模块.方法`格式
- [ ] 同模块下命名一致
- [ ] 使用驼峰命名法
- [ ] 方法名语义清晰

### 调用格式检查
- [ ] 服务间调用：分离参数格式
- [ ] 网关调用：组合Command格式
- [ ] 参数对象结构正确
- [ ] 错误处理完善

### 测试验证
- [ ] 单元测试覆盖
- [ ] 集成测试通过
- [ ] 日志输出正常
- [ ] 性能指标达标

## 🛠️ 常用代码模板

### MessagePattern定义
```typescript
/**
 * 获取球员列表
 */
@MessagePattern('hero.getList')
@Cacheable({
  key: 'character:heroes:#{payload.characterId}',
  ttl: 1800
})
async getHeroList(@Payload() query: GetHeroListDto) {
  this.logger.log(`获取球员列表: ${JSON.stringify(query)}`);
  const result = await this.heroService.getHeroList(query);
  return {
    code: 0,
    message: '获取成功',
    data: result,
  };
}
```

### 服务间调用模板
```typescript
async callHeroService(characterId: string) {
  try {
    const response = await this.microserviceClient.call(
      MICROSERVICE_NAMES.HERO_SERVICE,
      'hero.getList',
      { characterId }
    );
    
    if (response && response.code === 0) {
      return response.data;
    } else {
      this.logger.warn(`Hero服务调用失败: ${JSON.stringify(response)}`);
      return [];
    }
  } catch (error) {
    this.logger.error('Hero服务调用异常', error);
    return [];
  }
}
```

### 网关调用模板
```javascript
async callThroughGateway(characterId) {
  try {
    const response = await this.sendMessage('hero.hero.getList', {
      characterId: characterId,
      serverId: this.serverId
    });
    
    if (response?.payload?.success && response.payload.data.code === 0) {
      return response.payload.data.data;
    } else {
      console.warn('网关调用失败:', response?.payload?.data?.message);
      return [];
    }
  } catch (error) {
    console.error('网关调用异常:', error);
    return [];
  }
}
```

## 🔍 调试命令

### 检查MessagePattern规范
```bash
# 查找不规范的MessagePattern
grep -r "@MessagePattern('[^.]*')" apps/ | grep -v "hero\.\|character\.\|formation\."

# 查找所有MessagePattern
grep -r "@MessagePattern" apps/ --include="*.ts"
```

### 服务健康检查
```bash
# 检查服务状态
curl http://localhost:3001/health  # Auth服务
curl http://localhost:3002/health  # Character服务  
curl http://localhost:3007/health  # Hero服务
curl http://localhost:3000/health  # Gateway服务
```

### Redis连接检查
```bash
# 连接Redis并监控
redis-cli -h *************** -p 6379 -a 123456
> MONITOR
```

## ⚠️ 常见错误

### 1. TimeoutError
```
原因：MessagePattern不匹配
解决：检查调用的MessagePattern是否与服务端定义一致
```

### 2. 服务发现失败
```
原因：服务未正确注册到Redis
解决：检查Redis连接和服务启动顺序
```

### 3. 参数格式错误
```
原因：DTO验证失败或参数结构不正确
解决：检查参数对象是否符合DTO定义
```

## 📊 性能优化

### 缓存策略
```typescript
@Cacheable({
  key: 'service:method:#{payload.id}',
  ttl: 3600,
  dataType: 'server'
})
```

### 超时配置
```typescript
// 客户端超时设置
timeout: 5000,        // 5秒超时
retryAttempts: 3,     // 重试3次
retryDelay: 1000      // 重试间隔1秒
```

### 批量操作
```typescript
// 优先使用批量接口
await this.microserviceClient.call('hero', 'hero.getBatch', { ids });
// 而不是循环调用单个接口
```

## 🔗 相关链接

- [完整规范文档](./microservice-communication-guide.md)
- [问题排查指南](./troubleshooting-microservice-timeout.md)
- [Hero服务开发指南](./hero-microservice-development-guide.md)
- [NestJS微服务文档](https://docs.nestjs.com/microservices/basics)

---

**快速参考版本**: v1.0  
**适用范围**: NestJS + Redis微服务架构  
**更新频率**: 随规范变更同步更新
