# Mongoose Schema 配置完整指南

## 📋 概述

本文档详细介绍Mongoose Schema的`toObject`和`toJSON`配置选项，帮助开发者在Schema级别统一处理文档转换逻辑，避免在业务代码中重复处理转换问题。

## ⚙️ Schema配置基础

### toObject vs toJSON

Mongoose提供了两个主要的配置选项来控制文档转换行为：

```typescript
const userSchema = new Schema({
  name: String,
  email: String,
  password: String
}, {
  // toObject: 控制 doc.toObject() 的行为
  toObject: {
    virtuals: true,
    getters: true,
    versionKey: false
  },
  
  // toJSON: 控制 JSON.stringify(doc) 和 res.json(doc) 的行为
  toJSON: {
    virtuals: true,
    getters: true,
    versionKey: false,
    transform: function(doc, ret) {
      delete ret.password;
      return ret;
    }
  }
});
```

### 配置选项详解

#### 1. **virtuals（虚拟属性）**

```typescript
const userSchema = new Schema({
  firstName: String,
  lastName: String
});

// 定义虚拟属性
userSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// 配置是否包含虚拟属性
const schemaOptions = {
  toObject: { virtuals: true },  // doc.toObject() 包含虚拟属性
  toJSON: { virtuals: true }     // JSON.stringify() 包含虚拟属性
};

const User = mongoose.model('User', new Schema(userSchema, schemaOptions));

// 使用示例
const user = new User({ firstName: 'John', lastName: 'Doe' });
console.log(user.toObject()); // { firstName: 'John', lastName: 'Doe', fullName: 'John Doe' }
console.log(JSON.stringify(user)); // {"firstName":"John","lastName":"Doe","fullName":"John Doe"}
```

#### 2. **getters（获取器）**

```typescript
const userSchema = new Schema({
  email: {
    type: String,
    get: function(email) {
      return email ? email.toLowerCase() : email;
    }
  },
  phone: {
    type: String,
    get: function(phone) {
      // 格式化电话号码显示
      return phone ? phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1-$2-$3') : phone;
    }
  }
});

// 配置是否应用getters
const schemaOptions = {
  toObject: { getters: true },
  toJSON: { getters: true }
};

// 使用示例
const user = new User({ 
  email: '<EMAIL>', 
  phone: '13812345678' 
});

console.log(user.toObject()); 
// { email: '<EMAIL>', phone: '138-1234-5678' }
```

#### 3. **versionKey（版本键）**

```typescript
// 默认情况下，Mongoose会添加__v字段
const user = new User({ name: 'John' });
console.log(user.toObject()); 
// { _id: ObjectId, name: 'John', __v: 0 }

// 移除版本键
const schemaOptions = {
  toObject: { versionKey: false },
  toJSON: { versionKey: false }
};

console.log(user.toObject()); 
// { _id: ObjectId, name: 'John' }
```

#### 4. **transform（转换函数）**

```typescript
const userSchema = new Schema({
  name: String,
  email: String,
  password: String,
  role: String
});

const schemaOptions = {
  toJSON: {
    transform: function(doc, ret, options) {
      // 移除敏感信息
      delete ret.password;
      
      // 转换_id为id
      ret.id = ret._id;
      delete ret._id;
      
      // 根据角色控制字段显示
      if (ret.role !== 'admin') {
        delete ret.role;
      }
      
      return ret;
    }
  }
};

// 使用示例
const user = new User({ 
  name: 'John', 
  email: '<EMAIL>', 
  password: 'secret123',
  role: 'user'
});

console.log(JSON.stringify(user));
// {"id":"...","name":"John","email":"<EMAIL>"}
```

#### 5. **minimize（最小化）**

```typescript
const userSchema = new Schema({
  name: String,
  profile: {
    bio: String,
    preferences: {
      theme: String,
      notifications: {}  // 空对象
    }
  }
});

// minimize: true (默认) - 移除空对象
const user = new User({ 
  name: 'John',
  profile: {
    bio: 'Developer',
    preferences: {
      theme: 'dark',
      notifications: {}
    }
  }
});

console.log(user.toObject()); 
// { name: 'John', profile: { bio: 'Developer', preferences: { theme: 'dark' } } }
// notifications: {} 被移除

// minimize: false - 保留空对象
const schemaOptions = {
  toObject: { minimize: false },
  toJSON: { minimize: false }
};

console.log(user.toObject()); 
// { name: 'John', profile: { bio: 'Developer', preferences: { theme: 'dark', notifications: {} } } }
// notifications: {} 被保留
```

## 🎯 实际应用场景

### 场景1：用户认证系统

```typescript
// apps/auth/src/domain/users/entities/user.entity.ts
import { Schema, Document } from 'mongoose';

export interface User {
  name: string;
  email: string;
  password: string;
  salt: string;
  role: 'user' | 'admin' | 'moderator';
  profile: {
    firstName?: string;
    lastName?: string;
    avatar?: string;
    bio?: string;
  };
  settings: {
    theme: 'light' | 'dark';
    language: string;
    notifications: {
      email: boolean;
      push: boolean;
    };
  };
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type UserDocument = User & Document;

export const UserSchema = new Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true, select: false },
  salt: { type: String, required: true, select: false },
  role: { 
    type: String, 
    enum: ['user', 'admin', 'moderator'], 
    default: 'user' 
  },
  profile: {
    firstName: String,
    lastName: String,
    avatar: String,
    bio: String
  },
  settings: {
    theme: { type: String, enum: ['light', 'dark'], default: 'light' },
    language: { type: String, default: 'zh-CN' },
    notifications: {
      email: { type: Boolean, default: true },
      push: { type: Boolean, default: true }
    }
  },
  lastLoginAt: Date,
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
}, {
  // toObject配置：内部使用
  toObject: {
    virtuals: true,
    getters: true,
    versionKey: false,
    transform: function(doc, ret) {
      // 移除敏感信息
      delete ret.password;
      delete ret.salt;
      return ret;
    }
  },
  
  // toJSON配置：API响应
  toJSON: {
    virtuals: true,
    getters: true,
    versionKey: false,
    transform: function(doc, ret) {
      // API响应格式化
      ret.id = ret._id;
      delete ret._id;
      
      // 移除敏感信息
      delete ret.password;
      delete ret.salt;
      
      // 格式化日期
      if (ret.createdAt) {
        ret.createdAt = ret.createdAt.toISOString();
      }
      if (ret.updatedAt) {
        ret.updatedAt = ret.updatedAt.toISOString();
      }
      if (ret.lastLoginAt) {
        ret.lastLoginAt = ret.lastLoginAt.toISOString();
      }
      
      return ret;
    }
  }
});

// 添加虚拟属性
UserSchema.virtual('fullName').get(function() {
  if (this.profile?.firstName && this.profile?.lastName) {
    return `${this.profile.firstName} ${this.profile.lastName}`;
  }
  return this.name;
});

UserSchema.virtual('isAdmin').get(function() {
  return this.role === 'admin';
});

// 添加索引
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ createdAt: -1 });
```

### 场景2：游戏角色系统

```typescript
// apps/character/src/common/schemas/character.schema.ts
import { Schema, Document } from 'mongoose';

export interface Character {
  characterId: string;
  name: string;
  level: number;
  experience: number;
  serverId: string;
  stats: {
    health: number;
    mana: number;
    attack: number;
    defense: number;
    speed: number;
  };
  inventory: {
    items: Array<{
      itemId: string;
      quantity: number;
    }>;
    capacity: number;
  };
  location: {
    mapId: string;
    x: number;
    y: number;
  };
  createdAt: Date;
  lastActiveAt: Date;
}

export type CharacterDocument = Character & Document;

export const CharacterSchema = new Schema({
  characterId: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  level: { type: Number, default: 1, min: 1, max: 100 },
  experience: { type: Number, default: 0, min: 0 },
  serverId: { type: String, required: true },
  stats: {
    health: { type: Number, default: 100 },
    mana: { type: Number, default: 50 },
    attack: { type: Number, default: 10 },
    defense: { type: Number, default: 5 },
    speed: { type: Number, default: 10 }
  },
  inventory: {
    items: [{
      itemId: String,
      quantity: { type: Number, min: 0 }
    }],
    capacity: { type: Number, default: 50 }
  },
  location: {
    mapId: String,
    x: { type: Number, default: 0 },
    y: { type: Number, default: 0 }
  },
  createdAt: { type: Date, default: Date.now },
  lastActiveAt: { type: Date, default: Date.now }
}, {
  // 游戏数据配置：注重性能和数据完整性
  toObject: {
    virtuals: true,
    getters: false,        // 游戏数据不需要格式化
    versionKey: false,
    minimize: false,       // 保留空对象（重要！）
    transform: function(doc, ret) {
      // 确保数组字段存在
      if (!ret.inventory.items) {
        ret.inventory.items = [];
      }
      return ret;
    }
  },
  
  toJSON: {
    virtuals: true,
    getters: false,
    versionKey: false,
    minimize: false,
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      
      // 确保数组字段存在
      if (!ret.inventory.items) {
        ret.inventory.items = [];
      }
      
      return ret;
    }
  }
});

// 游戏相关虚拟属性
CharacterSchema.virtual('totalPower').get(function() {
  const stats = this.stats;
  return stats.attack + stats.defense + stats.speed;
});

CharacterSchema.virtual('nextLevelExp').get(function() {
  return this.level * 1000; // 简单的升级经验公式
});

CharacterSchema.virtual('inventoryUsage').get(function() {
  const usedSlots = this.inventory.items.length;
  const totalSlots = this.inventory.capacity;
  return {
    used: usedSlots,
    total: totalSlots,
    percentage: Math.round((usedSlots / totalSlots) * 100)
  };
});

// 游戏索引
CharacterSchema.index({ characterId: 1 });
CharacterSchema.index({ serverId: 1, level: -1 });
CharacterSchema.index({ serverId: 1, lastActiveAt: -1 });
```

### 场景3：战斗系统

```typescript
// apps/match/src/common/schemas/battle.schema.ts
import { Schema, Document } from 'mongoose';

export interface BattleRoom {
  roomUid: string;
  battleType: 'pve' | 'pvp' | 'tournament';
  teamA: BattleTeamData;
  teamB: BattleTeamData;
  status: 'waiting' | 'active' | 'finished';
  result?: {
    winner: 'teamA' | 'teamB' | 'draw';
    scoreA: number;
    scoreB: number;
    duration: number;
  };
  metadata: {
    leagueId?: string;
    tournamentId?: string;
    difficulty?: number;
  };
  createdAt: Date;
  startedAt?: Date;
  finishedAt?: Date;
}

export interface BattleTeamData {
  playerId: string;
  teamName: string;
  formation: number;
  tactic: number;
  heroes: BattleHero[];
  morale: number;
}

export interface BattleHero {
  heroUid: string;
  position: number;
  attack: number;
  defend: number;
  speed: number;
  power: number;
  technique: number;
  level: number;
  skills: string[];
}

export type BattleRoomDocument = BattleRoom & Document;

export const BattleRoomSchema = new Schema({
  roomUid: { type: String, required: true, unique: true },
  battleType: { 
    type: String, 
    enum: ['pve', 'pvp', 'tournament'], 
    required: true 
  },
  teamA: {
    playerId: { type: String, required: true },
    teamName: String,
    formation: Number,
    tactic: Number,
    heroes: [{
      heroUid: String,
      position: Number,
      attack: Number,
      defend: Number,
      speed: Number,
      power: Number,
      technique: Number,
      level: Number,
      skills: [String]
    }],
    morale: { type: Number, default: 0 }
  },
  teamB: {
    playerId: { type: String, required: true },
    teamName: String,
    formation: Number,
    tactic: Number,
    heroes: [{
      heroUid: String,
      position: Number,
      attack: Number,
      defend: Number,
      speed: Number,
      power: Number,
      technique: Number,
      level: Number,
      skills: [String]
    }],
    morale: { type: Number, default: 0 }
  },
  status: { 
    type: String, 
    enum: ['waiting', 'active', 'finished'], 
    default: 'waiting' 
  },
  result: {
    winner: { type: String, enum: ['teamA', 'teamB', 'draw'] },
    scoreA: Number,
    scoreB: Number,
    duration: Number
  },
  metadata: {
    leagueId: String,
    tournamentId: String,
    difficulty: Number
  },
  createdAt: { type: Date, default: Date.now },
  startedAt: Date,
  finishedAt: Date
}, {
  // 战斗数据配置：优化性能，保持数据完整性
  toObject: {
    virtuals: false,       // 战斗数据不需要虚拟属性
    getters: false,        // 不需要格式化
    versionKey: false,
    minimize: false,       // 重要：保留空对象和数组
    transform: function(doc, ret) {
      // 确保关键数组字段存在
      if (ret.teamA && !ret.teamA.heroes) {
        ret.teamA.heroes = [];
      }
      if (ret.teamB && !ret.teamB.heroes) {
        ret.teamB.heroes = [];
      }
      
      // 确保技能数组存在
      if (ret.teamA && ret.teamA.heroes) {
        ret.teamA.heroes.forEach(hero => {
          if (!hero.skills) hero.skills = [];
        });
      }
      if (ret.teamB && ret.teamB.heroes) {
        ret.teamB.heroes.forEach(hero => {
          if (!hero.skills) hero.skills = [];
        });
      }
      
      return ret;
    }
  },
  
  toJSON: {
    virtuals: false,
    getters: false,
    versionKey: false,
    minimize: false,
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      
      // 应用相同的数组保护逻辑
      if (ret.teamA && !ret.teamA.heroes) {
        ret.teamA.heroes = [];
      }
      if (ret.teamB && !ret.teamB.heroes) {
        ret.teamB.heroes = [];
      }
      
      return ret;
    }
  }
});

// 战斗相关索引
BattleRoomSchema.index({ roomUid: 1 });
BattleRoomSchema.index({ status: 1, createdAt: -1 });
BattleRoomSchema.index({ battleType: 1, status: 1 });
BattleRoomSchema.index({ 'teamA.playerId': 1, status: 1 });
BattleRoomSchema.index({ 'teamB.playerId': 1, status: 1 });
BattleRoomSchema.index({ finishedAt: -1 }, { sparse: true });
```

## 🏭 Schema配置工厂模式

### 统一配置管理

```typescript
// libs/common/src/database/schema-options.factory.ts
export class SchemaOptionsFactory {
  /**
   * 用户相关模型配置
   * 特点：安全性优先，API友好
   */
  static forUserModel(options: UserModelOptions = {}) {
    return {
      toObject: {
        virtuals: options.includeVirtuals ?? true,
        getters: options.applyGetters ?? true,
        versionKey: false,
        transform: function(doc: any, ret: any) {
          // 移除敏感信息
          delete ret.password;
          delete ret.salt;
          delete ret.resetToken;
          
          // 应用自定义转换
          if (options.customTransform) {
            ret = options.customTransform(doc, ret);
          }
          
          return ret;
        }
      },
      
      toJSON: {
        virtuals: options.includeVirtuals ?? true,
        getters: options.applyGetters ?? true,
        versionKey: false,
        transform: function(doc: any, ret: any) {
          // API响应格式化
          ret.id = ret._id;
          delete ret._id;
          
          // 移除敏感信息
          delete ret.password;
          delete ret.salt;
          delete ret.resetToken;
          
          // 格式化日期
          ['createdAt', 'updatedAt', 'lastLoginAt'].forEach(field => {
            if (ret[field] instanceof Date) {
              ret[field] = ret[field].toISOString();
            }
          });
          
          // 应用自定义转换
          if (options.customTransform) {
            ret = options.customTransform(doc, ret);
          }
          
          return ret;
        }
      }
    };
  }

  /**
   * 游戏数据模型配置
   * 特点：性能优先，数据完整性
   */
  static forGameDataModel(options: GameDataModelOptions = {}) {
    return {
      toObject: {
        virtuals: options.includeVirtuals ?? false,
        getters: options.applyGetters ?? false,
        versionKey: false,
        minimize: false, // 保留空对象
        transform: function(doc: any, ret: any) {
          // 确保数组字段存在
          if (options.ensureArrays) {
            options.ensureArrays.forEach(field => {
              if (!ret[field]) ret[field] = [];
            });
          }
          
          // 应用自定义转换
          if (options.customTransform) {
            ret = options.customTransform(doc, ret);
          }
          
          return ret;
        }
      },
      
      toJSON: {
        virtuals: options.includeVirtuals ?? false,
        getters: options.applyGetters ?? false,
        versionKey: false,
        minimize: false,
        transform: function(doc: any, ret: any) {
          ret.id = ret._id;
          delete ret._id;
          
          // 确保数组字段存在
          if (options.ensureArrays) {
            options.ensureArrays.forEach(field => {
              if (!ret[field]) ret[field] = [];
            });
          }
          
          // 应用自定义转换
          if (options.customTransform) {
            ret = options.customTransform(doc, ret);
          }
          
          return ret;
        }
      }
    };
  }

  /**
   * 日志/审计模型配置
   * 特点：只读，最小化处理
   */
  static forLogModel() {
    return {
      toObject: {
        virtuals: false,
        getters: false,
        versionKey: false
      },
      
      toJSON: {
        virtuals: false,
        getters: false,
        versionKey: false,
        transform: function(doc: any, ret: any) {
          ret.id = ret._id;
          delete ret._id;
          
          // 格式化时间戳
          if (ret.timestamp instanceof Date) {
            ret.timestamp = ret.timestamp.toISOString();
          }
          
          return ret;
        }
      }
    };
  }
}

// 配置选项接口
interface UserModelOptions {
  includeVirtuals?: boolean;
  applyGetters?: boolean;
  customTransform?: (doc: any, ret: any) => any;
}

interface GameDataModelOptions {
  includeVirtuals?: boolean;
  applyGetters?: boolean;
  ensureArrays?: string[];
  customTransform?: (doc: any, ret: any) => any;
}
```

### 使用示例

```typescript
// 用户模型
const UserSchema = new Schema({
  name: String,
  email: String,
  password: String
}, SchemaOptionsFactory.forUserModel({
  customTransform: (doc, ret) => {
    // 根据用户角色控制字段显示
    if (ret.role !== 'admin') {
      delete ret.adminNotes;
    }
    return ret;
  }
}));

// 角色模型
const CharacterSchema = new Schema({
  name: String,
  level: Number,
  inventory: { items: [] }
}, SchemaOptionsFactory.forGameDataModel({
  ensureArrays: ['inventory.items', 'skills', 'achievements']
}));

// 日志模型
const LogSchema = new Schema({
  action: String,
  userId: String,
  timestamp: Date
}, SchemaOptionsFactory.forLogModel());
```

## 🚨 常见问题和解决方案

### 1. **嵌套对象的minimize问题**

```typescript
// 问题：嵌套空对象被移除
const schema = new Schema({
  profile: {
    preferences: {
      notifications: {}
    }
  }
});

// 解决方案：设置minimize: false
const schema = new Schema({...}, {
  toObject: { minimize: false },
  toJSON: { minimize: false }
});
```

### 2. **虚拟属性在lean查询中丢失**

```typescript
// 问题：lean查询不包含虚拟属性
const users = await User.find().lean(); // 虚拟属性丢失

// 解决方案1：使用mongoose-lean-virtuals插件
const mongooseLeanVirtuals = require('mongoose-lean-virtuals');
schema.plugin(mongooseLeanVirtuals);

const users = await User.find().lean({ virtuals: true });

// 解决方案2：在Schema配置中处理
const schema = new Schema({...}, {
  toJSON: {
    virtuals: true,
    transform: function(doc, ret) {
      // 手动添加虚拟属性逻辑
      ret.fullName = `${ret.firstName} ${ret.lastName}`;
      return ret;
    }
  }
});
```

### 3. **transform函数中的this上下文**

```typescript
// 注意：transform函数中的this不是文档对象
const schema = new Schema({...}, {
  toJSON: {
    transform: function(doc, ret, options) {
      // ✅ 使用doc参数访问文档
      console.log(doc.name);
      
      // ❌ 不要使用this
      // console.log(this.name); // undefined
      
      return ret;
    }
  }
});
```

## 📊 性能影响分析

### transform函数的性能考虑

```typescript
// ❌ 性能较差：复杂的transform逻辑
const schema = new Schema({...}, {
  toJSON: {
    transform: function(doc, ret) {
      // 避免复杂计算
      ret.complexCalculation = heavyComputation(ret);
      
      // 避免同步I/O操作
      ret.fileContent = fs.readFileSync(ret.filePath);
      
      return ret;
    }
  }
});

// ✅ 性能较好：简单的transform逻辑
const schema = new Schema({...}, {
  toJSON: {
    transform: function(doc, ret) {
      // 简单的字段操作
      ret.id = ret._id;
      delete ret._id;
      delete ret.password;
      
      // 简单的格式化
      if (ret.createdAt) {
        ret.createdAt = ret.createdAt.toISOString();
      }
      
      return ret;
    }
  }
});
```

## 🎯 最佳实践总结

1. **合理使用Schema配置** - 在Schema级别统一处理转换逻辑
2. **区分toObject和toJSON** - 根据使用场景选择合适的配置
3. **保持transform函数简单** - 避免复杂计算和I/O操作
4. **注意minimize设置** - 游戏数据通常需要设置为false
5. **使用工厂模式** - 统一管理不同类型模型的配置
6. **测试配置效果** - 确保配置符合预期行为

通过正确配置Schema选项，可以在保持代码简洁的同时，统一处理文档转换逻辑，提升开发效率和代码质量。
