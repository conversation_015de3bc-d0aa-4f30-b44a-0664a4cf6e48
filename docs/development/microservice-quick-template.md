# 微服务快速开发模板

## 🚀 快速创建新微服务

### 📋 开发原则提醒
- **通信方式**: 优先使用@MessagePattern，特定场景保留HTTP（认证、监控、文件上传、第三方集成）
- **缓存策略**: 优先使用缓存装饰器，复杂场景使用手动缓存管理
- **数据访问**: 必须使用Repository模式

### 1. 创建基础目录结构
```bash
# 使用NestJS CLI创建新微服务
nest generate app {service-name}

# 创建标准目录结构
mkdir -p apps/{service-name}/src/{entity}/schemas
mkdir -p apps/{service-name}/src/{entity}/repositories
mkdir -p apps/{service-name}/src/{entity}/dto
mkdir -p apps/{service-name}/src/health
mkdir -p apps/{service-name}/src/{feature}/schemas
mkdir -p apps/{service-name}/src/{feature}/repositories
mkdir -p apps/{service-name}/src/{feature}/dto
```

### 2. 复制模板文件

#### main.ts 启动文件模板
```typescript
import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { {ServiceName}Module } from './{service-name}.module';

async function bootstrap() {
  const app = await NestFactory.create({ServiceName}Module);
  const logger = new Logger('{ServiceName}Service');
  
  const port = process.env.{SERVICE_NAME}_PORT || {port};
  const environment = process.env.NODE_ENV || 'development';
  
  await app.listen(port);
  
  // 启动日志输出
  logger.log(`🚀 {服务中文名}已启动`);
  logger.log(`📍 HTTP端口: ${port}`);
  logger.log(`🔗 微服务: Redis传输层`);
  logger.log(`🌍 环境: ${environment}`);
  logger.log(`🔗 健康检查: http://localhost:${port}/health`);

  if (environment !== 'production') {
    logger.log(`📚 API文档: http://localhost:${port}/docs`);
  }
}
bootstrap();
```

#### App Module配置模板
```typescript
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 功能模块
import { HealthModule } from './health/health.module';
import { {Entity}Module } from './{entity}/{entity}.module';

// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@app/database/mongodb.config';
import { RedisModule } from '@common/redis';

@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, '{service-name}'),
        ...setupDatabaseEvents('{service-name}'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块
    RedisModule.forRootAsync({
      service: '{service-name}',
      serverId: 'server_001',
    }),

    // 功能模块
    HealthModule,
    {Entity}Module,
  ],
})
export class AppModule {}
```

#### Entity Module模板
```typescript
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// {Entity}相关组件
import { {Entity}Controller } from './{entity}.controller';
import { {Entity}Service } from './{entity}.service';
import { {Entity}, {Entity}Schema } from './schemas/{entity}.schema';
import { {Entity}Repository } from './repositories/{entity}.repository';

@Module({
  imports: [
    // 注册{Entity} Schema
    MongooseModule.forFeature([
      { name: {Entity}.name, schema: {Entity}Schema },
    ]),
  ],

  controllers: [{Entity}Controller],

  providers: [
    {Entity}Service,
    {Entity}Repository,
  ],

  exports: [
    {Entity}Service,
    {Entity}Repository,
  ],
})
export class {Entity}Module {}
```

#### Health Module模板
```typescript
import { Module } from '@nestjs/common';

// Health相关组件
import { HealthController } from './health.controller';
import { HealthService } from './health.service';

@Module({
  controllers: [HealthController],

  providers: [HealthService],

  exports: [HealthService],
})
export class HealthModule {}
```

### 3. 环境变量配置

#### 添加到 .env 文件
```bash
# {服务中文名}服务配置
{SERVICE_NAME}_PORT={port}
{SERVICE_NAME}_MONGODB_URI=mongodb://{service-name}-admin:Dslcfjz2019@***************:27017/{service_name}_db
```

#### 添加到 package.json scripts
```json
{
  "build:{service-name}": "nest build {service-name}",
  "start:{service-name}": "nest start {service-name} --watch",
  "start:{service-name}:dev": "cross-env NODE_ENV=development nest start {service-name} --watch",
  "start:{service-name}:debug": "nest start {service-name} --debug --watch",
  "start:{service-name}:prod": "cross-env NODE_ENV=production nest start {service-name} --watch"
}
```

### 4. 数据库用户创建

#### 添加到 scripts/create-microservice-users.js
```javascript
// {服务中文名}服务数据库用户
var {serviceName}DB = db.getSiblingDB('{service_name}_db');
if (!{serviceName}DB.getUser("{service-name}-admin")) {
  {serviceName}DB.createUser({
    user: "{service-name}-admin",
    pwd: "Dslcfjz2019",
    roles: [
      { role: "dbOwner", db: "{service_name}_db" }
    ]
  });
  print("✅ {服务中文名}服务数据库用户创建成功: {service-name}-admin");
} else {
  print("ℹ️  {服务中文名}服务数据库用户已存在: {service-name}-admin");
}
```

### 5. 健康检查文件

#### 复制健康检查文件
```bash
# 复制健康检查模板
cp -r apps/character/src/health apps/{service-name}/src/

# 更新服务名称
sed -i 's/角色服务/{服务中文名}/g' apps/{service-name}/src/health/health.controller.ts
sed -i 's/角色服务/{服务中文名}/g' apps/{service-name}/src/health/health.service.ts
sed -i 's/character-service/{service-name}-service/g' apps/{service-name}/src/health/health.service.ts
```

#### 标准目录结构示例
```
apps/{service-name}/
├── src/
│   ├── {entity}/                           # 主要业务实体
│   │   ├── schemas/
│   │   │   └── {entity}.schema.ts
│   │   ├── repositories/
│   │   │   └── {entity}.repository.ts
│   │   ├── dto/
│   │   │   └── {entity}.dto.ts
│   │   ├── {entity}.controller.ts
│   │   ├── {entity}.service.ts
│   │   ├── {entity}.controller.spec.ts
│   │   └── {entity}.module.ts
│   ├── {feature}/                          # 其他功能模块
│   │   ├── schemas/
│   │   ├── repositories/
│   │   ├── dto/
│   │   ├── {feature}.controller.ts
│   │   ├── {feature}.service.ts
│   │   └── {feature}.module.ts
│   ├── health/
│   │   ├── health.controller.ts
│   │   ├── health.service.ts
│   │   └── health.module.ts
│   ├── app.module.ts                       # 应用主模块
│   └── main.ts                             # 启动入口
└── test/
```

## 📋 开发步骤检查清单

### Phase 1: 基础搭建
- [ ] 创建微服务应用和目录结构
- [ ] 配置main.ts启动文件
- [ ] 创建app.module.ts主模块
- [ ] 创建health.module.ts健康检查模块
- [ ] 添加环境变量配置
- [ ] 创建数据库用户
- [ ] 复制健康检查文件

### Phase 2: 实体设计
- [ ] 分析旧项目对应实体结构
- [ ] 设计新的Schema
- [ ] 创建Repository
- [ ] 定义DTO
- [ ] 创建实体Module

### Phase 3: 业务实现
- [ ] 实现Service业务逻辑
- [ ] 实现Controller（@MessagePattern + 缓存装饰器）
- [ ] 在实体Module中注册所有组件
- [ ] 在app.module.ts中导入功能模块
- [ ] 添加错误处理和日志

### Phase 4: 模块化验证
- [ ] 确保每个业务域有独立的Module
- [ ] 验证app.module.ts只导入功能模块
- [ ] 检查模块间的依赖关系
- [ ] 验证导入导出的正确性

### Phase 5: 测试验证
- [ ] 构建测试：`npm run build:{service-name}`
- [ ] 启动测试：`npm run start:{service-name}:dev`
- [ ] 健康检查：`http://localhost:{port}/health`
- [ ] 功能测试

## 🔧 常用命令

```bash
# 构建服务
npm run build:{service-name}

# 启动开发模式
npm run start:{service-name}:dev

# 启动调试模式
npm run start:{service-name}:debug

# 健康检查
curl http://localhost:{port}/health

# 查看日志
npm run start:{service-name}:dev | grep "{ServiceName}Service"
```

## 📝 命名规范速查

| 类型 | 规范 | 示例 |
|------|------|------|
| 服务名 | kebab-case | hero, formation, inventory |
| 类名 | PascalCase | HeroService, FormationController |
| 文件名 | kebab-case | hero.service.ts, formation.controller.ts |
| 模块名 | PascalCase + Module | HeroModule, FormationModule |
| 静态配置Schema | kebab-case + definition | skill-definition.schema.ts |
| 动态实例Schema | kebab-case | skill.schema.ts, hero.schema.ts |
| 数据库 | snake_case | hero_db, formation_db |
| 环境变量 | UPPER_SNAKE_CASE | HERO_PORT, FORMATION_MONGODB_URI |
| 消息模式 | dot.notation | hero.create, formation.update |
| 缓存键 | colon:separated | hero:info:#{heroId} |

## 🏗️ 模块化最佳实践

### ✅ 推荐做法
- **功能内聚**: 相关的controller、service、repository放在同一目录
- **模块独立**: 每个业务域有自己的module.ts文件
- **清晰边界**: app.module.ts只导入功能模块，不导入具体组件
- **统一导出**: 通过模块接口暴露服务给其他模块使用

### ❌ 避免做法
- 在app.module.ts中直接导入所有controller、service、repository
- 将不相关的功能混合在一个模块中
- 跨模块直接访问内部组件，应通过模块导出的接口

## ⚡ 快速替换脚本

创建新微服务时，使用以下替换规则：

```bash
# 替换占位符
{service-name} → 实际服务名（如：hero）
{ServiceName} → 首字母大写（如：Hero）  
{SERVICE_NAME} → 全大写（如：HERO）
{entity} → 实体名（如：hero）
{Entity} → 实体首字母大写（如：Hero）
{服务中文名} → 中文名称（如：球员服务）
{port} → 端口号（如：3007）
{service_name} → 下划线格式（如：hero）
```

---

**使用此模板可以在30分钟内搭建一个符合规范的新微服务基础架构。**
