# GameConfigFacade 增强功能使用示例

## 🎯 新增筛选方法概览

基于方案1的实施，GameConfigFacade现在支持以下新的筛选方法：

- `filter(predicate)` - 自定义条件筛选
- `findBy(field, value)` - 字段值筛选  
- `findOne(predicate)` - 查找第一个匹配项
- `findOneBy(field, value)` - 字段值查找第一个匹配项

## 📚 详细使用示例

### 1. filter() - 自定义条件筛选

```typescript
// ✅ 优化前：需要加载全量数据再筛选
const teamConfigs = await this.gameConfig.team.getAll();
const filtered = teamConfigs.filter(config => config.teamId === teamId);

// ✅ 优化后：使用filter方法，内部优化缓存机制
const highLevelHeroes = await this.gameConfig.hero.filter(
  hero => hero.level > 10 && hero.position === 'striker'
);

// 复杂筛选条件示例
const premiumItems = await this.gameConfig.item.filter(
  item => item.rarity >= 4 && item.price <= 1000 && item.type === 'weapon'
);

// 筛选特定等级范围的技能
const midLevelSkills = await this.gameConfig.skill.filter(
  skill => skill.level >= 5 && skill.level <= 15 && skill.type === 'active'
);
```

### 2. findBy() - 字段值筛选（最常用）

```typescript
// ✅ 获取指定队伍的所有球员配置（Tournament服务实际使用）
const teamHeroConfigs = await this.gameConfig.team.findBy('teamId', 90101);

// 获取指定位置的所有英雄
const goalkeepers = await this.gameConfig.hero.findBy('position', 'GK');
const strikers = await this.gameConfig.hero.findBy('position', 'ST');

// 获取指定等级的所有物品
const level5Items = await this.gameConfig.item.findBy('level', 5);

// 获取指定类型的所有商店配置
const weaponShops = await this.gameConfig.shop.findBy('category', 'weapon');

// 获取指定难度的所有任务
const easyTasks = await this.gameConfig.task.findBy('difficulty', 'easy');
```

### 3. findOne() - 查找第一个匹配项

```typescript
// 查找第一个满足条件的英雄
const firstHighLevelMage = await this.gameConfig.hero.findOne(
  hero => hero.type === 'mage' && hero.level > 20
);

// 查找第一个可用的免费商品
const firstFreeItem = await this.gameConfig.item.findOne(
  item => item.price === 0 && item.available === true
);

// 查找第一个满足条件的技能
const firstUnlockedSkill = await this.gameConfig.skill.findOne(
  skill => skill.unlockLevel <= playerLevel && !skill.isLocked
);
```

### 4. findOneBy() - 字段值查找第一个匹配项

```typescript
// 查找指定名称的英雄（等同于按名称搜索的第一个结果）
const messi = await this.gameConfig.hero.findOneBy('name', '梅西');

// 查找指定ID的配置（等同于get方法，但更灵活）
const hero = await this.gameConfig.hero.findOneBy('heroId', 90001);

// 查找指定类型的第一个商店
const firstWeaponShop = await this.gameConfig.shop.findOneBy('type', 'weapon');

// 查找指定等级的第一个任务
const firstLevel1Task = await this.gameConfig.task.findOneBy('level', 1);
```

## 🚀 实际业务场景应用

### 场景1：Tournament服务 - 获取AI对手队伍配置

```typescript
// apps/match/src/modules/tournament/tournament.service.ts
private async getTeamHeroConfigs(teamId: number): Promise<TeamDefinition[]> {
  try {
    // 🔥 优化：直接根据teamId筛选，避免加载全量数据
    const teamHeroConfigs = await this.gameConfig.team.findBy('teamId', teamId);
    
    if (!teamHeroConfigs || teamHeroConfigs.length === 0) {
      this.logger.warn(`未找到队伍球员配置: teamId=${teamId}`);
      return [];
    }

    this.logger.debug(`成功获取队伍${teamId}的球员配置，共${teamHeroConfigs.length}个球员`);
    return teamHeroConfigs;
  } catch (error) {
    this.logger.error('获取队伍球员配置失败', error);
    return [];
  }
}
```

### 场景2：Hero服务 - 获取指定位置的英雄池

```typescript
// apps/hero/src/modules/hero/hero.service.ts
async getHeroesByPosition(position: string): Promise<HeroDefinition[]> {
  try {
    // 获取指定位置的所有英雄配置
    const heroes = await this.gameConfig.hero.findBy('position', position);
    
    // 进一步筛选可招募的英雄
    const recruitableHeroes = await this.gameConfig.hero.filter(
      hero => hero.position === position && hero.recruitable === true
    );
    
    return recruitableHeroes;
  } catch (error) {
    this.logger.error('获取位置英雄失败', error);
    return [];
  }
}
```

### 场景3：Shop服务 - 获取商店商品

```typescript
// apps/economy/src/modules/shop/shop.service.ts
async getShopItemsByCategory(category: string): Promise<ShopDefinition[]> {
  try {
    // 获取指定分类的商品
    const items = await this.gameConfig.shop.findBy('category', category);
    
    // 筛选当前可购买的商品
    const availableItems = await this.gameConfig.shop.filter(
      item => item.category === category && 
              item.available === true && 
              item.stock > 0
    );
    
    return availableItems;
  } catch (error) {
    this.logger.error('获取商店商品失败', error);
    return [];
  }
}
```

### 场景4：Task服务 - 获取玩家可接受的任务

```typescript
// apps/activity/src/modules/task/task.service.ts
async getAvailableTasksForPlayer(playerLevel: number): Promise<TaskDefinition[]> {
  try {
    // 获取玩家等级可接受的任务
    const availableTasks = await this.gameConfig.task.filter(
      task => task.minLevel <= playerLevel && 
              task.maxLevel >= playerLevel &&
              task.isActive === true
    );
    
    // 查找第一个推荐任务
    const recommendedTask = await this.gameConfig.task.findOne(
      task => task.minLevel <= playerLevel && 
              task.recommended === true
    );
    
    return availableTasks;
  } catch (error) {
    this.logger.error('获取可用任务失败', error);
    return [];
  }
}
```

## ⚡ 性能对比

### 优化前（低效）
```typescript
// ❌ 每次都加载全量数据（51043条记录）
const teamConfigs = await this.gameConfig.team.getAll();
const filtered = teamConfigs.filter(config => config.teamId === teamId);
```

### 优化后（高效）
```typescript
// ✅ 直接筛选，利用缓存机制
const teamHeroConfigs = await this.gameConfig.team.findBy('teamId', teamId);
```

**性能提升**：
- 减少内存使用：只返回需要的数据
- 提高响应速度：避免不必要的数据传输
- 更好的缓存利用：ConfigManager内部优化缓存策略

## 🔧 类型安全保证

所有新方法都提供完整的TypeScript类型支持：

```typescript
// ✅ 编译时类型检查
const heroes = await this.gameConfig.hero.findBy('position', 'GK'); // 正确
const heroes = await this.gameConfig.hero.findBy('invalidField', 'GK'); // 编译错误

// ✅ 智能提示
const hero = await this.gameConfig.hero.findOneBy('name', '梅西');
if (hero) {
  console.log(hero.level); // 自动提示hero的所有属性
}
```

## 📊 使用建议

### 1. 方法选择指南

- **单个查找**: 使用 `findOneBy()` 或 `get()`
- **字段筛选**: 使用 `findBy()` （最常用）
- **复杂条件**: 使用 `filter()`
- **存在性检查**: 使用 `findOne()`

### 2. 性能最佳实践

- 优先使用 `findBy()` 进行简单字段筛选
- 复杂条件时使用 `filter()`，但避免过于复杂的逻辑
- 合理利用 `findOne()` 避免不必要的全量查询

### 3. 错误处理

```typescript
try {
  const configs = await this.gameConfig.team.findBy('teamId', teamId);
  if (configs.length === 0) {
    this.logger.warn(`未找到配置: teamId=${teamId}`);
    return defaultValue;
  }
  return configs;
} catch (error) {
  this.logger.error('配置查询失败', error);
  throw new ConfigError(`配置查询失败: ${error.message}`);
}
```

## 🎉 总结

通过扩展 ConfigTableAccessor 接口，我们实现了：

1. **✅ 性能优化**: 避免全量数据加载和内存筛选
2. **✅ 易用性提升**: 提供直观的筛选API
3. **✅ 类型安全**: 完整的TypeScript类型支持
4. **✅ 向后兼容**: 不影响现有代码
5. **✅ 自动生成**: 通过工具自动生成，保持一致性

这些新方法将显著提升配置查询的开发体验和运行性能，特别是对于像Team配置这样的大型数据表。
