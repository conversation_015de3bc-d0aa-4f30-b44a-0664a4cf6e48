# Character服务函数命名优化总结

## 优化原则

在严格迁移old项目核心功能的基础上，我们对函数命名进行了优化，使其更加优雅、科学和符合现代开发规范：

1. **避免下划线开头的函数名** - 将`_innerXxx`改为`xxxInternal`
2. **使用更语义化的命名** - 如`newTeamFormation`改为`createFormation`
3. **统一参数命名规范** - 如`uid`改为`formationId`、`heroUid`改为`playerId`
4. **优化动词选择** - 如`delItem`改为`removeItem`
5. **使用更准确的术语** - 如`num`改为`quantity`、`resId`改为`configId`

## Formation模块命名优化

### Service方法优化

| Old项目方法 | 原实现命名 | 优化后命名 | 优化说明 |
|------------|------------|------------|----------|
| `newTeamFormation` | `newTeamFormation` | `createFormation` | 更简洁的创建语义 |
| `addHeroInTeam` | `addHeroInTeam` | `addPlayerToPosition` | 更明确的位置概念 |
| `deleteHeroFromTeam` | `deleteHeroFromTeam` | `removePlayerFromPosition` | 更准确的移除语义 |
| `setCurrTeamFormationId` | `setCurrTeamFormationId` | `setActiveFormation` | 更简洁的激活概念 |
| `setLeagueTeamFormationId` | `setLeagueTeamFormationId` | `setLeagueFormation` | 简化命名 |
| `setWarOfFaithTeamFormationId` | `setWarOfFaithTeamFormationId` | `setWarOfFaithFormation` | 简化命名 |
| `copyTeamFormation` | `copyTeamFormation` | `copyFormation` | 简化命名 |
| `_InnercheckHeroIsSameResID` | `checkHeroIsSameResID` | `checkPlayerExists` | 去除下划线，更清晰语义 |

### API接口优化

| 原API | 优化后API | 优化说明 |
|-------|-----------|----------|
| `character.formation.newTeamFormation` | `character.formation.createFormation` | 统一创建语义 |
| `character.formation.addHeroInTeam` | `character.formation.addPlayerToPosition` | 明确位置操作 |
| `character.formation.deleteHeroFromTeam` | `character.formation.removePlayerFromPosition` | 准确的移除语义 |
| `character.formation.setCurrTeamFormationId` | `character.formation.setActiveFormation` | 简化激活概念 |
| `character.formation.copyTeamFormation` | `character.formation.copyFormation` | 简化命名 |

### 参数命名优化

| 原参数名 | 优化后参数名 | 优化说明 |
|----------|-------------|----------|
| `uid` | `formationId` | 更明确的标识符 |
| `heroUid` | `playerId` | 统一球员标识 |
| `srcUid` | `sourceFormationId` | 更清晰的源标识 |

## Item模块命名优化

### Service方法优化

| Old项目方法 | 原实现命名 | 优化后命名 | 优化说明 |
|------------|------------|------------|----------|
| `newItem` | `newItem` | `createItemInstance` | 更准确的实例创建语义 |
| `delItem` | `delItem` | `removeItem` | 更准确的移除语义 |
| `getItemNum` | `getItemNum` | `getItemQuantity` | 更准确的数量概念 |
| `getItemNumByResID` | `getItemNumByResID` | `getItemQuantityByConfigId` | 更清晰的配置ID概念 |
| `checkItemIsEnough` | `checkItemIsEnough` | `checkItemSufficient` | 更简洁的充足性检查 |
| `_innerAddItem` | `_innerAddItem` | `addItemInternal` | 去除下划线前缀 |
| `_InnerBatchAddItem` | `_InnerBatchAddItem` | `batchAddItems` | 去除下划线，更清晰语义 |
| `_innerDelItem` | `_innerDelItem` | `removeItemInternal` | 去除下划线，统一移除语义 |
| `_executeItemEffects` | `_executeItemEffects` | `executeItemEffects` | 去除下划线前缀 |

### API接口优化

| 原API | 优化后API | 优化说明 |
|-------|-----------|----------|
| `character.item.delItem` | `character.item.removeItem` | 统一移除语义 |
| `character.item.getItemNum` | `character.item.getItemQuantity` | 更准确的数量概念 |
| `character.item.getItemNumByResID` | `character.item.getItemQuantityByConfigId` | 更清晰的配置ID概念 |
| `character.item.checkItemIsEnough` | `character.item.checkItemSufficient` | 更简洁的充足性检查 |
| `character.item.newItem` | `character.item.createItemInstance` | 更准确的实例创建语义 |

### 参数命名优化

| 原参数名 | 优化后参数名 | 优化说明 |
|----------|-------------|----------|
| `uid` | `itemId` | 更明确的物品标识符 |
| `num` | `quantity` | 更准确的数量概念 |
| `resId` | `configId` | 更清晰的配置ID概念 |
| `needNum` | `requiredQuantity` | 更明确的需求数量 |

## 命名规范总结

### 1. 函数命名规范

- **创建操作**: 使用`create`而不是`new`
- **删除操作**: 使用`remove`而不是`del`或`delete`
- **获取操作**: 使用`get`保持一致
- **设置操作**: 使用`set`保持一致
- **检查操作**: 使用`check`保持一致

### 2. 参数命名规范

- **ID标识符**: 使用具体的业务含义，如`formationId`、`playerId`、`itemId`
- **数量概念**: 统一使用`quantity`而不是`num`
- **配置ID**: 使用`configId`而不是`resId`
- **需求数量**: 使用`requiredQuantity`而不是`needNum`

### 3. 私有方法命名规范

- **内部方法**: 使用`xxxInternal`后缀而不是`_innerXxx`前缀
- **批量操作**: 使用`batchXxx`前缀
- **执行操作**: 使用`executeXxx`前缀

### 4. API命名规范

- **保持RESTful风格**: 动词+名词的组合
- **避免冗余**: 去除不必要的重复词汇
- **语义清晰**: 确保API名称能够清楚表达功能

## 优化效果

通过这次命名优化，我们实现了：

1. **✅ 代码可读性提升** - 函数名更加语义化和直观
2. **✅ 维护性增强** - 统一的命名规范便于团队协作
3. **✅ 现代化标准** - 符合现代JavaScript/TypeScript开发规范
4. **✅ 业务语义清晰** - 函数名准确反映业务操作
5. **✅ 参数语义明确** - 参数名清楚表达其用途和含义

## 注意事项

1. **保持功能一致性** - 命名优化不改变任何业务逻辑
2. **向后兼容考虑** - 在实际部署时需要考虑API版本管理
3. **文档同步更新** - 所有相关文档需要同步更新新的命名
4. **测试用例更新** - 相关测试用例需要更新以匹配新的命名

这次命名优化在保持old项目功能完整性的基础上，显著提升了代码的专业性和可维护性。
