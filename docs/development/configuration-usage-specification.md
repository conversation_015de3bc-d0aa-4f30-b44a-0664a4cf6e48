# 配置表使用规范

## 概述

本文档详细规定了在Hero微服务中如何正确使用game-config配置表，包括可用配置列表、字段结构和使用示例。

## 可用配置表清单

基于`libs/game-config/src/facades/game-config.facade.ts`的实际内容：

### 1. Hero相关配置
```typescript
// ✅ 可用配置
this.gameConfig.hero              // HeroDefinition - 球员基础配置
this.gameConfig.heroSkill         // HeroSkillDefinition - 球员技能配置
this.gameConfig.heroTrain         // HeroTrainDefinition - 球员训练配置
this.gameConfig.heroPool          // HeroPoolDefinition - 球员池配置
```

### 2. 训练相关配置
```typescript
// ✅ 可用配置
this.gameConfig.feildTrainning    // FeildTrainningDefinition - 场地训练配置
this.gameConfig.trainningCoach    // TrainningCoachDefinition - 训练教练配置
```

### 3. 其他配置
```typescript
// ✅ 可用配置
this.gameConfig.item              // ItemDefinition - 道具配置
this.gameConfig.systemParam       // SystemParamDefinition - 系统参数配置
```

### 4. 禁用配置（不存在）
```typescript
// ❌ 禁止使用（不存在）
this.gameConfig.scoutScope        // 不存在
this.gameConfig.footballerPool    // 不存在
this.gameConfig.trainingGround     // 不存在
this.gameConfig.scoutReward        // 不存在
```

## 配置字段结构

### HeroDefinition
```typescript
interface HeroDefinition {
  id: number;
  name: string;
  position1: string;        // ✅ 注意：是position1，不是position
  position2?: string;
  star: number;
  // ... 其他字段
}

// ✅ 正确使用
const heroConfig = await this.gameConfig.hero.get(heroId);
const position = heroConfig.position1;  // 不是position

// ❌ 错误使用
const position = heroConfig.position;   // 字段不存在
```

### HeroSkillDefinition
```typescript
interface HeroSkillDefinition {
  id: number;
  skillName: string;        // ✅ 注意：是skillName，不是name
  skillRank: string;        // ✅ 注意：是字符串类型
  typeA: number;
  starValue: number;
  // ... 其他字段
}

// ✅ 正确使用
const skillConfig = await this.gameConfig.heroSkill.get(skillId);
const name = skillConfig.skillName;     // 不是name
const rank = skillConfig.skillRank;     // 字符串类型

// ❌ 错误使用
const name = skillConfig.name;          // 字段不存在
const rank = skillConfig.rarity;        // 字段不存在
```

### HeroPoolDefinition
```typescript
interface HeroPoolDefinition {
  id: number;
  resId: number;           // 对应的球员配置ID
  pool: number;            // 池子编号
  weight: number;          // 权重
}

// ✅ 正确使用
const poolHeroes = await this.gameConfig.heroPool.getAll();
const filtered = poolHeroes.filter(h => h.pool === 1);

// ❌ 错误使用
const filtered = poolHeroes.filter(h => h.quality === 1);  // 字段不存在
```

### FeildTrainningDefinition
```typescript
interface FeildTrainningDefinition {
  id: number;
  type: number;
  playerType: number;
  // ... 其他字段
}

// ✅ 正确使用
const trainingConfigs = await this.gameConfig.feildTrainning.getAll();

// ❌ 错误使用
const trainingConfigs = await this.gameConfig.trainingGround.getAll();  // 配置不存在
```

## 使用模式

### 1. 单个配置获取
```typescript
// ✅ 正确模式
async getHeroConfig(heroId: number) {
  const config = await this.gameConfig.hero.get(heroId);
  if (!config) {
    return { code: -1, message: '球员配置不存在' };
  }
  return { code: 0, data: config };
}
```

### 2. 批量配置获取
```typescript
// ✅ 正确模式
async getAllHeroSkills() {
  const skills = await this.gameConfig.heroSkill.getAll();
  return skills.map(skill => ({
    id: skill.id,
    name: skill.skillName,      // 使用正确字段名
    rank: skill.skillRank,      // 使用正确字段名
    type: skill.typeA,          // 使用正确字段名
  }));
}
```

### 3. 条件筛选
```typescript
// ✅ 正确模式
async getHeroesByPool(poolId: number) {
  const poolHeroes = await this.gameConfig.heroPool.getAll();
  const targetHeroes = poolHeroes.filter(h => h.pool === poolId);
  
  const heroConfigs = [];
  for (const poolHero of targetHeroes) {
    const config = await this.gameConfig.hero.get(poolHero.resId);
    if (config) {
      heroConfigs.push(config);
    }
  }
  
  return heroConfigs;
}
```

## 错误处理规范

### 1. 配置不存在
```typescript
// ✅ 正确处理
const config = await this.gameConfig.hero.get(heroId);
if (!config) {
  return { code: -2, message: '球员配置不存在' };
}

// ❌ 错误处理
const config = await this.gameConfig.hero.get(heroId);
const name = config.name;  // 可能报错：Cannot read property 'name' of null
```

### 2. 字段访问
```typescript
// ✅ 正确访问
const position = config.position1 || 'Unknown';
const skillName = skillConfig.skillName || '未知技能';

// ❌ 错误访问
const position = config.position;      // 字段不存在
const skillName = skillConfig.name;    // 字段不存在
```

## 常见陷阱

### 1. 字段名假设
```typescript
// ❌ 常见错误：假设字段名
hero.configId     // 实际是 hero.resId
hero.position     // 实际是 heroConfig.position1
skill.name        // 实际是 skill.skillName
skill.rarity      // 实际是 skill.skillRank
```

### 2. 类型假设
```typescript
// ❌ 常见错误：假设类型
skill.skillRank === 1        // 实际是字符串 "D", "C", "B", "A", "S"
hero.quality === "legendary" // 实际是数字 1, 2, 3, 4, 5, 6
```

### 3. 配置表假设
```typescript
// ❌ 常见错误：假设配置表存在
this.gameConfig.scoutScope        // 不存在
this.gameConfig.footballerPool    // 不存在
this.gameConfig.heroFoster        // 不存在
```

## 验证清单

在使用任何配置前，请检查：

- [ ] 配置表在GameConfigFacade中确实存在
- [ ] 查看了对应的interface文件确认字段结构
- [ ] 使用了正确的字段名（不是猜测的）
- [ ] 处理了配置不存在的情况
- [ ] 使用了正确的数据类型
- [ ] 编译通过且无类型错误

## 调试技巧

### 1. 查看可用配置
```typescript
// 在开发时可以打印所有可用配置
console.log('Available configs:', Object.keys(this.gameConfig));
```

### 2. 查看配置结构
```typescript
// 查看具体配置的字段结构
const config = await this.gameConfig.hero.get(1);
console.log('Hero config structure:', Object.keys(config || {}));
```

### 3. 验证字段存在
```typescript
// 验证字段是否存在
if ('position1' in config) {
  // 字段存在
} else {
  console.warn('position1 field not found in config');
}
```

---
*文档版本：v1.0*  
*创建日期：2025-07-14*  
*最后更新：2025-07-14*
