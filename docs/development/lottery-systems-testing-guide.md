# 抽奖系统测试开发指南

## 📋 概述

本文档基于Match服务的成功测试模式，为抽奖系统（Hero、Economy、Activity服务）提供完整的测试开发指南。所有测试脚本都遵循统一的架构模式和最佳实践。

## 🎯 核心测试架构

### 1. 测试脚本目录结构

```
apps/{service}/scripts/
├── test-{service}-system.js           # 入口脚本（继承MicroserviceWebSocketClient）
├── test-{module}-module.js             # 模块测试脚本
├── quick-test-setup.js                 # 快速测试数据准备脚本
├── package.json                        # 测试脚本依赖管理
└── {service}-testing-guide.md          # 服务特定测试指南
```

### 2. 入口脚本架构模式

基于Match服务的成功模式，所有入口脚本都：

- **继承MicroserviceWebSocketClient**：使用统一的WebSocket客户端基类
- **支持模块选择**：可以运行特定模块或全部模块
- **命令行参数解析**：支持灵活的测试参数
- **健康检查**：测试前验证所有依赖服务
- **错误处理**：优雅的错误处理和测试结果汇总
- **微服务调用**：所有的微服务调用包括服务间调用和测试脚本通过网关调用遵循文档要求[microservice-communication-guide.md](../microservice-communication-guide.md)

**使用示例：**
```bash
# 运行所有测试
node test-hero-system.js

# 运行特定模块
node test-hero-system.js scout

# 运行多个模块
node test-hero-system.js scout,cultivation

# 使用现有角色测试
node test-hero-system.js scout char_1752805335899_wm3tgjcxp
```

### 3. 模块测试脚本架构

每个模块测试脚本都遵循统一模式：

```javascript
class ModuleTester {
  constructor(socket, testData) {
    this.socket = socket;           // WebSocket连接
    this.testData = testData;       // 测试数据（token、characterId等）
    this.testResults = [];          // 测试结果收集
  }

  // WebSocket调用封装 - 使用match服务的成功模式
  async callWebSocket(command, data = {}) { ... }

  // 具体测试方法
  async testSpecificFunction() { ... }

  // 运行所有测试
  async runTests() { ... }
}
```

## 🚀 抽奖系统测试覆盖

### Hero服务测试（球探系统）

**测试模块：** `apps/hero/scripts/`

**核心测试功能：**
- ✅ 球探数据获取（getScoutData）
- ✅ 球探探索功能（getScoutReward）
- ✅ 球探搜索功能（scoutSearch）
- ✅ 球探签约功能（signScoutHero）
- ✅ 球探体力购买（buyScoutEnergy）
- ✅ 球探RP值兑换（exchangeScout）

**运行命令：**
```bash
# 运行球探系统测试
node apps/hero/scripts/test-hero-system.js scout

# 准备测试数据
node apps/hero/scripts/quick-test-setup.js
```

### Economy服务测试（传统抽奖系统）

**测试模块：** `apps/economy/scripts/`

**核心测试功能：**
- ✅ 抽奖配置获取（getLotteryConfig）
- ✅ 金币单抽测试（lotteryHero type=1, times=1）
- ✅ 金币十连抽测试（lotteryHero type=1, times=10）
- ✅ 代币单抽测试（lotteryHero type=2, times=1）
- ✅ 代币十连抽测试（lotteryHero type=2, times=10）
- ✅ 抽奖历史记录查询（getLotteryHistory）

**运行命令：**
```bash
# 运行传统抽奖系统测试
node apps/economy/scripts/test-economy-system.js lottery
```

### Activity服务测试（活动抽奖系统）

**测试模块：** `apps/activity/scripts/`

**核心测试功能：**
- ✅ 最佳11人抽奖（buyBestFootball 单抽/十连抽）
- ✅ 老虎机抽奖（buyTurntable 单抽/连抽）
- ✅ 拉霸抽奖（buySlots 单抽/十连抽）
- ✅ 周末返场抽奖（weekDayEncore）

**运行命令：**
```bash
# 运行活动抽奖系统测试
node apps/activity/scripts/test-activity-system.js lottery
```

## 🔧 测试数据准备

### 快速测试数据设置

每个服务都提供`quick-test-setup.js`脚本，用于快速准备测试数据：

```bash
# Hero服务测试数据准备
node apps/hero/scripts/quick-test-setup.js

# 使用现有角色准备数据
node apps/hero/scripts/quick-test-setup.js char_1752805335899_wm3tgjcxp
```

**自动添加的测试资源：**
- 球币：1,000,000
- 欧元：50,000
- 金币：100,000
- 体力：1,000
- 钻石：10,000

### 测试角色管理

**创建测试角色：**
- 自动生成唯一的测试用户ID
- 创建角色时使用时间戳确保唯一性
- 验证角色创建成功并获取characterId

**复用现有角色：**
- 支持通过命令行参数指定现有角色ID
- 自动验证角色是否存在和可用
- 显示角色当前资源状态

## 📊 测试结果分析

### 响应格式标准

基于Match服务的成功模式，所有微服务响应都使用统一格式：

```javascript
{
  "id": "test_xxx",
  "type": "response", 
  "service": "gateway",
  "action": "response",
  "payload": {
    "success": true,
    "data": {
      "code": 0,
      "message": "操作成功",
      "data": { /* 实际业务数据 */ }
    }
  }
}
```

### 测试结果收集

每个测试模块都会收集详细的测试结果：

```javascript
this.testResults.push({ 
  test: 'testName', 
  success: true/false, 
  error: 'errorMessage' 
});
```

### 测试报告生成

测试完成后自动生成汇总报告：
- 总测试项数量
- 成功项数量
- 失败项数量
- 详细的错误信息

## 🛠️ 调试和故障排除

### 常见问题解决

1. **WebSocket连接失败**
   - 检查网关服务是否启动（http://127.0.0.1:3000）
   - 验证.env配置文件中的端口设置

2. **认证失败**
   - 检查Auth服务是否启动（http://127.0.0.1:3001）
   - 验证测试用户创建是否成功

3. **微服务调用失败**
   - 检查目标微服务是否启动
   - 验证微服务间通信配置
   - 查看微服务日志获取详细错误信息

4. **资源不足错误**
   - 运行quick-test-setup.js准备充足的测试资源
   - 检查角色当前资源状态

### 调试模式

启用详细日志输出：
```bash
DEBUG=true node test-hero-system.js scout
```

## 📝 最佳实践

### 1. 测试脚本开发规范

- **继承基类**：所有入口脚本继承MicroserviceWebSocketClient
- **模块化设计**：按功能模块拆分测试代码
- **错误处理**：优雅处理各种异常情况
- **日志输出**：使用chalk库提供彩色日志输出
- **超时设置**：合理设置WebSocket调用超时时间

### 2. 测试数据管理

- **数据隔离**：每次测试使用独立的测试数据
- **资源充足**：确保测试账号有足够的游戏资源
- **数据清理**：测试完成后适当清理测试数据

### 3. 测试执行策略

- **健康检查优先**：测试前验证所有依赖服务
- **模块化测试**：支持单独测试特定模块
- **错误容忍**：单个模块失败不影响其他模块测试
- **结果汇总**：提供清晰的测试结果报告

## 🎯 下一步计划

1. **扩展测试覆盖**：为其他业务模块添加测试脚本
2. **自动化集成**：集成到CI/CD流程中
3. **性能测试**：添加性能和压力测试
4. **测试报告**：生成详细的HTML测试报告

---

*本指南基于Match服务的成功测试经验，为抽奖系统提供了完整的测试解决方案。*
