# 异常处理规范指南

## 问题背景

项目中出现IDE警告："本地捕获异常的 'throw'"，这是因为在try-catch块内部抛出异常，IDE认为这些异常会被局部catch块捕获，可能不是预期的行为。

## 当前异常处理模式分析

### 1. HTTP服务模式（推荐）
适用于：Gateway、Auth等HTTP接口服务

```typescript
// ✅ 正确：HTTP服务异常处理
async getCharacterInfo(characterId: string): Promise<CharacterInfoDto> {
  try {
    const character = await this.characterRepository.findById(characterId);
    if (!character) {
      throw new NotFoundException({
        code: ErrorCode.CHARACTER_NOT_FOUND,
        message: ErrorMessages[ErrorCode.CHARACTER_NOT_FOUND],
      });
    }
    return this.toCharacterInfoDto(character);
  } catch (error) {
    this.logger.error('获取角色信息失败', error);
    throw error; // 重新抛出，让全局异常过滤器处理
  }
}
```

### 2. 微服务返回码模式（old项目兼容）
适用于：微服务间通信、old项目迁移的业务逻辑

```typescript
// ✅ 正确：微服务返回码模式
async getHeroList(characterId: string): Promise<any> {
  try {
    const character = await this.characterRepository.findByCharacterId(characterId);
    if (!character) {
      return { code: -1, message: '角色不存在', heroList: [] };
    }
    
    const heroes = await this.getCharacterHeroes(characterId);
    return { code: 0, heroList: heroes };
  } catch (error) {
    this.logger.error('获取球员列表失败', error);
    return { code: -2, message: '系统错误', heroList: [] };
  }
}
```

## 解决IDE警告的方案

### 方案1：重构异常处理逻辑（推荐）

将异常检查逻辑提取到try块外部：

```typescript
// ❌ 问题代码：在try-catch内抛出异常
async createGuild(characterId: string, guildName: string): Promise<GuildOperationResultDto> {
  try {
    const existingGuild = await this.guildRepository.findByCharacterId(characterId);
    if (existingGuild) {
      throw new BadRequestException({
        code: ErrorCode.ALREADY_IN_GUILD,
        message: ErrorMessages[ErrorCode.ALREADY_IN_GUILD],
      });
    }
    // ... 其他逻辑
  } catch (error) {
    this.logger.error('创建公会失败', error);
    throw error;
  }
}

// ✅ 修复方案：提前验证，减少try块内的异常抛出
async createGuild(characterId: string, guildName: string): Promise<GuildOperationResultDto> {
  // 1. 预验证阶段（不在try块内）
  const existingGuild = await this.guildRepository.findByCharacterId(characterId);
  if (existingGuild) {
    throw new BadRequestException({
      code: ErrorCode.ALREADY_IN_GUILD,
      message: ErrorMessages[ErrorCode.ALREADY_IN_GUILD],
    });
  }

  const nameExists = await this.guildRepository.findByName(guildName);
  if (nameExists) {
    throw new BadRequestException({
      code: ErrorCode.GUILD_NAME_EXISTS,
      message: ErrorMessages[ErrorCode.GUILD_NAME_EXISTS],
    });
  }

  // 2. 核心业务逻辑（在try块内）
  try {
    const guildData = {
      guildId: this.generateGuildId(),
      guildName,
      creator: characterId,
      createTime: Date.now(),
      // ... 其他字段
    };

    const savedGuild = await this.guildRepository.create(guildData);
    await this.guildRepository.addMember(savedGuild.guildId, {
      characterId,
      characterName,
      pos: GuildPosition.PRESIDENT,
      joinTime: Date.now(),
    });

    this.logger.log(`公会创建成功: ${guildName}, 创建者: ${characterId}`);
    
    return {
      success: true,
      guildId: savedGuild.guildId,
      operationTime: Date.now(),
      message: '公会创建成功',
    };
  } catch (error) {
    this.logger.error('创建公会失败', error);
    throw error;
  }
}
```

### 方案2：使用专用的验证方法

```typescript
// ✅ 提取验证逻辑到专用方法
private async validateGuildCreation(characterId: string, guildName: string): Promise<void> {
  const existingGuild = await this.guildRepository.findByCharacterId(characterId);
  if (existingGuild) {
    throw new BadRequestException({
      code: ErrorCode.ALREADY_IN_GUILD,
      message: ErrorMessages[ErrorCode.ALREADY_IN_GUILD],
    });
  }

  const nameExists = await this.guildRepository.findByName(guildName);
  if (nameExists) {
    throw new BadRequestException({
      code: ErrorCode.GUILD_NAME_EXISTS,
      message: ErrorMessages[ErrorCode.GUILD_NAME_EXISTS],
    });
  }
}

async createGuild(characterId: string, guildName: string): Promise<GuildOperationResultDto> {
  // 预验证
  await this.validateGuildCreation(characterId, guildName);

  // 核心逻辑
  try {
    // ... 业务逻辑
  } catch (error) {
    this.logger.error('创建公会失败', error);
    throw error;
  }
}
```

## 最佳实践规范

### 1. 异常抛出原则

- **HTTP服务**：使用NestJS标准异常（NotFoundException, BadRequestException等）
- **微服务通信**：使用返回码模式，避免跨服务异常传播
- **验证逻辑**：在try块外进行预验证，减少try块内的异常抛出

### 2. 日志记录规范

```typescript
// ✅ 正确的日志记录
try {
  // 业务逻辑
} catch (error) {
  // 记录详细错误信息，但不改变异常类型
  this.logger.error('操作失败', {
    method: 'methodName',
    params: { /* 参数 */ },
    error: error.message,
    stack: error.stack,
  });
  throw error; // 重新抛出原始异常
}
```

### 3. 错误码统一管理

使用 `@app/game-constants` 中的 `ErrorCode` 和 `ErrorMessages`：

```typescript
import { ErrorCode, ErrorMessages } from '@app/game-constants';

throw new BadRequestException({
  code: ErrorCode.SPECIFIC_ERROR,
  message: ErrorMessages[ErrorCode.SPECIFIC_ERROR],
});
```

## 迁移计划

### 阶段1：修复当前警告
1. 识别所有产生IDE警告的文件
2. 应用方案1或方案2进行重构
3. 确保功能不受影响

### 阶段2：统一异常处理
1. 为每个微服务制定异常处理策略
2. 更新开发文档和代码规范
3. 在代码审查中强制执行

### 阶段3：监控和优化
1. 添加异常监控和告警
2. 分析异常模式，持续优化
3. 定期审查异常处理代码

## 工具配置

### IDE配置
可以在IDE中配置忽略特定的警告，但建议通过代码重构解决：

```json
// .vscode/settings.json
{
  "typescript.preferences.includePackageJsonAutoImports": "off",
  "typescript.suggest.autoImports": false
}
```

### ESLint规则
添加相关的ESLint规则来强制执行异常处理规范：

```json
// .eslintrc.js
{
  "rules": {
    "no-throw-literal": "error",
    "@typescript-eslint/no-throw-literal": "error"
  }
}
```
