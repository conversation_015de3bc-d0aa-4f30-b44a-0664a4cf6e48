# Redis前缀架构v2.0 快速参考

## 🎯 核心概念

### 前缀格式
```
{环境}:{项目}:{服务}:{具体键}
```

### 示例
```typescript
// 用户输入的键
'user:profile:123'

// 实际Redis键
'dev:fm:auth:user:profile:123'
```

## 🔧 配置方式

### 推荐方式：使用常量（避免硬编码）
```typescript
// apps/gateway/src/app.module.ts
import { MICROSERVICE_NAMES } from '@shared/constants';

RedisModule.forRootAsync({
  service: MICROSERVICE_NAMES.GATEWAY_SERVICE,  // ✅ 使用常量
  database: 0,
  useFactory: (configService: ConfigService) => ({
    host: configService.get('REDIS_HOST'),
    port: configService.get('REDIS_PORT'),
    password: configService.get('REDIS_PASSWORD'),
  }),
  inject: [ConfigService],
})
```

### 推荐方式：使用常量
```typescript
// apps/auth/src/app.module.ts
import { MICROSERVICE_NAMES } from '@shared/constants';

RedisModule.forRootAsync({
  service: MICROSERVICE_NAMES.AUTH_SERVICE,  // ✅ 使用常量，避免字符串硬编码
  database: 1,
  useFactory: (configService: ConfigService) => ({
    host: configService.get('REDIS_HOST'),
    port: configService.get('REDIS_PORT'),
    password: configService.get('REDIS_PASSWORD'),
  }),
  inject: [ConfigService],
})
```

### 环境变量方式
```bash
# .env
MICROSERVICE_NAME=auth
NODE_ENV=development
PROJECT_NAME=fm
```

```typescript
RedisModule.forRootAsync({
  // 从环境变量读取服务名
  useFactory: (configService: ConfigService) => ({...}),
})
```

## 📝 使用方式

### RedisService使用
```typescript
@Injectable()
export class UserService {
  constructor(private readonly redis: RedisService) {}

  async cacheUser(userId: string, userData: any) {
    // 用户只需提供业务键
    await this.redis.set(`user:profile:${userId}`, userData, 3600);
    // 实际存储: dev:fm:auth:user:profile:123
  }

  async getUser(userId: string) {
    // 用户只需提供业务键
    return await this.redis.get(`user:profile:${userId}`);
  }

  async findUserKeys(pattern: string) {
    // 模式匹配，自动处理前缀
    const keys = await this.redis.keys(`user:${pattern}`);
    // 返回业务键名，如: ['user:profile:123', 'user:settings:123']
    return keys;
  }

  async cleanupTempData() {
    // 批量删除，自动处理前缀
    const deletedCount = await this.redis.deletePattern('user:temp:*');
    return deletedCount;
  }
}
```

### 缓存装饰器使用
```typescript
@Controller('users')
export class UsersController {
  
  @Cacheable({
    key: 'user:profile:#{userId}',  // 简单的业务键
    ttl: 300
  })
  async getUserProfile(userId: string) {
    // 实际Redis键: dev:fm:auth:user:profile:user123
    return { id: userId, name: 'User Name' };
  }
  
  @CacheEvict({
    key: 'user:profile:#{userId}'
  })
  async updateUserProfile(userId: string, data: any) {
    // 自动清理对应的缓存
  }
  
  @CachePut({
    key: 'user:stats:#{userId}',
    ttl: 600
  })
  async updateUserStats(userId: string) {
    // 更新缓存
  }
}
```

## 🗄️ 数据库分配

| 服务 | 数据库 | 用途 |
|------|--------|------|
| gateway | 0 | 路由、配置、布隆过滤器 |
| auth | 1 | 认证、会话、权限 |
| user | 2 | 用户档案、缓存 |
| game | 3 | 游戏状态、比赛 |
| club | 4 | 俱乐部数据、财务 |
| match | 5 | 比赛实时数据 |
| player | 6 | 球员数据 |
| card | 7 | 卡牌数据 |
| transfer | 8 | 转会数据 |
| achievement | 9 | 成就数据 |
| notification | 10 | 通知数据 |

## 🔄 迁移对比

### 认证服务迁移

#### 迁移前
```typescript
// 使用 CacheModule
CacheModule.registerAsync({
  isGlobal: true,
  useFactory: async (configService: ConfigService) => ({
    store: redisStore,
    host: configService.get<string>('redis.host'),
    port: configService.get<number>('redis.port'),
    // ...
  }),
  inject: [ConfigService],
})
```

#### 迁移后
```typescript
// 使用 RedisModule
RedisModule.forRootAsync({
  service: 'auth',
  useFactory: (configService: ConfigService) => ({
    host: configService.get('REDIS_HOST'),
    port: configService.get('REDIS_PORT'),
    password: configService.get('REDIS_PASSWORD'),
  }),
  inject: [ConfigService],
})
```

### 备份系统迁移

#### 迁移前的备份模式
```typescript
// .env.backup
REDIS_BACKUP_PATTERNS=gateway:*,bloom:filter:*,user:*,session:*,game:*,club:*,match:*,player:*

// 备份服务使用固定模式
const includePatterns = this.configService.get<string>('REDIS_BACKUP_PATTERNS').split(',');
const prefixedPatterns = includePatterns.map(pattern => `${keyPrefix}${pattern}`);
```

#### 迁移后的备份模式
```typescript
// 备份系统自动适配新的前缀架构
const backupPatterns = [
  // 按服务自动生成模式
  `${env}:fm:gateway:*`,     // 网关服务所有数据
  `${env}:fm:auth:*`,        // 认证服务所有数据
  `${env}:fm:user:*`,        // 用户服务所有数据
  `${env}:fm:game:*`,        // 游戏服务所有数据
  `${env}:fm:club:*`,        // 俱乐部服务所有数据
];

// 支持按数据库备份
const databaseBackup = {
  0: 'gateway',     // 备份数据库0 (网关)
  1: 'auth',        // 备份数据库1 (认证)
  2: 'user',        // 备份数据库2 (用户)
  3: 'game',        // 备份数据库3 (游戏)
  4: 'club',        // 备份数据库4 (俱乐部)
};
```

### 键格式对比

#### 迁移前
```typescript
// 缓存装饰器
@Cacheable({ key: 'user:profile:#{userId}' })

// 实际Redis键
'user:profile:user123'
```

#### 迁移后
```typescript
// 缓存装饰器（无需修改）
@Cacheable({ key: 'user:profile:#{userId}' })

// 实际Redis键
'dev:fm:auth:user:profile:user123'
```

## 🧪 验证命令

### 检查Redis键格式
```bash
# 连接Redis
redis-cli -h *************** -p 6379 -a 123456

# 查看所有键
keys "*"

# 查看特定服务的键
keys "dev:fm:auth:*"

# 查看特定数据库
select 1
keys "*"
```

### 备份系统验证
```bash
# 服务感知备份
node scripts/backup-cli.js service --services=auth,user --description "测试服务备份"

# 数据库级备份
node scripts/backup-cli.js database --databases=1,2 --description "测试数据库备份"

# 混合备份策略
node scripts/backup-cli.js hybrid --description "测试混合备份"

# 验证备份内容
node scripts/backup-cli.js list
node scripts/backup-cli.js verify backup-service-xxx

# 测试恢复（dry-run模式）
node scripts/backup-cli.js restore backup-service-xxx --services=auth --dry-run
```

### 检查服务上下文
```typescript
// 在服务中获取上下文
const context = this.redis.getServiceContext();
console.log('当前服务:', context);

// 获取完整键名
const fullKey = this.redis.getFullKey('user:profile:123');
console.log('完整键名:', fullKey);
```

## ⚠️ 常见问题

### 1. 服务名推断失败
```typescript
// 问题：自动推断不正确
// 解决：显式指定服务名
RedisModule.forRootAsync({
  service: 'auth',  // 明确指定
})
```

### 2. 键前缀重复
```typescript
// ❌ 错误：手动添加前缀
await redis.set('dev:fm:auth:user:123', data);

// ✅ 正确：只提供业务键
await redis.set('user:123', data);
```

### 3. 缓存装饰器失效
```typescript
// 检查：确保CacheInterceptor正确注册
@Module({
  providers: [CacheInterceptor],
})
```

### 4. 跨服务访问
```typescript
// 如需访问其他服务的键，使用完整键名
const otherServiceKey = 'dev:fm:user:profile:123';
const data = await redis.getClient().get(otherServiceKey);
```

## 🔧 调试技巧

### 1. 查看实际Redis键
```typescript
// 在开发环境启用调试日志
const keys = await redis.keys('*');
console.log('所有Redis键:', keys);
```

### 2. 验证服务上下文
```typescript
// 检查服务上下文是否正确
@Injectable()
export class DebugService {
  constructor(
    private redis: RedisService,
    @Inject('REDIS_SERVICE_CONTEXT') private serviceContext: string
  ) {
    console.log('当前服务上下文:', serviceContext);
  }
}
```

### 3. 监控键操作
```typescript
// 添加操作日志
await redis.set(key, value);
console.log(`设置键: ${redis.getFullKey(key)}`);
```

## 📊 性能建议

### 1. 键命名最佳实践
```typescript
// ✅ 推荐
'user:profile:123'
'session:active:user456'
'cache:club:stats:789'

// ❌ 避免
'very:long:nested:key:structure'
'user123profile'
'temp_data_for_user_123'
```

### 2. TTL设置建议
```typescript
const TTL = {
  USER_SESSION: 7200,    // 2小时
  USER_PROFILE: 3600,    // 1小时
  GAME_STATE: 1800,      // 30分钟
  CLUB_STATS: 600,       // 10分钟
  LEADERBOARD: 300,      // 5分钟
  REAL_TIME: 60,         // 1分钟
};
```

## 🚀 快速开始

### 1. 修改模块配置
```typescript
// 将 CacheModule 替换为 RedisModule
RedisModule.forRootAsync({
  service: 'your-service-name',
  useFactory: (configService: ConfigService) => ({
    host: configService.get('REDIS_HOST'),
    port: configService.get('REDIS_PORT'),
    password: configService.get('REDIS_PASSWORD'),
  }),
  inject: [ConfigService],
})
```

### 2. 验证功能
```bash
# 启动服务
npm run start:dev

# 检查Redis键
redis-cli keys "*"
```

### 3. 测试缓存
```typescript
// 使用现有的缓存装饰器，无需修改
@Cacheable({ key: 'test:#{id}', ttl: 300 })
async testMethod(id: string) {
  return { id, timestamp: Date.now() };
}
```

---

**快速参考版本**: 2.0  
**最后更新**: 2025-07-03  
**适用范围**: Redis前缀架构v2.0
