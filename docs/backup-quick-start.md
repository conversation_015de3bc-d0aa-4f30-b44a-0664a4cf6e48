# 数据备份与恢复 - 快速启动指南

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装新增的依赖包
npm install
```

### 2. 配置环境变量

```bash
# 复制配置文件
cp .env.backup.example .env.backup

# 编辑配置文件
nano .env.backup
```

**最小配置示例**：
```bash
# 启用备份
BACKUP_ENABLED=true
BACKUP_INCREMENTAL_ENABLED=true

# 本地存储
BACKUP_STORAGE_PROVIDER=local
BACKUP_LOCAL_PATH=/app/backups

# 数据库连接
MONGODB_URI=mongodb://localhost:27017/football_manager
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 3. 集成到应用

在网关或认证服务中集成备份模块：

```typescript
// apps/gateway/src/app.module.ts
import { BackupModule } from '@common/backup';

@Module({
  imports: [
    // 其他模块...
    BackupModule.forRoot({
      enabled: true,
      schedulerEnabled: true,
    }),
  ],
})
export class AppModule {}
```

### 4. 启动服务

```bash
# 启动网关服务（包含备份功能）
npm run start:gateway

# 或启动认证服务
npm run start:auth
```

## 📋 基本使用

### CLI命令

```bash
# 创建完整备份
npm run backup:full

# 创建增量备份  
npm run backup:incremental

# 查看备份列表
npm run backup:list

# 查看备份状态
npm run backup:status

# 恢复备份
npm run backup:restore <backup-id>

# 清理过期备份
npm run backup:cleanup

# 验证备份完整性
npm run backup:verify <backup-id>
```

### API接口

```bash
# 创建备份
curl -X POST http://localhost:3000/api/backup \
  -H "Content-Type: application/json" \
  -d '{"type": "full", "description": "手动备份"}'

# 获取备份列表
curl http://localhost:3000/api/backup

# 获取备份状态
curl http://localhost:3000/api/backup/statistics/overview
```

## ⚙️ 配置说明

### 存储提供者

#### 本地存储
```bash
BACKUP_STORAGE_PROVIDER=local
BACKUP_LOCAL_PATH=/app/backups
```

#### AWS S3
```bash
BACKUP_STORAGE_PROVIDER=aws-s3
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
```

#### 阿里云OSS
```bash
BACKUP_STORAGE_PROVIDER=aliyun-oss
BACKUP_OSS_BUCKET=your-backup-bucket
BACKUP_OSS_REGION=oss-cn-hangzhou
BACKUP_OSS_ACCESS_KEY_ID=your-access-key
BACKUP_OSS_ACCESS_KEY_SECRET=your-secret-key
```

### 调度配置

```bash
# 每天凌晨2点完整备份
BACKUP_FULL_SCHEDULE=0 2 * * *

# 每6小时增量备份
BACKUP_INCREMENTAL_SCHEDULE=0 */6 * * *

# 每周日凌晨4点清理
BACKUP_CLEANUP_SCHEDULE=0 4 * * 0
```

### 安全配置

```bash
# 启用加密
BACKUP_ENCRYPTION_ENABLED=true
BACKUP_ENCRYPTION_KEY=your-32-character-encryption-key

# 启用压缩
BACKUP_COMPRESSION_ENABLED=true
```

## 🔧 高级配置

### 自定义备份策略

```typescript
// 在服务中注入备份管理器
@Injectable()
export class CustomBackupService {
  constructor(
    private readonly backupManager: BackupManagerService,
  ) {}

  async createCustomBackup() {
    // 创建带标签的备份
    const backupId = await this.backupManager.executeFullBackup();
    await this.backupManager.addBackupTags(backupId, ['custom', 'important']);
    
    return backupId;
  }
}
```

### 监控集成

```typescript
// 获取备份统计
const stats = this.monitoringService.getBackupStatistics();

// 检查健康状态
const health = await this.monitoringService.checkBackupHealth();

// 生成报告
const report = await this.monitoringService.generateBackupReport(30);
```

## 🚨 故障排除

### 常见问题

#### 1. 备份失败
```bash
# 检查日志
tail -f logs/backup.log

# 检查磁盘空间
df -h

# 检查权限
ls -la /app/backups
```

#### 2. 恢复失败
```bash
# 验证备份完整性
npm run backup:verify <backup-id>

# 检查目标数据库连接
mongosh $MONGODB_URI

# 检查Redis连接
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping
```

#### 3. 调度器不工作
```bash
# 检查调度器状态
curl http://localhost:3000/api/backup/scheduler/status

# 检查环境变量
echo $BACKUP_SCHEDULER_ENABLED
```

### 日志分析

```bash
# 查看备份日志
grep "backup" logs/application.log

# 查看错误日志
grep "ERROR" logs/backup.log

# 实时监控
tail -f logs/backup.log | grep -E "(ERROR|WARN|SUCCESS)"
```

## 📊 监控指标

### 关键指标

- **备份成功率**: 成功备份数 / 总备份数
- **平均备份时间**: 所有备份的平均耗时
- **备份大小趋势**: 备份文件大小变化
- **存储使用率**: 备份存储空间使用情况

### 告警规则

- 备份失败 → 立即告警
- 备份超时 → 高优先级告警
- 存储空间不足 → 中优先级告警
- 备份成功率低于80% → 低优先级告警

## 🔒 安全最佳实践

### 1. 加密配置
- 使用强加密密钥（至少32字符）
- 定期轮换加密密钥
- 密钥存储在安全的密钥管理系统中

### 2. 访问控制
- 限制备份文件访问权限
- 使用专用的备份用户账户
- 启用审计日志

### 3. 网络安全
- 使用HTTPS传输备份文件
- 配置防火墙规则
- 启用VPN访问

## 📈 性能优化

### 1. 备份优化
```bash
# 启用压缩
BACKUP_COMPRESSION_ENABLED=true
BACKUP_COMPRESSION_LEVEL=6

# 并行备份
BACKUP_CONCURRENCY=3

# 分片备份
BACKUP_CHUNK_SIZE=1048576
```

### 2. 存储优化
- 使用SSD存储提升性能
- 配置适当的备份保留策略
- 启用重复数据删除

### 3. 网络优化
- 使用CDN加速文件传输
- 配置带宽限制避免影响业务
- 选择就近的存储区域

## 🎯 生产环境部署

### 1. 部署检查清单

- [ ] 配置文件已正确设置
- [ ] 存储空间充足
- [ ] 网络连接正常
- [ ] 权限配置正确
- [ ] 监控告警已配置
- [ ] 恢复流程已测试

### 2. 监控配置

```bash
# Prometheus指标
backup_total_count
backup_success_rate
backup_duration_seconds
backup_size_bytes

# 告警规则
backup_failed > 0
backup_success_rate < 0.8
backup_duration > 3600
```

### 3. 运维建议

- 定期测试恢复流程
- 监控备份文件完整性
- 保持备份工具版本更新
- 建立备份恢复文档

---

**🎉 恭喜！您已成功配置数据备份与恢复系统！**

如有问题，请查看完整文档：`docs/data-backup-recovery-strategy.md`
