# Result类型转换规范指南

## 🎯 核心原则

### 1. 类型安全优先
- 保持原有的类型信息
- 避免使用`any`类型
- 确保编译时类型检查

### 2. 业务逻辑不变
- 保持方法的业务语义
- 保持调用方的期望
- 保持错误处理逻辑

## 📊 返回类型转换矩阵

| 原始返回类型 | 转换后类型 | 处理策略 | 示例 |
|-------------|------------|----------|------|
| `Promise<T>` | `Promise<Result<T>>` | ✅ 标准转换 | `Promise<HeroDocument>` → `Promise<Result<HeroDocument>>` |
| `T` | `Result<T>` | ✅ 标准转换 | `HeroInfoDto` → `Result<HeroInfoDto>` |
| `Promise<any>` | `Promise<Result<any>>` | ⚠️ 需要明确类型 | 应该定义具体的返回类型 |
| `any` | `Result<any>` | ⚠️ 需要明确类型 | 应该定义具体的返回类型 |
| `Promise<void>` | `Promise<Result<void>>` | ✅ 标准转换 | 操作成功/失败的方法 |
| `void` | `Result<void>` | ✅ 标准转换 | 同步操作方法 |
| `T[]` | `Result<T[]>` | ✅ 标准转换 | `HeroInfoDto[]` → `Result<HeroInfoDto[]>` |
| `Promise<T[]>` | `Promise<Result<T[]>>` | ✅ 标准转换 | `Promise<HeroInfoDto[]>` → `Promise<Result<HeroInfoDto[]>>` |

## 🔧 具体转换规则

### 规则1：明确类型的方法（推荐优先处理）
```typescript
// ✅ 原始方法
async createHero(dto: CreateHeroDto): Promise<HeroDocument>

// ✅ 转换后
async createHero(dto: CreateHeroDto): Promise<Result<HeroDocument>>
```

### 规则2：返回DTO的方法
```typescript
// ✅ 原始方法
async getBatchHeroes(heroIds: string[]): Promise<HeroInfoDto[]>

// ✅ 转换后
async getBatchHeroes(heroIds: string[]): Promise<Result<HeroInfoDto[]>>
```

### 规则3：返回any的方法（需要重新定义类型）
```typescript
// ❌ 原始方法（类型不明确）
async getHeroStatus(heroId: string): Promise<any>

// ✅ 先定义明确的返回类型
interface HeroStatusDto {
  heroId: string;
  fatigue: number;
  fatigueRatio: number;
  isTreat: boolean;
  // ... 其他字段
}

// ✅ 然后转换
async getHeroStatus(heroId: string): Promise<Result<HeroStatusDto>>
```

### 规则4：void方法的处理
```typescript
// ✅ 原始方法
async deleteHero(heroId: string): Promise<void>

// ✅ 转换后
async deleteHero(heroId: string): Promise<Result<void>>

// 实现中
return ResultUtils.success(undefined, '删除成功');
```

## 🚫 不适合转换的方法类型

### 1. 私有工具方法
```typescript
// ❌ 不转换
private calculateMarketValue(quality: number, level: number): number

// 原因：内部工具方法，不需要错误处理
```

### 2. 数据转换方法
```typescript
// ❌ 不转换
private toHeroInfoDto(hero: HeroDocument): HeroInfoDto

// 原因：纯数据转换，不会失败
```

### 3. 计算方法
```typescript
// ❌ 不转换
private calculateAttributeBonus(attributes: any): AttributeBonus

// 原因：纯计算逻辑，不涉及外部依赖
```

## 🛠️ AST工具处理策略

### 策略1：类型检测优先级
1. **高优先级**：有明确类型声明的public方法
2. **中优先级**：返回any但有明确业务语义的方法
3. **低优先级**：私有方法和工具方法

### 策略2：自动转换规则
```javascript
// AST工具中的类型转换逻辑
function shouldConvertMethod(method) {
  // 1. 检查是否是public方法
  if (method.hasModifier(SyntaxKind.PrivateKeyword)) {
    return false;
  }
  
  // 2. 检查是否有throw语句
  const hasThrowStatements = method.getDescendantsOfKind(SyntaxKind.ThrowStatement).length > 0;
  if (!hasThrowStatements) {
    return false;
  }
  
  // 3. 检查返回类型
  const returnType = method.getReturnTypeNode();
  if (!returnType) {
    return false; // 没有返回类型声明，跳过
  }
  
  const returnTypeText = returnType.getText();
  
  // 4. 排除不适合转换的类型
  const excludePatterns = [
    /^number$/,
    /^string$/,
    /^boolean$/,
    /^void$/,
    /^Observable</,
  ];
  
  if (excludePatterns.some(pattern => pattern.test(returnTypeText))) {
    return false;
  }
  
  return true;
}
```

### 策略3：类型推断增强
```javascript
function inferReturnType(method) {
  const returnType = method.getReturnTypeNode();
  if (!returnType) {
    // 尝试从方法名推断类型
    const methodName = method.getName();
    
    if (methodName.startsWith('get') && methodName.includes('List')) {
      return 'Array'; // 列表方法
    }
    
    if (methodName.startsWith('create') || methodName.startsWith('update')) {
      return 'Document'; // CRUD方法
    }
    
    if (methodName.startsWith('delete') || methodName.startsWith('remove')) {
      return 'void'; // 删除方法
    }
  }
  
  return returnType?.getText() || 'any';
}
```

## 📝 实施步骤

### 步骤1：类型定义完善
1. 为所有返回`any`的方法定义明确的DTO类型
2. 创建标准的响应类型接口
3. 建立类型映射表

### 步骤2：AST工具增强
1. 添加类型检测逻辑
2. 实现智能类型转换
3. 增加类型安全验证

### 步骤3：分批转换
1. **第1批**：明确类型的简单方法
2. **第2批**：需要类型定义的复杂方法
3. **第3批**：特殊情况的手动处理

## ⚠️ 注意事项

### 1. 调用方适配
```typescript
// 调用方需要适配Result模式
const result = await heroService.createHero(dto);
if (ResultUtils.isSuccess(result)) {
  const hero = result.data; // 类型安全的数据访问
} else {
  console.error(result.message); // 错误处理
}
```

### 2. 向后兼容
```typescript
// 可以提供兼容性包装器
async createHeroLegacy(dto: CreateHeroDto): Promise<HeroDocument> {
  const result = await this.createHero(dto);
  if (ResultUtils.isSuccess(result)) {
    return result.data;
  } else {
    throw new Error(result.message);
  }
}
```

### 3. 测试覆盖
- 每个转换后的方法都需要测试
- 验证成功和失败路径
- 确保类型安全性

## 🎯 质量标准

### 编译时检查
- ✅ 无TypeScript编译错误
- ✅ 类型推断正确
- ✅ 导入语句完整

### 运行时检查
- ✅ 业务逻辑正确
- ✅ 错误处理完整
- ✅ 性能无回归

### 代码质量
- ✅ 类型定义明确
- ✅ 错误信息清晰
- ✅ 文档注释完整
