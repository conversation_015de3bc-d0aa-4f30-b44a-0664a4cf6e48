# Gateway服务快速开始指南

## 🚀 概述

本指南将帮助您快速了解和使用Gateway服务的分区分服功能，包括环境搭建、基本配置、API调用示例等。

## 📋 前置要求

### 系统要求
- Node.js >= 18.0.0
- Redis >= 6.0
- MongoDB >= 5.0
- Docker & Docker Compose (可选)

### 依赖服务
- Auth服务 (认证服务)
- Character服务 (角色服务)
- Hero服务 (英雄服务)
- Match服务 (比赛服务)

## 🔧 环境搭建

### 1. 克隆项目
```bash
git clone <repository-url>
cd football-manager/server-new
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
创建 `.env` 文件：
```bash
# 基础配置
NODE_ENV=development
GATEWAY_PORT=3000

# JWT配置
GATEWAY_JWT_SECRET=your-super-secret-jwt-key
GATEWAY_JWT_EXPIRES_IN=24h

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/football-manager

# 微服务配置
MICROSERVICE_AUTH_URL=http://localhost:3001
MICROSERVICE_CHARACTER_URL=http://localhost:3002
MICROSERVICE_HERO_URL=http://localhost:3003
MICROSERVICE_MATCH_URL=http://localhost:3004
MICROSERVICE_GUILD_URL=http://localhost:3005

# 全服消息配置
GLOBAL_MESSAGING_ENABLED=true
GLOBAL_MESSAGING_REDIS_PREFIX=global_messages
GLOBAL_MESSAGING_MAX_RETRIES=3
DEAD_LETTER_RETENTION_DAYS=7

# CORS配置
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
```

### 4. 启动服务

#### 开发模式
```bash
# 启动Gateway服务
npm run start:dev:gateway

# 或者启动所有服务
npm run start:dev
```

#### Docker模式
```bash
# 使用Docker Compose启动
docker-compose up -d

# 查看服务状态
docker-compose ps
```

## 🔐 认证流程

### 1. 获取账号Token
```bash
# 用户登录获取账号Token
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'

# 响应示例
{
  "success": true,
  "data": {
    "accountToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user123",
      "username": "testuser"
    }
  }
}
```

### 2. 选择角色获取角色Token
```bash
# 使用账号Token选择角色
curl -X POST http://localhost:3000/api/character/select \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <account_token>" \
  -d '{
    "characterId": "char456",
    "serverId": "server001"
  }'

# 响应示例
{
  "success": true,
  "data": {
    "characterToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "character": {
      "id": "char456",
      "name": "MyCharacter",
      "serverId": "server001"
    }
  }
}
```

## 🔌 WebSocket连接

### 1. 建立连接
```javascript
// 前端JavaScript示例
import io from 'socket.io-client';

// 使用角色Token连接
const socket = io('ws://localhost:3000', {
  auth: {
    token: 'your_character_token_here'
  },
  transports: ['websocket']
});

// 连接成功
socket.on('connect', () => {
  console.log('WebSocket连接成功:', socket.id);
});

// 连接错误
socket.on('connect_error', (error) => {
  console.error('WebSocket连接失败:', error);
});
```

### 2. 发送消息
```javascript
// 获取角色信息
socket.emit('message', {
  command: 'character.profile.get',
  payload: {
    characterId: 'char456'
  }
});

// 监听响应
socket.on('character.profile.get', (response) => {
  if (response.success) {
    console.log('角色信息:', response.data);
  } else {
    console.error('获取失败:', response.error);
  }
});
```

### 3. 跨服操作
```javascript
// 跨服挑战
socket.emit('message', {
  command: 'cross.match.challenge',
  payload: {
    targetServerId: 'server002',
    targetUserId: 'user789',
    matchType: 'friendly'
  }
});

// 监听跨服响应
socket.on('cross.match.challenge', (response) => {
  console.log('跨服挑战结果:', response);
});
```

## 🌐 HTTP API调用

### 1. 区服特定请求
```bash
# 获取角色信息（自动路由到对应区服）
curl -X GET http://localhost:3000/api/character/profile \
  -H "Authorization: Bearer <character_token>"

# Gateway自动添加区服路由头部，代理到character-server001服务
```

### 2. 跨服请求
```bash
# 跨服排行榜查询
curl -X GET http://localhost:3000/api/cross-server/leaderboard \
  -H "Authorization: Bearer <character_token>" \
  -H "Content-Type: application/json"

# Gateway自动路由到跨服服务
```

### 3. 全服请求
```bash
# 获取全服公告
curl -X GET http://localhost:3000/api/global/announcements \
  -H "Authorization: Bearer <account_token>"

# Gateway路由到全服消息服务
```

## 📢 全服消息系统

### 1. 创建系统公告
```bash
# 管理员创建系统公告
curl -X POST http://localhost:3000/api/global-messaging/announcements \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "title": "系统维护通知",
    "content": "服务器将于今晚22:00-24:00进行维护，请提前下线。",
    "announcementType": "maintenance",
    "displayType": "popup",
    "priority": "urgent",
    "targetServers": "all",
    "startTime": "2024-01-01T22:00:00Z",
    "endTime": "2024-01-02T00:00:00Z"
  }'
```

### 2. 监听全服消息
```javascript
// 监听系统公告
socket.on('global.announcement', (data) => {
  console.log('收到系统公告:', data);
  
  // 显示弹窗或横幅
  if (data.displayType === 'popup') {
    showPopup(data.title, data.content);
  } else if (data.displayType === 'banner') {
    showBanner(data.title, data.content);
  }
});

// 监听活动通知
socket.on('global.event_notification', (data) => {
  console.log('收到活动通知:', data);
  
  // 如果需要跳转到活动页面
  if (data.jumpToEvent) {
    navigateToEvent(data.eventId);
  }
});
```

### 3. 确认消息已读
```bash
# 用户确认消息已读
curl -X POST http://localhost:3000/api/user/global-messages/msg_123/acknowledge \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <character_token>" \
  -d '{
    "userId": "user123"
  }'
```

## 🔧 开发调试

### 1. 健康检查
```bash
# 检查Gateway服务状态
curl http://localhost:3000/health

# 检查全服消息系统状态
curl -H "Authorization: Bearer <admin_token>" \
  http://localhost:3000/api/global-messaging/system/health
```

### 2. 查看日志
```bash
# 查看Gateway日志
npm run logs:gateway

# 或使用Docker
docker logs gateway-service
```

### 3. 监控指标
```bash
# 查看Prometheus指标
curl http://localhost:3000/metrics

# 查看WebSocket连接数
curl http://localhost:3000/metrics | grep websocket_connections
```

## 🧪 测试示例

### 1. 单元测试
```bash
# 运行Gateway单元测试
npm run test:gateway

# 运行特定测试文件
npm run test apps/gateway/test/websocket/message-router.service.spec.ts
```

### 2. 集成测试
```bash
# 运行集成测试
npm run test:e2e:gateway

# 测试WebSocket连接
npm run test:websocket
```

### 3. 手动测试脚本
```bash
# 运行测试脚本
node scripts/test-gateway-websocket.js
node scripts/test-global-messaging.js
```

## 🚨 常见问题

### 1. WebSocket连接失败
```bash
# 检查Token是否有效
curl -X GET http://localhost:3000/api/auth/verify \
  -H "Authorization: Bearer <your_token>"

# 检查CORS配置
# 确保客户端域名在CORS_ORIGIN中
```

### 2. 路由失败
```bash
# 检查微服务状态
curl http://localhost:3001/health  # Auth服务
curl http://localhost:3002/health  # Character服务
curl http://localhost:3003/health  # Hero服务

# 检查服务发现
curl http://localhost:3000/debug/services
```

### 3. 消息投递失败
```bash
# 检查Redis连接
redis-cli ping

# 检查消息队列状态
curl -H "Authorization: Bearer <admin_token>" \
  http://localhost:3000/api/global-messaging/system/queue-status

# 查看死信队列
curl -H "Authorization: Bearer <admin_token>" \
  http://localhost:3000/api/admin/global-messages/dead-letter/stats
```

## 📚 下一步

### 学习资源
- [Gateway架构增强文档](./gateway-multi-server-enhancement.md)
- [API参考文档](./gateway-api-reference.md)
- [分区分服架构设计](./multi-server-architecture.md)

### 开发指南
- [WebSocket开发指南](../development/websocket-development.md)
- [HTTP代理开发指南](../development/http-proxy-development.md)
- [全服消息开发指南](../development/global-messaging-development.md)

### 部署指南
- [生产环境部署](../deployment/production-deployment.md)
- [Docker部署指南](../deployment/docker-deployment.md)
- [Kubernetes部署指南](../deployment/k8s-deployment.md)

---

## 🎯 总结

通过本快速开始指南，您应该能够：

✅ **搭建Gateway开发环境**  
✅ **理解双层Token认证机制**  
✅ **使用WebSocket进行实时通信**  
✅ **调用HTTP API进行区服路由**  
✅ **使用全服消息系统**  
✅ **进行基本的调试和测试**

如果遇到问题，请参考详细的技术文档或联系开发团队。

---

*快速开始指南版本: v1.0*  
*最后更新: 2024-01-01*  
*维护者: Gateway开发团队*
