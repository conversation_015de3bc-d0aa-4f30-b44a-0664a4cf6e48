# 全服消息机制设计文档

## 📋 概述

全服消息机制是分区分服架构中的重要组成部分，负责处理需要跨越所有区服的消息广播，包括系统公告、活动通知、紧急维护通知等。本文档详细描述了全服消息系统的设计架构、实现方案和使用规范。

## 🎯 核心需求

### **1. 消息类型需求**
- **系统公告**：版本更新、维护通知、重要公告
- **活动通知**：全服活动开始/结束、奖励发放通知
- **紧急通知**：服务器异常、紧急维护、安全警告
- **跨服事件**：跨服战斗、排行榜更新、全服竞赛
- **运营消息**：节日祝福、游戏里程碑、社区活动

### **2. 功能需求**
- **实时广播**：消息能够实时推送到所有在线用户
- **离线存储**：离线用户上线后能够接收到重要消息
- **优先级管理**：不同类型消息有不同的优先级和处理方式
- **目标控制**：支持全服广播、指定区服、指定用户群体
- **消息持久化**：重要消息需要持久化存储和重发机制

### **3. 性能需求**
- **高并发**：支持同时向10万+用户广播消息
- **低延迟**：消息从发送到用户接收延迟 < 1秒
- **高可用**：消息系统故障不影响游戏核心功能
- **可扩展**：支持动态扩容和新区服接入

## 🏗️ 系统架构

### **1. 整体架构图**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管理后台      │    │   Gateway网关   │    │   各区服务器    │
│                 │    │                 │    │                 │
│ • 消息发布      │───►│ • 消息路由      │───►│ • 消息接收      │
│ • 优先级设置    │    │ • 广播分发      │    │ • 用户推送      │
│ • 目标选择      │    │ • 状态管理      │    │ • 状态反馈      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────►│   Redis集群     │◄─────────────┘
                        │                 │
                        │ • 消息队列      │
                        │ • 状态缓存      │
                        │ • 发布订阅      │
                        └─────────────────┘
```

### **2. 核心组件**

#### **2.1 消息管理中心（Message Management Center）**
```typescript
// 位置：apps/gateway/src/domain/global-messaging/
├── services/
│   ├── global-broadcast.service.ts     # 全服广播核心服务
│   ├── message-publisher.service.ts    # 消息发布服务
│   ├── message-scheduler.service.ts    # 消息调度服务
│   └── message-persistence.service.ts  # 消息持久化服务
├── controllers/
│   ├── admin-messaging.controller.ts   # 管理员消息接口
│   └── user-messaging.controller.ts    # 用户消息接口
└── entities/
    ├── global-message.entity.ts        # 全服消息实体
    └── message-delivery.entity.ts      # 消息投递记录
```

#### **2.2 消息路由器（Message Router）**
```typescript
// 位置：apps/gateway/src/domain/websocket/global-routing/
├── global-message-router.service.ts    # 全服消息路由
├── target-resolver.service.ts          # 目标解析服务
├── delivery-tracker.service.ts         # 投递跟踪服务
└── retry-manager.service.ts            # 重试管理服务
```

#### **2.3 消息队列系统（Message Queue System）**
```typescript
// 基于Redis的消息队列实现
├── queue-manager.service.ts            # 队列管理服务
├── priority-queue.service.ts           # 优先级队列
├── dead-letter-queue.service.ts        # 死信队列
└── queue-monitor.service.ts            # 队列监控服务
```

## 📊 消息类型定义

### **1. 基础消息接口**
```typescript
interface BaseGlobalMessage {
  id: string;                            // 消息唯一ID
  type: GlobalMessageType;               // 消息类型
  priority: MessagePriority;             // 消息优先级
  title: string;                         // 消息标题
  content: string;                       // 消息内容
  createdAt: Date;                       // 创建时间
  publishAt: Date;                       // 发布时间
  expireAt?: Date;                       // 过期时间
  target: MessageTarget;                 // 目标定义
  metadata: Record<string, any>;         // 扩展元数据
}

enum GlobalMessageType {
  SYSTEM_ANNOUNCEMENT = 'system_announcement',
  MAINTENANCE_NOTICE = 'maintenance_notice',
  EVENT_NOTIFICATION = 'event_notification',
  EMERGENCY_ALERT = 'emergency_alert',
  CROSS_SERVER_EVENT = 'cross_server_event',
  MARKETING_MESSAGE = 'marketing_message',
}

enum MessagePriority {
  LOW = 1,
  MEDIUM = 2,
  HIGH = 3,
  URGENT = 4,
  EMERGENCY = 5,
}
```

### **2. 目标定义**
```typescript
interface MessageTarget {
  scope: 'global' | 'servers' | 'users' | 'conditions';
  servers?: string[];                    // 指定区服列表
  users?: string[];                      // 指定用户列表
  conditions?: TargetCondition[];        // 条件筛选
}

interface TargetCondition {
  field: string;                         // 筛选字段
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'in' | 'nin';
  value: any;                            // 筛选值
}

// 使用示例：
const target: MessageTarget = {
  scope: 'conditions',
  conditions: [
    { field: 'level', operator: 'gt', value: 30 },      // 等级大于30
    { field: 'vipLevel', operator: 'gt', value: 0 },    // VIP用户
    { field: 'lastLoginTime', operator: 'gt', value: '2024-01-01' }, // 活跃用户
  ]
};
```

### **3. 具体消息类型**

#### **3.1 系统公告**
```typescript
interface SystemAnnouncement extends BaseGlobalMessage {
  type: GlobalMessageType.SYSTEM_ANNOUNCEMENT;
  displayType: 'popup' | 'banner' | 'notification' | 'marquee';
  autoClose: boolean;                    // 是否自动关闭
  closeDelay?: number;                   // 自动关闭延迟（秒）
  actionButton?: {                       // 操作按钮
    text: string;
    action: string;
    url?: string;
  };
}
```

#### **3.2 维护通知**
```typescript
interface MaintenanceNotice extends BaseGlobalMessage {
  type: GlobalMessageType.MAINTENANCE_NOTICE;
  maintenanceStart: Date;                // 维护开始时间
  maintenanceEnd: Date;                  // 维护结束时间
  affectedServers: string[];             // 受影响的区服
  compensationItems?: any[];             // 补偿物品
  forceLogout: boolean;                  // 是否强制登出
  countdownDisplay: boolean;             // 是否显示倒计时
}
```

#### **3.3 活动通知**
```typescript
interface EventNotification extends BaseGlobalMessage {
  type: GlobalMessageType.EVENT_NOTIFICATION;
  eventId: string;                       // 活动ID
  eventType: 'cross_server' | 'global' | 'seasonal';
  startTime: Date;                       // 活动开始时间
  endTime: Date;                         // 活动结束时间
  rewards?: any[];                       // 活动奖励
  requirements?: any;                    // 参与要求
  jumpToEvent: boolean;                  // 是否跳转到活动页面
}
```

#### **3.4 紧急警告**
```typescript
interface EmergencyAlert extends BaseGlobalMessage {
  type: GlobalMessageType.EMERGENCY_ALERT;
  alertLevel: 'warning' | 'critical' | 'emergency';
  action?: 'logout' | 'maintenance' | 'update' | 'none';
  countdown?: number;                    // 倒计时（秒）
  forceDisplay: boolean;                 // 强制显示
  blockGameplay: boolean;                // 是否阻止游戏操作
}
```

## 🔄 消息流程

### **1. 消息发布流程**
```mermaid
sequenceDiagram
    participant Admin as 管理后台
    participant Gateway as Gateway网关
    participant Redis as Redis集群
    participant Server as 区服服务器
    participant Client as 客户端

    Admin->>Gateway: 发布全服消息
    Gateway->>Gateway: 验证消息格式和权限
    Gateway->>Redis: 存储消息到持久化队列
    Gateway->>Redis: 发布消息到广播频道
    Redis->>Server: 推送消息到各区服
    Server->>Server: 解析目标用户
    Server->>Client: 推送消息到目标用户
    Client->>Server: 确认消息接收
    Server->>Redis: 更新投递状态
```

### **2. 消息投递流程**
```typescript
// 消息投递的详细步骤
class MessageDeliveryFlow {
  async deliverMessage(message: BaseGlobalMessage): Promise<void> {
    // 1. 消息预处理
    const processedMessage = await this.preprocessMessage(message);
    
    // 2. 目标解析
    const targets = await this.resolveTargets(message.target);
    
    // 3. 消息分发
    const deliveryTasks = targets.map(target => 
      this.deliverToTarget(processedMessage, target)
    );
    
    // 4. 并发投递
    const results = await Promise.allSettled(deliveryTasks);
    
    // 5. 结果处理
    await this.handleDeliveryResults(message.id, results);
    
    // 6. 失败重试
    await this.scheduleRetries(message.id, results);
  }
}
```

### **3. 消息接收流程**
```typescript
// 客户端消息接收处理
class ClientMessageHandler {
  @SubscribeMessage('global.message')
  async handleGlobalMessage(
    @MessageBody() message: BaseGlobalMessage,
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    // 1. 消息验证
    if (!this.validateMessage(message)) {
      return;
    }
    
    // 2. 用户权限检查
    const user = await this.getUserFromSocket(client);
    if (!this.checkMessagePermission(user, message)) {
      return;
    }
    
    // 3. 消息处理
    await this.processMessage(message, user);
    
    // 4. 发送确认
    client.emit('global.message.ack', { messageId: message.id });
    
    // 5. 记录日志
    this.logMessageDelivery(message.id, user.id, 'delivered');
  }
}
```

## 🎛️ 管理接口

### **1. 消息发布接口**
```typescript
// POST /api/gateway/admin/global-messages
@Post('global-messages')
@RequirePermission('GLOBAL_MESSAGE_PUBLISH')
async publishGlobalMessage(
  @Body() createDto: CreateGlobalMessageDto,
  @CurrentUser() admin: AdminUser,
): Promise<GlobalMessageResponse> {
  // 发布全服消息
}

// GET /api/gateway/admin/global-messages
@Get('global-messages')
@RequirePermission('GLOBAL_MESSAGE_VIEW')
async getGlobalMessages(
  @Query() query: GlobalMessageQueryDto,
): Promise<GlobalMessageListResponse> {
  // 查询全服消息列表
}

// PUT /api/gateway/admin/global-messages/:id/cancel
@Put('global-messages/:id/cancel')
@RequirePermission('GLOBAL_MESSAGE_CANCEL')
async cancelGlobalMessage(
  @Param('id') messageId: string,
): Promise<void> {
  // 取消全服消息
}
```

### **2. 消息统计接口**
```typescript
// GET /api/gateway/admin/global-messages/:id/stats
@Get('global-messages/:id/stats')
@RequirePermission('GLOBAL_MESSAGE_STATS')
async getMessageStats(
  @Param('id') messageId: string,
): Promise<MessageStatsResponse> {
  return {
    messageId,
    totalTargets: 10000,           // 目标用户总数
    delivered: 9500,               // 已投递数量
    failed: 200,                   // 投递失败数量
    pending: 300,                  // 待投递数量
    acknowledged: 9000,            // 已确认数量
    deliveryRate: 0.95,            // 投递成功率
    acknowledgmentRate: 0.947,     // 确认率
    avgDeliveryTime: 0.5,          // 平均投递时间（秒）
  };
}
```

## 🔧 技术实现

### **1. Redis消息队列**
```typescript
// 基于Redis的消息队列实现
class RedisMessageQueue {
  // 优先级队列
  async enqueue(message: BaseGlobalMessage): Promise<void> {
    const queueKey = `global_messages:priority:${message.priority}`;
    await this.redis.zadd(queueKey, message.publishAt.getTime(), JSON.stringify(message));
  }
  
  // 消息消费
  async dequeue(priority: MessagePriority): Promise<BaseGlobalMessage | null> {
    const queueKey = `global_messages:priority:${priority}`;
    const now = Date.now();
    const messages = await this.redis.zrangebyscore(queueKey, 0, now, 'LIMIT', 0, 1);
    
    if (messages.length > 0) {
      await this.redis.zrem(queueKey, messages[0]);
      return JSON.parse(messages[0]);
    }
    
    return null;
  }
  
  // 发布订阅
  async publishToServers(message: BaseGlobalMessage): Promise<void> {
    const channel = 'global_messages:broadcast';
    await this.redis.publish(channel, JSON.stringify(message));
  }
}
```

### **2. 消息持久化**
```typescript
// MongoDB消息持久化
@Entity('global_messages')
export class GlobalMessage {
  @PrimaryGeneratedColumn('uuid')
  id: string;
  
  @Column({ type: 'enum', enum: GlobalMessageType })
  type: GlobalMessageType;
  
  @Column({ type: 'enum', enum: MessagePriority })
  priority: MessagePriority;
  
  @Column()
  title: string;
  
  @Column('text')
  content: string;
  
  @Column({ type: 'json' })
  target: MessageTarget;
  
  @Column({ type: 'json' })
  metadata: Record<string, any>;
  
  @CreateDateColumn()
  createdAt: Date;
  
  @Column()
  publishAt: Date;
  
  @Column({ nullable: true })
  expireAt: Date;
  
  @Column({ default: 'pending' })
  status: 'pending' | 'published' | 'cancelled' | 'expired';
}
```

### **3. 投递跟踪**
```typescript
// 消息投递状态跟踪
@Entity('message_deliveries')
export class MessageDelivery {
  @PrimaryGeneratedColumn('uuid')
  id: string;
  
  @Column()
  messageId: string;
  
  @Column()
  userId: string;
  
  @Column()
  serverId: string;
  
  @Column({ default: 'pending' })
  status: 'pending' | 'delivered' | 'failed' | 'acknowledged';
  
  @Column({ nullable: true })
  deliveredAt: Date;
  
  @Column({ nullable: true })
  acknowledgedAt: Date;
  
  @Column({ nullable: true })
  failureReason: string;
  
  @Column({ default: 0 })
  retryCount: number;
}
```

## 📊 监控和统计

### **1. 关键指标**
- **消息投递率**：成功投递的消息占总消息的比例
- **消息确认率**：用户确认接收的消息占已投递消息的比例
- **平均投递时间**：从消息发布到用户接收的平均时间
- **队列积压**：待处理消息的数量
- **失败重试率**：需要重试的消息占总消息的比例

### **2. 监控告警**
```typescript
// 监控告警规则
const alertRules = {
  deliveryRate: {
    threshold: 0.95,               // 投递率低于95%告警
    window: '5m',                  // 5分钟窗口
    severity: 'warning',
  },
  queueBacklog: {
    threshold: 1000,               // 队列积压超过1000条告警
    window: '1m',                  // 1分钟窗口
    severity: 'critical',
  },
  avgDeliveryTime: {
    threshold: 2.0,                // 平均投递时间超过2秒告警
    window: '5m',                  // 5分钟窗口
    severity: 'warning',
  },
};
```

## 🔒 安全考虑

### **1. 权限控制**
- **发布权限**：只有特定角色可以发布全服消息
- **目标限制**：限制可以选择的目标范围
- **内容审核**：敏感内容自动检测和人工审核
- **频率限制**：防止消息轰炸和滥用

### **2. 数据安全**
- **消息加密**：敏感消息内容加密传输
- **访问日志**：记录所有消息操作的详细日志
- **数据备份**：重要消息数据定期备份
- **权限审计**：定期审计消息发布权限

---

> **重要说明**：全服消息机制是分区分服架构的重要组成部分，必须与Gateway的WebSocket认证、分区路由等功能紧密集成，确保消息的安全性、可靠性和高性能。
