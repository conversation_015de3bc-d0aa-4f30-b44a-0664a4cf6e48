# 合服系统设计 - 安全可靠的数据迁移

## 📋 概述

本文档详细设计合服系统的实现方案，包括数据迁移、冲突解决、补偿机制和回滚策略等核心功能。

## 🔄 合服主流程设计

### **1. 合服管理服务**

```typescript
// apps/auth/src/domain/server-merge/ (新增模块)
├── server-merge.module.ts
├── services/
│   ├── server-merge.service.ts              # 合服主服务
│   ├── data-migration.service.ts            # 数据迁移服务
│   ├── conflict-resolution.service.ts       # 冲突解决服务
│   ├── compensation.service.ts              # 补偿服务
│   └── merge-rollback.service.ts            # 回滚服务
├── controllers/
│   └── server-merge.controller.ts           # 合服控制器
├── entities/
│   ├── server-merge-task.entity.ts          # 合服任务实体
│   ├── merge-conflict.entity.ts             # 合服冲突实体
│   └── merge-compensation.entity.ts         # 合服补偿实体
└── dto/
    ├── server-merge-request.dto.ts
    ├── merge-progress.dto.ts
    └── merge-result.dto.ts

// 合服主服务
@Injectable()
export class ServerMergeService {
  private readonly logger = new Logger(ServerMergeService.name);
  private readonly activeMergeTasks = new Map<string, MergeTask>();

  constructor(
    private readonly dataMigrationService: DataMigrationService,
    private readonly conflictResolutionService: ConflictResolutionService,
    private readonly compensationService: CompensationService,
    private readonly rollbackService: MergeRollbackService,
    private readonly microserviceClient: ServerAwareMicroserviceClient,
    private readonly redisService: RedisService,
    private readonly serverRepository: ServerRepository,
    private readonly mergeTaskRepository: MergeTaskRepository
  ) {}

  // 启动合服操作
  async startServerMerge(request: ServerMergeRequest): Promise<MergeTask> {
    const { sourceServerIds, targetServerId, strategy, operatorId } = request;
    
    this.logger.log(`启动合服操作: ${sourceServerIds.join(',')} -> ${targetServerId}`);

    try {
      // 1. 预检查
      await this.validateMergeRequest(sourceServerIds, targetServerId);

      // 2. 创建合服任务
      const mergeTask = await this.createMergeTask({
        sourceServerIds,
        targetServerId,
        strategy,
        operatorId,
      });

      // 3. 设置服务器状态
      await this.setServersStatus([...sourceServerIds, targetServerId], 'merging');

      // 4. 启动异步合服流程
      this.executeMergeAsync(mergeTask.id);

      return mergeTask;

    } catch (error) {
      this.logger.error(`启动合服操作失败: ${error.message}`);
      throw error;
    }
  }

  // 异步执行合服流程
  private async executeMergeAsync(taskId: string): Promise<void> {
    const task = await this.mergeTaskRepository.findById(taskId);
    if (!task) {
      this.logger.error(`合服任务不存在: ${taskId}`);
      return;
    }

    try {
      // 更新任务状态
      await this.updateTaskStatus(taskId, 'running');

      // 阶段1: 数据备份
      await this.updateTaskProgress(taskId, 'backup', 10);
      const backupInfo = await this.createDataBackups(task.sourceServerIds, task.targetServerId);

      // 阶段2: 数据迁移
      await this.updateTaskProgress(taskId, 'migration', 30);
      const migrationResult = await this.dataMigrationService.migrateAllData(
        task.sourceServerIds,
        task.targetServerId,
        task.strategy
      );

      // 阶段3: 冲突解决
      await this.updateTaskProgress(taskId, 'conflict_resolution', 60);
      const conflictResolution = await this.conflictResolutionService.resolveAllConflicts(
        migrationResult.conflicts,
        task.targetServerId
      );

      // 阶段4: 补偿发放
      await this.updateTaskProgress(taskId, 'compensation', 80);
      await this.compensationService.distributeAllCompensation(
        migrationResult.affectedPlayers,
        task.targetServerId,
        conflictResolution
      );

      // 阶段5: 数据验证
      await this.updateTaskProgress(taskId, 'validation', 90);
      await this.validateMergeResult(task.targetServerId, migrationResult);

      // 阶段6: 完成合服
      await this.updateTaskProgress(taskId, 'finalizing', 95);
      await this.finalizeMerge(task);

      // 更新任务状态为完成
      await this.updateTaskStatus(taskId, 'completed', {
        migratedPlayers: migrationResult.totalMigrated,
        resolvedConflicts: conflictResolution.totalResolved,
        compensationDistributed: migrationResult.affectedPlayers.length,
        backupInfo,
      });

      this.logger.log(`合服操作完成: ${taskId}`);

    } catch (error) {
      this.logger.error(`合服操作失败: ${taskId}, ${error.message}`);

      // 更新任务状态为失败
      await this.updateTaskStatus(taskId, 'failed', { error: error.message });

      // 尝试回滚
      try {
        await this.rollbackService.rollbackMerge(taskId);
      } catch (rollbackError) {
        this.logger.error(`合服回滚失败: ${taskId}, ${rollbackError.message}`);
      }
    }
  }

  // 获取合服进度
  async getMergeProgress(taskId: string): Promise<MergeProgress> {
    const task = await this.mergeTaskRepository.findById(taskId);
    
    if (!task) {
      throw new NotFoundException('合服任务不存在');
    }

    return {
      taskId: task.id,
      status: task.status,
      progress: task.progress,
      currentStage: task.currentStage,
      startTime: task.startTime,
      estimatedEndTime: this.calculateEstimatedEndTime(task),
      details: task.details,
    };
  }

  // 取消合服操作
  async cancelMerge(taskId: string, operatorId: string): Promise<void> {
    const task = await this.mergeTaskRepository.findById(taskId);
    
    if (!task) {
      throw new NotFoundException('合服任务不存在');
    }

    if (task.status === 'completed') {
      throw new BadRequestException('合服已完成，无法取消');
    }

    if (task.status === 'running' && task.progress > 50) {
      throw new BadRequestException('合服进度过半，无法取消');
    }

    this.logger.log(`取消合服操作: ${taskId}, 操作员: ${operatorId}`);

    // 更新任务状态
    await this.updateTaskStatus(taskId, 'cancelled', { cancelledBy: operatorId });

    // 执行回滚
    await this.rollbackService.rollbackMerge(taskId);

    // 恢复服务器状态
    await this.setServersStatus([...task.sourceServerIds, task.targetServerId], 'active');
  }

  // 私有方法：验证合服请求
  private async validateMergeRequest(sourceServerIds: string[], targetServerId: string): Promise<void> {
    // 验证源服务器
    for (const serverId of sourceServerIds) {
      const server = await this.serverRepository.findById(serverId);
      if (!server) {
        throw new NotFoundException(`源服务器不存在: ${serverId}`);
      }
      if (server.status !== 'active') {
        throw new BadRequestException(`源服务器状态异常: ${serverId} (${server.status})`);
      }
    }

    // 验证目标服务器
    const targetServer = await this.serverRepository.findById(targetServerId);
    if (!targetServer) {
      throw new NotFoundException(`目标服务器不存在: ${targetServerId}`);
    }
    if (targetServer.status !== 'active') {
      throw new BadRequestException(`目标服务器状态异常: ${targetServerId} (${targetServer.status})`);
    }

    // 验证容量
    const totalPlayers = await this.calculateTotalPlayers(sourceServerIds);
    const targetCapacity = targetServer.maxPlayerCount - targetServer.currentPlayerCount;
    
    if (totalPlayers > targetCapacity) {
      throw new BadRequestException(`目标服务器容量不足: 需要 ${totalPlayers}, 可用 ${targetCapacity}`);
    }

    // 检查是否有正在进行的合服任务
    const activeTasks = await this.mergeTaskRepository.findActiveTasks([...sourceServerIds, targetServerId]);
    if (activeTasks.length > 0) {
      throw new BadRequestException('相关服务器存在正在进行的合服任务');
    }
  }

  // 私有方法：创建合服任务
  private async createMergeTask(data: CreateMergeTaskData): Promise<MergeTask> {
    const task = await this.mergeTaskRepository.create({
      sourceServerIds: data.sourceServerIds,
      targetServerId: data.targetServerId,
      strategy: data.strategy,
      operatorId: data.operatorId,
      status: 'pending',
      progress: 0,
      currentStage: 'initializing',
      startTime: new Date(),
    });

    this.activeMergeTasks.set(task.id, task);
    return task;
  }

  // 私有方法：更新任务状态
  private async updateTaskStatus(taskId: string, status: MergeStatus, details?: any): Promise<void> {
    await this.mergeTaskRepository.updateById(taskId, {
      status,
      ...(details && { details }),
      ...(status === 'completed' && { endTime: new Date() }),
      ...(status === 'failed' && { endTime: new Date() }),
      ...(status === 'cancelled' && { endTime: new Date() }),
    });

    // 发布状态更新事件
    await this.publishMergeEvent(taskId, 'status_updated', { status, details });
  }

  // 私有方法：更新任务进度
  private async updateTaskProgress(taskId: string, stage: string, progress: number): Promise<void> {
    await this.mergeTaskRepository.updateById(taskId, {
      currentStage: stage,
      progress,
      lastUpdated: new Date(),
    });

    // 发布进度更新事件
    await this.publishMergeEvent(taskId, 'progress_updated', { stage, progress });
  }

  // 私有方法：设置服务器状态
  private async setServersStatus(serverIds: string[], status: ServerStatus): Promise<void> {
    const updatePromises = serverIds.map(serverId =>
      this.serverRepository.updateById(serverId, { status })
    );

    await Promise.all(updatePromises);

    // 通知所有在线玩家
    await this.notifyPlayersAboutServerStatus(serverIds, status);
  }

  // 私有方法：创建数据备份
  private async createDataBackups(sourceServerIds: string[], targetServerId: string): Promise<BackupInfo> {
    const backupInfo: BackupInfo = {
      backupId: this.generateBackupId(),
      timestamp: new Date(),
      servers: {},
    };

    // 备份源服务器数据
    for (const serverId of sourceServerIds) {
      const backup = await this.createServerBackup(serverId);
      backupInfo.servers[serverId] = backup;
    }

    // 备份目标服务器数据
    const targetBackup = await this.createServerBackup(targetServerId);
    backupInfo.servers[targetServerId] = targetBackup;

    return backupInfo;
  }

  // 私有方法：创建服务器备份
  private async createServerBackup(serverId: string): Promise<ServerBackup> {
    const backupPath = `backups/${serverId}_${Date.now()}`;
    
    // 调用各微服务创建备份
    const backupPromises = [
      this.microserviceClient.callCrossServer('character', 'createBackup', { serverId, backupPath }, serverId),
      this.microserviceClient.callCrossServer('hero', 'createBackup', { serverId, backupPath }, serverId),
      this.microserviceClient.callCrossServer('economy', 'createBackup', { serverId, backupPath }, serverId),
      this.microserviceClient.callCrossServer('social', 'createBackup', { serverId, backupPath }, serverId),
      this.microserviceClient.callCrossServer('activity', 'createBackup', { serverId, backupPath }, serverId),
      this.microserviceClient.callCrossServer('match', 'createBackup', { serverId, backupPath }, serverId),
    ];

    const backupResults = await Promise.allSettled(backupPromises);
    
    return {
      serverId,
      backupPath,
      timestamp: new Date(),
      services: backupResults.map((result, index) => ({
        service: ['character', 'hero', 'economy', 'social', 'activity', 'match'][index],
        status: result.status,
        ...(result.status === 'fulfilled' && { data: result.value }),
        ...(result.status === 'rejected' && { error: result.reason.message }),
      })),
    };
  }

  // 私有方法：完成合服
  private async finalizeMerge(task: MergeTask): Promise<void> {
    // 设置目标服务器为活跃状态
    await this.setServersStatus([task.targetServerId], 'active');

    // 关闭源服务器
    await this.setServersStatus(task.sourceServerIds, 'closed');

    // 更新服务器玩家数量
    await this.updateServerPlayerCounts(task);

    // 发布合服完成事件
    await this.publishMergeEvent(task.id, 'merge_completed', {
      sourceServerIds: task.sourceServerIds,
      targetServerId: task.targetServerId,
      timestamp: new Date(),
    });

    // 清理活跃任务
    this.activeMergeTasks.delete(task.id);
  }

  // 私有方法：验证合服结果
  private async validateMergeResult(targetServerId: string, migrationResult: MigrationResult): Promise<void> {
    // 验证数据完整性
    const validationPromises = [
      this.microserviceClient.callCrossServer('character', 'validateData', { serverId: targetServerId }, targetServerId),
      this.microserviceClient.callCrossServer('hero', 'validateData', { serverId: targetServerId }, targetServerId),
      this.microserviceClient.callCrossServer('economy', 'validateData', { serverId: targetServerId }, targetServerId),
    ];

    const validationResults = await Promise.allSettled(validationPromises);
    
    const failures = validationResults.filter(result => result.status === 'rejected');
    if (failures.length > 0) {
      throw new Error(`数据验证失败: ${failures.map(f => f.reason.message).join(', ')}`);
    }

    this.logger.log(`合服数据验证通过: ${targetServerId}`);
  }

  // 私有方法：发布合服事件
  private async publishMergeEvent(taskId: string, event: string, data: any): Promise<void> {
    const message = {
      taskId,
      event,
      data,
      timestamp: new Date(),
    };

    await this.redisService.publish('server_merge:events', JSON.stringify(message));
  }

  // 私有方法：通知玩家服务器状态变更
  private async notifyPlayersAboutServerStatus(serverIds: string[], status: ServerStatus): Promise<void> {
    const notifications = serverIds.map(serverId =>
      this.microserviceClient.callCrossServer(
        'gateway',
        'broadcastToServer',
        {
          serverId,
          event: 'server_status_changed',
          data: { status, timestamp: new Date() },
        },
        serverId
      )
    );

    await Promise.allSettled(notifications);
  }

  private generateBackupId(): string {
    return `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateEstimatedEndTime(task: MergeTask): Date {
    const elapsed = Date.now() - task.startTime.getTime();
    const estimatedTotal = task.progress > 0 ? (elapsed / task.progress) * 100 : elapsed * 10;
    return new Date(task.startTime.getTime() + estimatedTotal);
  }

  private async calculateTotalPlayers(serverIds: string[]): Promise<number> {
    let total = 0;
    for (const serverId of serverIds) {
      const server = await this.serverRepository.findById(serverId);
      total += server?.currentPlayerCount || 0;
    }
    return total;
  }

  private async updateServerPlayerCounts(task: MergeTask): Promise<void> {
    // 计算迁移后的玩家数量
    const totalPlayers = await this.calculateTotalPlayers(task.sourceServerIds);
    
    // 更新目标服务器玩家数量
    const targetServer = await this.serverRepository.findById(task.targetServerId);
    await this.serverRepository.updateById(task.targetServerId, {
      currentPlayerCount: targetServer.currentPlayerCount + totalPlayers,
    });

    // 清零源服务器玩家数量
    const updatePromises = task.sourceServerIds.map(serverId =>
      this.serverRepository.updateById(serverId, { currentPlayerCount: 0 })
    );
    
    await Promise.all(updatePromises);
  }
}
```

### **2. 数据迁移服务**

```typescript
// 数据迁移服务
@Injectable()
export class DataMigrationService {
  private readonly logger = new Logger(DataMigrationService.name);

  constructor(
    private readonly microserviceClient: ServerAwareMicroserviceClient,
    private readonly conflictDetector: ConflictDetectorService
  ) {}

  // 迁移所有数据
  async migrateAllData(
    sourceServerIds: string[],
    targetServerId: string,
    strategy: MergeStrategy
  ): Promise<MigrationResult> {
    const result: MigrationResult = {
      totalMigrated: 0,
      conflicts: [],
      affectedPlayers: [],
      serviceResults: {},
    };

    this.logger.log(`开始数据迁移: ${sourceServerIds.join(',')} -> ${targetServerId}`);

    try {
      // 按顺序迁移各个服务的数据
      const services = ['character', 'hero', 'economy', 'social', 'activity', 'match'];

      for (const service of services) {
        this.logger.log(`迁移 ${service} 服务数据`);
        
        const serviceResult = await this.migrateServiceData(
          service,
          sourceServerIds,
          targetServerId,
          strategy
        );

        result.serviceResults[service] = serviceResult;
        result.totalMigrated += serviceResult.migratedCount;
        result.conflicts.push(...serviceResult.conflicts);
        
        // 合并受影响的玩家列表
        serviceResult.affectedPlayers.forEach(player => {
          if (!result.affectedPlayers.find(p => p.accountId === player.accountId)) {
            result.affectedPlayers.push(player);
          }
        });
      }

      this.logger.log(`数据迁移完成: 总计迁移 ${result.totalMigrated} 条记录, 冲突 ${result.conflicts.length} 个`);
      
      return result;

    } catch (error) {
      this.logger.error(`数据迁移失败: ${error.message}`);
      throw error;
    }
  }

  // 迁移单个服务的数据
  private async migrateServiceData(
    serviceName: string,
    sourceServerIds: string[],
    targetServerId: string,
    strategy: MergeStrategy
  ): Promise<ServiceMigrationResult> {
    const result: ServiceMigrationResult = {
      serviceName,
      migratedCount: 0,
      conflicts: [],
      affectedPlayers: [],
      errors: [],
    };

    for (const sourceServerId of sourceServerIds) {
      try {
        this.logger.log(`迁移 ${serviceName} 服务数据: ${sourceServerId} -> ${targetServerId}`);

        // 调用微服务的迁移接口
        const migrationResult = await this.microserviceClient.callCrossServer(
          serviceName as MicroserviceName,
          'migrateServerData',
          {
            sourceServerId,
            targetServerId,
            strategy,
          },
          sourceServerId
        );

        result.migratedCount += migrationResult.migratedCount || 0;
        result.conflicts.push(...(migrationResult.conflicts || []));
        result.affectedPlayers.push(...(migrationResult.affectedPlayers || []));

      } catch (error) {
        this.logger.error(`迁移 ${serviceName} 服务数据失败: ${sourceServerId}, ${error.message}`);
        result.errors.push({
          sourceServerId,
          error: error.message,
        });
      }
    }

    return result;
  }

  // 验证迁移结果
  async validateMigration(
    targetServerId: string,
    expectedCounts: Record<string, number>
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      actualCounts: {},
    };

    for (const [service, expectedCount] of Object.entries(expectedCounts)) {
      try {
        const actualCount = await this.microserviceClient.callCrossServer(
          service as MicroserviceName,
          'getDataCount',
          { serverId: targetServerId },
          targetServerId
        );

        result.actualCounts[service] = actualCount;

        if (actualCount !== expectedCount) {
          result.valid = false;
          result.errors.push({
            service,
            expected: expectedCount,
            actual: actualCount,
            message: `数据数量不匹配`,
          });
        }

      } catch (error) {
        result.valid = false;
        result.errors.push({
          service,
          error: error.message,
        });
      }
    }

    return result;
  }
}
```

### **3. 冲突解决服务**

```typescript
// 冲突解决服务
@Injectable()
export class ConflictResolutionService {
  private readonly logger = new Logger(ConflictResolutionService.name);

  constructor(
    private readonly microserviceClient: ServerAwareMicroserviceClient,
    private readonly conflictRepository: MergeConflictRepository
  ) {}

  // 解决所有冲突
  async resolveAllConflicts(
    conflicts: DataConflict[],
    targetServerId: string
  ): Promise<ConflictResolutionResult> {
    const result: ConflictResolutionResult = {
      totalResolved: 0,
      totalFailed: 0,
      resolutions: [],
      failures: [],
    };

    this.logger.log(`开始解决数据冲突: ${conflicts.length} 个冲突`);

    // 按类型分组处理冲突
    const conflictGroups = this.groupConflictsByType(conflicts);

    for (const [type, typeConflicts] of Object.entries(conflictGroups)) {
      this.logger.log(`处理 ${type} 类型冲突: ${typeConflicts.length} 个`);

      for (const conflict of typeConflicts) {
        try {
          const resolution = await this.resolveConflict(conflict, targetServerId);
          
          result.resolutions.push(resolution);
          result.totalResolved++;

          // 保存冲突解决记录
          await this.conflictRepository.create({
            conflictId: conflict.id,
            type: conflict.type,
            resolution: resolution.strategy,
            details: resolution.details,
            resolvedAt: new Date(),
          });

        } catch (error) {
          this.logger.error(`解决冲突失败: ${conflict.id}, ${error.message}`);
          
          result.failures.push({
            conflict,
            error: error.message,
          });
          result.totalFailed++;
        }
      }
    }

    this.logger.log(`冲突解决完成: 成功 ${result.totalResolved}, 失败 ${result.totalFailed}`);
    
    return result;
  }

  // 解决单个冲突
  private async resolveConflict(conflict: DataConflict, targetServerId: string): Promise<ConflictResolution> {
    switch (conflict.type) {
      case 'character_name_duplicate':
        return this.resolveCharacterNameConflict(conflict, targetServerId);
      
      case 'guild_name_duplicate':
        return this.resolveGuildNameConflict(conflict, targetServerId);
      
      case 'ranking_overlap':
        return this.resolveRankingConflict(conflict, targetServerId);
      
      case 'economy_duplicate':
        return this.resolveEconomyConflict(conflict, targetServerId);
      
      default:
        throw new Error(`未知的冲突类型: ${conflict.type}`);
    }
  }

  // 解决角色名称冲突
  private async resolveCharacterNameConflict(
    conflict: DataConflict,
    targetServerId: string
  ): Promise<ConflictResolution> {
    const { sourceData, targetData } = conflict;
    
    // 策略：为源服务器角色添加服务器后缀
    const newName = `${sourceData.name}_S${sourceData.serverId}`;
    
    // 调用角色服务更新名称
    await this.microserviceClient.callCrossServer(
      'character',
      'updateCharacterName',
      {
        characterId: sourceData.characterId,
        newName,
        reason: 'merge_conflict_resolution',
      },
      targetServerId
    );

    return {
      conflictId: conflict.id,
      strategy: 'rename_source',
      details: {
        originalName: sourceData.name,
        newName,
        characterId: sourceData.characterId,
      },
      timestamp: new Date(),
    };
  }

  // 解决公会名称冲突
  private async resolveGuildNameConflict(
    conflict: DataConflict,
    targetServerId: string
  ): Promise<ConflictResolution> {
    const { sourceData, targetData } = conflict;
    
    // 策略：解散源服务器公会，给成员补偿
    await this.microserviceClient.callCrossServer(
      'social',
      'dissolveGuild',
      {
        guildId: sourceData.guildId,
        reason: 'merge_conflict_resolution',
        compensation: true,
      },
      targetServerId
    );

    return {
      conflictId: conflict.id,
      strategy: 'dissolve_source_guild',
      details: {
        dissolvedGuildId: sourceData.guildId,
        dissolvedGuildName: sourceData.name,
        memberCount: sourceData.memberCount,
      },
      timestamp: new Date(),
    };
  }

  // 解决排名冲突
  private async resolveRankingConflict(
    conflict: DataConflict,
    targetServerId: string
  ): Promise<ConflictResolution> {
    // 策略：重新计算全服排名
    await this.microserviceClient.callCrossServer(
      'character',
      'recalculateRankings',
      {
        serverId: targetServerId,
        reason: 'merge_conflict_resolution',
      },
      targetServerId
    );

    return {
      conflictId: conflict.id,
      strategy: 'recalculate_rankings',
      details: {
        affectedCategories: conflict.details.categories,
      },
      timestamp: new Date(),
    };
  }

  // 解决经济冲突
  private async resolveEconomyConflict(
    conflict: DataConflict,
    targetServerId: string
  ): Promise<ConflictResolution> {
    const { sourceData, targetData } = conflict;
    
    // 策略：合并经济数据，保留较高值
    const mergedData = {
      gold: Math.max(sourceData.gold, targetData.gold),
      diamond: Math.max(sourceData.diamond, targetData.diamond),
      items: [...sourceData.items, ...targetData.items],
    };

    await this.microserviceClient.callCrossServer(
      'economy',
      'mergePlayerEconomy',
      {
        playerId: sourceData.playerId,
        mergedData,
        reason: 'merge_conflict_resolution',
      },
      targetServerId
    );

    return {
      conflictId: conflict.id,
      strategy: 'merge_economy_data',
      details: {
        playerId: sourceData.playerId,
        mergedData,
      },
      timestamp: new Date(),
    };
  }

  // 按类型分组冲突
  private groupConflictsByType(conflicts: DataConflict[]): Record<string, DataConflict[]> {
    const groups: Record<string, DataConflict[]> = {};
    
    conflicts.forEach(conflict => {
      if (!groups[conflict.type]) {
        groups[conflict.type] = [];
      }
      groups[conflict.type].push(conflict);
    });

    return groups;
  }
}
```

### **4. 补偿服务**

```typescript
// 补偿服务
@Injectable()
export class CompensationService {
  private readonly logger = new Logger(CompensationService.name);

  constructor(
    private readonly microserviceClient: ServerAwareMicroserviceClient,
    private readonly compensationRepository: MergeCompensationRepository
  ) {}

  // 发放所有补偿
  async distributeAllCompensation(
    affectedPlayers: AffectedPlayer[],
    targetServerId: string,
    conflictResolution: ConflictResolutionResult
  ): Promise<CompensationResult> {
    const result: CompensationResult = {
      totalDistributed: 0,
      totalFailed: 0,
      compensations: [],
      failures: [],
    };

    this.logger.log(`开始发放合服补偿: ${affectedPlayers.length} 个玩家`);

    for (const player of affectedPlayers) {
      try {
        const compensation = await this.calculatePlayerCompensation(player, conflictResolution);
        
        if (compensation.items.length > 0 || Object.keys(compensation.currency).length > 0) {
          await this.distributePlayerCompensation(player.accountId, compensation, targetServerId);
          
          result.compensations.push({
            accountId: player.accountId,
            compensation,
          });
          result.totalDistributed++;
        }

      } catch (error) {
        this.logger.error(`发放补偿失败: ${player.accountId}, ${error.message}`);
        
        result.failures.push({
          accountId: player.accountId,
          error: error.message,
        });
        result.totalFailed++;
      }
    }

    this.logger.log(`补偿发放完成: 成功 ${result.totalDistributed}, 失败 ${result.totalFailed}`);
    
    return result;
  }

  // 计算玩家补偿
  private async calculatePlayerCompensation(
    player: AffectedPlayer,
    conflictResolution: ConflictResolutionResult
  ): Promise<CompensationPackage> {
    const compensation: CompensationPackage = {
      accountId: player.accountId,
      items: [],
      currency: {},
      titles: [],
      reason: 'server_merge',
      calculatedAt: new Date(),
    };

    // 基础合服补偿
    compensation.currency.gold = 100000; // 10万金币
    compensation.items.push({
      itemId: 'merge_commemoration_gift',
      quantity: 1,
      reason: 'merge_participation',
    });

    // 根据玩家等级给予补偿
    if (player.level >= 50) {
      compensation.currency.diamond = 1000; // 高级玩家额外钻石
      compensation.items.push({
        itemId: 'senior_player_gift',
        quantity: 1,
        reason: 'high_level_compensation',
      });
    }

    // 根据冲突解决情况给予额外补偿
    const playerConflicts = conflictResolution.resolutions.filter(r => 
      r.details.characterId === player.characterId || 
      r.details.playerId === player.accountId
    );

    for (const resolution of playerConflicts) {
      switch (resolution.strategy) {
        case 'rename_source':
          compensation.items.push({
            itemId: 'name_change_card',
            quantity: 3,
            reason: 'name_conflict_compensation',
          });
          break;

        case 'dissolve_source_guild':
          compensation.currency.gold = (compensation.currency.gold || 0) + 200000;
          compensation.items.push({
            itemId: 'guild_creation_token',
            quantity: 1,
            reason: 'guild_dissolution_compensation',
          });
          break;

        case 'merge_economy_data':
          compensation.items.push({
            itemId: 'economy_merge_bonus',
            quantity: 1,
            reason: 'economy_merge_compensation',
          });
          break;
      }
    }

    // 特殊称号
    compensation.titles.push('merge_veteran');

    return compensation;
  }

  // 发放玩家补偿
  private async distributePlayerCompensation(
    accountId: string,
    compensation: CompensationPackage,
    targetServerId: string
  ): Promise<void> {
    // 发放货币
    for (const [currencyType, amount] of Object.entries(compensation.currency)) {
      await this.microserviceClient.callCrossServer(
        'economy',
        'addCurrency',
        {
          accountId,
          currencyType,
          amount,
          reason: 'server_merge_compensation',
        },
        targetServerId
      );
    }

    // 发放物品
    for (const item of compensation.items) {
      await this.microserviceClient.callCrossServer(
        'economy',
        'addItem',
        {
          accountId,
          itemId: item.itemId,
          quantity: item.quantity,
          reason: item.reason,
        },
        targetServerId
      );
    }

    // 发放称号
    for (const titleId of compensation.titles) {
      await this.microserviceClient.callCrossServer(
        'character',
        'grantTitle',
        {
          accountId,
          titleId,
          reason: 'server_merge_compensation',
        },
        targetServerId
      );
    }

    // 记录补偿日志
    await this.compensationRepository.create({
      accountId,
      compensationType: 'server_merge',
      compensationData: compensation,
      distributedAt: new Date(),
      serverId: targetServerId,
    });

    this.logger.log(`补偿发放完成: ${accountId}`);
  }
}
```

---

> **核心优势**：
> - 🔄 **分阶段执行**：将复杂的合服流程分解为可控的阶段
> - 🛡️ **安全备份**：完整的数据备份和回滚机制
> - ⚖️ **智能冲突解决**：自动化的数据冲突检测和解决
> - 🎁 **公平补偿**：基于影响程度的智能补偿计算
> - 📊 **实时监控**：完整的进度跟踪和状态监控
