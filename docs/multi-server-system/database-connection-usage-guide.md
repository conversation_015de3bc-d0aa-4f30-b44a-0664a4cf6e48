# 数据库连接方案V2.0使用指南

## 📋 概述

本指南详细说明如何使用新的数据库连接方案V2.0，该方案基于正确的分区分服架构理解，实现了简单、高效、易维护的数据库连接管理。

## 🎯 核心特性

### ✅ 已实现功能
- **配置自动生成**：一键生成区服配置文件
- **Docker Compose支持**：自动生成容器编排配置
- **数据库初始化**：自动生成数据库创建脚本
- **健康检查集成**：标准化的健康检查接口
- **Redis前缀隔离**：保持现有Redis分区分服机制
- **跨平台支持**：JavaScript实现，支持Windows和Linux

### ✅ 架构优势
- **简单直接**：每个服务实例固定连接对应数据库
- **配置驱动**：通过环境变量控制所有配置
- **易于维护**：标准化的配置模板和部署流程
- **向后兼容**：保持现有数据库配置的兼容性

## 🔧 工具使用

### 1. 配置生成器

#### 基本用法
```bash
# 生成单个服务的环境配置
node tools/config-generator/server-config-generator.js env --server=server001 --service=character

# 生成Docker Compose服务配置
node tools/config-generator/server-config-generator.js docker-compose --server=server001 --service=character --instance=1

# 生成完整的Docker Compose文件
node tools/config-generator/server-config-generator.js docker-compose-full --server=server001 --services character hero economy

# 生成数据库初始化脚本
node tools/config-generator/server-config-generator.js database-init --server=server001

# 一键生成区服完整配置
node tools/config-generator/server-config-generator.js full-server --server=server001 --services character hero economy
```

#### 支持的服务
- `character` - 角色管理服务（2个实例）
- `hero` - 英雄管理服务（2个实例）
- `economy` - 经济系统服务（1个实例）
- `social` - 社交系统服务（1个实例）
- `activity` - 活动系统服务（1个实例）
- `match` - 比赛系统服务（2个实例）

### 2. 部署脚本

#### 简化部署脚本（推荐）
```bash
# 部署完整区服（所有服务）
node scripts/deploy-server-simple.js server001

# 部署指定服务
node scripts/deploy-server-simple.js server001 character,hero,economy

# 查看帮助
node scripts/deploy-server-simple.js
```

#### 配置测试脚本
```bash
# 测试配置生成（不需要Docker）
node scripts/test-config-generation.js test server001

# 测试指定服务
node scripts/test-config-generation.js test server001 character,hero

# 查看生成的配置内容
node scripts/test-config-generation.js show server001 character

# 清理测试文件
node scripts/test-config-generation.js cleanup server001
```

## 📁 生成的文件结构

### 配置文件
```
config/
└── server001/
    ├── .env.character      # Character服务配置
    ├── .env.hero          # Hero服务配置
    ├── .env.economy       # Economy服务配置
    ├── .env.social        # Social服务配置
    ├── .env.activity      # Activity服务配置
    └── .env.match         # Match服务配置
```

### 部署文件
```
docker-compose.server001.yml           # Docker Compose配置
scripts/init-server001-databases.sh    # 数据库初始化脚本
scripts/deploy-server001.sh           # 部署脚本（如果生成）
```

## ⚙️ 配置文件详解

### 环境配置文件示例
```bash
# 角色管理服务 - server001 配置
# 自动生成于: 2024-01-01T00:00:00.000Z

# 基础配置
NODE_ENV=production
SERVICE_NAME=character
SERVER_ID=server001
PORT=3002

# 数据库配置
CHARACTER_MONGODB_URI=*******************************************************************************

# Redis配置（保持现有前缀机制）
REDIS_HOST=***************
REDIS_PORT=6379
# Redis前缀通过RedisModule自动处理，格式：{环境}:{项目}:server{serverId}:{服务}:

# 微服务配置
MICROSERVICE_CLIENT_ID=character-server001
MICROSERVICE_CLIENT_SECRET=${MICROSERVICE_CLIENT_SECRET}

# 网关配置
GATEWAY_URL=http://gateway:3000

# 监控配置
METRICS_ENABLED=true
METRICS_PORT=9102

# 健康检查配置
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
```

### Docker Compose配置示例
```yaml
version: '3.8'

networks:
  microservices:
    external: true

services:
  character-server001-1:
    build:
      context: .
      dockerfile: apps/character/Dockerfile
    container_name: character-server001-1
    env_file:
      - config/server001/.env.character
    environment:
      - INSTANCE_INDEX=1
      - PORT=3002
      - METRICS_PORT=9102
    ports:
      - "3002:3002"
      - "9102:9102"
    depends_on:
      - mongodb
      - redis
    networks:
      - microservices
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "service=character"
      - "server=server001"
      - "instance=1"

  character-server001-2:
    # 第二个实例配置...
```

## 🗄️ 数据库配置

### 数据库命名规则
- **Auth服务**：`auth_db`（全局唯一，不分区服）
- **其他服务**：`{service}_db_{serverId}`

### 数据库连接示例
```
# 区服1
character_db_server001
hero_db_server001
economy_db_server001
social_db_server001
activity_db_server001
match_db_server001

# 区服2
character_db_server002
hero_db_server002
economy_db_server002
social_db_server002
activity_db_server002
match_db_server002
```

### 数据库用户权限
每个服务都有独立的数据库用户：
- 用户名：`{service}-admin`
- 密码：`password`（生产环境请修改）
- 权限：`readWrite` + `dbAdmin`

## 🚀 部署流程

### 1. 准备环境
```bash
# 确保必要工具已安装
node --version
docker --version
docker-compose --version
```

### 2. 生成配置
```bash
# 为新区服生成完整配置
node tools/config-generator/server-config-generator.js full-server --server=server001
```

### 3. 初始化数据库
```bash
# 执行数据库初始化脚本
bash scripts/init-server001-databases.sh
```

### 4. 部署服务
```bash
# 启动服务实例
docker-compose -f docker-compose.server001.yml up -d
```

### 5. 验证部署
```bash
# 查看容器状态
docker-compose -f docker-compose.server001.yml ps

# 检查健康状态
curl http://localhost:3002/health  # character-server001-1
curl http://localhost:3012/health  # character-server001-2
curl http://localhost:3003/health  # hero-server001-1
curl http://localhost:3013/health  # hero-server001-2
```

## 🔧 管理命令

### 服务管理
```bash
# 查看服务状态
docker-compose -f docker-compose.server001.yml ps

# 查看服务日志
docker-compose -f docker-compose.server001.yml logs -f

# 重启服务
docker-compose -f docker-compose.server001.yml restart

# 停止服务
docker-compose -f docker-compose.server001.yml down

# 更新服务
docker-compose -f docker-compose.server001.yml pull
docker-compose -f docker-compose.server001.yml up -d
```

### 健康检查
```bash
# 简单健康检查
curl http://localhost:3002/health/simple

# 详细健康检查
curl http://localhost:3002/health/detailed

# 服务信息
curl http://localhost:3002/health/info
```

## 🏥 监控和调试

### 健康检查端点
- `/health` - 完整健康检查
- `/health/simple` - 简单健康检查（用于负载均衡器）
- `/health/detailed` - 详细健康信息
- `/health/ready` - 就绪检查（用于Kubernetes）
- `/health/live` - 存活检查（用于Kubernetes）
- `/health/info` - 服务信息

### 监控指标
每个服务实例都暴露Prometheus指标：
- Character服务：`http://localhost:9102/metrics`
- Hero服务：`http://localhost:9103/metrics`
- Economy服务：`http://localhost:9104/metrics`

### 日志查看
```bash
# 查看特定服务日志
docker-compose -f docker-compose.server001.yml logs character-server001-1

# 实时跟踪日志
docker-compose -f docker-compose.server001.yml logs -f character-server001-1

# 查看所有服务日志
docker-compose -f docker-compose.server001.yml logs
```

## 🔍 故障排查

### 常见问题

#### 1. 配置生成失败
```bash
# 检查Node.js版本
node --version

# 检查配置生成器
node tools/config-generator/server-config-generator.js --help

# 检查权限
ls -la tools/config-generator/server-config-generator.js
```

#### 2. 数据库连接失败
```bash
# 检查MongoDB连接
mongo --host ***************:27017

# 检查数据库是否存在
mongo --host ***************:27017 --eval "show dbs"

# 检查用户权限
mongo --host ***************:27017 -u character-admin -p password character_db_server001
```

#### 3. 服务启动失败
```bash
# 查看容器日志
docker-compose -f docker-compose.server001.yml logs character-server001-1

# 检查端口占用
netstat -tulpn | grep 3002

# 检查Docker网络
docker network ls
docker network inspect microservices
```

#### 4. 健康检查失败
```bash
# 检查服务是否启动
docker-compose -f docker-compose.server001.yml ps

# 手动健康检查
curl -v http://localhost:3002/health/simple

# 检查服务内部状态
docker exec character-server001-1 curl http://localhost:3002/health
```

## 📝 最佳实践

### 1. 配置管理
- 使用版本控制管理配置模板
- 生产环境使用独立的配置文件
- 定期备份配置文件

### 2. 数据库管理
- 定期备份数据库
- 使用强密码
- 监控数据库性能

### 3. 服务部署
- 使用蓝绿部署策略
- 实施健康检查
- 配置日志轮转

### 4. 监控运维
- 设置告警规则
- 定期检查服务状态
- 监控资源使用情况

---

## 📚 相关文档

- [架构纠正改造计划](./architecture-correction-plan.md)
- [数据库连接方案V2.0](./database-connection-plan-v2.md)
- [Gateway多服务器增强文档](./gateway-multi-server-enhancement.md)

---

*使用指南版本: v1.0*  
*最后更新: 2024-01-01*  
*维护者: 数据库团队*
