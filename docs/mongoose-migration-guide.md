# Mongoose 最佳实践迁移指南

## 📋 概述

本文档提供了将现有项目迁移到Mongoose最佳实践的详细步骤和实施计划，帮助团队安全、高效地完成架构优化。

## 🎯 迁移目标

### 当前问题
- Repository层直接返回Mongoose文档，导致业务层需要手动处理转换
- 缺乏统一的Schema配置，转换逻辑分散在各处
- 性能敏感的查询没有使用lean()优化
- 代码中存在大量重复的`toObject()`调用

### 迁移后的收益
- **性能提升**：列表查询速度提升2-3倍，内存使用减少50-70%
- **代码简化**：消除业务层的手动转换代码
- **架构统一**：所有服务采用一致的数据处理模式
- **类型安全**：更好的TypeScript类型支持

## 📅 迁移计划

### 阶段1：准备阶段（1-2天）
- [ ] 创建Schema配置工厂
- [ ] 建立性能测试基准
- [ ] 准备迁移工具和脚本

### 阶段2：新功能优先（1周）
- [ ] 新增的Repository方法使用最佳实践
- [ ] 新的Schema使用统一配置
- [ ] 建立代码审查检查点

### 阶段3：渐进式优化（2-3周）
- [ ] 优化现有的列表查询
- [ ] 更新Schema配置
- [ ] 重构高频使用的Repository方法

### 阶段4：全面优化（1-2周）
- [ ] 完成所有Repository的优化
- [ ] 性能测试和验证
- [ ] 文档更新和团队培训

## 🛠️ 实施步骤

### 步骤1：创建基础设施

#### 1.1 创建Schema配置工厂

```typescript
// libs/common/src/database/schema-options.factory.ts
export class SchemaOptionsFactory {
  static forUserModel() {
    return {
      toObject: {
        virtuals: true,
        getters: true,
        versionKey: false,
        transform: function(doc: any, ret: any) {
          delete ret.password;
          delete ret.salt;
          return ret;
        }
      },
      toJSON: {
        virtuals: true,
        getters: true,
        versionKey: false,
        transform: function(doc: any, ret: any) {
          ret.id = ret._id;
          delete ret._id;
          delete ret.password;
          delete ret.salt;
          return ret;
        }
      }
    };
  }

  static forGameDataModel() {
    return {
      toObject: {
        virtuals: false,
        getters: false,
        versionKey: false,
        minimize: false
      },
      toJSON: {
        virtuals: false,
        getters: false,
        versionKey: false,
        minimize: false,
        transform: function(doc: any, ret: any) {
          ret.id = ret._id;
          delete ret._id;
          return ret;
        }
      }
    };
  }
}
```

#### 1.2 创建Repository基类

```typescript
// libs/common/src/database/base.repository.ts
export abstract class BaseRepository<T, TDocument extends Document> {
  constructor(protected model: Model<TDocument>) {}

  // 标准查询方法
  async findById(id: string): Promise<TDocument | null> {
    return await this.model.findById(id).exec();
  }

  // lean查询方法
  async findByIdLean(id: string): Promise<T | null> {
    return await this.model.findById(id).lean().exec();
  }

  // 分页查询（默认使用lean）
  async findWithPagination(
    filter: FilterQuery<TDocument> = {},
    options: PaginationOptions
  ): Promise<PaginatedResult<T>> {
    const [data, total] = await Promise.all([
      this.model
        .find(filter)
        .sort(options.sort || { createdAt: -1 })
        .skip((options.page - 1) * options.limit)
        .limit(options.limit)
        .lean()
        .exec(),
      this.model.countDocuments(filter)
    ]);

    return {
      data,
      total,
      page: options.page,
      limit: options.limit,
      pages: Math.ceil(total / options.limit)
    };
  }

  // 列表查询（默认使用lean）
  async findAll(
    filter: FilterQuery<TDocument> = {},
    options: QueryOptions = {}
  ): Promise<T[]> {
    let query = this.model.find(filter);

    if (options.select) query = query.select(options.select);
    if (options.sort) query = query.sort(options.sort);
    if (options.skip) query = query.skip(options.skip);
    if (options.limit) query = query.limit(options.limit);

    return await query.lean().exec();
  }
}
```

### 步骤2：迁移现有Schema

#### 2.1 用户相关Schema迁移

```typescript
// 迁移前
const UserSchema = new Schema({
  name: String,
  email: String,
  password: String
});

// 迁移后
const UserSchema = new Schema({
  name: String,
  email: String,
  password: String
}, SchemaOptionsFactory.forUserModel());

// 添加虚拟属性
UserSchema.virtual('displayName').get(function() {
  return this.name || this.email.split('@')[0];
});
```

#### 2.2 游戏数据Schema迁移

```typescript
// 迁移前
const BattleRoomSchema = new Schema({
  roomUid: String,
  teamA: { /* ... */ },
  teamB: { /* ... */ }
});

// 迁移后
const BattleRoomSchema = new Schema({
  roomUid: String,
  teamA: { /* ... */ },
  teamB: { /* ... */ }
}, SchemaOptionsFactory.forGameDataModel());
```

### 步骤3：迁移Repository层

#### 3.1 扩展现有Repository

```typescript
// 迁移策略：添加新方法，保持向后兼容

// apps/character/src/common/repositories/character.repository.ts
@Injectable()
export class CharacterRepository extends BaseRepository<Character, CharacterDocument> {
  constructor(
    @InjectModel(Character.name) private characterModel: Model<CharacterDocument>,
  ) {
    super(characterModel);
  }

  // 保留现有方法（向后兼容）
  async findById(characterId: string): Promise<CharacterDocument | null> {
    return await this.characterModel.findOne({ characterId }).exec();
  }

  // 新增lean方法
  async findByIdLean(characterId: string): Promise<Character | null> {
    return await this.characterModel.findOne({ characterId }).lean().exec();
  }

  // 新增优化的列表方法
  async findByServerIdLean(serverId: string): Promise<Character[]> {
    return await this.characterModel
      .find({ serverId })
      .select('characterId name level')
      .sort({ level: -1 })
      .lean()
      .exec();
  }

  // 迁移现有方法
  async findByServerId(serverId: string): Promise<Character[]> {
    // 逐步迁移到lean查询
    return await this.findByServerIdLean(serverId);
  }
}
```

#### 3.2 Service层适配

```typescript
// apps/character/src/modules/character/character.service.ts
@Injectable()
export class CharacterService {
  constructor(private characterRepository: CharacterRepository) {}

  // 需要文档操作的方法
  async updateCharacter(characterId: string, updateData: UpdateCharacterDto): Promise<Character> {
    const character = await this.characterRepository.findById(characterId);
    if (!character) {
      throw new NotFoundException('角色不存在');
    }

    // 使用文档方法
    character.set(updateData);
    character.updatedAt = new Date();
    await character.save();

    return character.toObject(); // 返回时转换
  }

  // 只读查询的方法
  async getCharacterProfile(characterId: string): Promise<Character> {
    const character = await this.characterRepository.findByIdLean(characterId);
    if (!character) {
      throw new NotFoundException('角色不存在');
    }
    return character; // 已经是纯对象
  }

  // 列表查询的方法
  async getServerCharacters(serverId: string): Promise<Character[]> {
    return await this.characterRepository.findByServerIdLean(serverId);
  }
}
```

### 步骤4：消除业务层转换代码

#### 4.1 识别需要修复的代码

```bash
# 搜索需要修复的代码模式
grep -r "toObject()" apps/
grep -r "\.toObject" apps/
grep -r "as any.*toObject" apps/
```

#### 4.2 修复战斗引擎

```typescript
// 修复前：apps/match/src/modules/battle/battle-engine.ts
private initializeBattleData(battleRoom: BattleRoom): any {
  const teamA = (battleRoom.teamA as any)?.toObject ? 
    (battleRoom.teamA as any).toObject() : battleRoom.teamA;
  const teamB = (battleRoom.teamB as any)?.toObject ? 
    (battleRoom.teamB as any).toObject() : battleRoom.teamB;
  // ...
}

// 修复后：使用lean查询
@Injectable()
export class BattleEngine {
  async initializeBattle(roomUid: string): Promise<any> {
    // 直接获取纯对象
    const battleRoom = await this.battleRepository.findBattleDataByRoomUid(roomUid);
    if (!battleRoom) {
      throw new NotFoundException('战斗房间不存在');
    }

    // 直接使用，无需转换
    const battleData = {
      roomUid: battleRoom.roomUid,
      battleType: battleRoom.battleType,
      teamA: { ...battleRoom.teamA, score: 0 },
      teamB: { ...battleRoom.teamB, score: 0 },
      // ...
    };

    return battleData;
  }
}
```

## 📊 迁移检查清单

### Schema迁移检查
- [ ] 所有Schema都使用了统一的配置工厂
- [ ] 用户相关Schema配置了安全的transform函数
- [ ] 游戏数据Schema设置了minimize: false
- [ ] 虚拟属性正确配置

### Repository迁移检查
- [ ] 列表查询方法使用lean()
- [ ] 分页查询方法使用lean()
- [ ] 只读查询方法使用lean()
- [ ] 需要文档操作的方法保持普通查询
- [ ] 添加了类型安全的方法签名

### Service层迁移检查
- [ ] 消除了手动toObject()调用
- [ ] 更新操作使用文档查询
- [ ] 只读操作使用lean查询
- [ ] 列表操作使用lean查询

### 性能验证检查
- [ ] 列表查询性能提升2-3倍
- [ ] 内存使用减少50-70%
- [ ] 响应时间明显改善
- [ ] 无功能回归问题

## 🧪 测试策略

### 1. 单元测试更新

```typescript
// 测试lean查询返回的数据格式
describe('CharacterRepository', () => {
  it('should return plain object from lean query', async () => {
    const character = await characterRepository.findByIdLean('test-id');
    
    expect(character).toBeDefined();
    expect(character.save).toBeUndefined(); // 确保是纯对象
    expect(character.characterId).toBe('test-id');
  });

  it('should return document from normal query', async () => {
    const character = await characterRepository.findById('test-id');
    
    expect(character).toBeDefined();
    expect(typeof character.save).toBe('function'); // 确保是文档
    expect(character.characterId).toBe('test-id');
  });
});
```

### 2. 性能测试

```typescript
// 性能对比测试
describe('Performance Tests', () => {
  it('should improve list query performance', async () => {
    const startTime = Date.now();
    
    // 测试lean查询
    const characters = await characterRepository.findByServerIdLean('server-1');
    
    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(100); // 期望在100ms内完成
    expect(characters.length).toBeGreaterThan(0);
  });
});
```

### 3. 集成测试

```typescript
// API响应格式测试
describe('Character API', () => {
  it('should return properly formatted character list', async () => {
    const response = await request(app)
      .get('/api/character/list')
      .expect(200);

    expect(response.body.data).toBeInstanceOf(Array);
    expect(response.body.data[0]).toHaveProperty('id'); // 不是_id
    expect(response.body.data[0]).not.toHaveProperty('password');
  });
});
```

## 🚨 风险控制

### 1. 回滚计划

```typescript
// 保留原有方法作为备份
@Injectable()
export class CharacterRepository {
  // 新方法
  async findByIdLean(characterId: string): Promise<Character | null> {
    return await this.characterModel.findOne({ characterId }).lean().exec();
  }

  // 原有方法（备份）
  async findByIdOriginal(characterId: string): Promise<CharacterDocument | null> {
    return await this.characterModel.findOne({ characterId }).exec();
  }

  // 当前使用的方法（可以快速切换）
  async findById(characterId: string): Promise<Character | null> {
    return await this.findByIdLean(characterId); // 可以快速改为findByIdOriginal
  }
}
```

### 2. 功能开关

```typescript
// 使用配置控制迁移进度
@Injectable()
export class CharacterService {
  constructor(
    private characterRepository: CharacterRepository,
    private configService: ConfigService
  ) {}

  async getCharacters(serverId: string): Promise<Character[]> {
    const useLeanQuery = this.configService.get('USE_LEAN_QUERIES', false);
    
    if (useLeanQuery) {
      return await this.characterRepository.findByServerIdLean(serverId);
    } else {
      const docs = await this.characterRepository.findByServerId(serverId);
      return docs.map(doc => doc.toObject());
    }
  }
}
```

### 3. 监控和告警

```typescript
// 添加性能监控
@Injectable()
export class PerformanceMonitor {
  async monitorQuery<T>(queryName: string, queryFn: () => Promise<T>): Promise<T> {
    const start = Date.now();
    const result = await queryFn();
    const duration = Date.now() - start;

    // 记录性能指标
    console.log(`Query ${queryName}: ${duration}ms`);

    // 性能告警
    if (duration > 1000) {
      console.warn(`Slow query detected: ${queryName} took ${duration}ms`);
    }

    return result;
  }
}
```

## 📈 成功指标

### 性能指标
- [ ] 列表查询响应时间减少50%以上
- [ ] 内存使用减少30%以上
- [ ] 数据库连接数稳定
- [ ] CPU使用率降低

### 代码质量指标
- [ ] 消除90%以上的手动toObject()调用
- [ ] Repository方法类型安全性100%
- [ ] 单元测试覆盖率保持在90%以上
- [ ] 代码重复度降低

### 业务指标
- [ ] API响应时间改善
- [ ] 用户体验提升
- [ ] 系统稳定性保持
- [ ] 功能完整性100%

## 🎯 总结

通过遵循这个迁移指南，团队可以安全、高效地完成Mongoose最佳实践的迁移，获得显著的性能提升和代码质量改善。关键是采用渐进式的迁移策略，确保在优化性能的同时保持系统的稳定性和功能完整性。
