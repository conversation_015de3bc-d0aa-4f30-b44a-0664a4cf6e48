# Redis自动前缀管理规范与手动缓存使用分析

## 🎯 Redis前缀管理规范概述

### 前缀架构设计
```
{环境}:{项目}:{服务}:{具体键}
```

**示例**:
```typescript
// 实际Redis键
'development:fm:auth:cache:users:user:id:123'
'development:fm:auth:cache:users:user:username:john'
'development:fm:auth:meta:users:user:id:123'
```

### 核心设计原则

1. **连接级自动前缀**: Redis连接时设置`keyPrefix`，自动处理大部分操作
2. **用户层透明**: 用户始终使用业务键名，无需关心前缀细节
3. **服务上下文感知**: 自动推断或配置服务身份
4. **统一Redis模块**: 所有微服务使用统一的RedisModule

## 🔧 手动缓存管理的前缀使用流程

### 1. 服务级前缀配置

#### Auth服务配置示例
```typescript
// apps/auth/src/app.module.ts
RedisModule.forRootAsync({
  service: MICROSERVICE_NAMES.AUTH_SERVICE,  // 'auth'
  database: 1,
  useFactory: (configService: ConfigService) => ({
    host: configService.get('REDIS_HOST'),
    port: configService.get('REDIS_PORT'),
    password: configService.get('REDIS_PASSWORD'),
  }),
  inject: [ConfigService],
})
```

#### 自动前缀生成
```typescript
// RedisModule内部逻辑
private static buildServicePrefix(service: string, configService: ConfigService): string {
  const env = configService.get('NODE_ENV', 'development');
  const project = configService.get('PROJECT_NAME', 'fm');
  return `${env}:${project}:${service}:`;
}

// 结果: 'development:fm:auth:'
```

### 2. CacheManagerService的前缀处理

#### 获取缓存仓库
```typescript
// apps/auth/src/domain/users/users.service.ts
async findById(id: string): Promise<UserDocument> {
  // 1. 获取缓存仓库（自动应用服务前缀）
  const repository = this.cacheManager.getRepository<UserDocument>('users');
  
  // 2. 使用业务键名（无需手动添加前缀）
  const user = await repository.getOrLoad(
    `user:id:${id}`,  // 用户输入的业务键
    async () => {
      // 数据加载逻辑
      return await this.userRepository.findById(id);
    },
    {
      ttl: 300,
      enableProtection: true,
    }
  );
  
  return user;
}
```

#### 前缀处理链路

```mermaid
graph TD
    A[用户输入: 'user:id:123'] --> B[CacheRepository.buildCacheKey]
    B --> C["'cache:users:user:id:123'"]
    C --> D[RedisService.get]
    D --> E[Redis连接级前缀]
    E --> F["实际Redis键: 'development:fm:auth:cache:users:user:id:123'"]
```

### 3. BaseCacheRepository的键构建逻辑

#### buildCacheKey方法
```typescript
// libs/common/src/redis/cache/cache-repository.ts
protected buildCacheKey(key: string): string {
  // 注意：不在这里添加前缀，让RedisService自动处理
  // RedisService会自动添加 {env}:{project}:{service}: 前缀
  return `cache:${this.name}:${key}`;
}
```

**处理流程**:
1. **用户输入**: `user:id:123`
2. **Repository处理**: `cache:users:user:id:123`
3. **Redis连接前缀**: `development:fm:auth:cache:users:user:id:123`

#### 元数据键构建
```typescript
protected buildMetadataKey(key: string): string {
  return `meta:${this.name}:${key}`;
}
```

**元数据键示例**:
- **用户输入**: `user:id:123`
- **最终Redis键**: `development:fm:auth:meta:users:user:id:123`

### 4. RedisProtectionService的前缀处理

#### 保护模式下的前缀管理
```typescript
// cache-repository.ts
if (options.enableProtection) {
  // 架构修复：让 RedisCacheService 负责添加完整前缀
  // 传递原始键和完整前缀，避免重复添加
  await this.protectionService.setProtected(key, data, {
    ttl,
    ttlVariance: options.ttlVariance,
    enableAvalancheProtection: options.enableAvalancheProtection,
    enableBreakdownProtection: options.enableBreakdownProtection,
    enablePenetrationProtection: options.enablePenetrationProtection,
    prefix: `cache:${this.name}:`, // 指定完整前缀，RedisCacheService不再添加默认前缀
  });
}
```

## 📊 完整的前缀处理流程

### 用户代码示例
```typescript
// Service层代码
const repository = this.cacheManager.getRepository<UserDocument>('users');

// 查询缓存
await repository.get('user:id:123');

// 设置缓存
await repository.set('user:id:123', userData, { ttl: 300 });

// 删除缓存
await repository.delete('user:id:123');
```

### 前缀转换过程

| 层级 | 输入键 | 输出键 | 说明 |
|------|--------|--------|------|
| **用户层** | `user:id:123` | - | 业务键名 |
| **Repository层** | `user:id:123` | `cache:users:user:id:123` | 添加仓库前缀 |
| **RedisService层** | `cache:users:user:id:123` | `development:fm:auth:cache:users:user:id:123` | 连接级自动前缀 |
| **Redis存储** | - | `development:fm:auth:cache:users:user:id:123` | 最终存储键 |

### 不同操作的前缀处理

#### 1. 基础缓存操作
```typescript
// get操作
repository.get('user:id:123')
// → Redis键: 'development:fm:auth:cache:users:user:id:123'

// set操作  
repository.set('user:id:123', data)
// → Redis键: 'development:fm:auth:cache:users:user:id:123'

// delete操作
repository.delete('user:id:123')
// → Redis键: 'development:fm:auth:cache:users:user:id:123'
```

#### 2. 批量操作
```typescript
// 批量获取
repository.mget(['user:id:123', 'user:id:456'])
// → Redis键: 
//   'development:fm:auth:cache:users:user:id:123'
//   'development:fm:auth:cache:users:user:id:456'
```

#### 3. 模式匹配操作
```typescript
// 清除模式
repository.clear('user:*')
// → 搜索模式: 'development:fm:auth:cache:users:user:*'
```

## 🎯 架构优势分析

### 1. 透明化处理
- ✅ **用户友好**: 开发者只需关心业务键名
- ✅ **自动化**: 前缀自动生成和应用
- ✅ **一致性**: 所有操作统一前缀处理

### 2. 性能优化
- ✅ **连接级前缀**: 利用Redis底层C++实现，性能最优
- ✅ **避免重复处理**: 前缀只在连接时设置一次
- ✅ **批量操作优化**: 支持pipeline和批量前缀处理

### 3. 服务隔离
- ✅ **环境隔离**: development/production环境分离
- ✅ **服务隔离**: 不同微服务使用不同数据库和前缀
- ✅ **业务隔离**: 不同业务模块使用不同仓库前缀

### 4. 可维护性
- ✅ **统一配置**: 所有服务使用相同的RedisModule
- ✅ **自动推断**: 服务名自动推断，减少配置
- ✅ **调试友好**: 清晰的前缀结构便于调试

## 🔍 实际使用示例

### Auth服务中的用户缓存
```typescript
// 用户输入
const userId = '123';
const username = 'john';
const email = '<EMAIL>';

// 缓存操作
await repository.set(`user:id:${userId}`, userData);
await repository.set(`user:username:${username}`, userData);
await repository.set(`user:email:${email}`, userData);

// 实际Redis键
// development:fm:auth:cache:users:user:id:123
// development:fm:auth:cache:users:user:username:john
// development:fm:auth:cache:users:user:email:<EMAIL>
```

### 缓存更新和失效
```typescript
// 更新用户信息时，同时更新多个缓存键
private async updateUserCache(user: UserDocument): Promise<void> {
  const repository = this.cacheManager.getRepository<UserDocument>('users');
  
  const promises = [
    repository.setThrough(`user:id:${user.id}`, user, { ttl: 300 }),
    repository.setThrough(`user:username:${user.username}`, user, { ttl: 300 }),
    repository.setThrough(`user:email:${user.email}`, user, { ttl: 300 }),
  ];

  await Promise.all(promises);
}
```

## 🎉 总结

Redis自动前缀管理规范通过以下机制实现了高效的手动缓存管理：

1. **连接级自动前缀**: 在Redis连接时设置服务级前缀
2. **分层前缀构建**: Repository层添加业务前缀，Redis层添加服务前缀
3. **透明化API**: 用户只需使用业务键名，系统自动处理前缀
4. **统一配置管理**: 通过RedisModule统一管理所有服务的Redis配置

这种设计既保证了性能（利用Redis底层前缀处理），又提供了良好的开发体验（透明化API），同时实现了完整的服务隔离和环境隔离。
