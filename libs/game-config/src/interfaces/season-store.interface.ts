// Auto-generated from SeasonStore.json
// Generated at: 2025-07-20T12:56:05.983Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface SeasonStoreDefinition {
  itemId: number; // 物品ID 例: 8, 10500 (原: ItemId)
  idType: number; // 唯一标识符 例: 1 (原: IdType)
  seasonId: number; // 唯一标识符 例: 1 (原: SeasonId)
  id: number; // 唯一标识符 例: 1, 2
  awardGroup: number; // 数值 例: 1, 2 (原: AwardGroup)
  disCount: number; // 数量 例: 9500, 9000 (原: Discount)
  moneyType: number; // 类型 例: 2, 1 (原: MoneyType)
  num: number; // 数值 例: 1, 5 (原: Num)
  price: number; // 价格 例: 35, 100 (原: Price)
  rate: number; // 比率 例: 500, 400 (原: Rate)
  showPrice: number; // 价格 例: 33, 158 (原: ShowPrice)
  totalPrice: number; // 价格 例: 35, 175 (原: TotalPrice)
}

// 字段映射：新字段名 -> 原始字段名
export const SeasonStoreFieldMappings = {
  itemId: 'ItemId',
  idType: 'IdType',
  seasonId: 'SeasonId',
  awardGroup: 'AwardGroup',
  disCount: 'Discount',
  moneyType: 'MoneyType',
  num: 'Num',
  price: 'Price',
  rate: 'Rate',
  showPrice: 'ShowPrice',
  totalPrice: 'TotalPrice',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const SeasonStoreReverseFieldMappings = {
  'ItemId': 'itemId',
  'IdType': 'idType',
  'SeasonId': 'seasonId',
  'AwardGroup': 'awardGroup',
  'Discount': 'disCount',
  'MoneyType': 'moneyType',
  'Num': 'num',
  'Price': 'price',
  'Rate': 'rate',
  'ShowPrice': 'showPrice',
  'TotalPrice': 'totalPrice',
} as const;

export const SeasonStoreMeta = {
  tableName: 'SeasonStore',
  dataFileName: 'SeasonStore.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 12,
  requiredFields: ['itemId', 'idType', 'seasonId', 'id', 'awardGroup', 'disCount', 'moneyType', 'num', 'price', 'rate', 'showPrice', 'totalPrice'],
  optionalFields: [],
  renamedFieldsCount: 11,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: SeasonStoreFieldMappings,
  reverseFieldMappings: SeasonStoreReverseFieldMappings,
} as const;

export type SeasonStoreConfigMeta = typeof SeasonStoreMeta;
export type SeasonStoreFieldMapping = typeof SeasonStoreFieldMappings;
export type SeasonStoreReverseFieldMapping = typeof SeasonStoreReverseFieldMappings;
