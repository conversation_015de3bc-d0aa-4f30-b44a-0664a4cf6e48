// Auto-generated from BusinessMatchReward.json
// Generated at: 2025-07-20T12:55:59.369Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface BusinessMatchRewardDefinition {
  id: number; // 唯一标识符 例: 1, 2
  item1: number; // 数值 例: 90001 (原: Item1)
  item2: number; // 数值 例: 1 (原: Item2)
  item3: number; // 数值 例: 10700 (原: Item3)
  item4: number; // 数值 例: 0 (原: Item4)
  num1: number; // 数值 例: 5, 4 (原: Num1)
  num2: number; // 数值 例: 10000000, 9000000 (原: Num2)
  num3: number; // 数值 例: 10, 9 (原: Num3)
  num4: number; // 数值 例: 0 (原: Num4)
  rankingMax: number; // 数值 例: 1, 2 (原: RankingMax)
  rankingMin: number; // 数值 例: 1, 2 (原: RankingMin)
  rankingShow: string; // 字符串 例: 第1名, 第2名 (原: RankingShow)
}

// 字段映射：新字段名 -> 原始字段名
export const BusinessMatchRewardFieldMappings = {
  item1: 'Item1',
  item2: 'Item2',
  item3: 'Item3',
  item4: 'Item4',
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  num4: 'Num4',
  rankingMax: 'RankingMax',
  rankingMin: 'RankingMin',
  rankingShow: 'RankingShow',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const BusinessMatchRewardReverseFieldMappings = {
  'Item1': 'item1',
  'Item2': 'item2',
  'Item3': 'item3',
  'Item4': 'item4',
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Num4': 'num4',
  'RankingMax': 'rankingMax',
  'RankingMin': 'rankingMin',
  'RankingShow': 'rankingShow',
} as const;

export const BusinessMatchRewardMeta = {
  tableName: 'BusinessMatchReward',
  dataFileName: 'BusinessMatchReward.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 12,
  requiredFields: ['id', 'item1', 'item2', 'item3', 'item4', 'num1', 'num2', 'num3', 'num4', 'rankingMax', 'rankingMin', 'rankingShow'],
  optionalFields: [],
  renamedFieldsCount: 11,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: BusinessMatchRewardFieldMappings,
  reverseFieldMappings: BusinessMatchRewardReverseFieldMappings,
} as const;

export type BusinessMatchRewardConfigMeta = typeof BusinessMatchRewardMeta;
export type BusinessMatchRewardFieldMapping = typeof BusinessMatchRewardFieldMappings;
export type BusinessMatchRewardReverseFieldMapping = typeof BusinessMatchRewardReverseFieldMappings;
