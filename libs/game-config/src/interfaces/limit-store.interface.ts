// Auto-generated from limitStore.json
// Generated at: 2025-07-20T12:56:04.962Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface limitStoreDefinition {
  id: number; // 唯一标识符 例: 1, 2
  endTime: string; // 结束时间 例: 2020-7-7 23:59:59, 2020-6-30 23:59:59 (原: EndTime)
  periods: number; // 数值 例: 29, 28 (原: Periods)
  showEndTime: string; // 时间 例: 2020-7-9 23:59:59, 2020-7-2 23:59:59 (原: ShowEndTime)
  showTime: string; // 时间 例: 2020-7-3 00:00:00, 2020-6-24 00:00:00 (原: ShowTime)
  startTime: string; // 开始时间 例: 2020-7-5 00:00:00, 2020-6-28 00:00:00 (原: StartTime)
}

// 字段映射：新字段名 -> 原始字段名
export const limitStoreFieldMappings = {
  endTime: 'EndTime',
  periods: 'Periods',
  showEndTime: 'ShowEndTime',
  showTime: 'ShowTime',
  startTime: 'StartTime',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const limitStoreReverseFieldMappings = {
  'EndTime': 'endTime',
  'Periods': 'periods',
  'ShowEndTime': 'showEndTime',
  'ShowTime': 'showTime',
  'StartTime': 'startTime',
} as const;

export const limitStoreMeta = {
  tableName: 'limitStore',
  dataFileName: 'limitStore.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 6,
  requiredFields: ['id', 'endTime', 'periods', 'showEndTime', 'showTime', 'startTime'],
  optionalFields: [],
  renamedFieldsCount: 5,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: limitStoreFieldMappings,
  reverseFieldMappings: limitStoreReverseFieldMappings,
} as const;

export type limitStoreConfigMeta = typeof limitStoreMeta;
export type limitStoreFieldMapping = typeof limitStoreFieldMappings;
export type limitStoreReverseFieldMapping = typeof limitStoreReverseFieldMappings;
