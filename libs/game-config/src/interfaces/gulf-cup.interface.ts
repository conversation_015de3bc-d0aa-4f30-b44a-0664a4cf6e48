// Auto-generated from GulfCup.json
// Generated at: 2025-07-20T12:56:03.569Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface GulfCupDefinition {
  id: number; // 唯一标识符 例: 1, 2
  text: string; // 字符串 例: 赢下三场比赛且我的总进球数大于10, 赢下三场比赛且我的总失球数小于5
}

export const GulfCupMeta = {
  tableName: 'GulfCup',
  dataFileName: 'GulfCup.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 2,
  requiredFields: ['id', 'text'],
  optionalFields: [],
  renamedFieldsCount: 0,
  hasFieldMappings: false,
  isTableRenamed: false,
} as const;

export type GulfCupConfigMeta = typeof GulfCupMeta;
