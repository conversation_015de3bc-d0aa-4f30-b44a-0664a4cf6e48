// Auto-generated from ActiveShowControl.json
// Generated at: 2025-07-20T12:55:58.985Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface ActiveShowControlDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 名人堂球员, 大牌青训
  actENumber: number; // 数值 例: 21, 30 (原: ActEnum)
  actType: number; // 类型 例: 2, 0 (原: ActType)
  icon: string; // 图标 例: icon_mrtqy, icon_dpqx (原: Icon)
  nav: number; // 数值 例: 32, 33 (原: Nav)
  noOpenType: number; // 类型 例: 0, 1 (原: NoOpenType)
  order: number; // 顺序 例: 1, 2 (原: Order)
  redPointENumber: number; // 数值 例: 0, 23 (原: RedPointEnum)
  redPointType: number; // 类型 例: 0, 1 (原: RedPointType)
  tip?: string; // 字符串 例:  (原: Tip)
}

// 字段映射：新字段名 -> 原始字段名
export const ActiveShowControlFieldMappings = {
  actENumber: 'ActEnum',
  actType: 'ActType',
  icon: 'Icon',
  nav: 'Nav',
  noOpenType: 'NoOpenType',
  order: 'Order',
  redPointENumber: 'RedPointEnum',
  redPointType: 'RedPointType',
  tip: 'Tip',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const ActiveShowControlReverseFieldMappings = {
  'ActEnum': 'actENumber',
  'ActType': 'actType',
  'Icon': 'icon',
  'Nav': 'nav',
  'NoOpenType': 'noOpenType',
  'Order': 'order',
  'RedPointEnum': 'redPointENumber',
  'RedPointType': 'redPointType',
  'Tip': 'tip',
} as const;

export const ActiveShowControlMeta = {
  tableName: 'ActiveShowControl',
  dataFileName: 'ActiveShowControl.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 11,
  requiredFields: ['id', 'name', 'actENumber', 'actType', 'icon', 'nav', 'noOpenType', 'order', 'redPointENumber', 'redPointType'],
  optionalFields: ['tip'],
  renamedFieldsCount: 9,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: ActiveShowControlFieldMappings,
  reverseFieldMappings: ActiveShowControlReverseFieldMappings,
} as const;

export type ActiveShowControlConfigMeta = typeof ActiveShowControlMeta;
export type ActiveShowControlFieldMapping = typeof ActiveShowControlFieldMappings;
export type ActiveShowControlReverseFieldMapping = typeof ActiveShowControlReverseFieldMappings;
