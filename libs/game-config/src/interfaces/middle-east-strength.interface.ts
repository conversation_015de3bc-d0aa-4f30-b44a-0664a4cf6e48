// Auto-generated from MiddleEastStrength.json
// Generated at: 2025-07-20T12:56:05.138Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface MiddleEastStrengthDefinition {
  playerId: number; // 玩家ID 例: 90220, 90221 (原: PlayerId)
  id: number; // 唯一标识符 例: 1001, 1002
  level: number; // 等级 例: 1, 2 (原: Level)
}

// 字段映射：新字段名 -> 原始字段名
export const MiddleEastStrengthFieldMappings = {
  playerId: 'PlayerId',
  level: 'Level',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const MiddleEastStrengthReverseFieldMappings = {
  'PlayerId': 'playerId',
  'Level': 'level',
} as const;

export const MiddleEastStrengthMeta = {
  tableName: 'MiddleEastStrength',
  dataFileName: 'MiddleEastStrength.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 3,
  requiredFields: ['playerId', 'id', 'level'],
  optionalFields: [],
  renamedFieldsCount: 2,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: MiddleEastStrengthFieldMappings,
  reverseFieldMappings: MiddleEastStrengthReverseFieldMappings,
} as const;

export type MiddleEastStrengthConfigMeta = typeof MiddleEastStrengthMeta;
export type MiddleEastStrengthFieldMapping = typeof MiddleEastStrengthFieldMappings;
export type MiddleEastStrengthReverseFieldMapping = typeof MiddleEastStrengthReverseFieldMappings;
