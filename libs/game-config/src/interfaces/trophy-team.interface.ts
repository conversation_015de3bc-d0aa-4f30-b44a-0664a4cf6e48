// Auto-generated from TrophyTeam.json
// Generated at: 2025-07-20T12:56:07.031Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface TrophyTeamDefinition {
  randomRewardId2: number; // 唯一标识符 例: 2000, 2001 (原: RandomRewardId2)
  randomRewardId1: number; // 唯一标识符 例: 0 (原: RandomRewardId1)
  fixedRewardId: number; // 唯一标识符 例: 1000 (原: FixedRewardId)
  defenseId: number; // 唯一标识符 例: 1101 (原: DefenseID)
  offensiveId: number; // 唯一标识符 例: 101 (原: OffensiveID)
  teamId: number; // 队伍ID 例: 91206, 91207 (原: TeamID)
  id: number; // 唯一标识符 例: 1, 2
  teamName: string; // 队伍名称 例: 意大利之夏, 荣耀之地 (原: TeamName)
  consumeType: number; // 类型 例: 3, 2 (原: ConsumeType)
  consumeValue: number; // 数值 例: 7, 100 (原: ConsumeValue)
  copyIcon: number; // 图标 例: 0 (原: CopyIcon)
  copyType: number; // 类型 例: 1, 2 (原: CopyType)
  formation: number; // 数值 例: 442201, 442101 (原: Formation)
  limitNumber: number; // 数值 例: 1, 2 (原: LimitNum)
  lv: number; // 数值 例: 1 (原: Lv)
  purchaseMaxNumber: number; // 数值 例: 2, 3 (原: PurchaseMaxNum)
  purchaseParameters: number; // 参数 例: 100 (原: PurchaseParameters)
  purchaseType: number; // 类型 例: 1, 2 (原: PurchaseType)
  rewardIcon1: number; // 奖励 例: 10010 (原: RewardIcon1)
  rewardIcon2: number; // 奖励 例: 10011 (原: RewardIcon2)
  rewardIcon3: number; // 奖励 例: 10012 (原: RewardIcon3)
  rewardType1: number; // 类型 例: 0 (原: RewardType1)
  rewardType2: number; // 类型 例: 0 (原: RewardType2)
  rewardType3: number; // 类型 例: 0 (原: RewardType3)
  teamOrder: number; // 顺序 例: 1, 2 (原: TeamOrder)
  teamRemarks: string; // 字符串 例: 随机获得意大利籍球员, 随机获得火星籍球员 (原: TeamRemarks)
  weight1: number; // 权重 例: 9000 (原: Weight1)
  weight2: number; // 权重 例: 1000 (原: Weight2)
}

// 字段映射：新字段名 -> 原始字段名
export const TrophyTeamFieldMappings = {
  randomRewardId2: 'RandomRewardId2',
  randomRewardId1: 'RandomRewardId1',
  fixedRewardId: 'FixedRewardId',
  defenseId: 'DefenseID',
  offensiveId: 'OffensiveID',
  teamId: 'TeamID',
  teamName: 'TeamName',
  consumeType: 'ConsumeType',
  consumeValue: 'ConsumeValue',
  copyIcon: 'CopyIcon',
  copyType: 'CopyType',
  formation: 'Formation',
  limitNumber: 'LimitNum',
  lv: 'Lv',
  purchaseMaxNumber: 'PurchaseMaxNum',
  purchaseParameters: 'PurchaseParameters',
  purchaseType: 'PurchaseType',
  rewardIcon1: 'RewardIcon1',
  rewardIcon2: 'RewardIcon2',
  rewardIcon3: 'RewardIcon3',
  rewardType1: 'RewardType1',
  rewardType2: 'RewardType2',
  rewardType3: 'RewardType3',
  teamOrder: 'TeamOrder',
  teamRemarks: 'TeamRemarks',
  weight1: 'Weight1',
  weight2: 'Weight2',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const TrophyTeamReverseFieldMappings = {
  'RandomRewardId2': 'randomRewardId2',
  'RandomRewardId1': 'randomRewardId1',
  'FixedRewardId': 'fixedRewardId',
  'DefenseID': 'defenseId',
  'OffensiveID': 'offensiveId',
  'TeamID': 'teamId',
  'TeamName': 'teamName',
  'ConsumeType': 'consumeType',
  'ConsumeValue': 'consumeValue',
  'CopyIcon': 'copyIcon',
  'CopyType': 'copyType',
  'Formation': 'formation',
  'LimitNum': 'limitNumber',
  'Lv': 'lv',
  'PurchaseMaxNum': 'purchaseMaxNumber',
  'PurchaseParameters': 'purchaseParameters',
  'PurchaseType': 'purchaseType',
  'RewardIcon1': 'rewardIcon1',
  'RewardIcon2': 'rewardIcon2',
  'RewardIcon3': 'rewardIcon3',
  'RewardType1': 'rewardType1',
  'RewardType2': 'rewardType2',
  'RewardType3': 'rewardType3',
  'TeamOrder': 'teamOrder',
  'TeamRemarks': 'teamRemarks',
  'Weight1': 'weight1',
  'Weight2': 'weight2',
} as const;

export const TrophyTeamMeta = {
  tableName: 'TrophyTeam',
  dataFileName: 'TrophyTeam.json',
  primaryKey: 'id',
  searchFields: ['teamName'],
  fieldsCount: 28,
  requiredFields: ['randomRewardId2', 'randomRewardId1', 'fixedRewardId', 'defenseId', 'offensiveId', 'teamId', 'id', 'teamName', 'consumeType', 'consumeValue', 'copyIcon', 'copyType', 'formation', 'limitNumber', 'lv', 'purchaseMaxNumber', 'purchaseParameters', 'purchaseType', 'rewardIcon1', 'rewardIcon2', 'rewardIcon3', 'rewardType1', 'rewardType2', 'rewardType3', 'teamOrder', 'teamRemarks', 'weight1', 'weight2'],
  optionalFields: [],
  renamedFieldsCount: 27,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: TrophyTeamFieldMappings,
  reverseFieldMappings: TrophyTeamReverseFieldMappings,
} as const;

export type TrophyTeamConfigMeta = typeof TrophyTeamMeta;
export type TrophyTeamFieldMapping = typeof TrophyTeamFieldMappings;
export type TrophyTeamReverseFieldMapping = typeof TrophyTeamReverseFieldMappings;
