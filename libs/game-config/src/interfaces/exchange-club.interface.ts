// Auto-generated from ExchangeClub.json
// Generated at: 2025-07-20T12:56:00.206Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface ExchangeClubDefinition {
  id: number; // 唯一标识符 例: 10000, 10001
  club: string; // 字符串 例: AC米兰, 德玛西亚 (原: Club)
  icon: number; // 图标 例: 10053, 10054 (原: Icon)
}

// 字段映射：新字段名 -> 原始字段名
export const ExchangeClubFieldMappings = {
  club: 'Club',
  icon: 'Icon',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const ExchangeClubReverseFieldMappings = {
  'Club': 'club',
  'Icon': 'icon',
} as const;

export const ExchangeClubMeta = {
  tableName: 'ExchangeClub',
  dataFileName: 'ExchangeClub.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 3,
  requiredFields: ['id', 'club', 'icon'],
  optionalFields: [],
  renamedFieldsCount: 2,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: ExchangeClubFieldMappings,
  reverseFieldMappings: ExchangeClubReverseFieldMappings,
} as const;

export type ExchangeClubConfigMeta = typeof ExchangeClubMeta;
export type ExchangeClubFieldMapping = typeof ExchangeClubFieldMappings;
export type ExchangeClubReverseFieldMapping = typeof ExchangeClubReverseFieldMappings;
