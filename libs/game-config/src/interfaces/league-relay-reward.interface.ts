// Auto-generated from LeagueRelayReward.json
// Generated at: 2025-07-20T12:56:04.810Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface LeagueRelayRewardDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 懂超, 懂冠
  num1: number; // 数值 例: 3684211, 3157895 (原: Num1)
  num2: number; // 数值 例: 4210526, 3421053 (原: Num2)
  num3: number; // 数值 例: 4884211, 3684211 (原: Num3)
  reward1: number; // 奖励 例: 1 (原: Reward1)
  reward2: number; // 奖励 例: 1 (原: Reward2)
  reward3: number; // 奖励 例: 1 (原: Reward3)
  rewardType1: number; // 类型 例: 0 (原: RewardType1)
  rewardType2: number; // 类型 例: 0 (原: RewardType2)
  rewardType3: number; // 类型 例: 0 (原: RewardType3)
}

// 字段映射：新字段名 -> 原始字段名
export const LeagueRelayRewardFieldMappings = {
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  reward1: 'Reward1',
  reward2: 'Reward2',
  reward3: 'Reward3',
  rewardType1: 'RewardType1',
  rewardType2: 'RewardType2',
  rewardType3: 'RewardType3',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const LeagueRelayRewardReverseFieldMappings = {
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Reward1': 'reward1',
  'Reward2': 'reward2',
  'Reward3': 'reward3',
  'RewardType1': 'rewardType1',
  'RewardType2': 'rewardType2',
  'RewardType3': 'rewardType3',
} as const;

export const LeagueRelayRewardMeta = {
  tableName: 'LeagueRelayReward',
  dataFileName: 'LeagueRelayReward.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 11,
  requiredFields: ['id', 'name', 'num1', 'num2', 'num3', 'reward1', 'reward2', 'reward3', 'rewardType1', 'rewardType2', 'rewardType3'],
  optionalFields: [],
  renamedFieldsCount: 9,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: LeagueRelayRewardFieldMappings,
  reverseFieldMappings: LeagueRelayRewardReverseFieldMappings,
} as const;

export type LeagueRelayRewardConfigMeta = typeof LeagueRelayRewardMeta;
export type LeagueRelayRewardFieldMapping = typeof LeagueRelayRewardFieldMappings;
export type LeagueRelayRewardReverseFieldMapping = typeof LeagueRelayRewardReverseFieldMappings;
