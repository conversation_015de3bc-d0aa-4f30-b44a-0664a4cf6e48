// Auto-generated from ExchangeControl.json
// Generated at: 2025-07-20T12:56:00.230Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface ExchangeControlDefinition {
  id: number; // 唯一标识符 例: 1, 2
  maxFrequency: number; // 最大 例: 1, 5 (原: MaxFrequency)
  minFrequency: number; // 最小 例: 1, 2 (原: MinFrequency)
  num1: number; // 数值 例: 100, 150 (原: Num1)
  num2: number; // 数值 例: 0, 200 (原: Num2)
  refreshLv: number; // 数值 例: 1, 2 (原: RefreshLv)
  type1: number; // 类型 例: 10 (原: Type1)
  type2: number; // 类型 例: 0, 2 (原: Type2)
}

// 字段映射：新字段名 -> 原始字段名
export const ExchangeControlFieldMappings = {
  maxFrequency: 'MaxFrequency',
  minFrequency: 'MinFrequency',
  num1: 'Num1',
  num2: 'Num2',
  refreshLv: 'RefreshLv',
  type1: 'Type1',
  type2: 'Type2',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const ExchangeControlReverseFieldMappings = {
  'MaxFrequency': 'maxFrequency',
  'MinFrequency': 'minFrequency',
  'Num1': 'num1',
  'Num2': 'num2',
  'RefreshLv': 'refreshLv',
  'Type1': 'type1',
  'Type2': 'type2',
} as const;

export const ExchangeControlMeta = {
  tableName: 'ExchangeControl',
  dataFileName: 'ExchangeControl.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 8,
  requiredFields: ['id', 'maxFrequency', 'minFrequency', 'num1', 'num2', 'refreshLv', 'type1', 'type2'],
  optionalFields: [],
  renamedFieldsCount: 7,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: ExchangeControlFieldMappings,
  reverseFieldMappings: ExchangeControlReverseFieldMappings,
} as const;

export type ExchangeControlConfigMeta = typeof ExchangeControlMeta;
export type ExchangeControlFieldMapping = typeof ExchangeControlFieldMappings;
export type ExchangeControlReverseFieldMapping = typeof ExchangeControlReverseFieldMappings;
