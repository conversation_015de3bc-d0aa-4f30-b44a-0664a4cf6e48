// Auto-generated from MailText.json
// Generated at: 2025-07-20T12:56:05.037Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface MailTextDefinition {
  id: number; // 唯一标识符 例: 1, 2
  enclosureType: number; // 类型 例: 1, 0 (原: Enclosuretype)
  mailType: number; // 类型 例: 0 (原: Mailtype)
  result1: string; // 字符串 例: 恭喜您#0#星通关#0#球队，获得以下道具奖励！, 恭喜您全星通关#0#联赛，获得奖励如下！ (原: Result1)
  result2?: string; // 字符串 例:  (原: Result2)
  title: string; // 字符串 例: 通关奖励, 联赛奖励 (原: Title)
}

// 字段映射：新字段名 -> 原始字段名
export const MailTextFieldMappings = {
  enclosureType: 'Enclosuretype',
  mailType: 'Mailtype',
  result1: 'Result1',
  result2: 'Result2',
  title: 'Title',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const MailTextReverseFieldMappings = {
  'Enclosuretype': 'enclosureType',
  'Mailtype': 'mailType',
  'Result1': 'result1',
  'Result2': 'result2',
  'Title': 'title',
} as const;

export const MailTextMeta = {
  tableName: 'MailText',
  dataFileName: 'MailText.json',
  primaryKey: 'id',
  searchFields: ['title'],
  fieldsCount: 6,
  requiredFields: ['id', 'enclosureType', 'mailType', 'result1', 'title'],
  optionalFields: ['result2'],
  renamedFieldsCount: 5,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: MailTextFieldMappings,
  reverseFieldMappings: MailTextReverseFieldMappings,
} as const;

export type MailTextConfigMeta = typeof MailTextMeta;
export type MailTextFieldMapping = typeof MailTextFieldMappings;
export type MailTextReverseFieldMapping = typeof MailTextReverseFieldMappings;
