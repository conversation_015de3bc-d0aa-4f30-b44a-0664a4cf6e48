// Auto-generated from NewbieGuide.json
// Generated at: 2025-07-20T12:56:05.409Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface NewbieGuideDefinition {
  videoId: string; // 唯一标识符 例: 123,  (原: VideoId)
  typeId: number; // 唯一标识符 例: 0 (原: TypeId)
  conditionId: number; // 唯一标识符 例: 0, 30170 (原: ConditionId)
  rearId: number; // 唯一标识符 例: 2, 3 (原: RearId)
  frontId: number; // 唯一标识符 例: 0, 1 (原: FrontId)
  guideType: number; // 唯一标识符 例: 1 (原: GuideType)
  groupId: number; // 唯一标识符 例: 1, 2 (原: GroupId)
  id: number; // 唯一标识符 例: 1, 2
  btn: string; // 字符串 例: , 0.1,200,OPEN_GIVE_MONEY,0 (原: Btn)
  canComplete: number; // 是否可以 例: 0, 2 (原: CanComplete)
  canIgnore: number; // 是否可以 例: 0, 1
  condition: number; // 条件 例: 0, 3 (原: Condition)
  gift: number; // 数值 例: 0, 20001 (原: Gift)
  module: string; // 字符串 例: , game.MainPageMediator (原: Module)
  num: number; // 数值 例: 0, 10 (原: Num)
  pattern: number; // 数值 例: 0, 2 (原: Pattern)
  patType: number; // 类型 例: 0 (原: PatType)
  text: string; // 字符串 例: , 欢迎来到足球世界！对于足球，请问您是新手还是老手？ (原: Text)
  typeSort: number; // 类型 例: 0, 2 (原: TypeSort)
  userGroup: number; // 数值 例: 1, 2 (原: UserGroup)
}

// 字段映射：新字段名 -> 原始字段名
export const NewbieGuideFieldMappings = {
  videoId: 'VideoId',
  typeId: 'TypeId',
  conditionId: 'ConditionId',
  rearId: 'RearId',
  frontId: 'FrontId',
  guideType: 'GuideType',
  groupId: 'GroupId',
  btn: 'Btn',
  canComplete: 'CanComplete',
  condition: 'Condition',
  gift: 'Gift',
  module: 'Module',
  num: 'Num',
  pattern: 'Pattern',
  patType: 'PatType',
  text: 'Text',
  typeSort: 'TypeSort',
  userGroup: 'UserGroup',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const NewbieGuideReverseFieldMappings = {
  'VideoId': 'videoId',
  'TypeId': 'typeId',
  'ConditionId': 'conditionId',
  'RearId': 'rearId',
  'FrontId': 'frontId',
  'GuideType': 'guideType',
  'GroupId': 'groupId',
  'Btn': 'btn',
  'CanComplete': 'canComplete',
  'Condition': 'condition',
  'Gift': 'gift',
  'Module': 'module',
  'Num': 'num',
  'Pattern': 'pattern',
  'PatType': 'patType',
  'Text': 'text',
  'TypeSort': 'typeSort',
  'UserGroup': 'userGroup',
} as const;

export const NewbieGuideMeta = {
  tableName: 'NewbieGuide',
  dataFileName: 'NewbieGuide.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 20,
  requiredFields: ['videoId', 'typeId', 'conditionId', 'rearId', 'frontId', 'guideType', 'groupId', 'id', 'btn', 'canComplete', 'canIgnore', 'condition', 'gift', 'module', 'num', 'pattern', 'patType', 'text', 'typeSort', 'userGroup'],
  optionalFields: [],
  renamedFieldsCount: 18,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: NewbieGuideFieldMappings,
  reverseFieldMappings: NewbieGuideReverseFieldMappings,
} as const;

export type NewbieGuideConfigMeta = typeof NewbieGuideMeta;
export type NewbieGuideFieldMapping = typeof NewbieGuideFieldMappings;
export type NewbieGuideReverseFieldMapping = typeof NewbieGuideReverseFieldMappings;
