// Auto-generated from MatchEntrance.json
// Generated at: 2025-07-20T12:56:05.040Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface MatchEntranceDefinition {
  id: number; // 唯一标识符 例: 2, 3
  iconName: string; // 名称 例: btn_xh_bs, rukou
  name: string; // 名称 例: 世界杯, 中东杯
  picture: string; // 字符串 例: atlas_storycup (原: Picture)
}

// 字段映射：新字段名 -> 原始字段名
export const MatchEntranceFieldMappings = {
  picture: 'Picture',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const MatchEntranceReverseFieldMappings = {
  'Picture': 'picture',
} as const;

export const MatchEntranceMeta = {
  tableName: 'MatchEntrance',
  dataFileName: 'MatchEntrance.json',
  primaryKey: 'id',
  searchFields: ['iconName', 'name'],
  fieldsCount: 4,
  requiredFields: ['id', 'iconName', 'name', 'picture'],
  optionalFields: [],
  renamedFieldsCount: 1,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: MatchEntranceFieldMappings,
  reverseFieldMappings: MatchEntranceReverseFieldMappings,
} as const;

export type MatchEntranceConfigMeta = typeof MatchEntranceMeta;
export type MatchEntranceFieldMapping = typeof MatchEntranceFieldMappings;
export type MatchEntranceReverseFieldMapping = typeof MatchEntranceReverseFieldMappings;
