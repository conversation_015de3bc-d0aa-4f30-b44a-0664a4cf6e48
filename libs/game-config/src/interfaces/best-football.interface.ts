// Auto-generated from BestFootball.json
// Generated at: 2025-07-20T12:55:59.313Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface BestFootballDefinition {
  id: number; // 唯一标识符 例: 32816, 32820
  name: string; // 名称 例: L.菲戈·升星卡, 卡福·升星卡
  num: number; // 数值 例: 2, 1 (原: Num)
  rate: number; // 比率 例: 1200, 200 (原: Rate)
  team: number; // 数值 例: 1 (原: Team)
}

// 字段映射：新字段名 -> 原始字段名
export const BestFootballFieldMappings = {
  num: 'Num',
  rate: 'Rate',
  team: 'Team',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const BestFootballReverseFieldMappings = {
  'Num': 'num',
  'Rate': 'rate',
  'Team': 'team',
} as const;

export const BestFootballMeta = {
  tableName: 'BestFootball',
  dataFileName: 'BestFootball.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 5,
  requiredFields: ['id', 'name', 'num', 'rate', 'team'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: BestFootballFieldMappings,
  reverseFieldMappings: BestFootballReverseFieldMappings,
} as const;

export type BestFootballConfigMeta = typeof BestFootballMeta;
export type BestFootballFieldMapping = typeof BestFootballFieldMappings;
export type BestFootballReverseFieldMapping = typeof BestFootballReverseFieldMappings;
