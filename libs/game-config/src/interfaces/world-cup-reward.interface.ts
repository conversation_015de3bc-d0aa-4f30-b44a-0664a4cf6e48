// Auto-generated from WorldCupReward.json
// Generated at: 2025-07-20T12:56:07.350Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface WorldCupRewardDefinition {
  worldCupId: number; // 唯一标识符 例: 1, 2 (原: WorldCupId)
  id: number; // 唯一标识符 例: 1, 2
  num1: number; // 数值 例: 20000, 30000 (原: Num1)
  num2: number; // 数值 例: 26, 36 (原: Num2)
  num3: number; // 数值 例: 0, 1 (原: Num3)
  num4: number; // 数值 例: 0 (原: Num4)
  reward1: number; // 奖励 例: 1, 90134 (原: Reward1)
  reward2: number; // 奖励 例: 5 (原: Reward2)
  reward3: number; // 奖励 例: 0, 90041 (原: Reward3)
  reward4: number; // 奖励 例: 0 (原: Reward4)
  rewardType1: number; // 类型 例: 0 (原: RewardType1)
  rewardType2: number; // 类型 例: 0 (原: RewardType2)
  rewardType3: number; // 类型 例: 0 (原: RewardType3)
  rewardType4: number; // 类型 例: 0 (原: RewardType4)
  round: number; // 数值 例: 3, 4 (原: Round)
  worldCup: string; // 字符串 例: 意大利之夏, 荣耀之地 (原: WorldCup)
}

// 字段映射：新字段名 -> 原始字段名
export const WorldCupRewardFieldMappings = {
  worldCupId: 'WorldCupId',
  num1: 'Num1',
  num2: 'Num2',
  num3: 'Num3',
  num4: 'Num4',
  reward1: 'Reward1',
  reward2: 'Reward2',
  reward3: 'Reward3',
  reward4: 'Reward4',
  rewardType1: 'RewardType1',
  rewardType2: 'RewardType2',
  rewardType3: 'RewardType3',
  rewardType4: 'RewardType4',
  round: 'Round',
  worldCup: 'WorldCup',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const WorldCupRewardReverseFieldMappings = {
  'WorldCupId': 'worldCupId',
  'Num1': 'num1',
  'Num2': 'num2',
  'Num3': 'num3',
  'Num4': 'num4',
  'Reward1': 'reward1',
  'Reward2': 'reward2',
  'Reward3': 'reward3',
  'Reward4': 'reward4',
  'RewardType1': 'rewardType1',
  'RewardType2': 'rewardType2',
  'RewardType3': 'rewardType3',
  'RewardType4': 'rewardType4',
  'Round': 'round',
  'WorldCup': 'worldCup',
} as const;

export const WorldCupRewardMeta = {
  tableName: 'WorldCupReward',
  dataFileName: 'WorldCupReward.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 16,
  requiredFields: ['worldCupId', 'id', 'num1', 'num2', 'num3', 'num4', 'reward1', 'reward2', 'reward3', 'reward4', 'rewardType1', 'rewardType2', 'rewardType3', 'rewardType4', 'round', 'worldCup'],
  optionalFields: [],
  renamedFieldsCount: 15,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: WorldCupRewardFieldMappings,
  reverseFieldMappings: WorldCupRewardReverseFieldMappings,
} as const;

export type WorldCupRewardConfigMeta = typeof WorldCupRewardMeta;
export type WorldCupRewardFieldMapping = typeof WorldCupRewardFieldMappings;
export type WorldCupRewardReverseFieldMapping = typeof WorldCupRewardReverseFieldMappings;
