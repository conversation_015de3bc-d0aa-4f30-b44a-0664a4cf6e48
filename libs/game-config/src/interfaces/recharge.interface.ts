// Auto-generated from Recharge.json
// Generated at: 2025-07-20T12:56:05.950Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface RechargeDefinition {
  id: number; // 唯一标识符 例: 1, 2
  attachedAmount: number; // 数值 例: 0.6, 3 (原: AttachedAmount)
  firstRechargeAmount: number; // 数值 例: 6, 30 (原: FirstRechargeAmount)
  isDisplay: number; // 是否 例: 1, 0 (原: IsDisplay)
  mutualExclusion: number; // 数值 例: 0 (原: MutualExclusion)
  rechargeAmount: number; // 数值 例: 6, 30 (原: RechargeAmount)
}

// 字段映射：新字段名 -> 原始字段名
export const RechargeFieldMappings = {
  attachedAmount: 'AttachedAmount',
  firstRechargeAmount: 'FirstRechargeAmount',
  isDisplay: 'IsDisplay',
  mutualExclusion: 'MutualExclusion',
  rechargeAmount: 'RechargeAmount',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const RechargeReverseFieldMappings = {
  'AttachedAmount': 'attachedAmount',
  'FirstRechargeAmount': 'firstRechargeAmount',
  'IsDisplay': 'isDisplay',
  'MutualExclusion': 'mutualExclusion',
  'RechargeAmount': 'rechargeAmount',
} as const;

export const RechargeMeta = {
  tableName: 'Recharge',
  dataFileName: 'Recharge.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 6,
  requiredFields: ['id', 'attachedAmount', 'firstRechargeAmount', 'isDisplay', 'mutualExclusion', 'rechargeAmount'],
  optionalFields: [],
  renamedFieldsCount: 5,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: RechargeFieldMappings,
  reverseFieldMappings: RechargeReverseFieldMappings,
} as const;

export type RechargeConfigMeta = typeof RechargeMeta;
export type RechargeFieldMapping = typeof RechargeFieldMappings;
export type RechargeReverseFieldMapping = typeof RechargeReverseFieldMappings;
