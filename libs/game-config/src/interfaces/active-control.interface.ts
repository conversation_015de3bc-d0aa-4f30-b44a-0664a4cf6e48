// Auto-generated from ActiveControl.json
// Generated at: 2025-07-20T12:55:58.895Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface ActiveControlDefinition {
  id: number; // 唯一标识符 例: 1, 2
  activityName: string; // 名称 例: 每日充值礼包, 累计充值活动 (原: ActivityName)
  activityDepict: string; // 字符串 例: , 活动期间累计充值达到指定额度即可领取奖励！ (原: ActivityDepict)
  activityIcon: number; // 图标 例: 0 (原: ActivityIcon)
  activityType: number; // 类型 例: 1, 2 (原: ActivityType)
  comBoxOrder: number; // 顺序 例: 0 (原: ComBoxOrder)
  endTime: string; // 结束时间 例: , 2020/7/5 23:59:59 (原: EndTime)
  firstLoginShow: number; // 数值 例: 0, 1 (原: FirstLoginShow)
  jump: number; // 数值 例: 0 (原: Jump)
  order: number; // 顺序 例: 1, 2 (原: Order)
  periods: number; // 数值 例: 0, 28 (原: Periods)
  pushWay: number; // 数值 例: 2, 1 (原: PushWay)
  refreshCycle: number; // 数值 例: 1, 0 (原: RefreshCycle)
  showEndTime: string; // 时间 例: , 2020/7/5 23:59:59 (原: ShowEndTime)
  showStartTime: string; // 时间 例: , 2020/6/29 00:0:00 (原: ShowStartTime)
  startTime: string; // 开始时间 例: , 2020/6/29 00:0:00 (原: StartTime)
  timeType: number; // 类型 例: 1, 2 (原: TimeType)
  uiType: number; // 类型 例: 0, 1 (原: UiType)
}

// 字段映射：新字段名 -> 原始字段名
export const ActiveControlFieldMappings = {
  activityName: 'ActivityName',
  activityDepict: 'ActivityDepict',
  activityIcon: 'ActivityIcon',
  activityType: 'ActivityType',
  comBoxOrder: 'ComBoxOrder',
  endTime: 'EndTime',
  firstLoginShow: 'FirstLoginShow',
  jump: 'Jump',
  order: 'Order',
  periods: 'Periods',
  pushWay: 'PushWay',
  refreshCycle: 'RefreshCycle',
  showEndTime: 'ShowEndTime',
  showStartTime: 'ShowStartTime',
  startTime: 'StartTime',
  timeType: 'TimeType',
  uiType: 'UiType',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const ActiveControlReverseFieldMappings = {
  'ActivityName': 'activityName',
  'ActivityDepict': 'activityDepict',
  'ActivityIcon': 'activityIcon',
  'ActivityType': 'activityType',
  'ComBoxOrder': 'comBoxOrder',
  'EndTime': 'endTime',
  'FirstLoginShow': 'firstLoginShow',
  'Jump': 'jump',
  'Order': 'order',
  'Periods': 'periods',
  'PushWay': 'pushWay',
  'RefreshCycle': 'refreshCycle',
  'ShowEndTime': 'showEndTime',
  'ShowStartTime': 'showStartTime',
  'StartTime': 'startTime',
  'TimeType': 'timeType',
  'UiType': 'uiType',
} as const;

export const ActiveControlMeta = {
  tableName: 'ActiveControl',
  dataFileName: 'ActiveControl.json',
  primaryKey: 'id',
  searchFields: ['activityName'],
  fieldsCount: 18,
  requiredFields: ['id', 'activityName', 'activityDepict', 'activityIcon', 'activityType', 'comBoxOrder', 'endTime', 'firstLoginShow', 'jump', 'order', 'periods', 'pushWay', 'refreshCycle', 'showEndTime', 'showStartTime', 'startTime', 'timeType', 'uiType'],
  optionalFields: [],
  renamedFieldsCount: 17,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: ActiveControlFieldMappings,
  reverseFieldMappings: ActiveControlReverseFieldMappings,
} as const;

export type ActiveControlConfigMeta = typeof ActiveControlMeta;
export type ActiveControlFieldMapping = typeof ActiveControlFieldMappings;
export type ActiveControlReverseFieldMapping = typeof ActiveControlReverseFieldMappings;
