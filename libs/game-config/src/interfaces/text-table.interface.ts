// Auto-generated from TextTable.json
// Generated at: 2025-07-20T12:56:06.952Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface TextTableDefinition {
  id: number; // 唯一标识符 例: 1, 2
  content: string; // 字符串 例: 作为足球界的明日之星，超级赛事怎能错过，一起观摩下世界..., 1.懂联赛是《我是教练3.0》中最顶级的赛事，比赛共7...
  remarks?: string; // 字符串 例:  (原: Remarks)
}

// 字段映射：新字段名 -> 原始字段名
export const TextTableFieldMappings = {
  remarks: 'Remarks',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const TextTableReverseFieldMappings = {
  'Remarks': 'remarks',
} as const;

export const TextTableMeta = {
  tableName: 'TextTable',
  dataFileName: 'TextTable.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 3,
  requiredFields: ['id', 'content'],
  optionalFields: ['remarks'],
  renamedFieldsCount: 1,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: TextTableFieldMappings,
  reverseFieldMappings: TextTableReverseFieldMappings,
} as const;

export type TextTableConfigMeta = typeof TextTableMeta;
export type TextTableFieldMapping = typeof TextTableFieldMappings;
export type TextTableReverseFieldMapping = typeof TextTableReverseFieldMappings;
