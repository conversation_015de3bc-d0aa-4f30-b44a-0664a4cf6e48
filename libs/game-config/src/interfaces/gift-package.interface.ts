// Auto-generated from GiftPackage.json
// Generated at: 2025-07-20T12:56:03.555Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface GiftPackageDefinition {
  resId5: number; // 唯一标识符 例: 0, 6005 (原: ResId5)
  resId4: number; // 唯一标识符 例: 0, 6004 (原: ResId4)
  resId3: number; // 唯一标识符 例: 0, 6003 (原: ResId3)
  resId2: number; // 唯一标识符 例: 0, 6002 (原: ResId2)
  resId1: number; // 唯一标识符 例: 10001, 6001 (原: ResId1)
  id: number; // 唯一标识符 例: 90000, 90001
  name: string; // 名称 例: 长生不老礼包, 大中华球员礼包
  rewardNum1: number; // 奖励 例: 1, 9999 (原: RewardNum1)
  rewardNum2: number; // 奖励 例: 0, 1 (原: RewardNum2)
  rewardNum3: number; // 奖励 例: 0, 1 (原: RewardNum3)
  rewardNum4: number; // 奖励 例: 0, 1 (原: RewardNum4)
  rewardNum5: number; // 奖励 例: 0, 1 (原: RewardNum5)
  rewardType1: number; // 类型 例: 1, 2 (原: RewardType1)
  rewardType2: number; // 类型 例: 0, 2 (原: RewardType2)
  rewardType3: number; // 类型 例: 0, 2 (原: RewardType3)
  rewardType4: number; // 类型 例: 0, 2 (原: RewardType4)
  rewardType5: number; // 类型 例: 0, 2 (原: RewardType5)
  type: number; // 类型 例: 1, 2 (原: Type)
  weight1: number; // 权重 例: 0, 2000 (原: Weight1)
  weight2: number; // 权重 例: 0, 2000 (原: Weight2)
  weight3: number; // 权重 例: 0, 2000 (原: Weight3)
  weight4: number; // 权重 例: 0, 2000 (原: Weight4)
  weight5: number; // 权重 例: 0, 2000 (原: Weight5)
}

// 字段映射：新字段名 -> 原始字段名
export const GiftPackageFieldMappings = {
  resId5: 'ResId5',
  resId4: 'ResId4',
  resId3: 'ResId3',
  resId2: 'ResId2',
  resId1: 'ResId1',
  rewardNum1: 'RewardNum1',
  rewardNum2: 'RewardNum2',
  rewardNum3: 'RewardNum3',
  rewardNum4: 'RewardNum4',
  rewardNum5: 'RewardNum5',
  rewardType1: 'RewardType1',
  rewardType2: 'RewardType2',
  rewardType3: 'RewardType3',
  rewardType4: 'RewardType4',
  rewardType5: 'RewardType5',
  type: 'Type',
  weight1: 'Weight1',
  weight2: 'Weight2',
  weight3: 'Weight3',
  weight4: 'Weight4',
  weight5: 'Weight5',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const GiftPackageReverseFieldMappings = {
  'ResId5': 'resId5',
  'ResId4': 'resId4',
  'ResId3': 'resId3',
  'ResId2': 'resId2',
  'ResId1': 'resId1',
  'RewardNum1': 'rewardNum1',
  'RewardNum2': 'rewardNum2',
  'RewardNum3': 'rewardNum3',
  'RewardNum4': 'rewardNum4',
  'RewardNum5': 'rewardNum5',
  'RewardType1': 'rewardType1',
  'RewardType2': 'rewardType2',
  'RewardType3': 'rewardType3',
  'RewardType4': 'rewardType4',
  'RewardType5': 'rewardType5',
  'Type': 'type',
  'Weight1': 'weight1',
  'Weight2': 'weight2',
  'Weight3': 'weight3',
  'Weight4': 'weight4',
  'Weight5': 'weight5',
} as const;

export const GiftPackageMeta = {
  tableName: 'GiftPackage',
  dataFileName: 'GiftPackage.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 23,
  requiredFields: ['resId5', 'resId4', 'resId3', 'resId2', 'resId1', 'id', 'name', 'rewardNum1', 'rewardNum2', 'rewardNum3', 'rewardNum4', 'rewardNum5', 'rewardType1', 'rewardType2', 'rewardType3', 'rewardType4', 'rewardType5', 'type', 'weight1', 'weight2', 'weight3', 'weight4', 'weight5'],
  optionalFields: [],
  renamedFieldsCount: 21,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: GiftPackageFieldMappings,
  reverseFieldMappings: GiftPackageReverseFieldMappings,
} as const;

export type GiftPackageConfigMeta = typeof GiftPackageMeta;
export type GiftPackageFieldMapping = typeof GiftPackageFieldMappings;
export type GiftPackageReverseFieldMapping = typeof GiftPackageReverseFieldMappings;
