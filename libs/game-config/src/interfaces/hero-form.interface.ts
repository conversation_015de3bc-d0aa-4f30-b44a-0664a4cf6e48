// Auto-generated from FootballerForm.json
// Generated at: 2025-07-20T12:56:01.659Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据
// 表名已规范化: FootballerForm → HeroForm

export interface HeroFormDefinition {
  id: number; // 唯一标识符 例: 1, 2
  form: number; // 数值 例: 1, 2 (原: Form)
  ratio: number; // 数值 例: 11000, 10500 (原: Ratio)
  weight: number; // 权重 例: 100, 500 (原: Weight)
}

// 字段映射：新字段名 -> 原始字段名
export const HeroFormFieldMappings = {
  form: 'Form',
  ratio: 'Ratio',
  weight: 'Weight',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const HeroFormReverseFieldMappings = {
  'Form': 'form',
  'Ratio': 'ratio',
  'Weight': 'weight',
} as const;

export const HeroFormMeta = {
  tableName: 'HeroForm',
  originalTableName: 'FootballerForm',
  dataFileName: 'FootballerForm.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 4,
  requiredFields: ['id', 'form', 'ratio', 'weight'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: true,
  fieldMappings: HeroFormFieldMappings,
  reverseFieldMappings: HeroFormReverseFieldMappings,
} as const;

export type HeroFormConfigMeta = typeof HeroFormMeta;
export type HeroFormFieldMapping = typeof HeroFormFieldMappings;
export type HeroFormReverseFieldMapping = typeof HeroFormReverseFieldMappings;
