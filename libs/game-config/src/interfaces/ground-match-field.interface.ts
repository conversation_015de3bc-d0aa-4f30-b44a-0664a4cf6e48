// Auto-generated from GroundMatchField.json
// Generated at: 2025-07-20T12:56:03.559Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface GroundMatchFieldDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 社区级, 地区级
  level: number; // 等级 例: 5, 10 (原: Level)
  ratio: number; // 数值 例: 0.8, 0.9 (原: Ratio)
  train: number; // 数值 例: 1, 2 (原: Train)
}

// 字段映射：新字段名 -> 原始字段名
export const GroundMatchFieldFieldMappings = {
  level: 'Level',
  ratio: 'Ratio',
  train: 'Train',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const GroundMatchFieldReverseFieldMappings = {
  'Level': 'level',
  'Ratio': 'ratio',
  'Train': 'train',
} as const;

export const GroundMatchFieldMeta = {
  tableName: 'GroundMatchField',
  dataFileName: 'GroundMatchField.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 5,
  requiredFields: ['id', 'name', 'level', 'ratio', 'train'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: GroundMatchFieldFieldMappings,
  reverseFieldMappings: GroundMatchFieldReverseFieldMappings,
} as const;

export type GroundMatchFieldConfigMeta = typeof GroundMatchFieldMeta;
export type GroundMatchFieldFieldMapping = typeof GroundMatchFieldFieldMappings;
export type GroundMatchFieldReverseFieldMapping = typeof GroundMatchFieldReverseFieldMappings;
