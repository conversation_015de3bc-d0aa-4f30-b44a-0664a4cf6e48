// Auto-generated from Decompose.json
// Generated at: 2025-07-20T12:55:59.802Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface DecomposeDefinition {
  id: number; // 唯一标识符 例: 0
  get: number; // 数值 例: 0 (原: Get)
  item: number; // 数值 例: 30001, 30002 (原: Item)
  type: number; // 类型 例: 1, 0 (原: Type)
}

// 字段映射：新字段名 -> 原始字段名
export const DecomposeFieldMappings = {
  get: 'Get',
  item: 'Item',
  type: 'Type',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const DecomposeReverseFieldMappings = {
  'Get': 'get',
  'Item': 'item',
  'Type': 'type',
} as const;

export const DecomposeMeta = {
  tableName: 'Decompose',
  dataFileName: 'Decompose.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 4,
  requiredFields: ['id', 'get', 'item', 'type'],
  optionalFields: [],
  renamedFieldsCount: 3,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: DecomposeFieldMappings,
  reverseFieldMappings: DecomposeReverseFieldMappings,
} as const;

export type DecomposeConfigMeta = typeof DecomposeMeta;
export type DecomposeFieldMapping = typeof DecomposeFieldMappings;
export type DecomposeReverseFieldMapping = typeof DecomposeReverseFieldMappings;
