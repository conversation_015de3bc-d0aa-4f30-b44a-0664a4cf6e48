// Auto-generated from NewPositionMatch.json
// Generated at: 2025-07-20T12:56:05.423Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface NewPositionMatchDefinition {
  id: number; // 唯一标识符 例: 4005, 4016
  AM: number; // 数值 例: 0.5, 1
  DC: number; // 数值 例: 0.3, 0.4
  DL: number; // 数值 例: 0.3, 0.4
  DM: number; // 数值 例: 0.9, 0.6
  DR: number; // 数值 例: 0.8, 0.4
  GK: number; // 数值 例: 0.3
  MC: number; // 数值 例: 1, 0.8
  ML: number; // 数值 例: 0.8, 0.95
  MR: number; // 数值 例: 1, 0.8
  position: string; // 位置 例: 右前卫, 前腰 (原: Position)
  ST: number; // 数值 例: 0.4, 0.9
  WL: number; // 数值 例: 0.5, 1
  WR: number; // 数值 例: 0.8, 0.5
}

// 字段映射：新字段名 -> 原始字段名
export const NewPositionMatchFieldMappings = {
  position: 'Position',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const NewPositionMatchReverseFieldMappings = {
  'Position': 'position',
} as const;

export const NewPositionMatchMeta = {
  tableName: 'NewPositionMatch',
  dataFileName: 'NewPositionMatch.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 14,
  requiredFields: ['id', 'AM', 'DC', 'DL', 'DM', 'DR', 'GK', 'MC', 'ML', 'MR', 'position', 'ST', 'WL', 'WR'],
  optionalFields: [],
  renamedFieldsCount: 1,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: NewPositionMatchFieldMappings,
  reverseFieldMappings: NewPositionMatchReverseFieldMappings,
} as const;

export type NewPositionMatchConfigMeta = typeof NewPositionMatchMeta;
export type NewPositionMatchFieldMapping = typeof NewPositionMatchFieldMappings;
export type NewPositionMatchReverseFieldMapping = typeof NewPositionMatchReverseFieldMappings;
