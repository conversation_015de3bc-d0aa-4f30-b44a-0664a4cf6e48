// Auto-generated from TurntableReward.json
// Generated at: 2025-07-20T12:56:07.042Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface TurntableRewardDefinition {
  itemId: number; // 物品ID 例: 11004, 11005 (原: ItemId)
  groupId: number; // 唯一标识符 例: 1, 2 (原: GroupId)
  id: number; // 唯一标识符 例: 1, 2
  itemName: string; // 物品名称 例: 球探体力卡（小）, 球探体力卡（中） (原: ItemName)
  num: number; // 数值 例: 1 (原: Num)
  type: number; // 类型 例: 1
  weight: number; // 权重 例: 555, 333 (原: Weight)
}

// 字段映射：新字段名 -> 原始字段名
export const TurntableRewardFieldMappings = {
  itemId: 'ItemId',
  groupId: 'GroupId',
  itemName: 'ItemName',
  num: 'Num',
  weight: 'Weight',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const TurntableRewardReverseFieldMappings = {
  'ItemId': 'itemId',
  'GroupId': 'groupId',
  'ItemName': 'itemName',
  'Num': 'num',
  'Weight': 'weight',
} as const;

export const TurntableRewardMeta = {
  tableName: 'TurntableReward',
  dataFileName: 'TurntableReward.json',
  primaryKey: 'id',
  searchFields: ['itemName'],
  fieldsCount: 7,
  requiredFields: ['itemId', 'groupId', 'id', 'itemName', 'num', 'type', 'weight'],
  optionalFields: [],
  renamedFieldsCount: 5,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: TurntableRewardFieldMappings,
  reverseFieldMappings: TurntableRewardReverseFieldMappings,
} as const;

export type TurntableRewardConfigMeta = typeof TurntableRewardMeta;
export type TurntableRewardFieldMapping = typeof TurntableRewardFieldMappings;
export type TurntableRewardReverseFieldMapping = typeof TurntableRewardReverseFieldMappings;
