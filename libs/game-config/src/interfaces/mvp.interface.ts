// Auto-generated from MVP.json
// Generated at: 2025-07-20T12:56:05.368Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface MVPDefinition {
  itemId: number; // 物品ID 例: 42767, 42779 (原: ItemId)
  id: number; // 唯一标识符 例: 1, 2
  price: number; // 价格 例: 4580, 2980 (原: Price)
}

// 字段映射：新字段名 -> 原始字段名
export const MVPFieldMappings = {
  itemId: 'ItemId',
  price: 'Price',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const MVPReverseFieldMappings = {
  'ItemId': 'itemId',
  'Price': 'price',
} as const;

export const MVPMeta = {
  tableName: 'MVP',
  dataFileName: 'MVP.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 3,
  requiredFields: ['itemId', 'id', 'price'],
  optionalFields: [],
  renamedFieldsCount: 2,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: MVPFieldMappings,
  reverseFieldMappings: MVPReverseFieldMappings,
} as const;

export type MVPConfigMeta = typeof MVPMeta;
export type MVPFieldMapping = typeof MVPFieldMappings;
export type MVPReverseFieldMapping = typeof MVPReverseFieldMappings;
