// Auto-generated from Share.json
// Generated at: 2025-07-20T12:56:05.987Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface ShareDefinition {
  id: number; // 唯一标识符 例: 1, 2
  headImage: string; // 图片 例: 资源100 (原: HeadImage)
  image: string; // 图片 例: 资源1|资源2|资源3,  (原: Image)
  merlType: number; // 类型 例: 1, 2 (原: MerlType)
  shareText: string; // 字符串 例: 我在我是教练3.0巡回赛中大胜#BattleName#..., 我在中东杯中大获全胜！快来看我的球队的精彩表演吧！ (原: ShareText)
  shareTitle: string; // 字符串 例: 我是教练3.0精彩比赛推荐, 测试 (原: ShareTitle)
  shareType: number; // 类型 例: 1, 2 (原: ShareType)
}

// 字段映射：新字段名 -> 原始字段名
export const ShareFieldMappings = {
  headImage: 'HeadImage',
  image: 'Image',
  merlType: 'MerlType',
  shareText: 'ShareText',
  shareTitle: 'ShareTitle',
  shareType: 'ShareType',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const ShareReverseFieldMappings = {
  'HeadImage': 'headImage',
  'Image': 'image',
  'MerlType': 'merlType',
  'ShareText': 'shareText',
  'ShareTitle': 'shareTitle',
  'ShareType': 'shareType',
} as const;

export const ShareMeta = {
  tableName: 'Share',
  dataFileName: 'Share.json',
  primaryKey: 'id',
  searchFields: ['shareTitle'],
  fieldsCount: 7,
  requiredFields: ['id', 'headImage', 'image', 'merlType', 'shareText', 'shareTitle', 'shareType'],
  optionalFields: [],
  renamedFieldsCount: 6,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: ShareFieldMappings,
  reverseFieldMappings: ShareReverseFieldMappings,
} as const;

export type ShareConfigMeta = typeof ShareMeta;
export type ShareFieldMapping = typeof ShareFieldMappings;
export type ShareReverseFieldMapping = typeof ShareReverseFieldMappings;
