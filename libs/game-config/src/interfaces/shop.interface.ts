// Auto-generated from Shop.json
// Generated at: 2025-07-20T12:56:05.999Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface ShopDefinition {
  id: number; // 唯一标识符 例: 1, 2
  customer: number; // 数值 例: 1, 0 (原: Customer)
  disCount: number; // 数量 例: 4, 5 (原: Discount)
  discountPrice: number; // 数量 例: 188, 150 (原: DiscountPrice)
  endingTime: string; // 时间 例: , 2020/7/7 23:59:59 (原: EndingTime)
  isLimitedTime: number; // 时间 例: 0, 1 (原: IsLimitedTime)
  itemType: number; // 类型 例: 0 (原: ItemType)
  limitParameter: number; // 参数 例: 0 (原: LimitParameter)
  limitType: number; // 类型 例: 0 (原: LimitType)
  num: number; // 数值 例: 1, 0 (原: Num)
  pageSign: number; // 数值 例: 1, 2 (原: PageSign)
  parameters: number; // 参数 例: 90054, 90055 (原: Parameters)
  periods: number; // 数值 例: 0, 29 (原: Periods)
  price: number; // 价格 例: 430, 300 (原: Price)
  priceType: number; // 类型 例: 2, 1 (原: PriceType)
  purchase: number; // 数值 例: 1, 0 (原: Purchase)
  refresh: number; // 数值 例: 1, 0 (原: Refresh)
  refreshType: number; // 类型 例: 1, 0 (原: RefreshType)
  startTime: string; // 开始时间 例: , 2020/7/5 00:00:00 (原: StartTime)
  yNDisCount: number; // 数量 例: 1, 0 (原: YNDiscount)
}

// 字段映射：新字段名 -> 原始字段名
export const ShopFieldMappings = {
  customer: 'Customer',
  disCount: 'Discount',
  discountPrice: 'DiscountPrice',
  endingTime: 'EndingTime',
  isLimitedTime: 'IsLimitedTime',
  itemType: 'ItemType',
  limitParameter: 'LimitParameter',
  limitType: 'LimitType',
  num: 'Num',
  pageSign: 'PageSign',
  parameters: 'Parameters',
  periods: 'Periods',
  price: 'Price',
  priceType: 'PriceType',
  purchase: 'Purchase',
  refresh: 'Refresh',
  refreshType: 'RefreshType',
  startTime: 'StartTime',
  yNDisCount: 'YNDiscount',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const ShopReverseFieldMappings = {
  'Customer': 'customer',
  'Discount': 'disCount',
  'DiscountPrice': 'discountPrice',
  'EndingTime': 'endingTime',
  'IsLimitedTime': 'isLimitedTime',
  'ItemType': 'itemType',
  'LimitParameter': 'limitParameter',
  'LimitType': 'limitType',
  'Num': 'num',
  'PageSign': 'pageSign',
  'Parameters': 'parameters',
  'Periods': 'periods',
  'Price': 'price',
  'PriceType': 'priceType',
  'Purchase': 'purchase',
  'Refresh': 'refresh',
  'RefreshType': 'refreshType',
  'StartTime': 'startTime',
  'YNDiscount': 'yNDisCount',
} as const;

export const ShopMeta = {
  tableName: 'Shop',
  dataFileName: 'Shop.json',
  primaryKey: 'id',
  searchFields: [],
  fieldsCount: 20,
  requiredFields: ['id', 'customer', 'disCount', 'discountPrice', 'endingTime', 'isLimitedTime', 'itemType', 'limitParameter', 'limitType', 'num', 'pageSign', 'parameters', 'periods', 'price', 'priceType', 'purchase', 'refresh', 'refreshType', 'startTime', 'yNDisCount'],
  optionalFields: [],
  renamedFieldsCount: 19,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: ShopFieldMappings,
  reverseFieldMappings: ShopReverseFieldMappings,
} as const;

export type ShopConfigMeta = typeof ShopMeta;
export type ShopFieldMapping = typeof ShopFieldMappings;
export type ShopReverseFieldMapping = typeof ShopReverseFieldMappings;
