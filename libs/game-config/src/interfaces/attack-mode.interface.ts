// Auto-generated from AttackMode.json
// Generated at: 2025-07-20T12:55:59.044Z
// 字段名已优化为驼峰命名法，原始字段名映射见元数据

export interface AttackModeDefinition {
  id: number; // 唯一标识符 例: 1, 2
  name: string; // 名称 例: 头球, 远射
  a1AMOdds: number; // 数值 例: 50, 250 (原: A1AMOdds)
  a1BrFactor1: number; // 数值 例: 0, 2000 (原: A1BrFactor1)
  a1BrFactor2: number; // 数值 例: 0, 1000 (原: A1BrFactor2)
  a1BrType1: number; // 类型 例: 0, 6 (原: A1BrType1)
  a1BrType2: string | number; // 类型 例: 0, 1 (原: A1BrType2)
  a1DCOdds: number; // 数值 例: 20, 10 (原: A1DCOdds)
  a1DLOdds: number; // 数值 例: 100, 10 (原: A1DLOdds)
  a1DMOdds: number; // 数值 例: 20, 10 (原: A1DMOdds)
  a1DROdds: number; // 数值 例: 100, 10 (原: A1DROdds)
  a1GKOdds: number; // 数值 例: 0 (原: A1GKOdds)
  a1MCOdds: number; // 数值 例: 50, 10 (原: A1MCOdds)
  a1MLOdds: number; // 数值 例: 200, 10 (原: A1MLOdds)
  a1MROdds: number; // 数值 例: 200, 10 (原: A1MROdds)
  a1ShFactor1: number; // 数值 例: 1500, 1000 (原: A1ShFactor1)
  a1ShFactor2: number; // 数值 例: 0, 1000 (原: A1ShFactor2)
  a1ShType1: number; // 类型 例: 11, 12 (原: A1ShType1)
  a1ShType2: number; // 类型 例: 0, 16 (原: A1ShType2)
  a1STOdds: number; // 数值 例: 0, 280 (原: A1STOdds)
  a1WLOdds: number; // 数值 例: 130, 200 (原: A1WLOdds)
  a1WROdds: number; // 数值 例: 130, 200 (原: A1WROdds)
  a2AMOdds: number; // 数值 例: 100, 0 (原: A2AMOdds)
  a2DCOdds: number; // 数值 例: 10, 0 (原: A2DCOdds)
  a2DLOdds: number; // 数值 例: 10, 0 (原: A2DLOdds)
  a2DMOdds: number; // 数值 例: 20, 0 (原: A2DMOdds)
  a2DROdds: number; // 数值 例: 10, 0 (原: A2DROdds)
  a2GKOdds: number; // 数值 例: 0 (原: A2GKOdds)
  a2MCOdds: number; // 数值 例: 40, 0 (原: A2MCOdds)
  a2MLOdds: number; // 数值 例: 20, 0 (原: A2MLOdds)
  a2MROdds: number; // 数值 例: 20, 0 (原: A2MROdds)
  a2ShFactor1: number; // 数值 例: 1800, 0 (原: A2ShFactor1)
  a2ShFactor2: number; // 数值 例: 1300, 0 (原: A2ShFactor2)
  a2ShType1: number; // 类型 例: 8, 0 (原: A2ShType1)
  a2ShType2: string | number; // 类型 例: 3, 0 (原: A2ShType2)
  a2STOdds: number; // 数值 例: 530, 0 (原: A2STOdds)
  a2WLOdds: number; // 数值 例: 120, 0 (原: A2WLOdds)
  a2WROdds: number; // 数值 例: 120, 0 (原: A2WROdds)
  bBrFactor1: number; // 数值 例: 0, 2000 (原: BBrFactor1)
  bBrFactor2: number; // 数值 例: 0, 1000 (原: BBrFactor2)
  bBrType1: number; // 类型 例: 0, 9 (原: BBrType1)
  bBrType2: string | number; // 类型 例: 0, 1 (原: BBrType2)
  brFailMoive: string; // 字符串 例: takePass_shoot_break_1.f4v, oneOnOne_break_failed_1.f4v (原: BrFailMoive)
  bShFactor1: number; // 数值 例: 1300, 0 (原: BShFactor1)
  bShFactor2: number; // 数值 例: 1300, 0 (原: BShFactor2)
  bShType1: string | number; // 类型 例: 2, 0 (原: BShType1)
  bShType2: number; // 类型 例: 3, 0 (原: BShType2)
  gKShFactor1: number; // 数值 例: 2000, 1500 (原: GKShFactor1)
  gKShFactor2: number; // 数值 例: 1000, 0 (原: GKShFactor2)
  gKShType1: number; // 类型 例: 18, 19 (原: GKShType1)
  gKShType2: string | number; // 类型 例: 19, 16 (原: GKShType2)
  shFailMoive: string[]; // 数组 例: headShot_shoot_failed_2.f4v..., headShot_shoot_failed_2.f4v... (原: ShFailMoive)
  shSucMoive: string; // 字符串 例: headShot_shoot_succeed_1.f4v, corner_shoot_succeed_1.f4v (原: ShSucMoive)
}

// 字段映射：新字段名 -> 原始字段名
export const AttackModeFieldMappings = {
  a1AMOdds: 'A1AMOdds',
  a1BrFactor1: 'A1BrFactor1',
  a1BrFactor2: 'A1BrFactor2',
  a1BrType1: 'A1BrType1',
  a1BrType2: 'A1BrType2',
  a1DCOdds: 'A1DCOdds',
  a1DLOdds: 'A1DLOdds',
  a1DMOdds: 'A1DMOdds',
  a1DROdds: 'A1DROdds',
  a1GKOdds: 'A1GKOdds',
  a1MCOdds: 'A1MCOdds',
  a1MLOdds: 'A1MLOdds',
  a1MROdds: 'A1MROdds',
  a1ShFactor1: 'A1ShFactor1',
  a1ShFactor2: 'A1ShFactor2',
  a1ShType1: 'A1ShType1',
  a1ShType2: 'A1ShType2',
  a1STOdds: 'A1STOdds',
  a1WLOdds: 'A1WLOdds',
  a1WROdds: 'A1WROdds',
  a2AMOdds: 'A2AMOdds',
  a2DCOdds: 'A2DCOdds',
  a2DLOdds: 'A2DLOdds',
  a2DMOdds: 'A2DMOdds',
  a2DROdds: 'A2DROdds',
  a2GKOdds: 'A2GKOdds',
  a2MCOdds: 'A2MCOdds',
  a2MLOdds: 'A2MLOdds',
  a2MROdds: 'A2MROdds',
  a2ShFactor1: 'A2ShFactor1',
  a2ShFactor2: 'A2ShFactor2',
  a2ShType1: 'A2ShType1',
  a2ShType2: 'A2ShType2',
  a2STOdds: 'A2STOdds',
  a2WLOdds: 'A2WLOdds',
  a2WROdds: 'A2WROdds',
  bBrFactor1: 'BBrFactor1',
  bBrFactor2: 'BBrFactor2',
  bBrType1: 'BBrType1',
  bBrType2: 'BBrType2',
  brFailMoive: 'BrFailMoive',
  bShFactor1: 'BShFactor1',
  bShFactor2: 'BShFactor2',
  bShType1: 'BShType1',
  bShType2: 'BShType2',
  gKShFactor1: 'GKShFactor1',
  gKShFactor2: 'GKShFactor2',
  gKShType1: 'GKShType1',
  gKShType2: 'GKShType2',
  shFailMoive: 'ShFailMoive',
  shSucMoive: 'ShSucMoive',
} as const;

// 反向映射：原始字段名 -> 新字段名
export const AttackModeReverseFieldMappings = {
  'A1AMOdds': 'a1AMOdds',
  'A1BrFactor1': 'a1BrFactor1',
  'A1BrFactor2': 'a1BrFactor2',
  'A1BrType1': 'a1BrType1',
  'A1BrType2': 'a1BrType2',
  'A1DCOdds': 'a1DCOdds',
  'A1DLOdds': 'a1DLOdds',
  'A1DMOdds': 'a1DMOdds',
  'A1DROdds': 'a1DROdds',
  'A1GKOdds': 'a1GKOdds',
  'A1MCOdds': 'a1MCOdds',
  'A1MLOdds': 'a1MLOdds',
  'A1MROdds': 'a1MROdds',
  'A1ShFactor1': 'a1ShFactor1',
  'A1ShFactor2': 'a1ShFactor2',
  'A1ShType1': 'a1ShType1',
  'A1ShType2': 'a1ShType2',
  'A1STOdds': 'a1STOdds',
  'A1WLOdds': 'a1WLOdds',
  'A1WROdds': 'a1WROdds',
  'A2AMOdds': 'a2AMOdds',
  'A2DCOdds': 'a2DCOdds',
  'A2DLOdds': 'a2DLOdds',
  'A2DMOdds': 'a2DMOdds',
  'A2DROdds': 'a2DROdds',
  'A2GKOdds': 'a2GKOdds',
  'A2MCOdds': 'a2MCOdds',
  'A2MLOdds': 'a2MLOdds',
  'A2MROdds': 'a2MROdds',
  'A2ShFactor1': 'a2ShFactor1',
  'A2ShFactor2': 'a2ShFactor2',
  'A2ShType1': 'a2ShType1',
  'A2ShType2': 'a2ShType2',
  'A2STOdds': 'a2STOdds',
  'A2WLOdds': 'a2WLOdds',
  'A2WROdds': 'a2WROdds',
  'BBrFactor1': 'bBrFactor1',
  'BBrFactor2': 'bBrFactor2',
  'BBrType1': 'bBrType1',
  'BBrType2': 'bBrType2',
  'BrFailMoive': 'brFailMoive',
  'BShFactor1': 'bShFactor1',
  'BShFactor2': 'bShFactor2',
  'BShType1': 'bShType1',
  'BShType2': 'bShType2',
  'GKShFactor1': 'gKShFactor1',
  'GKShFactor2': 'gKShFactor2',
  'GKShType1': 'gKShType1',
  'GKShType2': 'gKShType2',
  'ShFailMoive': 'shFailMoive',
  'ShSucMoive': 'shSucMoive',
} as const;

export const AttackModeMeta = {
  tableName: 'AttackMode',
  dataFileName: 'AttackMode.json',
  primaryKey: 'id',
  searchFields: ['name'],
  fieldsCount: 53,
  requiredFields: ['id', 'name', 'a1AMOdds', 'a1BrFactor1', 'a1BrFactor2', 'a1BrType1', 'a1BrType2', 'a1DCOdds', 'a1DLOdds', 'a1DMOdds', 'a1DROdds', 'a1GKOdds', 'a1MCOdds', 'a1MLOdds', 'a1MROdds', 'a1ShFactor1', 'a1ShFactor2', 'a1ShType1', 'a1ShType2', 'a1STOdds', 'a1WLOdds', 'a1WROdds', 'a2AMOdds', 'a2DCOdds', 'a2DLOdds', 'a2DMOdds', 'a2DROdds', 'a2GKOdds', 'a2MCOdds', 'a2MLOdds', 'a2MROdds', 'a2ShFactor1', 'a2ShFactor2', 'a2ShType1', 'a2ShType2', 'a2STOdds', 'a2WLOdds', 'a2WROdds', 'bBrFactor1', 'bBrFactor2', 'bBrType1', 'bBrType2', 'brFailMoive', 'bShFactor1', 'bShFactor2', 'bShType1', 'bShType2', 'gKShFactor1', 'gKShFactor2', 'gKShType1', 'gKShType2', 'shFailMoive', 'shSucMoive'],
  optionalFields: [],
  renamedFieldsCount: 51,
  hasFieldMappings: true,
  isTableRenamed: false,
  fieldMappings: AttackModeFieldMappings,
  reverseFieldMappings: AttackModeReverseFieldMappings,
} as const;

export type AttackModeConfigMeta = typeof AttackModeMeta;
export type AttackModeFieldMapping = typeof AttackModeFieldMappings;
export type AttackModeReverseFieldMapping = typeof AttackModeReverseFieldMappings;
