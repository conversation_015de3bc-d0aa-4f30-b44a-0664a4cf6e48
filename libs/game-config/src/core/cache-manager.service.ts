import { Injectable, Logger } from '@nestjs/common';
import { LRUCache } from 'lru-cache';
import {DataType, RedisService} from '@common/redis';

/**
 * 缓存管理器
 * 实现分区分服的多级缓存（内存 + Redis）
 *
 * 架构责任边界：
 * - CacheManager：负责缓存逻辑（L1/L2缓存、命中统计、键名构建）
 * - RedisService：负责数据序列化/反序列化和Redis操作
 */
@Injectable()
export class CacheManager {
  private readonly logger = new Logger(CacheManager.name);
  
  // L1缓存：内存LRU缓存
  private readonly memory = new LRUCache<string, any>({ 
    max: 10000,           // 最大缓存项数
    ttl: 3600000,         // 1小时TTL
    allowStale: false,    // 不允许返回过期数据
    updateAgeOnGet: true, // 访问时更新年龄
  });

  // 缓存统计
  private stats = {
    memoryHits: 0,
    redisHits: 0,
    misses: 0,
    sets: 0,
    invalidations: 0,
  };

  constructor(private readonly redisService: RedisService) {}

  /**
   * 获取缓存数据
   * @param key 缓存键
   * @param dataType 数据类型：global（全局）、cross（跨服）、server（区服）
   */
  async get<T>(key: string, dataType: DataType = 'global'): Promise<T | null> {
    const cacheKey = this.buildCacheKey(key, dataType);
    
    try {
      // L1缓存查找
      const memoryValue = this.memory.get(cacheKey);
      if (memoryValue !== undefined) {
        this.recordHit('memory', cacheKey);
        return memoryValue;
      }
      
      // L2缓存查找（Redis分区分服）
      const redisValue = await this.redisService.get<T>(key, dataType);
      if (redisValue) {
        // 🔧 统一架构：RedisService负责序列化/反序列化，CacheManager只处理缓存逻辑
        // redisValue已经是反序列化后的对象，直接使用

        // 回填L1缓存
        this.memory.set(cacheKey, redisValue);
        this.recordHit('redis', cacheKey);
        return redisValue;
      }
      
      this.recordMiss(cacheKey);
      return null;
      
    } catch (error) {
      this.logger.error(`Cache get failed for key: ${cacheKey}`, error.stack);
      this.recordMiss(cacheKey);
      return null;
    }
  }

  /**
   * 设置缓存数据
   * @param key 缓存键
   * @param value 缓存值
   * @param ttl 过期时间（秒）
   * @param dataType 数据类型
   */
  async set<T>(
    key: string, 
    value: T, 
    ttl: number = 7200, 
    dataType: DataType = 'global'
  ): Promise<void> {
    const cacheKey = this.buildCacheKey(key, dataType);
    
    try {
      // 设置L1缓存
      this.memory.set(cacheKey, value);

      // 设置L2缓存（Redis分区分服）
      // 🔧 统一架构：RedisService负责序列化/反序列化，CacheManager只传递原始数据
      await this.redisService.set(key, value, ttl, dataType);
      
      this.stats.sets++;
      this.logger.debug(`Cache set: ${cacheKey}, TTL: ${ttl}s`);
      
    } catch (error) {
      this.logger.error(`Cache set failed for key: ${cacheKey}`, error.stack);
      throw error;
    }
  }

  /**
   * 批量设置缓存
   */
  async setBatch<T>(
    items: Map<string, T>, 
    ttl: number = 7200, 
    dataType: DataType = 'global'
  ): Promise<void> {
    const promises: Promise<void>[] = [];
    
    for (const [key, value] of items) {
      promises.push(this.set(key, value, ttl, dataType));
    }
    
    await Promise.all(promises);
  }

  /**
   * 删除缓存
   * @param pattern 缓存键模式（支持通配符*）
   * @param dataType 数据类型
   */
  async invalidate(pattern: string, dataType: DataType = 'global'): Promise<void> {
    const cachePattern = this.buildCacheKey(pattern, dataType);
    
    try {
      // 清理L1内存缓存
      if (pattern.includes('*')) {
        const regex = new RegExp(cachePattern.replace(/\*/g, '.*'));
        for (const key of this.memory.keys()) {
          if (regex.test(key)) {
            this.memory.delete(key);
          }
        }
      } else {
        this.memory.delete(cachePattern);
      }
      
      // 清理L2 Redis缓存（使用分区分服删除）
      await this.redisService.del(pattern, dataType);
      
      this.stats.invalidations++;
      this.logger.debug(`Cache invalidated: ${cachePattern}`);
      
    } catch (error) {
      this.logger.error(`Cache invalidation failed for pattern: ${cachePattern}`, error.stack);
      throw error;
    }
  }

  /**
   * 清理指定表的所有缓存
   */
  async invalidateTable(tableName: string, dataType: DataType = 'global'): Promise<void> {
    await this.invalidate(`config:${tableName}:*`, dataType);
  }

  /**
   * 清理所有配置缓存
   */
  async invalidateAll(dataType: DataType = 'global'): Promise<void> {
    await this.invalidate('config:*', dataType);
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const memoryStats = {
      size: this.memory.size,
      max: this.memory.max,
      calculatedSize: this.memory.calculatedSize,
    };

    const hitRate = this.stats.memoryHits + this.stats.redisHits + this.stats.misses;
    const hitRatio = hitRate > 0 ? 
      ((this.stats.memoryHits + this.stats.redisHits) / hitRate * 100).toFixed(2) : '0.00';

    return {
      memory: memoryStats,
      hits: {
        memory: this.stats.memoryHits,
        redis: this.stats.redisHits,
        total: this.stats.memoryHits + this.stats.redisHits,
      },
      misses: this.stats.misses,
      sets: this.stats.sets,
      invalidations: this.stats.invalidations,
      hitRatio: `${hitRatio}%`,
    };
  }

  /**
   * 重置缓存统计
   */
  resetStats(): void {
    this.stats = {
      memoryHits: 0,
      redisHits: 0,
      misses: 0,
      sets: 0,
      invalidations: 0,
    };
  }

  /**
   * 构建缓存键
   */
  private buildCacheKey(key: string, dataType: string): string {
    return `${dataType}:${key}`;
  }

  /**
   * 记录缓存命中
   */
  private recordHit(type: 'memory' | 'redis', key: string): void {
    if (type === 'memory') {
      this.stats.memoryHits++;
    } else {
      this.stats.redisHits++;
    }
    
    this.logger.debug(`Cache ${type} hit: ${key}`);
  }

  /**
   * 记录缓存未命中
   */
  private recordMiss(key: string): void {
    this.stats.misses++;
    this.logger.debug(`Cache miss: ${key}`);
  }
}

/**
 * 缓存统计信息接口
 */
export interface CacheStats {
  memory: {
    size: number;
    max: number;
    calculatedSize: number;
  };
  hits: {
    memory: number;
    redis: number;
    total: number;
  };
  misses: number;
  sets: number;
  invalidations: number;
  hitRatio: string;
}
