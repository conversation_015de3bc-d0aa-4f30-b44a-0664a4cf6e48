import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigLoader } from './config-loader.service';
import { CacheManager } from './cache-manager.service';
import { EventManager } from './event-manager.service';

/**
 * 配置管理器
 * 统一的配置访问接口，支持缓存和热更新
 */
@Injectable()
export class ConfigManager implements OnModuleInit {
  private readonly logger = new Logger(ConfigManager.name);
  private isInitialized = false;
  private initializationPromise: Promise<void>;

  constructor(
    private readonly loader: ConfigLoader,
    private readonly cache: CacheManager,
    private readonly events: EventManager,
  ) {}

  async onModuleInit() {
    this.initializationPromise = this.initialize();
    await this.initializationPromise;
  }

  /**
   * 初始化配置管理器
   * 🔥 优化：移除预加载职责，只负责基础系统初始化
   */
  private async initialize(): Promise<void> {
    try {
      this.logger.log('Initializing ConfigManager...');

      // 启动文件监听
      this.events.startWatching();

      // 监听配置更新事件
      this.events.onConfigUpdated(async (event) => {
        await this.handleConfigUpdate(event);
      });

      this.isInitialized = true;
      this.logger.log('ConfigManager initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize ConfigManager', error.stack);
      throw error;
    }
  }

  /**
   * 获取单个配置项
   */
  async get<T>(tableName: string, id: number): Promise<T | null> {
    await this.ensureInitialized();
    
    const cacheKey = `config:${tableName}:${id}`;
    
    try {
      // 先从缓存获取
      let config = await this.cache.get<T>(cacheKey, 'global');
      
      if (config === null) {
        // 缓存未命中，从文件加载
        config = await this.loader.loadById<T>(tableName, id);
        
        if (config) {
          // 缓存结果
          await this.cache.set(cacheKey, config, 7200, 'global');
        }
      }
      
      return config;
      
    } catch (error) {
      this.logger.error(`Failed to get config: ${tableName}:${id}`, error.stack);
      throw new ConfigError(`Cannot get ${tableName}:${id}: ${error.message}`);
    }
  }

  /**
   * 获取所有配置项
   */
  async getAll<T>(tableName: string): Promise<T[]> {
    await this.ensureInitialized();
    
    const cacheKey = `config:${tableName}:all`;
    
    try {
      // 先从缓存获取
      let configs = await this.cache.get<T[]>(cacheKey, 'global');
      
      if (configs === null) {
        // 缓存未命中，从文件加载
        configs = await this.loader.loadConfig<T>(tableName);
        
        // 缓存结果
        await this.cache.set(cacheKey, configs, 7200, 'global');
      }
      
      return configs || [];
      
    } catch (error) {
      this.logger.error(`Failed to get all configs: ${tableName}`, error.stack);
      throw new ConfigError(`Cannot get all ${tableName}: ${error.message}`);
    }
  }

  /**
   * 批量获取配置项
   */
  async getBatch<T>(tableName: string, ids: number[]): Promise<Map<number, T>> {
    await this.ensureInitialized();
    
    const result = new Map<number, T>();
    const missingIds: number[] = [];
    
    try {
      // 先从缓存批量获取
      for (const id of ids) {
        const cacheKey = `config:${tableName}:${id}`;
        const config = await this.cache.get<T>(cacheKey, 'global');
        
        if (config) {
          result.set(id, config);
        } else {
          missingIds.push(id);
        }
      }
      
      // 批量加载缺失的配置
      if (missingIds.length > 0) {
        const missingConfigs = await this.loader.loadBatch<T>(tableName, missingIds);
        
        // 缓存并添加到结果
        for (const [id, config] of missingConfigs) {
          const cacheKey = `config:${tableName}:${id}`;
          await this.cache.set(cacheKey, config, 7200, 'global');
          result.set(id, config);
        }
      }
      
      return result;
      
    } catch (error) {
      this.logger.error(`Failed to get batch configs: ${tableName}`, error.stack);
      throw new ConfigError(`Cannot get batch ${tableName}: ${error.message}`);
    }
  }

  /**
   * 搜索配置项
   */
  async search<T>(tableName: string, keyword: string): Promise<T[]> {
    await this.ensureInitialized();

    try {
      // 搜索不使用缓存，直接从文件搜索
      return await this.loader.search<T>(tableName, keyword);

    } catch (error) {
      this.logger.error(`Failed to search configs: ${tableName}`, error.stack);
      throw new ConfigError(`Cannot search ${tableName}: ${error.message}`);
    }
  }

  /**
   * 根据自定义条件筛选配置项
   * 优化：先从缓存获取全量数据，然后在内存中进行筛选，避免重复IO操作
   * @param tableName 配置表名称
   * @param predicate 筛选条件函数，返回true的项目会被包含在结果中
   * @returns 符合条件的配置项数组
   * @example
   * ```typescript
   * // 筛选等级大于10的英雄
   * const highLevelHeroes = await configManager.filter('Hero',
   *   hero => hero.level > 10
   * );
   * ```
   */
  async filter<T>(tableName: string, predicate: (item: T) => boolean): Promise<T[]> {
    await this.ensureInitialized();

    try {
      // 获取全量数据（利用现有缓存机制）
      const allConfigs = await this.getAll<T>(tableName);

      // 在内存中进行筛选
      return allConfigs.filter(predicate);

    } catch (error) {
      this.logger.error(`Failed to filter configs: ${tableName}`, error.stack);
      throw new ConfigError(`Cannot filter ${tableName}: ${error.message}`);
    }
  }

  /**
   * 根据指定字段和值筛选配置项
   * 优化：针对字段查询进行性能优化，支持索引查询（未来可扩展）
   * @param tableName 配置表名称
   * @param field 要筛选的字段名
   * @param value 字段值
   * @returns 字段值匹配的配置项数组
   * @example
   * ```typescript
   * // 获取指定队伍的所有球员配置
   * const teamPlayers = await configManager.findBy('Team', 'teamId', 90101);
   * ```
   */
  async findBy<T>(tableName: string, field: keyof T, value: any): Promise<T[]> {
    await this.ensureInitialized();

    try {
      // 使用filter方法实现，未来可以优化为索引查询
      return await this.filter<T>(tableName, (item: T) => item[field] === value);

    } catch (error) {
      this.logger.error(`Failed to findBy configs: ${tableName}.${String(field)}=${value}`, error.stack);
      throw new ConfigError(`Cannot findBy ${tableName}.${String(field)}: ${error.message}`);
    }
  }

  /**
   * 根据自定义条件查找第一个匹配的配置项
   * @param tableName 配置表名称
   * @param predicate 查找条件函数
   * @returns 第一个符合条件的配置项，如果没有找到则返回null
   * @example
   * ```typescript
   * // 查找第一个满足条件的英雄
   * const firstMage = await configManager.findOne('Hero',
   *   hero => hero.type === 'mage' && hero.level > 10
   * );
   * ```
   */
  async findOne<T>(tableName: string, predicate: (item: T) => boolean): Promise<T | null> {
    await this.ensureInitialized();

    try {
      // 获取全量数据
      const allConfigs = await this.getAll<T>(tableName);

      // 查找第一个匹配项
      const found = allConfigs.find(predicate);
      return found || null;

    } catch (error) {
      this.logger.error(`Failed to findOne config: ${tableName}`, error.stack);
      throw new ConfigError(`Cannot findOne ${tableName}: ${error.message}`);
    }
  }

  /**
   * 根据指定字段和值查找第一个匹配的配置项
   * @param tableName 配置表名称
   * @param field 要查找的字段名
   * @param value 字段值
   * @returns 第一个字段值匹配的配置项，如果没有找到则返回null
   * @example
   * ```typescript
   * // 查找指定名称的英雄
   * const hero = await configManager.findOneBy('Hero', 'name', '梅西');
   * ```
   */
  async findOneBy<T>(tableName: string, field: keyof T, value: any): Promise<T | null> {
    await this.ensureInitialized();

    try {
      // 使用findOne方法实现
      return await this.findOne<T>(tableName, (item: T) => item[field] === value);

    } catch (error) {
      this.logger.error(`Failed to findOneBy config: ${tableName}.${String(field)}=${value}`, error.stack);
      throw new ConfigError(`Cannot findOneBy ${tableName}.${String(field)}: ${error.message}`);
    }
  }

  /**
   * 重新加载指定表的配置
   */
  async reload(tableName: string): Promise<void> {
    await this.ensureInitialized();
    
    try {
      this.logger.log(`Reloading config table: ${tableName}`);
      
      // 清理缓存
      await this.cache.invalidateTable(tableName, 'global');
      
      // 验证配置文件
      const isValid = await this.loader.validateConfigFile(tableName);
      if (!isValid) {
        throw new Error(`Config file not found or invalid: ${tableName}`);
      }
      
      // 预加载到缓存
      await this.preloadTable(tableName);
      
      this.logger.log(`Config table reloaded: ${tableName}`);
      
    } catch (error) {
      this.logger.error(`Failed to reload config: ${tableName}`, error.stack);
      throw new ConfigError(`Cannot reload ${tableName}: ${error.message}`);
    }
  }

  /**
   * 获取健康状态
   */
  async getHealthStatus(): Promise<ConfigHealthStatus> {
    const cacheStats = this.cache.getStats();
    const availableTables = this.loader.getAvailableTables();
    
    return {
      isInitialized: this.isInitialized,
      availableTables: availableTables.length,
      tableNames: availableTables,
      cache: cacheStats,
      lastUpdate: new Date().toISOString(),
    };
  }


  /**
   * 预加载单个表
   */
  private async preloadTable(tableName: string): Promise<void> {
    const configs = await this.loader.loadConfig(tableName);
    const cacheKey = `config:${tableName}:all`;
    
    // 缓存所有数据
    await this.cache.set(cacheKey, configs, 7200, 'global');
    
    // 缓存单个项目
    for (const config of configs) {
      const meta = this.loader['getTableMeta'](tableName);
      const id = config[meta.primaryKey];
      const itemCacheKey = `config:${tableName}:${id}`;
      await this.cache.set(itemCacheKey, config, 7200, 'global');
    }
  }

  /**
   * 处理配置更新事件
   */
  private async handleConfigUpdate(event: any): Promise<void> {
    try {
      await this.reload(event.tableName);
      this.logger.log(`Config updated and reloaded: ${event.tableName}`);
    } catch (error) {
      this.logger.error(`Failed to handle config update: ${event.tableName}`, error.stack);
    }
  }

  /**
   * 确保已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initializationPromise;
    }
  }
}

/**
 * 配置错误
 */
export class ConfigError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ConfigError';
  }
}

/**
 * 配置健康状态接口
 */
export interface ConfigHealthStatus {
  isInitialized: boolean;
  availableTables: number;
  tableNames: string[];
  cache: any;
  lastUpdate: string;
}
