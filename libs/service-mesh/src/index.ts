// 核心服务
export * from './registry/server-aware-registry.service';
export * from './load-balancing/server-aware-load-balancer.service';

// 实例管理服务
export * from './lifecycle/instance-lifecycle.service';
export * from './orchestration/server-config-generator.service';
export * from './orchestration/container-orchestration.service';

// 自动注册服务
export * from './registry/service-auto-registration.service';

// 统一服务发现
export * from './discovery/unified-service-discovery.service';

// 全局服务注册
export * from './registry/global-service-registry.service';
export * from './registry/global-service-auto-registration.service';

// 🆕 集成的调用功能导出
export * from './client/microservice-client.service';
export * from './client/connection-pool.service';
export * from './client/utils/context-extractor.service';

// 🚀 生命周期管理导出
export * from './lifecycle/service-mesh-lifecycle.service';

// 🔧 配置导出
export * from './config/microservice.config';
export * from './config/default.config';

// 模块
export * from './service-mesh.module';

// 🚀 统一架构相关导出
export * from './interfaces/service-options.interface';
export * from './interfaces/service-instance.interfaces';
export * from './utils/service-inference.util';

// 🎯 配置构建器导出
export { ServiceConfigurationBuilder } from './config/service-configuration.builder';
export { ServiceConfiguration } from './interfaces/service-instance.interfaces';
