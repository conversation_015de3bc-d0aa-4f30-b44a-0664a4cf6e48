import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';

/**
 * 服务配置模板接口
 */
export interface ServiceConfigTemplate {
  serviceName: string;
  serverId: string;
  instanceIndex: number;
  port: number;
  host: string;
  environment: Record<string, string>;
  resources: {
    cpu: string;
    memory: string;
    disk?: string;
  };
  healthCheck: {
    path: string;
    interval: number;
    timeout: number;
    retries: number;
  };
  scaling: {
    minInstances: number;
    maxInstances: number;
    targetCPU: number;
    targetMemory: number;
  };
  networking: {
    ports: Array<{
      name: string;
      port: number;
      targetPort: number;
      protocol: 'TCP' | 'UDP';
    }>;
    ingress?: {
      enabled: boolean;
      host?: string;
      path?: string;
    };
  };
  volumes?: Array<{
    name: string;
    mountPath: string;
    type: 'configMap' | 'secret' | 'persistentVolume';
    source: string;
  }>;
  metadata: Record<string, any>;
}

/**
 * 部署配置生成选项
 */
export interface DeploymentGenerationOptions {
  platform: 'docker-compose' | 'kubernetes' | 'docker-swarm';
  environment: 'development' | 'staging' | 'production';
  namespace?: string;
  registry?: string;
  imageTag?: string;
  replicas?: number;
  enableMonitoring?: boolean;
  enableLogging?: boolean;
  enableTracing?: boolean;
}

/**
 * 企业级服务配置生成器
 * 
 * 核心功能：
 * - 多平台部署配置生成（Docker Compose、Kubernetes、Docker Swarm）
 * - 环境特定的配置模板
 * - 自动化的配置验证和优化
 * - 监控、日志、追踪集成
 * - 配置版本管理和回滚
 */
@Injectable()
export class ServerConfigGeneratorService {
  private readonly logger = new Logger(ServerConfigGeneratorService.name);
  
  // 服务配置模板
  private readonly serviceTemplates = new Map<string, Partial<ServiceConfigTemplate>>();
  
  // 环境特定配置
  private readonly environmentConfigs = new Map<string, Record<string, any>>();

  constructor(private readonly configService: ConfigService) {
    this.initializeServiceTemplates();
    this.initializeEnvironmentConfigs();
  }

  /**
   * 生成服务配置模板
   */
  generateServiceConfig(
    serviceName: string,
    serverId: string,
    instanceIndex: number = 1,
    options: Partial<DeploymentGenerationOptions> = {}
  ): ServiceConfigTemplate {
    this.logger.debug(`🔧 生成服务配置: ${serviceName}@${serverId}-${instanceIndex}`);

    const baseTemplate = this.serviceTemplates.get(serviceName) || {};
    const envConfig = this.environmentConfigs.get(options.environment || 'development') || {};
    
    const config: ServiceConfigTemplate = {
      serviceName,
      serverId,
      instanceIndex,
      port: this.calculatePort(serviceName, instanceIndex),
      host: options.environment === 'production' ? `${serviceName}-${serverId}-${instanceIndex}` : 'localhost',
      
      environment: {
        NODE_ENV: options.environment || 'development',
        SERVICE_NAME: serviceName,
        SERVER_ID: serverId,
        INSTANCE_INDEX: instanceIndex.toString(),
        PORT: this.calculatePort(serviceName, instanceIndex).toString(),
        
        // 数据库配置
        [`${serviceName.toUpperCase()}_MONGODB_URI`]: this.generateMongoUri(serviceName, serverId),
        
        // Redis配置
        REDIS_HOST: envConfig.redis?.host || 'redis',
        REDIS_PORT: envConfig.redis?.port || '6379',
        
        // 微服务配置
        MICROSERVICE_CLIENT_ID: `${serviceName}-${serverId}`,
        GATEWAY_URL: envConfig.gateway?.url || 'http://gateway:3000',
        
        // 监控配置
        METRICS_ENABLED: (options.enableMonitoring !== false).toString(),
        METRICS_PORT: (9100 + this.getServiceOffset(serviceName) + instanceIndex - 1).toString(),
        
        // 日志配置
        LOG_LEVEL: envConfig.logging?.level || 'info',
        LOG_FORMAT: envConfig.logging?.format || 'json',
        
        // 追踪配置
        TRACING_ENABLED: (options.enableTracing === true).toString(),
        JAEGER_ENDPOINT: envConfig.tracing?.endpoint || '',
        
        ...baseTemplate.environment,
        ...envConfig.environment,
      },
      
      resources: {
        cpu: this.getResourceRequirement(serviceName, 'cpu', options.environment),
        memory: this.getResourceRequirement(serviceName, 'memory', options.environment),
        disk: this.getResourceRequirement(serviceName, 'disk', options.environment),
        ...baseTemplate.resources,
      },
      
      healthCheck: {
        path: '/health/simple',
        interval: 30000,
        timeout: 10000,
        retries: 3,
        ...baseTemplate.healthCheck,
      },
      
      scaling: {
        minInstances: this.getScalingConfig(serviceName, 'min', options.environment),
        maxInstances: this.getScalingConfig(serviceName, 'max', options.environment),
        targetCPU: 70,
        targetMemory: 80,
        ...baseTemplate.scaling,
      },
      
      networking: {
        ports: [
          {
            name: 'http',
            port: this.calculatePort(serviceName, instanceIndex),
            targetPort: this.calculatePort(serviceName, instanceIndex),
            protocol: 'TCP',
          },
          {
            name: 'metrics',
            port: 9100 + this.getServiceOffset(serviceName) + instanceIndex - 1,
            targetPort: 9100 + this.getServiceOffset(serviceName) + instanceIndex - 1,
            protocol: 'TCP',
          },
        ],
        ingress: {
          enabled: options.environment === 'production',
          host: `${serviceName}-${serverId}.example.com`,
          path: `/api/${serviceName}`,
        },
        ...baseTemplate.networking,
      },
      
      volumes: [
        {
          name: 'config',
          mountPath: '/app/config',
          type: 'configMap',
          source: `${serviceName}-${serverId}-config`,
        },
        {
          name: 'logs',
          mountPath: '/app/logs',
          type: 'persistentVolume',
          source: `${serviceName}-${serverId}-logs`,
        },
        ...(baseTemplate.volumes || []),
      ],
      
      metadata: {
        labels: {
          app: serviceName,
          server: serverId,
          instance: instanceIndex.toString(),
          version: this.configService.get('APP_VERSION', '1.0.0'),
          environment: options.environment || 'development',
        },
        annotations: {
          'deployment.kubernetes.io/revision': '1',
          'prometheus.io/scrape': 'true',
          'prometheus.io/port': (9100 + this.getServiceOffset(serviceName) + instanceIndex - 1).toString(),
          'prometheus.io/path': '/metrics',
        },
        ...baseTemplate.metadata,
      },
    };

    this.logger.debug(`✅ 服务配置生成完成: ${serviceName}@${serverId}-${instanceIndex}`);
    return config;
  }

  /**
   * 生成Docker Compose配置
   */
  generateDockerCompose(
    services: Array<{ serviceName: string; serverId: string; instances: number }>,
    options: DeploymentGenerationOptions
  ): string {
    this.logger.log(`🐳 生成Docker Compose配置: ${services.length} 个服务`);

    const compose = {
      version: '3.8',
      networks: {
        microservices: {
          external: true,
        },
      },
      volumes: this.generateDockerVolumes(services),
      services: {} as Record<string, any>,
    };

    // 生成每个服务的配置
    for (const service of services) {
      for (let i = 1; i <= service.instances; i++) {
        const config = this.generateServiceConfig(service.serviceName, service.serverId, i, options);
        const serviceName = `${service.serviceName}-${service.serverId}-${i}`;
        
        compose.services[serviceName] = {
          build: {
            context: '.',
            dockerfile: `apps/${service.serviceName}/Dockerfile`,
          },
          container_name: serviceName,
          environment: config.environment,
          ports: config.networking.ports.map(p => `${p.port}:${p.targetPort}`),
          volumes: config.volumes?.map(v => `${v.source}:${v.mountPath}`) || [],
          networks: ['microservices'],
          restart: 'unless-stopped',
          healthcheck: {
            test: [`CMD`, `curl`, `-f`, `http://localhost:${config.port}${config.healthCheck.path}`],
            interval: `${config.healthCheck.interval / 1000}s`,
            timeout: `${config.healthCheck.timeout / 1000}s`,
            retries: config.healthCheck.retries,
            start_period: '40s',
          },
          labels: Object.entries(config.metadata.labels).map(([k, v]) => `${k}=${v}`),
          deploy: {
            resources: {
              limits: {
                cpus: config.resources.cpu,
                memory: config.resources.memory,
              },
            },
          },
        };
      }
    }

    return yaml.dump(compose, { indent: 2 });
  }

  /**
   * 生成Kubernetes配置
   */
  generateKubernetes(
    services: Array<{ serviceName: string; serverId: string; instances: number }>,
    options: DeploymentGenerationOptions
  ): string[] {
    this.logger.log(`☸️ 生成Kubernetes配置: ${services.length} 个服务`);

    const manifests: string[] = [];

    for (const service of services) {
      const config = this.generateServiceConfig(service.serviceName, service.serverId, 1, options);
      
      // Deployment
      const deployment = {
        apiVersion: 'apps/v1',
        kind: 'Deployment',
        metadata: {
          name: `${service.serviceName}-${service.serverId}`,
          namespace: options.namespace || 'default',
          labels: config.metadata.labels,
          annotations: config.metadata.annotations,
        },
        spec: {
          replicas: service.instances,
          selector: {
            matchLabels: {
              app: service.serviceName,
              server: service.serverId,
            },
          },
          template: {
            metadata: {
              labels: config.metadata.labels,
              annotations: config.metadata.annotations,
            },
            spec: {
              containers: [
                {
                  name: service.serviceName,
                  image: `${options.registry || 'localhost'}/${service.serviceName}:${options.imageTag || 'latest'}`,
                  ports: config.networking.ports.map(p => ({
                    name: p.name,
                    containerPort: p.targetPort,
                    protocol: p.protocol,
                  })),
                  env: Object.entries(config.environment).map(([name, value]) => ({ name, value })),
                  resources: {
                    requests: {
                      cpu: config.resources.cpu,
                      memory: config.resources.memory,
                    },
                    limits: {
                      cpu: config.resources.cpu,
                      memory: config.resources.memory,
                    },
                  },
                  livenessProbe: {
                    httpGet: {
                      path: config.healthCheck.path,
                      port: config.port,
                    },
                    initialDelaySeconds: 30,
                    periodSeconds: config.healthCheck.interval / 1000,
                    timeoutSeconds: config.healthCheck.timeout / 1000,
                    failureThreshold: config.healthCheck.retries,
                  },
                  readinessProbe: {
                    httpGet: {
                      path: '/health/ready',
                      port: config.port,
                    },
                    initialDelaySeconds: 10,
                    periodSeconds: 10,
                  },
                  volumeMounts: config.volumes?.map(v => ({
                    name: v.name,
                    mountPath: v.mountPath,
                  })) || [],
                },
              ],
              volumes: config.volumes?.map(v => ({
                name: v.name,
                [v.type]: {
                  name: v.source,
                },
              })) || [],
            },
          },
        },
      };

      // Service
      const k8sService = {
        apiVersion: 'v1',
        kind: 'Service',
        metadata: {
          name: `${service.serviceName}-${service.serverId}`,
          namespace: options.namespace || 'default',
          labels: config.metadata.labels,
        },
        spec: {
          selector: {
            app: service.serviceName,
            server: service.serverId,
          },
          ports: config.networking.ports.map(p => ({
            name: p.name,
            port: p.port,
            targetPort: p.targetPort,
            protocol: p.protocol,
          })),
          type: 'ClusterIP',
        },
      };

      // HorizontalPodAutoscaler
      const hpa = {
        apiVersion: 'autoscaling/v2',
        kind: 'HorizontalPodAutoscaler',
        metadata: {
          name: `${service.serviceName}-${service.serverId}`,
          namespace: options.namespace || 'default',
        },
        spec: {
          scaleTargetRef: {
            apiVersion: 'apps/v1',
            kind: 'Deployment',
            name: `${service.serviceName}-${service.serverId}`,
          },
          minReplicas: config.scaling.minInstances,
          maxReplicas: config.scaling.maxInstances,
          metrics: [
            {
              type: 'Resource',
              resource: {
                name: 'cpu',
                target: {
                  type: 'Utilization',
                  averageUtilization: config.scaling.targetCPU,
                },
              },
            },
            {
              type: 'Resource',
              resource: {
                name: 'memory',
                target: {
                  type: 'Utilization',
                  averageUtilization: config.scaling.targetMemory,
                },
              },
            },
          ],
        },
      };

      manifests.push(yaml.dump(deployment, { indent: 2 }));
      manifests.push('---');
      manifests.push(yaml.dump(k8sService, { indent: 2 }));
      manifests.push('---');
      manifests.push(yaml.dump(hpa, { indent: 2 }));
      manifests.push('---');
    }

    return manifests;
  }

  /**
   * 保存配置到文件
   */
  async saveConfigToFile(content: string, filePath: string): Promise<void> {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(filePath, content, 'utf8');
    this.logger.log(`💾 配置已保存: ${filePath}`);
  }

  /**
   * 验证配置
   */
  validateConfig(config: ServiceConfigTemplate): string[] {
    const errors: string[] = [];
    
    if (!config.serviceName) {
      errors.push('服务名称不能为空');
    }
    
    if (!config.serverId) {
      errors.push('区服ID不能为空');
    }
    
    if (config.port < 1024 || config.port > 65535) {
      errors.push('端口号必须在1024-65535范围内');
    }
    
    if (!config.resources.cpu || !config.resources.memory) {
      errors.push('必须指定CPU和内存资源');
    }
    
    return errors;
  }

  // ==================== 私有方法 ====================

  /**
   * 初始化服务模板
   */
  private initializeServiceTemplates(): void {
    // Character服务模板
    this.serviceTemplates.set('character', {
      resources: { cpu: '500m', memory: '512Mi' },
      scaling: { minInstances: 2, maxInstances: 5, targetCPU: 70, targetMemory: 80 },
    });

    // Hero服务模板
    this.serviceTemplates.set('hero', {
      resources: { cpu: '500m', memory: '512Mi' },
      scaling: { minInstances: 2, maxInstances: 5, targetCPU: 70, targetMemory: 80 },
    });

    // Economy服务模板
    this.serviceTemplates.set('economy', {
      resources: { cpu: '300m', memory: '256Mi' },
      scaling: { minInstances: 1, maxInstances: 3, targetCPU: 70, targetMemory: 80 },
    });
    
    // 其他服务...
  }

  /**
   * 初始化环境配置
   */
  private initializeEnvironmentConfigs(): void {
    this.environmentConfigs.set('development', {
      redis: { host: 'localhost', port: '6379' },
      gateway: { url: 'http://localhost:3000' },
      logging: { level: 'debug', format: 'pretty' },
      environment: { DEBUG: 'true' },
    });
    
    this.environmentConfigs.set('production', {
      redis: { host: 'redis-cluster', port: '6379' },
      gateway: { url: 'http://gateway:3000' },
      logging: { level: 'info', format: 'json' },
      tracing: { endpoint: 'http://jaeger:14268/api/traces' },
      environment: { DEBUG: 'false' },
    });
  }

  /**
   * 计算服务端口
   */
  private calculatePort(serviceName: string, instanceIndex: number): number {
    const basePort = 3000 + this.getServiceOffset(serviceName);
    return basePort + (instanceIndex - 1) * 10;
  }

  /**
   * 获取服务偏移量
   */
  private getServiceOffset(serviceName: string): number {
    const offsets: Record<string, number> = {
      auth: 1, character: 2, hero: 3, economy: 4,
      social: 5, activity: 6, match: 7,
    };
    return offsets[serviceName] || 10;
  }

  /**
   * 生成MongoDB连接URI
   */
  private generateMongoUri(serviceName: string, serverId: string): string {
    const host = this.configService.get('MONGODB_HOST', 'mongodb');
    const port = this.configService.get('MONGODB_PORT', '27017');
    const dbName = serviceName === 'auth' ? 'auth_db' : `${serviceName}_db_${serverId}`;
    
    return `mongodb://${serviceName}-admin:password@${host}:${port}/${dbName}`;
  }

  /**
   * 获取资源需求
   */
  private getResourceRequirement(serviceName: string, resource: string, environment?: string): string {
    const requirements: Record<string, Record<string, Record<string, string>>> = {
      development: {
        character: { cpu: '200m', memory: '256Mi', disk: '1Gi' },
        hero: { cpu: '200m', memory: '256Mi', disk: '1Gi' },
        economy: { cpu: '100m', memory: '128Mi', disk: '500Mi' },
      },
      production: {
        character: { cpu: '1000m', memory: '1Gi', disk: '5Gi' },
        hero: { cpu: '1000m', memory: '1Gi', disk: '5Gi' },
        economy: { cpu: '500m', memory: '512Mi', disk: '2Gi' },
      },
    };
    
    const env = environment || 'development';
    return requirements[env]?.[serviceName]?.[resource] || '100m';
  }

  /**
   * 获取扩缩容配置
   */
  private getScalingConfig(serviceName: string, type: 'min' | 'max', environment?: string): number {
    const configs: Record<string, Record<string, Record<string, number>>> = {
      development: {
        character: { min: 1, max: 2 },
        hero: { min: 1, max: 2 },
        economy: { min: 1, max: 1 },
      },
      production: {
        character: { min: 2, max: 10 },
        hero: { min: 2, max: 10 },
        economy: { min: 1, max: 5 },
      },
    };
    
    const env = environment || 'development';
    return configs[env]?.[serviceName]?.[type] || 1;
  }

  /**
   * 生成Docker卷配置
   */
  private generateDockerVolumes(services: Array<{ serviceName: string; serverId: string }>): Record<string, any> {
    const volumes: Record<string, any> = {};
    
    for (const service of services) {
      volumes[`${service.serviceName}-${service.serverId}-logs`] = {
        driver: 'local',
      };
    }
    
    return volumes;
  }
}
