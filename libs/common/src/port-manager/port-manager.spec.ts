import * as net from 'net';
import { PortManager } from './port-manager';

describe('PortManager', () => {
  // 保存原始环境变量
  const originalEnv = process.env;

  beforeEach(() => {
    // 重置环境变量
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    // 恢复原始环境变量
    process.env = originalEnv;
  });

  describe('calculatePort', () => {
    it('应该正确计算character服务的端口', () => {
      const port = PortManager.calculatePort('character', 'server_001', 0);
      expect(port).toBe(3210); // 3200 + (1 * 10) + 0
    });

    it('应该正确处理不同的区服ID', () => {
      expect(PortManager.calculatePort('character', 'server_001', 0)).toBe(3210);
      expect(PortManager.calculatePort('character', 'server_002', 0)).toBe(3220);
      expect(PortManager.calculatePort('character', 'server_123', 0)).toBe(4430);
    });

    it('应该正确处理不同的实例ID', () => {
      expect(PortManager.calculatePort('character', 'server_001', 0)).toBe(3210);
      expect(PortManager.calculatePort('character', 'server_001', 1)).toBe(3211);
      expect(PortManager.calculatePort('character', 'server_001', 9)).toBe(3219);
    });

    it('应该正确处理无区服ID的情况', () => {
      expect(PortManager.calculatePort('character', undefined, 0)).toBe(3200);
      expect(PortManager.calculatePort('character', '', 0)).toBe(3200);
    });

    it('应该正确计算所有服务的端口', () => {
      expect(PortManager.calculatePort('gateway', 'server_001', 0)).toBe(3010);
      expect(PortManager.calculatePort('auth', 'server_001', 0)).toBe(3110);
      expect(PortManager.calculatePort('hero', 'server_001', 0)).toBe(3310);
      expect(PortManager.calculatePort('economy', 'server_001', 0)).toBe(3410);
      expect(PortManager.calculatePort('social', 'server_001', 0)).toBe(3510);
      expect(PortManager.calculatePort('activity', 'server_001', 0)).toBe(3610);
      expect(PortManager.calculatePort('match', 'server_001', 0)).toBe(3710);
    });

    it('应该抛出错误当服务名未知时', () => {
      expect(() => PortManager.calculatePort('unknown', 'server_001', 0))
        .toThrow('未找到服务 unknown 的基础端口配置');
    });

    it('应该抛出错误当实例ID超出范围时', () => {
      expect(() => PortManager.calculatePort('character', 'server_001', -1))
        .toThrow('实例ID -1 超出有效范围 [0-9]');
      
      expect(() => PortManager.calculatePort('character', 'server_001', 10))
        .toThrow('实例ID 10 超出有效范围 [0-9]');
    });

    it('应该抛出错误当区服编号过大时', () => {
      expect(() => PortManager.calculatePort('character', 'server_9999', 0))
        .toThrow('区服编号 9999 过大，最大支持999');
    });

    it('应该抛出错误当计算端口超出范围时', () => {
      // 模拟极大的基础端口
      process.env.CHARACTER_BASE_PORT = '65000';
      expect(() => PortManager.calculatePort('character', 'server_100', 0))
        .toThrow('计算的端口');
    });
  });

  describe('extractServerNumber', () => {
    it('应该正确提取区服编号', () => {
      expect(PortManager.extractServerNumber('server_001')).toBe(1);
      expect(PortManager.extractServerNumber('server_123')).toBe(123);
      expect(PortManager.extractServerNumber('test_999')).toBe(999);
      expect(PortManager.extractServerNumber('abc_456')).toBe(456);
    });

    it('应该处理无数字的情况', () => {
      expect(PortManager.extractServerNumber('server')).toBe(0);
      expect(PortManager.extractServerNumber('test_abc')).toBe(0);
      expect(PortManager.extractServerNumber('no_numbers_here')).toBe(0);
    });

    it('应该处理特殊输入', () => {
      expect(PortManager.extractServerNumber(undefined)).toBe(0);
      expect(PortManager.extractServerNumber('')).toBe(0);
      expect(PortManager.extractServerNumber('123')).toBe(123);
    });

    it('应该抛出错误当区服编号过大时', () => {
      expect(() => PortManager.extractServerNumber('server_1000'))
        .toThrow('区服编号 1000 过大，最大支持999');
    });
  });

  describe('getBasePort', () => {
    it('应该从环境变量读取基础端口', () => {
      process.env.CHARACTER_BASE_PORT = '4000';
      expect(PortManager.getBasePort('character')).toBe(4000);
    });

    it('应该使用默认配置当环境变量不存在时', () => {
      delete process.env.CHARACTER_BASE_PORT;
      expect(PortManager.getBasePort('character')).toBe(3200);
    });

    it('应该处理环境变量中的空格', () => {
      process.env.CHARACTER_BASE_PORT = ' 4000 ';
      expect(PortManager.getBasePort('character')).toBe(4000);
    });

    it('应该抛出错误当环境变量无效时', () => {
      process.env.CHARACTER_BASE_PORT = 'invalid';
      expect(PortManager.getBasePort('character')).toBe(3200); // 回退到默认值
    });

    it('应该抛出错误当服务未知时', () => {
      expect(() => PortManager.getBasePort('unknown'))
        .toThrow('未找到服务 unknown 的基础端口配置');
    });
  });

  describe('validatePortAvailability', () => {
    it('应该检测端口可用性', async () => {
      // 使用一个不太可能被占用的端口
      const port = 19999;
      const isAvailable = await PortManager.validatePortAvailability(port);
      expect(typeof isAvailable).toBe('boolean');
    });

    it('应该检测端口被占用的情况', async () => {
      // 创建一个服务器占用端口
      const server = net.createServer();
      const port = 19998;
      
      await new Promise<void>(resolve => {
        server.listen(port, () => resolve());
      });
      
      const isAvailable = await PortManager.validatePortAvailability(port);
      expect(isAvailable).toBe(false);
      
      server.close();
    });

    it('应该处理无效端口号', async () => {
      expect(await PortManager.validatePortAvailability(0)).toBe(false);
      expect(await PortManager.validatePortAvailability(-1)).toBe(false);
      expect(await PortManager.validatePortAvailability(65536)).toBe(false);
    });

    it('应该有超时机制', async () => {
      const startTime = Date.now();
      await PortManager.validatePortAvailability(19997);
      const endTime = Date.now();
      
      // 应该在合理时间内完成（不超过6秒，包含5秒超时）
      expect(endTime - startTime).toBeLessThan(6000);
    }, 10000);
  });

  describe('getPortRange', () => {
    it('应该返回正确的端口范围', () => {
      const range = PortManager.getPortRange('character');
      expect(range.min).toBe(3200);
      expect(range.max).toBe(4199); // 3200 + 1000 - 1
    });

    it('应该使用环境变量中的范围大小', () => {
      process.env.PORT_RANGE_SIZE = '100';
      const range = PortManager.getPortRange('character');
      expect(range.min).toBe(3200);
      expect(range.max).toBe(3299); // 3200 + 100 - 1
    });
  });

  describe('getServiceUrl', () => {
    it('应该生成正确的服务URL', () => {
      const url = PortManager.getServiceUrl('character', 'server_001', 0);
      expect(url).toBe('http://localhost:3210');
    });

    it('应该支持不同的协议', () => {
      expect(PortManager.getServiceUrl('character', 'server_001', 0, 'https'))
        .toBe('https://localhost:3210');
      
      expect(PortManager.getServiceUrl('character', 'server_001', 0, 'ws'))
        .toBe('ws://localhost:3210');
    });

    it('应该支持自定义主机', () => {
      const url = PortManager.getServiceUrl('character', 'server_001', 0, 'http', '*************');
      expect(url).toBe('http://*************:3210');
    });
  });

  describe('validateMultiplePorts', () => {
    it('应该批量验证端口可用性', async () => {
      const ports = [19990, 19991, 19992];
      const results = await PortManager.validateMultiplePorts(ports);
      
      expect(results.size).toBe(3);
      ports.forEach(port => {
        expect(results.has(port)).toBe(true);
        expect(typeof results.get(port)).toBe('boolean');
      });
    });
  });

  describe('findNextAvailablePort', () => {
    it('应该找到下一个可用端口', async () => {
      const startPort = 19980;
      const availablePort = await PortManager.findNextAvailablePort(startPort, 5);
      
      expect(availablePort).toBeGreaterThanOrEqual(startPort);
      expect(availablePort).toBeLessThan(startPort + 5);
    });

    it('应该在没有可用端口时返回null', async () => {
      // 占用一系列端口
      const servers: net.Server[] = [];
      const startPort = 19970;
      
      for (let i = 0; i < 3; i++) {
        const server = net.createServer();
        await new Promise<void>(resolve => {
          server.listen(startPort + i, () => resolve());
        });
        servers.push(server);
      }
      
      const availablePort = await PortManager.findNextAvailablePort(startPort, 3);
      expect(availablePort).toBeNull();
      
      // 清理
      servers.forEach(server => server.close());
    });
  });

  describe('validatePortConfiguration', () => {
    it('应该验证端口配置', () => {
      const result = PortManager.validatePortConfiguration();
      
      expect(result).toHaveProperty('valid');
      expect(result).toHaveProperty('errors');
      expect(result).toHaveProperty('warnings');
      expect(Array.isArray(result.errors)).toBe(true);
      expect(Array.isArray(result.warnings)).toBe(true);
    });

    it('应该检测配置错误', () => {
      // 模拟无效配置
      process.env.INVALID_SERVICE_BASE_PORT = 'invalid';
      
      const result = PortManager.validatePortConfiguration();
      // 由于我们没有invalid_service在默认配置中，这应该不会产生错误
      expect(typeof result.valid).toBe('boolean');
    });
  });
});
