import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';
import { DataType } from './types/redis.types';

export interface LockOptions {
  ttl?: number; // 锁的生存时间（秒）
  retryDelay?: number; // 重试延迟（毫秒）
  maxRetries?: number; // 最大重试次数
  identifier?: string; // 锁的标识符

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

export interface LockInfo {
  key: string;
  identifier: string;
  acquiredAt: Date;
  ttl: number;
  isLocked: boolean;
}

export interface LockStats {
  totalAcquired: number;
  totalReleased: number;
  totalFailed: number;
  activeLocks: number;
  averageHoldTime: number;
}

@Injectable()
export class RedisLockService {
  private readonly logger = new Logger(RedisLockService.name);
  private readonly activeLocks = new Map<string, LockInfo>();
  private stats: LockStats = {
    totalAcquired: 0,
    totalReleased: 0,
    totalFailed: 0,
    activeLocks: 0,
    averageHoldTime: 0,
  };

  // Lua 脚本用于原子性操作
  private readonly acquireLockScript = `
    if redis.call("get", KEYS[1]) == false then
      redis.call("set", KEYS[1], ARGV[1], "EX", ARGV[2])
      return 1
    else
      return 0
    end
  `;

  private readonly releaseLockScript = `
    if redis.call("get", KEYS[1]) == ARGV[1] then
      return redis.call("del", KEYS[1])
    else
      return 0
    end
  `;

  private readonly extendLockScript = `
    if redis.call("get", KEYS[1]) == ARGV[1] then
      return redis.call("expire", KEYS[1], ARGV[2])
    else
      return 0
    end
  `;

  constructor(private readonly redisService: RedisService) {}

  // ==================== 基础锁操作 ====================

  /**
   * 获取分布式锁 
   */
  async acquireLock(key: string, options: LockOptions = {}): Promise<string | null> {
    const {
      ttl = 30,
      retryDelay = 100,
      maxRetries = 0,
      identifier = this.generateIdentifier(),
      dataType,
      serverId
    } = options;

    const lockKey = this.buildLockKey(key, dataType);
    // 构建完整的Redis键名
    const fullLockKey = this.redisService.buildDataTypeKey(lockKey, dataType, serverId);
    let attempts = 0;

    while (attempts <= maxRetries) {
      try {
        const result = await this.redisService.getClient().eval(
          this.acquireLockScript,
          1,
          fullLockKey,
          identifier,
          ttl
        );

        if (result === 1) {
          const lockInfo: LockInfo = {
            key: fullLockKey,
            identifier,
            acquiredAt: new Date(),
            ttl,
            isLocked: true,
          };

          this.activeLocks.set(fullLockKey, lockInfo);
          this.stats.totalAcquired++;
          this.stats.activeLocks = this.activeLocks.size;

          this.logger.debug(`Lock acquired: ${fullLockKey} with identifier ${identifier} (dataType: ${dataType || 'server'})`);
          return identifier;
        }

        if (attempts < maxRetries) {
          await this.sleep(retryDelay);
          attempts++;
        } else {
          break;
        }
      } catch (error) {
        this.logger.error(`Failed to acquire lock ${lockKey}: ${error.message}`);
        this.stats.totalFailed++;
        throw error;
      }
    }

    this.stats.totalFailed++;
    this.logger.debug(`Failed to acquire lock: ${lockKey} after ${attempts + 1} attempts`);
    return null;
  }

  /**
   * 释放分布式锁 
   */
  async releaseLock(
    key: string,
    identifier: string,
    dataType?: DataType,
    serverId?: string
  ): Promise<boolean> {
    const lockKey = this.buildLockKey(key, dataType);
    const fullLockKey = this.redisService.buildDataTypeKey(lockKey, dataType, serverId);

    try {
      const result = await this.redisService.getClient().eval(
        this.releaseLockScript,
        1,
        fullLockKey,
        identifier
      );

      if (result === 1) {
        const lockInfo = this.activeLocks.get(fullLockKey);
        if (lockInfo) {
          const holdTime = Date.now() - lockInfo.acquiredAt.getTime();
          this.updateAverageHoldTime(holdTime);
          this.activeLocks.delete(fullLockKey);
        }

        this.stats.totalReleased++;
        this.stats.activeLocks = this.activeLocks.size;

        this.logger.debug(`Lock released: ${fullLockKey} (dataType: ${dataType || 'server'})`);
        return true;
      }

      this.logger.warn(`Failed to release lock ${fullLockKey}: identifier mismatch or lock not found`);
      return false;
    } catch (error) {
      this.logger.error(`Failed to release lock ${fullLockKey}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 延长锁的生存时间 
   */
  async extendLock(key: string, identifier: string, ttl: number, dataType?: DataType, serverId?: string): Promise<boolean> {
    const lockKey = this.buildLockKey(key, dataType);
    const fullLockKey = this.redisService.buildDataTypeKey(lockKey, dataType, serverId);

    try {
      const result = await this.redisService.getClient().eval(
        this.extendLockScript,
        1,
        fullLockKey,
        identifier,
        ttl
      );

      if (result === 1) {
        const lockInfo = this.activeLocks.get(fullLockKey);
        if (lockInfo) {
          lockInfo.ttl = ttl;
        }

        this.logger.debug(`Lock extended: ${lockKey} with TTL ${ttl}s`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error extending lock ${lockKey}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查锁是否存在
   */
  async isLocked(key: string): Promise<boolean> {
    const lockKey = this.buildLockKey(key);
    return await this.redisService.exists(lockKey);
  }

  /**
   * 获取锁信息
   */
  async getLockInfo(key: string): Promise<LockInfo | null> {
    const lockKey = this.buildLockKey(key);
    const lockInfo = this.activeLocks.get(lockKey);

    if (lockInfo) {
      const ttl = await this.redisService.ttl(lockKey);
      lockInfo.ttl = ttl;
      lockInfo.isLocked = ttl > 0;
      return lockInfo;
    }

    return null;
  }

  // ==================== 高级锁操作 ====================

  /**
   * 带自动释放的锁执行
   */
  async withLock<T>(
    key: string,
    fn: () => Promise<T>,
    options: LockOptions = {}
  ): Promise<T> {
    const identifier = await this.acquireLock(key, options);
    
    if (!identifier) {
      throw new Error(`Failed to acquire lock: ${key}`);
    }

    try {
      const result = await fn();
      return result;
    } finally {
      await this.releaseLock(key, identifier);
    }
  }

  /**
   * 可重入锁（同一标识符可以多次获取）
   */
  async acquireReentrantLock(key: string, options: LockOptions = {}): Promise<string | null> {
    const {
      ttl = 30,
      identifier = this.generateIdentifier(),
    } = options;

    const lockKey = this.buildLockKey(key);
    const countKey = `${lockKey}:count`;

    try {
      // 使用 Lua 脚本实现可重入锁
      const reentrantScript = `
        local current = redis.call("get", KEYS[1])
        if current == false or current == ARGV[1] then
          redis.call("set", KEYS[1], ARGV[1], "EX", ARGV[2])
          local count = redis.call("incr", KEYS[2])
          redis.call("expire", KEYS[2], ARGV[2])
          return count
        else
          return 0
        end
      `;

      const result = await this.redisService.getClient().eval(
        reentrantScript,
        2,
        lockKey,
        countKey,
        identifier,
        ttl
      );

      if ((result as number) > 0) {
        this.logger.debug(`Reentrant lock acquired: ${lockKey}, count: ${result}`);
        return identifier;
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to acquire reentrant lock ${lockKey}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 释放可重入锁
   */
  async releaseReentrantLock(key: string, identifier: string): Promise<boolean> {
    const lockKey = this.buildLockKey(key);
    const countKey = `${lockKey}:count`;

    try {
      const reentrantReleaseScript = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          local count = redis.call("decr", KEYS[2])
          if count <= 0 then
            redis.call("del", KEYS[1])
            redis.call("del", KEYS[2])
          end
          return count
        else
          return -1
        end
      `;

      const result = await this.redisService.getClient().eval(
        reentrantReleaseScript,
        2,
        lockKey,
        countKey,
        identifier
      );

      if ((result as number) >= 0) {
        this.logger.debug(`Reentrant lock released: ${lockKey}, remaining count: ${result}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error releasing reentrant lock ${lockKey}: ${error.message}`);
      throw error;
    }
  }

  // ==================== 游戏业务锁 ====================

  /**
   * 球员转会锁
   */
  async acquireTransferLock(playerId: string, options: LockOptions = {}): Promise<string | null> {
    return await this.acquireLock(`transfer:player:${playerId}`, {
      ttl: 300, // 5分钟
      maxRetries: 3,
      retryDelay: 1000,
      ...options,
    });
  }

  /**
   * 俱乐部操作锁
   */
  async acquireClubLock(clubId: string, operation: string, options: LockOptions = {}): Promise<string | null> {
    return await this.acquireLock(`club:${clubId}:${operation}`, {
      ttl: 60, // 1分钟
      maxRetries: 5,
      retryDelay: 500,
      ...options,
    });
  }

  /**
   * 比赛模拟锁
   */
  async acquireMatchLock(matchId: string, options: LockOptions = {}): Promise<string | null> {
    return await this.acquireLock(`match:simulation:${matchId}`, {
      ttl: 1800, // 30分钟
      maxRetries: 1,
      ...options,
    });
  }

  /**
   * 用户操作锁
   */
  async acquireUserLock(userId: string, operation: string, options: LockOptions = {}): Promise<string | null> {
    return await this.acquireLock(`user:${userId}:${operation}`, {
      ttl: 30, // 30秒
      maxRetries: 3,
      retryDelay: 200,
      ...options,
    });
  }

  /**
   * 排行榜更新锁
   */
  async acquireLeaderboardLock(type: string, options: LockOptions = {}): Promise<string | null> {
    return await this.acquireLock(`leaderboard:update:${type}`, {
      ttl: 120, // 2分钟
      maxRetries: 10,
      retryDelay: 100,
      ...options,
    });
  }

  // ==================== 管理功能 ====================

  /**
   * 获取锁统计信息
   */
  getStats(): LockStats {
    return { ...this.stats };
  }

  /**
   * 获取活跃锁列表
   */
  getActiveLocks(): LockInfo[] {
    return Array.from(this.activeLocks.values());
  }

  /**
   * 强制释放锁（管理员功能）
   */
  async forceReleaseLock(key: string): Promise<boolean> {
    const lockKey = this.buildLockKey(key);
    
    try {
      const result = await this.redisService.del(lockKey);
      
      if (result > 0) {
        this.activeLocks.delete(lockKey);
        this.stats.activeLocks = this.activeLocks.size;
        this.logger.warn(`Lock force released: ${lockKey}`);
        return true;
      }
      
      return false;
    } catch (error) {
      this.logger.error(`Error force releasing lock ${lockKey}: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理过期锁信息
   */
  async cleanupExpiredLocks(): Promise<void> {
    const expiredKeys: string[] = [];
    
    for (const [key, lockInfo] of this.activeLocks.entries()) {
      const exists = await this.redisService.exists(key);
      if (!exists) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.activeLocks.delete(key));
    this.stats.activeLocks = this.activeLocks.size;
    
    if (expiredKeys.length > 0) {
      this.logger.debug(`Cleaned up ${expiredKeys.length} expired lock entries`);
    }
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalAcquired: 0,
      totalReleased: 0,
      totalFailed: 0,
      activeLocks: this.activeLocks.size,
      averageHoldTime: 0,
    };
    this.logger.log('Lock statistics reset');
  }

  // ==================== 私有方法 ====================

  /**
   * 构建锁键名 
   * 注意：这里只构建业务键名，完整的前缀由RedisService.buildDataTypeKey处理
   */
  private buildLockKey(key: string, dataType?: DataType): string {
    // 锁键名包含dataType信息，用于区分不同类型的锁
    const typePrefix = dataType ? `${dataType}:` : 'server:';
    return `${typePrefix}lock:${key}`;
  }

  private generateIdentifier(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private updateAverageHoldTime(holdTime: number): void {
    const totalReleased = this.stats.totalReleased;
    const currentAverage = this.stats.averageHoldTime;
    
    this.stats.averageHoldTime = (currentAverage * (totalReleased - 1) + holdTime) / totalReleased;
  }
}
