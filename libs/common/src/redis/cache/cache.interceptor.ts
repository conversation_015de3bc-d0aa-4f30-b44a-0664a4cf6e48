import {
  Injectable,
  NestInterceptor,
  Exec<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
  Inject,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { CacheManagerService } from './cache-manager.service';
import { DataType } from '../types/redis.types';

import {
  CACHEABLE_METADATA,
  CACHE_EVICT_METADATA,
  CACHE_PUT_METADATA,
} from './cache.constants';

import {
  resolveCacheKey,
  resolveCacheKeys,
  evaluateCondition,
} from './cache.utils';

/**
 * Spring Boot风格缓存装饰器的元数据接口
 */
interface CacheableMetadata {
  key?: string;
  keys?: string[];
  repository?: string;
  ttl?: number;
  condition?: string;
  unless?: string;
  paramNames?: string[];
  originalMethod?: Function;
  name?: string;

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

interface CacheEvictMetadata {
  keys?: string[];
  repository?: string;
  allEntries?: boolean;
  beforeInvocation?: boolean;
  condition?: string;
  paramNames?: string[];
  originalMethod?: Function;
  name?: string;

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

interface CachePutMetadata {
  key?: string;
  repository?: string;
  ttl?: number;
  condition?: string;
  unless?: string;
  paramNames?: string[];
  originalMethod?: Function;
  name?: string;

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

/**
 * 缓存拦截器
 * 支持Spring Boot风格的缓存装饰器语法
 */
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(
    private readonly cacheManager: CacheManagerService,
    private readonly reflector: Reflector,
    @Inject('REDIS_SERVICE_CONTEXT') private readonly serviceContext: string = 'unknown',
  ) {}

  /**
   * 从请求中提取serverId
   */
  private extractServerIdFromRequest(context: ExecutionContext): string | undefined {
    try {
      const request = context.switchToHttp().getRequest();
      return request?.headers?.['x-server-id'] ||
             request?.query?.serverId ||
             request?.params?.serverId ||
             process.env.SERVER_ID;
    } catch (error) {
      // 非HTTP上下文，返回环境变量中的SERVER_ID
      return process.env.SERVER_ID;
    }
  }

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const contextType = context.getType<'http' | 'rpc' | 'ws'>();

    // 🎯 支持HTTP和RPC上下文，跳过其他类型
    if (!['http', 'rpc'].includes(contextType)) {
      return next.handle();
    }

    const handler = context.getHandler();
    const target = context.getClass();
    const name = handler.name;

    // 🔧 调试：记录拦截器调用和上下文类型
    this.logger.debug(`🔍 CacheInterceptor called for ${target.name}.${name} (context: ${contextType})`);

    // 🔧 根据上下文类型获取参数和参数名
    let args: any[];
    let paramNames: string[];

    if (contextType === 'http') {
      const httpContext = context.switchToHttp();
      const request = httpContext.getRequest();
      args = [request.body, request.params, request.query];
      paramNames = this.getHttpParamNames(handler);
    } else if (contextType === 'rpc') {
      const rpcContext = context.switchToRpc();
      args = [rpcContext.getData()]; // 微服务的payload
      paramNames = ['payload']; // 微服务固定参数名
      this.logger.debug(`🎯 RPC Context - Payload: ${JSON.stringify(args[0])}`);
    }

    // 获取Spring Boot风格装饰器元数据
    const cacheableMetadata = this.reflector.get<CacheableMetadata>(CACHEABLE_METADATA, handler);
    const cacheEvictMetadata = this.reflector.get<CacheEvictMetadata>(CACHE_EVICT_METADATA, handler);
    const cachePutMetadata = this.reflector.get<CachePutMetadata>(CACHE_PUT_METADATA, handler);

    // 调试日志：检查是否有缓存装饰器
    if (cacheableMetadata || cacheEvictMetadata || cachePutMetadata) {
      this.logger.log(`🔍 Cache interceptor triggered for ${target.name}.${name}`);
      if (cacheableMetadata) this.logger.log(`  - @Cacheable: ${JSON.stringify(cacheableMetadata)}`);
      if (cacheEvictMetadata) this.logger.log(`  - @CacheEvict: ${JSON.stringify(cacheEvictMetadata)}`);
      if (cachePutMetadata) this.logger.log(`  - @CachePut: ${JSON.stringify(cachePutMetadata)}`);
    }

    // 如果没有任何缓存装饰器，直接执行方法
    if (!cacheableMetadata && !cacheEvictMetadata && !cachePutMetadata) {
      return next.handle();
    }

    // 获取方法参数和上下文信息
    // args 和 paramNames 已在上面根据上下文类型设置
    const targetInstance = target;

    try {
      // 1. 处理 @CacheEvict (beforeInvocation = true)
      if (cacheEvictMetadata?.beforeInvocation) {
        await this.handleCacheEvict(cacheEvictMetadata, args, paramNames, null, name, targetInstance);
      }

      // 2. 处理 @Cacheable - 尝试从缓存获取
      if (cacheableMetadata) {
        const cachedResult = await this.handleCacheable(cacheableMetadata, args, paramNames, name, targetInstance);
        if (cachedResult !== undefined) {
          this.logger.log(`✅ Cache hit for ${name}`);
          return of(cachedResult);
        }
        this.logger.log(`❌ Cache miss for ${name}`);
      }

      // 3. 执行原方法
      return next.handle().pipe(
        tap(async (result) => {
          try {
            // 4. 处理 @Cacheable 结果缓存
            if (cacheableMetadata) {
              await this.handleCacheableResult(cacheableMetadata, args, paramNames, result, name, targetInstance);
            }

            // 5. 处理 @CachePut
            if (cachePutMetadata) {
              await this.handleCachePut(cachePutMetadata, args, paramNames, result, name, targetInstance);
            }

            // 6. 处理 @CacheEvict (beforeInvocation = false，默认)
            if (cacheEvictMetadata && !cacheEvictMetadata.beforeInvocation) {
              await this.handleCacheEvict(cacheEvictMetadata, args, paramNames, result, name, targetInstance);
            }
          } catch (error) {
            this.logger.error(`❌ Cache operation failed for ${name}: ${error.message}`);
          }
        }),
        catchError((error) => {
          this.logger.error(`❌ Method execution failed for ${name}: ${error.message}`);
          throw error;
        }),
      );
    } catch (error) {
      this.logger.error(`❌ Cache interceptor error for ${name}: ${error.message}`);
      return next.handle();
    }
  }

  // ==================== @Cacheable 处理 ====================

  private async handleCacheable(
    metadata: CacheableMetadata,
    args: any[],
    paramNames: string[],
    name: string,
    target: any,
  ): Promise<any> {
    // 检查条件
    if (metadata.condition && !evaluateCondition(metadata.condition, args, paramNames)) {
      this.logger.debug(`Cache condition not met for ${name}`);
      return undefined;
    }

    if (!metadata.key) {
      this.logger.warn(`No cache key specified for ${name}`);
      return undefined;
    }

    // 🔧 使用传入的paramNames而不是metadata.paramNames
    const key = resolveCacheKey(metadata.key, args, paramNames, undefined, target, name);
    const repository = this.cacheManager.getRepository(metadata.repository || 'default');

    // 🔧 解析serverId表达式
    const resolvedServerId = metadata.serverId
      ? resolveCacheKey(metadata.serverId, args, paramNames, undefined, target, name)
      : undefined;

    // 构建缓存选项，包含dataType和解析后的serverId
    const cacheOptions = {
      dataType: metadata.dataType,
      serverId: resolvedServerId
    };

    try {
      const result = await repository.get(key, cacheOptions);
      if (result.hit && result.data !== null) {
        // 检查 unless 条件
        if (metadata.unless && !evaluateCondition(metadata.unless, args, paramNames, result.data)) {
          this.logger.debug(`Cache unless condition met for ${name}, ignoring cached result`);
          return undefined;
        }

        this.logger.debug(`Cache hit for ${name} with key: ${key} (dataType: ${metadata.dataType || 'server'})`);
        return result.data;
      }
    } catch (error) {
      this.logger.error(`Cache get failed for ${name}: ${error.message}`);
    }

    return undefined;
  }

  private async handleCacheableResult(
    metadata: CacheableMetadata,
    args: any[],
    paramNames: string[],
    result: any,
    name: string,
    target: any,
  ): Promise<void> {
    // 检查 unless 条件
    if (metadata.unless && !evaluateCondition(metadata.unless, args, paramNames, result)) {
      this.logger.debug(`Cache unless condition met for ${name}, not caching result`);
      return;
    }

    if (!metadata.key) {
      return;
    }

    // 🔧 使用传入的paramNames
    const key = resolveCacheKey(metadata.key, args, paramNames, result, target, name);
    const repository = this.cacheManager.getRepository(metadata.repository || 'default');

    // 🔧 解析serverId表达式
    const resolvedServerId = metadata.serverId
      ? resolveCacheKey(metadata.serverId, args, paramNames, result, target, name)
      : undefined;

    try {
      // 构建缓存选项，包含dataType和解析后的serverId
      const options = {
        ttl: metadata.ttl,
        dataType: metadata.dataType,
        serverId: resolvedServerId
      };
      await repository.set(key, result, options);
      this.logger.log(`✅ Cached result for ${name} with key: ${key} (dataType: ${metadata.dataType || 'server'})`);
    } catch (error) {
      this.logger.error(`❌ Cache set failed for ${name}: ${error.message}`);
    }
  }

  // ==================== @CachePut 处理 ====================

  private async handleCachePut(
    metadata: CachePutMetadata,
    args: any[],
    paramNames: string[],
    result: any,
    name: string,
    target: any,
  ): Promise<void> {
    // 检查条件
    if (metadata.condition && !evaluateCondition(metadata.condition, args, paramNames)) {
      this.logger.debug(`CachePut condition not met for ${name}`);
      return;
    }

    // 检查 unless 条件
    if (metadata.unless && !evaluateCondition(metadata.unless, args, paramNames, result)) {
      this.logger.debug(`CachePut unless condition met for ${name}`);
      return;
    }

    if (!metadata.key) {
      this.logger.warn(`No cache key specified for CachePut in ${name}`);
      return;
    }

    const key = resolveCacheKey(metadata.key, args, paramNames, result, target, name);
    const repository = this.cacheManager.getRepository(metadata.repository || 'default');

    // 🔧 解析serverId表达式
    const resolvedServerId = metadata.serverId
      ? resolveCacheKey(metadata.serverId, args, paramNames, result, target, name)
      : undefined;

    try {
      // 构建缓存选项，包含dataType和解析后的serverId
      const options = {
        ttl: metadata.ttl,
        dataType: metadata.dataType,
        serverId: resolvedServerId
      };
      await repository.set(key, result, options);
      this.logger.debug(`✅ CachePut completed for ${name} with key: ${key} (dataType: ${metadata.dataType || 'server'})`);
    } catch (error) {
      this.logger.error(`❌ CachePut failed for ${name}: ${error.message}`);
    }
  }

  // ==================== @CacheEvict 处理 ====================

  private async handleCacheEvict(
    metadata: CacheEvictMetadata,
    args: any[],
    paramNames: string[],
    result: any,
    name: string,
    target: any,
  ): Promise<void> {
    // 检查条件
    if (metadata.condition && !evaluateCondition(metadata.condition, args, paramNames, result)) {
      this.logger.debug(`CacheEvict condition not met for ${name}`);
      return;
    }

    const repository = this.cacheManager.getRepository(metadata.repository || 'default');

    // 🔧 解析serverId表达式
    const resolvedServerId = metadata.serverId
      ? resolveCacheKey(metadata.serverId, args, paramNames, result, target, name)
      : undefined;

    // 构建缓存选项，包含dataType和解析后的serverId
    const cacheOptions = {
      dataType: metadata.dataType,
      serverId: resolvedServerId
    };

    try {
      if (metadata.allEntries) {
        // 清除所有条目
        const cleared = await repository.clear(undefined, cacheOptions);
        this.logger.debug(`✅ CacheEvict cleared ${cleared} entries for ${name} (dataType: ${metadata.dataType || 'server'})`);
      } else if (metadata.keys && metadata.keys.length > 0) {
        // 清除多个键
        const keys = resolveCacheKeys(metadata.keys, args, paramNames, result, target, name);
        let deletedCount = 0;
        for (const key of keys) {
          const deleted = await repository.delete(key, cacheOptions);
          if (deleted) deletedCount++;
          this.logger.debug(`CacheEvict ${deleted ? 'deleted' : 'attempted to delete'} key: ${key} (dataType: ${metadata.dataType || 'server'})`);
        }
        this.logger.debug(`✅ CacheEvict deleted ${deletedCount}/${keys.length} keys for ${name}`);
      }
    } catch (error) {
      this.logger.error(`❌ CacheEvict failed for ${name}: ${error.message}`);
    }
  }

  /**
   * 获取HTTP上下文的参数名称
   */
  private getHttpParamNames(handler: Function): string[] {
    // 从反射元数据获取参数名称，如果没有则使用默认值
    const paramNames = this.reflector.get('PARAM_NAMES', handler);
    return paramNames || ['body', 'params', 'query'];
  }
}
