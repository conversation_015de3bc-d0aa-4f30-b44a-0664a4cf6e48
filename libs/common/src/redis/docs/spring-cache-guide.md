# Spring Boot风格缓存装饰器完整指南

> **⚠️ 重要说明**：Spring Boot风格缓存装饰器应该在**控制器层**使用，而不是服务层。
> 这是因为NestJS的全局拦截器只应用于HTTP请求处理管道，控制器方法是HTTP请求的入口点。

## 🎯 概述

本指南详细介绍了Spring Boot风格的缓存装饰器系统，提供科学、优雅、直观的缓存解决方案。支持`@Cacheable`、`@CacheEvict`、`@CachePut`三个核心装饰器，以及强大的表达式语法。

## 🏗️ 架构设计

### 分层架构
```
┌─────────────────────────────────────┐
│          业务层 (Business)           │
│  @Cacheable, @CacheEvict, @CachePut │
├─────────────────────────────────────┤
│         缓存拦截器 (Interceptor)      │
│     CacheInterceptor + 表达式解析    │
├─────────────────────────────────────┤
│         缓存管理层 (Manager)         │
│  CacheManager, CacheRepository     │
├─────────────────────────────────────┤
│         Redis服务层 (Service)        │
│           RedisService             │
└─────────────────────────────────────┘
```

### 核心组件
- **CacheInterceptor**: 缓存拦截器，自动处理装饰器逻辑
- **ExpressionParser**: 表达式解析器，支持`#{paramName}`语法
- **CacheManager**: 缓存管理器，统一管理缓存存储库
- **CacheRepository**: 缓存存储库，实现具体的缓存操作

## 📋 @Cacheable 装饰器详解

### 基本语法
```typescript
@Cacheable({
  key: string,           // 缓存键模板，支持表达式
  ttl?: number,          // 缓存TTL（秒）
  repository?: string,   // 缓存仓库名称
  condition?: string,    // 缓存条件表达式
  unless?: string,       // 排除条件表达式
  paramNames?: string[]  // 参数名列表（可选，用于类型安全）
})
```

### 功能说明
- **作用**: 方法执行前检查缓存，命中则直接返回；未命中则执行方法并缓存结果
- **适用场景**: 读多写少的数据，如用户信息、配置数据、统计信息等
- **缓存策略**: Cache-Aside模式

### 使用示例（控制器层）

#### 1. 基础用法
```typescript
@Controller('users')
export class UserController {
  constructor(private readonly characterService: UserService) {}

  @Get(':id')
  @Cacheable({
    key: 'user:id:#{id}',
    ttl: 300,
    paramNames: ['id']
  })
  async findById(@Param('id') id: string): Promise<User> {
    return await this.characterService.findById(id);
  }
}
```

#### 2. 条件缓存
```typescript
@Controller('users')
export class UserController {
  @Get('search')
  @Cacheable({
    key: 'user:search:#{keyword}',
    ttl: 600,
    condition: '#{keyword != null}',  // 只有关键字不为空时才缓存
    paramNames: ['keyword']
  })
  async searchUsers(@Query('keyword') keyword: string): Promise<User[]> {
    return await this.characterService.searchUsers(keyword);
  }
}
```

#### 3. 排除条件
```typescript
@Controller('users')
export class UserController {
  @Get(':id/profile')
  @Cacheable({
    key: 'user:profile:#{id}',
    ttl: 300,
    unless: '#{result.isPrivate}',  // 私有用户不缓存
    paramNames: ['id']
  })
  async getUserProfile(@Param('id') id: string): Promise<UserProfile> {
    return await this.characterService.getUserProfile(id);
  }
}
```

#### 4. 多参数缓存
```typescript
@Controller('users')
export class UserController {
  @Get()
  @Cacheable({
    key: 'user:list:#{page}:#{size}:#{status}',
    ttl: 180,
    paramNames: ['page', 'size', 'status']
  })
  async getUsers(
    @Query('page') page: number,
    @Query('size') size: number,
    @Query('status') status: UserStatus
  ): Promise<User[]> {
    return await this.characterService.findByStatus(status, page, size);
  }
}
```

## 📋 @CacheEvict 装饰器详解

### 基本语法
```typescript
@CacheEvict({
  key?: string | string[],    // 缓存键模板或数组
  repository?: string,        // 缓存仓库名称
  allEntries?: boolean,       // 是否清除所有条目
  beforeInvocation?: boolean, // 是否在方法调用前清除
  condition?: string,         // 清除条件表达式
  paramNames?: string[]       // 参数名列表
})
```

### 功能说明
- **作用**: 方法执行时清除指定的缓存条目
- **适用场景**: 数据更新、删除操作，需要保证缓存一致性
- **清除时机**: 默认方法执行后清除，可配置为执行前清除

### 使用示例（控制器层）

#### 1. 单键清除
```typescript
@Controller('users')
export class UserController {
  @Delete(':id')
  @CacheEvict({
    key: 'user:id:#{id}',
    paramNames: ['id']
  })
  async deleteUser(@Param('id') id: string): Promise<void> {
    await this.characterService.deleteUser(id);
  }
}
```

#### 2. 多键清除
```typescript
@Controller('users')
export class UserController {
  @Put(':id')
  @CacheEvict({
    key: [
      'user:id:#{id}',
      'user:username:#{result.username}',
      'user:email:#{result.email}',
      'user:statistics'
    ],
    paramNames: ['id', 'updateUserDto']
  })
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto
  ): Promise<User> {
    return await this.characterService.updateUser(id, updateUserDto);
  }
}
```

#### 3. 清除所有条目
```typescript
@Controller('users')
export class UserController {
  @Post('reset-cache')
  @CacheEvict({
    allEntries: true,
    repository: 'users'
  })
  async resetAllUserCache(): Promise<void> {
    // 清除用户仓库中的所有缓存
    await this.characterService.resetAllCache();
  }
}
```

#### 4. 条件清除
```typescript
@Controller('users')
export class UserController {
  @Put(':id/data')
  @CacheEvict({
    key: 'user:id:#{id}',
    condition: '#{force == true}',  // 只有强制更新时才清除缓存
    paramNames: ['id', 'data', 'force']
  })
  async updateUserData(
    @Param('id') id: string,
    @Body() data: any,
    @Query('force') force: boolean
  ): Promise<User> {
    return await this.characterService.updateUserData(id, data);
  }
}
```

#### 5. 方法执行前清除
```typescript
@Controller('sessions')
export class SessionController {
  @Delete(':sessionId/cleanup')
  @CacheEvict({
    key: 'user:temp:#{sessionId}',
    beforeInvocation: true,  // 方法执行前清除
    paramNames: ['sessionId']
  })
  async cleanupUserSession(@Param('sessionId') sessionId: string): Promise<void> {
    // 即使方法执行失败，缓存也会被清除
    await this.sessionService.cleanup(sessionId);
  }
}
```

## 📋 @CachePut 装饰器详解

### 基本语法
```typescript
@CachePut({
  key: string,           // 缓存键模板
  ttl?: number,          // 缓存TTL（秒）
  repository?: string,   // 缓存仓库名称
  condition?: string,    // 缓存条件表达式
  unless?: string,       // 排除条件表达式
  paramNames?: string[]  // 参数名列表
})
```

### 功能说明
- **作用**: 方法执行后，无论缓存是否存在都会更新缓存
- **适用场景**: 数据更新操作，需要确保缓存与数据库同步
- **缓存策略**: Write-Through模式

### 使用示例（控制器层）

#### 1. 基础用法
```typescript
@Controller('users')
export class UserController {
  @Put(':id')
  @CachePut({
    key: 'user:id:#{id}',
    ttl: 300,
    paramNames: ['id', 'updateData']
  })
  async updateUser(
    @Param('id') id: string,
    @Body() updateData: UpdateUserDto
  ): Promise<User> {
    const updatedUser = await this.characterService.updateUser(id, updateData);
    // 方法执行后，结果会被缓存到 'user:id:123' 键中
    return updatedUser;
  }
}
```

#### 2. 条件更新缓存
```typescript
@CachePut({
  key: 'user:profile:#{userId}',
  ttl: 600,
  condition: '#{updateProfile == true}',  // 只有更新个人资料时才缓存
  paramNames: ['userId', 'data', 'updateProfile']
})
async updateUserInfo(userId: string, data: any, updateProfile: boolean): Promise<User> {
  return await this.userRepository.updateInfo(userId, data);
}
```

#### 3. 排除敏感数据
```typescript
@CachePut({
  key: 'user:public:#{userId}',
  ttl: 300,
  unless: '#{result.containsSensitiveData}',  // 包含敏感数据时不缓存
  paramNames: ['userId', 'publicData']
})
async updatePublicProfile(userId: string, publicData: any): Promise<UserProfile> {
  return await this.userRepository.updatePublicProfile(userId, publicData);
}
```

## 📋 表达式语法指南

### 支持的表达式语法

#### 1. 参数引用
```typescript
// 基本参数引用
key: 'user:#{userId}'           // 引用 userId 参数
key: 'search:#{keyword}'        // 引用 keyword 参数

// 多参数组合
key: 'user:#{userId}:#{type}'   // 组合多个参数
```

#### 2. 返回值引用
```typescript
// 引用返回值属性
key: 'user:#{result.id}'        // 引用返回值的 id 属性
key: 'user:#{result.username}'  // 引用返回值的 username 属性

// 嵌套属性引用
key: 'user:#{result.profile.type}'  // 引用嵌套属性
```

#### 3. 条件表达式
```typescript
// 空值检查
condition: '#{userId != null}'     // 参数不为空
condition: '#{result == null}'     // 返回值为空

// 值比较
condition: '#{type == "admin"}'    // 字符串比较
condition: '#{age >= 18}'          // 数值比较

// 布尔值检查
condition: '#{isActive}'           // 布尔值为真
condition: '#{!isDeleted}'         // 布尔值为假
```

#### 4. 复杂表达式
```typescript
// 组合条件
condition: '#{userId != null && type == "user"}'

// 属性检查
unless: '#{result.isPrivate || result.isDeleted}'

// 方法名引用
key: 'cache:#{methodName}:#{id}'   // 包含方法名
```

### 表达式最佳实践

#### 1. 参数名明确指定
```typescript
// 推荐：明确指定参数名
@Cacheable({
  key: 'user:#{id}',
  paramNames: ['id']  // 明确指定参数名，提高类型安全
})
async findById(id: string): Promise<User> { }
```

#### 2. 条件表达式简洁
```typescript
// 推荐：简洁的条件表达式
condition: '#{id != null}'

// 避免：过于复杂的条件
condition: '#{id != null && id.length() > 0 && type == "user" && status == "active"}'
```

#### 3. 键名规范
```typescript
// 推荐：有层次的键名
key: 'user:profile:#{userId}'
key: 'game:match:#{matchId}:#{season}'

// 避免：扁平的键名
key: 'userprofile#{userId}'
key: 'match#{matchId}#{season}'
```

## 🔄 装饰器组合使用

### 1. 读写分离模式
```typescript
@Injectable()
export class UserService {
  // 读操作：使用缓存
  @Cacheable({
    key: 'user:id:#{id}',
    ttl: 300,
    paramNames: ['id']
  })
  async findById(id: string): Promise<User> {
    return await this.userRepository.findById(id);
  }

  // 写操作：更新缓存
  @CachePut({
    key: 'user:id:#{id}',
    ttl: 300,
    paramNames: ['id', 'updateData']
  })
  async updateUser(id: string, updateData: UpdateUserDto): Promise<User> {
    return await this.userRepository.update(id, updateData);
  }

  // 删除操作：清除缓存
  @CacheEvict({
    key: 'user:id:#{id}',
    paramNames: ['id']
  })
  async deleteUser(id: string): Promise<void> {
    await this.userRepository.delete(id);
  }
}
```

### 2. 多级缓存清除
```typescript
@CacheEvict({
  key: [
    'user:id:#{id}',                    // 清除用户详情缓存
    'user:username:#{result.username}', // 清除用户名索引缓存
    'user:email:#{result.email}',       // 清除邮箱索引缓存
    'user:statistics',                  // 清除统计信息缓存
    'user:list:*'                       // 清除所有用户列表缓存
  ],
  paramNames: ['id', 'updateData']
})
async updateUserProfile(id: string, updateData: UpdateUserDto): Promise<User> {
  return await this.userRepository.update(id, updateData);
}
```

### 3. 条件缓存策略
```typescript
@Injectable()
export class GameService {
  // 只缓存公开的比赛信息
  @Cacheable({
    key: 'match:#{matchId}',
    ttl: 600,
    condition: '#{includePrivate == false}',
    paramNames: ['matchId', 'includePrivate']
  })
  async getMatchInfo(matchId: string, includePrivate: boolean): Promise<Match> {
    return await this.matchRepository.findById(matchId, includePrivate);
  }

  // 比赛结束后更新缓存，但不缓存进行中的比赛
  @CachePut({
    key: 'match:#{matchId}',
    ttl: 3600,
    unless: '#{result.status == "PLAYING"}',
    paramNames: ['matchId']
  })
  async updateMatchResult(matchId: string, result: MatchResult): Promise<Match> {
    return await this.matchRepository.updateResult(matchId, result);
  }
}
```

## 🎯 实际业务场景示例

### 1. 用户管理服务
```typescript
@Injectable()
export class UserManagementService {
  // 用户详情缓存
  @Cacheable({
    key: 'user:detail:#{userId}',
    ttl: 300,
    condition: '#{userId != null}',
    paramNames: ['userId']
  })
  async getUserDetail(userId: string): Promise<UserDetail> {
    return await this.userRepository.getDetailById(userId);
  }

  // 用户权限缓存
  @Cacheable({
    key: 'user:permissions:#{userId}',
    ttl: 600,
    paramNames: ['userId']
  })
  async getUserPermissions(userId: string): Promise<Permission[]> {
    return await this.permissionRepository.findByUserId(userId);
  }

  // 更新用户信息，清除相关缓存
  @CacheEvict({
    key: [
      'user:detail:#{userId}',
      'user:permissions:#{userId}',
      'user:statistics'
    ],
    paramNames: ['userId', 'updateData']
  })
  async updateUserInfo(userId: string, updateData: UpdateUserDto): Promise<User> {
    return await this.userRepository.update(userId, updateData);
  }
}
```

### 2. 游戏数据服务
```typescript
@Injectable()
export class GameDataService {
  // 球员信息缓存
  @Cacheable({
    key: 'player:#{playerId}:#{season}',
    ttl: 1800,
    paramNames: ['playerId', 'season']
  })
  async getPlayerStats(playerId: string, season: string): Promise<PlayerStats> {
    return await this.playerRepository.getStats(playerId, season);
  }

  // 俱乐部排行榜缓存
  @Cacheable({
    key: 'ranking:club:#{league}:#{season}',
    ttl: 300,
    paramNames: ['league', 'season']
  })
  async getClubRanking(league: string, season: string): Promise<ClubRanking[]> {
    return await this.rankingRepository.getClubRanking(league, season);
  }

  // 比赛结果更新，清除相关排行榜
  @CacheEvict({
    key: [
      'ranking:club:#{match.league}:#{match.season}',
      'player:#{match.homeTeam.players}:#{match.season}',
      'player:#{match.awayTeam.players}:#{match.season}'
    ],
    paramNames: ['matchId', 'result']
  })
  async updateMatchResult(matchId: string, result: MatchResult): Promise<Match> {
    return await this.matchRepository.updateResult(matchId, result);
  }
}
```

## 🚀 性能优化建议

### 1. TTL策略
```typescript
// 根据数据特性设置合适的TTL
@Cacheable({
  key: 'user:profile:#{userId}',
  ttl: 300,        // 用户资料：5分钟
})

@Cacheable({
  key: 'game:config',
  ttl: 3600,       // 游戏配置：1小时
})

@Cacheable({
  key: 'player:stats:#{playerId}',
  ttl: 1800,       // 球员统计：30分钟
})
```

### 2. 缓存仓库分离
```typescript
// 按业务模块分离缓存仓库
@Cacheable({
  key: 'user:#{id}',
  repository: 'users',    // 用户相关缓存
  ttl: 300
})

@Cacheable({
  key: 'match:#{id}',
  repository: 'games',    // 游戏相关缓存
  ttl: 600
})

@Cacheable({
  key: 'config:#{key}',
  repository: 'configs',  // 配置相关缓存
  ttl: 3600
})
```

### 3. 批量操作优化
```typescript
// 避免在循环中使用缓存装饰器
// ❌ 不推荐
async getMultipleUsers(userIds: string[]): Promise<User[]> {
  const users = [];
  for (const id of userIds) {
    users.push(await this.findById(id)); // 每次都会触发缓存检查
  }
  return users;
}

// ✅ 推荐
@Cacheable({
  key: 'users:batch:#{userIds}',
  ttl: 300,
  paramNames: ['userIds']
})
async getMultipleUsers(userIds: string[]): Promise<User[]> {
  return await this.userRepository.findByIds(userIds);
}
```

## 🔍 监控和调试

### 1. 缓存命中率监控
```typescript
// 在健康检查中监控缓存性能
@Injectable()
export class CacheHealthService {
  @Cacheable({
    key: 'cache:stats',
    ttl: 60  // 缓存统计信息1分钟
  })
  async getCacheStats(): Promise<CacheStats> {
    return {
      hitRate: await this.cacheManager.getHitRate(),
      missRate: await this.cacheManager.getMissRate(),
      totalKeys: await this.cacheManager.getTotalKeys(),
      memoryUsage: await this.cacheManager.getMemoryUsage()
    };
  }
}
```

### 2. 缓存键调试
```typescript
// 开发环境下记录缓存键
@Cacheable({
  key: 'user:#{userId}:#{type}',
  ttl: 300,
  paramNames: ['userId', 'type']
})
async getUser(userId: string, type: string): Promise<User> {
  // 在开发环境下，缓存键会被记录为: user:123:admin
  return await this.userRepository.findById(userId);
}
```

## 📝 最佳实践总结

### 1. 装饰器使用位置 ⚠️
- **正确位置**: 在控制器层使用缓存装饰器
- **错误位置**: 在服务层使用缓存装饰器
- **技术原因**: NestJS全局拦截器只应用于HTTP请求处理管道
- **架构原则**: 控制器负责缓存策略，服务层专注业务逻辑

### 2. 装饰器选择原则
- **@Cacheable**: 用于读操作，提高查询性能
- **@CacheEvict**: 用于写操作，保证数据一致性
- **@CachePut**: 用于更新操作，同步缓存和数据库

### 3. 缓存键设计原则
- 使用有层次的命名空间
- 包含足够的上下文信息
- 避免键名冲突
- 考虑键的可读性和可维护性

### 4. TTL设置原则
- 根据数据更新频率设置TTL
- 静态数据使用较长TTL
- 动态数据使用较短TTL
- 考虑业务场景的容错性

### 5. 条件表达式原则
- 保持表达式简洁明了
- 避免复杂的业务逻辑
- 优先使用参数检查
- 谨慎使用返回值检查

### 6. 错误处理原则
- 缓存操作失败不应影响业务逻辑
- 提供降级机制
- 记录缓存异常日志
- 监控缓存健康状态
