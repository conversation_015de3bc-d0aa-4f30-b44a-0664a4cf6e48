# Redis 服务设计原则

## 🎯 核心设计理念

### 1. 通用性优先 (Generality First)

**原则**: Redis 服务应该是业务无关的，提供通用的数据操作能力。

**实践**:
- ❌ **错误**: `addMatchSimulation(matchData)` - 包含业务逻辑
- ✅ **正确**: `addJob(queueName, jobType, payload, options)` - 通用接口

**好处**:
- 可以在任何项目中复用
- 降低维护成本
- 提高代码质量

### 2. 分层架构 (Layered Architecture)

```
┌─────────────────────────────────────┐
│        业务层 (Business Layer)        │
│  GameQueueService, GameEventService  │
├─────────────────────────────────────┤
│       服务层 (Service Layer)         │
│ RedisQueueService, RedisPubSubService │
├─────────────────────────────────────┤
│       基础层 (Infrastructure Layer)   │
│           RedisService              │
└─────────────────────────────────────┘
```

**职责分离**:
- **基础层**: 提供 Redis 基础操作
- **服务层**: 提供通用的高级功能（队列、发布订阅、锁等）
- **业务层**: 封装业务特定的逻辑

### 3. 配置驱动 (Configuration Driven)

**原则**: 通过配置而非硬编码来适应不同场景。

```typescript
// ❌ 硬编码
async addNotification(data) {
  return await this.queue.add('notification', data, {
    priority: 8, // 硬编码优先级
    attempts: 5, // 硬编码重试次数
  });
}

// ✅ 配置驱动
async addJob(queueName, jobType, payload, options = {}) {
  return await this.addJobToQueue(queueName, jobType, payload, {
    priority: options.priority || 5,
    maxRetries: options.maxRetries || 3,
    ...options
  });
}
```

## 🏗️ 架构模式

### 1. 业务层封装模式

**目的**: 在业务模块中封装业务特定的逻辑。

```typescript
// libs/game/src/queue/game-queue.service.ts
@Injectable()
export class GameQueueService {
  constructor(private readonly queueService: RedisQueueService) {}

  async addMatchSimulation(matchData: MatchSimulationData): Promise<string> {
    return await this.queueService.addJob(
      'match-simulation',
      'simulate',
      matchData,
      {
        priority: 10, // 比赛模拟高优先级
        maxRetries: 3,
        retryDelay: 2000,
      }
    );
  }

  async addNotification(notification: NotificationData): Promise<string> {
    const priority = this.getNotificationPriority(notification.type);
    
    return await this.queueService.addJob(
      'notification',
      'send',
      notification,
      {
        priority,
        maxRetries: 5,
        retryDelay: 1000,
      }
    );
  }

  private getNotificationPriority(type: string): number {
    const priorities = {
      sms: 10,
      push: 8,
      email: 4,
    };
    return priorities[type] || 5;
  }
}
```

### 2. 事件驱动模式

**目的**: 使用通用的事件发布订阅机制。

```typescript
// libs/game/src/events/game-event.service.ts
@Injectable()
export class GameEventService {
  constructor(private readonly pubSubService: RedisPubSubService) {}

  // 发布事件
  async publishMatchUpdate(matchId: string, update: MatchUpdate): Promise<number> {
    return await this.pubSubService.publishToEntity(
      'match',
      matchId,
      'updates',
      update
    );
  }

  // 订阅事件
  async subscribeToMatchUpdates(matchId: string, callback: (update: MatchUpdate) => void): Promise<void> {
    await this.pubSubService.subscribeToEntity(
      'match',
      matchId,
      'updates',
      callback
    );
  }
}
```

### 3. 资源锁定模式

**目的**: 使用分布式锁保护关键资源。

```typescript
// libs/game/src/locks/game-lock.service.ts
@Injectable()
export class GameLockService {
  constructor(private readonly lockService: RedisLockService) {}

  async withTransferLock<T>(
    playerId: string,
    operation: () => Promise<T>
  ): Promise<T> {
    return await this.lockService.withLock(
      `transfer:player:${playerId}`,
      operation,
      {
        ttl: 300, // 5分钟
        maxRetries: 3,
        retryDelay: 1000,
      }
    );
  }
}
```

## 📋 最佳实践

### 1. 命名约定

**队列命名**:
```
{domain}-{action}
例如: match-simulation, user-notification, data-sync
```

**事件频道命名**:
```
{entity-type}:{entity-id}:{event-type}
例如: match:123:updates, user:456:status, club:789:events

{namespace}:{event-type}
例如: global:notifications, system:maintenance
```

**锁键命名**:
```
{operation}:{resource-type}:{resource-id}
例如: transfer:player:123, update:club:456, simulation:match:789
```

### 2. 错误处理

```typescript
// ✅ 正确的错误处理
async addJobSafely(queueName: string, jobType: string, payload: any): Promise<string | null> {
  try {
    return await this.queueService.addJob(queueName, jobType, payload);
  } catch (error) {
    this.logger.error(`Failed to add job to queue ${queueName}: ${error.message}`);
    
    // 根据错误类型决定是否重试
    if (this.isRetryableError(error)) {
      // 可以实现重试逻辑
    }
    
    return null;
  }
}
```

### 3. 配置管理

```typescript
// config/queue.config.ts
export const queueConfig = {
  'match-simulation': {
    priority: 10,
    maxRetries: 3,
    retryDelay: 2000,
    ttl: 1800, // 30分钟
  },
  'notification': {
    priority: 8,
    maxRetries: 5,
    retryDelay: 1000,
    ttl: 3600, // 1小时
  },
  'user-analytics': {
    priority: 1,
    maxRetries: 2,
    retryDelay: 5000,
    delay: 5000, // 延迟处理
  },
};
```

### 4. 监控和日志

```typescript
@Injectable()
export class GameQueueService {
  private readonly logger = new Logger(GameQueueService.name);

  async addJob(jobType: string, payload: any): Promise<string> {
    const startTime = Date.now();
    
    try {
      const jobId = await this.queueService.addJob('game-queue', jobType, payload);
      
      const duration = Date.now() - startTime;
      this.logger.log(`Job added successfully: ${jobId} (${duration}ms)`);
      
      return jobId;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Failed to add job: ${error.message} (${duration}ms)`);
      throw error;
    }
  }
}
```

## 🚫 反模式 (Anti-patterns)

### 1. 业务逻辑泄露

```typescript
// ❌ 错误：在 Redis 服务中包含业务逻辑
class RedisQueueService {
  async addMatchSimulation(matchData) {
    // 业务逻辑不应该在这里
    if (matchData.homeTeam === matchData.awayTeam) {
      throw new Error('Teams cannot play against themselves');
    }
    
    const priority = matchData.isImportant ? 10 : 5; // 业务规则
    // ...
  }
}

// ✅ 正确：业务逻辑在业务层
class GameQueueService {
  async addMatchSimulation(matchData) {
    // 业务验证
    this.validateMatchData(matchData);
    
    // 业务规则
    const priority = this.calculateMatchPriority(matchData);
    
    // 调用通用服务
    return await this.queueService.addJob('match-simulation', 'simulate', matchData, {
      priority
    });
  }
}
```

### 2. 硬编码配置

```typescript
// ❌ 错误：硬编码
async addNotification(data) {
  return await this.addJob('notification', 'send', data, {
    priority: 8, // 硬编码
    maxRetries: 5, // 硬编码
  });
}

// ✅ 正确：配置驱动
async addNotification(data, options = {}) {
  const config = this.getQueueConfig('notification');
  return await this.addJob('notification', 'send', data, {
    ...config,
    ...options
  });
}
```

### 3. 紧耦合

```typescript
// ❌ 错误：直接依赖具体实现
class UserService {
  constructor(private readonly bullQueue: Queue) {} // 紧耦合到 Bull
}

// ✅ 正确：依赖抽象接口
class UserService {
  constructor(private readonly queueService: RedisQueueService) {} // 依赖抽象
}
```

## 📈 迁移指南

如果你的项目中已经有业务特定的 Redis 服务，可以按以下步骤迁移：

### 1. 识别业务逻辑

找出当前 Redis 服务中的业务特定代码：
- 硬编码的队列名称
- 业务验证逻辑
- 特定的优先级规则
- 业务相关的错误处理

### 2. 创建业务层服务

在对应的业务模块中创建封装服务：
```typescript
// apps/game-service/src/queue/game-queue.service.ts
@Injectable()
export class GameQueueService {
  constructor(private readonly queueService: RedisQueueService) {}
  
  // 迁移业务特定的方法到这里
}
```

### 3. 逐步替换

逐步将业务代码中的直接 Redis 调用替换为业务层服务调用。

### 4. 清理通用服务

移除通用 Redis 服务中的业务特定代码，使其变得通用。

通过遵循这些设计原则，我们可以构建出既强大又灵活的 Redis 服务架构，既满足当前需求，又为未来的扩展留下空间。
