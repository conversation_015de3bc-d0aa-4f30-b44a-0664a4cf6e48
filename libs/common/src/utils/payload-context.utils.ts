import { InjectedContext } from '../types';

/**
 * Payload上下文工具函数
 * 
 * 🎯 用途：
 * - 简化Controller中对注入上下文的访问
 * - 提供类型安全的上下文提取
 * - 支持二次微服务调用的上下文传递
 */

/**
 * 🎯 从payload中安全提取注入上下文
 */
export function getInjectedContext(payload: any): InjectedContext | null {
  if (!payload) return null;

  // 如果有完整的 injectedContext，直接返回
  if (payload.injectedContext && isValidInjectedContext(payload.injectedContext)) {
    return payload.injectedContext;
  }

  return null;
}

/**
 * 🎯 获取可信的用户ID
 */
export function getTrustedUserId(payload: any): string | null {
  const context = getInjectedContext(payload);
  return context?.userId || null;
}

/**
 * 🎯 获取可信的角色ID
 */
export function getTrustedCharacterId(payload: any): string | null {
  const context = getInjectedContext(payload);
  return context?.serverContext?.characterId || null;
}

/**
 * 🎯 获取可信的区服ID
 */
export function getTrustedServerId(payload: any): string | null {
  const context = getInjectedContext(payload);
  return context?.serverContext?.serverId || null;
}

/**
 * 🎯 获取请求ID（用于链路追踪）
 */
export function getRequestId(payload: any): string | null {
  const context = getInjectedContext(payload);
  return context?.requestId || null;
}

/**
 * 🎯 获取WebSocket上下文
 */
export function getWsContext(payload: any): InjectedContext['wsContext'] | null {
  const context = getInjectedContext(payload);
  return context?.wsContext || null;
}

/**
 * 🎯 为二次微服务调用准备数据
 * 将业务数据与注入上下文合并
 */
export function prepareForMicroserviceCall<T = any>(
  businessData: T,
  sourcePayload: any
): T & { injectedContext?: InjectedContext } {
  const injectedContext = getInjectedContext(sourcePayload);
  
  if (!injectedContext) {
    console.warn('⚠️ 无法提取注入上下文，二次调用可能丢失上下文信息');
    return businessData as T & { injectedContext?: InjectedContext };
  }

  return {
    ...businessData,
    injectedContext,
  };
}

/**
 * 🎯 记录上下文信息到日志
 */
export function logContextInfo(logger: any, payload: any, apiName: string): void {
  const context = getInjectedContext(payload);
  
  if (!context) {
    logger.warn(`📡 ${apiName}: 无注入上下文信息`);
    return;
  }

  logger.log(`📡 ${apiName} 调用信息:`);
  logger.log(`  - 用户ID: ${context.userId}`);
  logger.log(`  - 角色ID: ${context.serverContext?.characterId || 'N/A'}`);
  logger.log(`  - 区服ID: ${context.serverContext?.serverId || 'N/A'}`);
  logger.log(`  - 请求ID: ${context.requestId || 'N/A'}`);
  logger.log(`  - 时间戳: ${context.wsContext?.timestamp || 'N/A'}`);
  logger.log(`  - 路由策略: ${context.wsContext?.routingStrategy || 'N/A'}`);
}

/**
 * 🎯 验证操作权限（基础版本）
 * 检查用户是否有权限操作指定的角色
 */
export function validateOperationPermission(payload: any, targetCharacterId?: string): boolean {
  const context = getInjectedContext(payload);
  
  if (!context) {
    return false;
  }

  // 如果没有指定目标角色ID，使用payload中的角色ID
  const actualTargetCharacterId = targetCharacterId || getTrustedCharacterId(payload);
  
  if (!actualTargetCharacterId) {
    return false;
  }

  // 基础权限检查：只能操作自己的角色
  return context.serverContext?.characterId === actualTargetCharacterId;
}

/**
 * 🎯 创建标准的成功响应
 */
export function createSuccessResponse<T = any>(data: T, message: string = '操作成功'): {
  code: number;
  message: string;
  data: T;
} {
  return {
    code: 0,
    message,
    data,
  };
}

/**
 * 🎯 创建标准的错误响应
 */
export function createErrorResponse(message: string, code: number = -1): {
  code: number;
  message: string;
  data: null;
} {
  return {
    code,
    message,
    data: null,
  };
}

// ==================== 内部工具函数 ====================

/**
 * 验证 InjectedContext 的有效性
 */
function isValidInjectedContext(context: any): context is InjectedContext {
  return context && 
         typeof context.userId === 'string' && 
         context.wsContext && 
         typeof context.wsContext.timestamp === 'number';
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// ==================== 类型定义 ====================

/**
 * 标准响应接口
 */
export interface StandardResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

/**
 * 操作上下文接口
 */
export interface OperationContext {
  userId: string;
  characterId?: string;
  serverId?: string;
  requestId?: string;
  timestamp: number;
}
