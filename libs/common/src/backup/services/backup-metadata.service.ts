import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as path from 'path';
import { BackupResult } from '../interfaces/backup.interface';

export interface BackupInfo {
  id: string;
  type: 'full' | 'incremental';
  timestamp: string;
  duration: number;
  results: BackupResult[];
  since?: string;
  status: 'success' | 'failed' | 'partial';
  size: number;
  checksum: string;
  retention: {
    expiresAt: string;
    protected: boolean;
  };
  tags: string[];
  description?: string;
}

@Injectable()
export class BackupMetadataService {
  private readonly logger = new Logger(BackupMetadataService.name);
  private readonly metadataPath: string;

  constructor(private readonly configService: ConfigService) {
    const basePath = this.configService.get<string>('backup.localPath', '/app/backups');
    this.metadataPath = path.join(basePath, 'metadata');
  }

  /**
   * 保存备份元数据
   */
  async saveBackupMetadata(backupId: string, data: Partial<BackupInfo>): Promise<void> {
    try {
      // 确保元数据目录存在
      await fs.mkdir(this.metadataPath, { recursive: true });

      const retentionDays = this.configService.get<number>('backup.retentionDays', 30);
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + retentionDays);

      const metadata: BackupInfo = {
        id: backupId,
        type: data.type || 'full',
        timestamp: data.timestamp || new Date().toISOString(),
        duration: data.duration || 0,
        results: data.results || [],
        since: data.since,
        status: this.determineStatus(data.results || []),
        size: this.calculateTotalSize(data.results || []),
        checksum: this.calculateChecksum(data.results || []),
        retention: {
          expiresAt: expiresAt.toISOString(),
          protected: false,
        },
        tags: data.tags || [],
        description: data.description,
      };

      const metadataFile = path.join(this.metadataPath, `${backupId}.json`);
      await fs.writeFile(metadataFile, JSON.stringify(metadata, null, 2));

      // 更新索引
      await this.updateBackupIndex(metadata);

      this.logger.debug(`备份元数据已保存: ${backupId}`);

    } catch (error) {
      this.logger.error(`保存备份元数据失败: ${backupId}`, error);
      throw error;
    }
  }

  /**
   * 获取备份信息
   */
  async getBackupInfo(backupId: string): Promise<BackupInfo | null> {
    try {
      const metadataFile = path.join(this.metadataPath, `${backupId}.json`);
      const data = await fs.readFile(metadataFile, 'utf-8');
      return JSON.parse(data);

    } catch (error) {
      if (error.code === 'ENOENT') {
        return null;
      }
      this.logger.error(`获取备份信息失败: ${backupId}`, error);
      throw error;
    }
  }

  /**
   * 获取最后一次备份信息
   */
  async getLastBackupInfo(): Promise<BackupInfo | null> {
    try {
      const index = await this.getBackupIndex();
      if (index.length === 0) {
        return null;
      }

      // 按时间戳排序，获取最新的
      const latest = index.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )[0];

      return await this.getBackupInfo(latest.id);

    } catch (error) {
      this.logger.error('获取最后备份信息失败', error);
      return null;
    }
  }

  /**
   * 获取最后一次成功备份信息
   */
  async getLastSuccessfulBackup(): Promise<BackupInfo | null> {
    try {
      const index = await this.getBackupIndex();
      
      // 按时间戳排序，查找最新的成功备份
      const successful = index
        .filter(item => item.status === 'success')
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

      if (successful.length === 0) {
        return null;
      }

      return await this.getBackupInfo(successful[0].id);

    } catch (error) {
      this.logger.error('获取最后成功备份信息失败', error);
      return null;
    }
  }

  /**
   * 获取过期备份列表
   */
  async getExpiredBackups(beforeDate: Date): Promise<BackupInfo[]> {
    try {
      const index = await this.getBackupIndex();
      const expired: BackupInfo[] = [];

      for (const item of index) {
        if (new Date(item.timestamp) < beforeDate && !item.retention?.protected) {
          const metadata = await this.getBackupInfo(item.id);
          if (metadata) {
            expired.push(metadata);
          }
        }
      }

      return expired;

    } catch (error) {
      this.logger.error('获取过期备份列表失败', error);
      return [];
    }
  }

  /**
   * 删除备份元数据
   */
  async deleteBackupMetadata(backupId: string): Promise<void> {
    try {
      const metadataFile = path.join(this.metadataPath, `${backupId}.json`);
      await fs.rm(metadataFile, { force: true });

      // 更新索引
      await this.removeFromBackupIndex(backupId);

      this.logger.debug(`备份元数据已删除: ${backupId}`);

    } catch (error) {
      this.logger.error(`删除备份元数据失败: ${backupId}`, error);
      throw error;
    }
  }

  /**
   * 列出所有备份
   */
  async listBackups(options: {
    type?: 'full' | 'incremental';
    status?: 'success' | 'failed' | 'partial';
    limit?: number;
    offset?: number;
  } = {}): Promise<BackupInfo[]> {
    try {
      const index = await this.getBackupIndex();
      let filtered = index;

      // 应用过滤器
      if (options.type) {
        filtered = filtered.filter(item => item.type === options.type);
      }

      if (options.status) {
        filtered = filtered.filter(item => item.status === options.status);
      }

      // 按时间戳排序（最新的在前）
      filtered.sort((a, b) => 
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      // 应用分页
      const offset = options.offset || 0;
      const limit = options.limit || 50;
      filtered = filtered.slice(offset, offset + limit);

      // 获取完整元数据
      const backups: BackupInfo[] = [];
      for (const item of filtered) {
        const metadata = await this.getBackupInfo(item.id);
        if (metadata) {
          backups.push(metadata);
        }
      }

      return backups;

    } catch (error) {
      this.logger.error('列出备份失败', error);
      return [];
    }
  }

  /**
   * 保护备份（防止自动清理）
   */
  async protectBackup(backupId: string, protect: boolean = true): Promise<void> {
    try {
      const metadata = await this.getBackupInfo(backupId);
      if (!metadata) {
        throw new Error(`备份不存在: ${backupId}`);
      }

      metadata.retention.protected = protect;
      await this.saveBackupMetadata(backupId, metadata);

      this.logger.log(`备份保护状态已更新: ${backupId}, 保护: ${protect}`);

    } catch (error) {
      this.logger.error(`更新备份保护状态失败: ${backupId}`, error);
      throw error;
    }
  }

  /**
   * 添加备份标签
   */
  async addBackupTags(backupId: string, tags: string[]): Promise<void> {
    try {
      const metadata = await this.getBackupInfo(backupId);
      if (!metadata) {
        throw new Error(`备份不存在: ${backupId}`);
      }

      metadata.tags = [...new Set([...metadata.tags, ...tags])];
      await this.saveBackupMetadata(backupId, metadata);

      this.logger.debug(`备份标签已添加: ${backupId}, 标签: ${tags.join(', ')}`);

    } catch (error) {
      this.logger.error(`添加备份标签失败: ${backupId}`, error);
      throw error;
    }
  }

  // 私有方法
  private async getBackupIndex(): Promise<Array<{
    id: string;
    type: 'full' | 'incremental';
    timestamp: string;
    status: 'success' | 'failed' | 'partial';
    size: number;
    retention?: { protected: boolean };
  }>> {
    try {
      const indexFile = path.join(this.metadataPath, 'index.json');
      const data = await fs.readFile(indexFile, 'utf-8');
      return JSON.parse(data);

    } catch (error) {
      if (error.code === 'ENOENT') {
        return [];
      }
      throw error;
    }
  }

  private async updateBackupIndex(metadata: BackupInfo): Promise<void> {
    try {
      const index = await this.getBackupIndex();
      
      // 移除现有条目（如果存在）
      const filtered = index.filter(item => item.id !== metadata.id);
      
      // 添加新条目
      filtered.push({
        id: metadata.id,
        type: metadata.type,
        timestamp: metadata.timestamp,
        status: metadata.status,
        size: metadata.size,
        retention: metadata.retention,
      });

      const indexFile = path.join(this.metadataPath, 'index.json');
      await fs.writeFile(indexFile, JSON.stringify(filtered, null, 2));

    } catch (error) {
      this.logger.error('更新备份索引失败', error);
    }
  }

  private async removeFromBackupIndex(backupId: string): Promise<void> {
    try {
      const index = await this.getBackupIndex();
      const filtered = index.filter(item => item.id !== backupId);

      const indexFile = path.join(this.metadataPath, 'index.json');
      await fs.writeFile(indexFile, JSON.stringify(filtered, null, 2));

    } catch (error) {
      this.logger.error('从备份索引中移除失败', error);
    }
  }

  private determineStatus(results: BackupResult[]): 'success' | 'failed' | 'partial' {
    if (results.length === 0) {
      return 'failed';
    }

    const successful = results.filter(r => r.status === 'success').length;
    const total = results.length;

    if (successful === total) {
      return 'success';
    } else if (successful > 0) {
      return 'partial';
    } else {
      return 'failed';
    }
  }

  private calculateTotalSize(results: BackupResult[]): number {
    return results.reduce((sum, r) => sum + (r.size || 0), 0);
  }

  private calculateChecksum(results: BackupResult[]): string {
    const data = results.map(r => `${r.id}-${r.size}-${r.timestamp}`).join('|');
    return Buffer.from(data).toString('base64');
  }
}
