# Gateway服务新架构目录结构创建脚本

Write-Host "🏗️ 创建Gateway服务新架构目录结构..." -ForegroundColor Green

# 基础目录
$baseDir = "apps/gateway/src"

# 创建modules目录结构
Write-Host "📁 创建功能模块目录..." -ForegroundColor Yellow

# 1. routing模块
New-Item -ItemType Directory -Path "$baseDir/modules/routing/controllers" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/routing/services" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/routing/interceptors" -Force | Out-Null

# 2. gateway-auth模块
New-Item -ItemType Directory -Path "$baseDir/modules/gateway-auth/controllers" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/gateway-auth/services" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/gateway-auth/strategies" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/gateway-auth/guards" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/gateway-auth/dto" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/gateway-auth/middleware" -Force | Out-Null

# 3. load-balancing模块
New-Item -ItemType Directory -Path "$baseDir/modules/load-balancing/services" -Force | Out-Null

# 4. circuit-breaker模块
New-Item -ItemType Directory -Path "$baseDir/modules/circuit-breaker/services" -Force | Out-Null

# 5. rate-limiting模块
New-Item -ItemType Directory -Path "$baseDir/modules/rate-limiting/services" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/rate-limiting/middleware" -Force | Out-Null

# 6. caching模块
New-Item -ItemType Directory -Path "$baseDir/modules/caching/services" -Force | Out-Null

# 7. websocket模块
New-Item -ItemType Directory -Path "$baseDir/modules/websocket/gateways" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/websocket/services" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/websocket/guards" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/websocket/context" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/websocket/decorators" -Force | Out-Null

# 8. server-discovery模块
New-Item -ItemType Directory -Path "$baseDir/modules/server-discovery/controllers" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/server-discovery/services" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/server-discovery/dto" -Force | Out-Null

# 9. character模块
New-Item -ItemType Directory -Path "$baseDir/modules/character/controllers" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/character/services" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/character/dto" -Force | Out-Null

# 10. global-messaging模块
New-Item -ItemType Directory -Path "$baseDir/modules/global-messaging/controllers" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/global-messaging/services" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/global-messaging/queue" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/global-messaging/dto" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/global-messaging/interfaces" -Force | Out-Null

# 11. graphql模块
New-Item -ItemType Directory -Path "$baseDir/modules/graphql/gateways" -Force | Out-Null

# 12. health模块
New-Item -ItemType Directory -Path "$baseDir/modules/health/controllers" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/health/services" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/health/indicators" -Force | Out-Null

# 13. monitoring模块
New-Item -ItemType Directory -Path "$baseDir/modules/monitoring/controllers" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/modules/monitoring/services" -Force | Out-Null

# 14. administration模块
New-Item -ItemType Directory -Path "$baseDir/modules/administration/controllers" -Force | Out-Null

Write-Host "📁 创建shared目录..." -ForegroundColor Yellow

# 创建shared目录结构
New-Item -ItemType Directory -Path "$baseDir/shared/config" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/constants" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/interfaces" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/dto" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/decorators" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/utils" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/filters" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/interceptors" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/middleware" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/jwt" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/microservices" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/discovery" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/logging" -Force | Out-Null
New-Item -ItemType Directory -Path "$baseDir/shared/tracing" -Force | Out-Null

Write-Host "✅ Gateway服务新架构目录结构创建完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 创建的目录结构：" -ForegroundColor Cyan
Write-Host "├── modules/ (14个功能模块)" -ForegroundColor White
Write-Host "│   ├── routing/" -ForegroundColor White
Write-Host "│   ├── gateway-auth/" -ForegroundColor White
Write-Host "│   ├── load-balancing/" -ForegroundColor White
Write-Host "│   ├── circuit-breaker/" -ForegroundColor White
Write-Host "│   ├── rate-limiting/" -ForegroundColor White
Write-Host "│   ├── caching/" -ForegroundColor White
Write-Host "│   ├── websocket/" -ForegroundColor White
Write-Host "│   ├── server-discovery/" -ForegroundColor White
Write-Host "│   ├── character/" -ForegroundColor White
Write-Host "│   ├── global-messaging/" -ForegroundColor White
Write-Host "│   ├── graphql/" -ForegroundColor White
Write-Host "│   ├── health/" -ForegroundColor White
Write-Host "│   ├── monitoring/" -ForegroundColor White
Write-Host "│   └── administration/" -ForegroundColor White
Write-Host "└── shared/ (共享资源)" -ForegroundColor White
Write-Host ""
Write-Host "🎯 下一步：请手动拖动122个.ts文件到对应目录" -ForegroundColor Yellow
Write-Host "💡 利用IDE的自动更新导入功能" -ForegroundColor Yellow
