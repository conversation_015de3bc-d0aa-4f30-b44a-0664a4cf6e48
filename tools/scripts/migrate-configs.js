#!/usr/bin/env node

/**
 * 配置迁移脚本
 * 执行配置文件迁移任务
 */

const path = require('path');
const chalk = require('../../node_modules/chalk');
const ora = require('../../node_modules/ora');

// 设置工作目录为项目根目录
process.chdir(path.resolve(__dirname, '../..'));

const ConfigMigrator = require('../config-tools/config-migrator');

async function main() {
  console.log(chalk.blue.bold('🚀 配置迁移工具'));
  console.log(chalk.gray('从old项目迁移配置文件到新项目\n'));

  const spinner = ora('正在迁移配置文件...').start();

  try {
    const migrator = new ConfigMigrator();
    
    // 显示迁移前的统计信息
    const beforeStats = await migrator.getStats();
    spinner.info(`源目录: ${beforeStats.sourceCount} 个配置文件`);
    spinner.info(`目标目录: ${beforeStats.targetCount} 个配置文件`);
    
    if (beforeStats.sourceCount === 0) {
      spinner.fail('未找到源配置文件，请检查old项目路径');
      process.exit(1);
    }

    spinner.start('正在迁移配置文件...');
    
    // 执行迁移
    const result = await migrator.migrateAll();
    
    if (result.success) {
      spinner.succeed('配置迁移完成！');
      
      // 显示迁移后的统计信息
      const afterStats = await migrator.getStats();
      console.log(chalk.green(`\n✅ 迁移成功: ${result.report.successFiles}/${result.report.totalFiles} 个文件`));
      console.log(chalk.cyan(`📊 目标目录现有: ${afterStats.targetCount} 个配置文件`));
      
      if (result.report.standardizedFields.size > 0) {
        console.log(chalk.yellow(`🔧 标准化字段: ${result.report.standardizedFields.size} 个`));
      }
      
    } else {
      spinner.fail('配置迁移失败');
      console.log(chalk.red(`❌ 错误: ${result.error || result.message}`));
      process.exit(1);
    }
    
  } catch (error) {
    spinner.fail('迁移过程中发生错误');
    console.error(chalk.red('❌ 错误详情:'), error.message);
    console.error(chalk.gray(error.stack));
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的Promise拒绝:'), reason);
  process.exit(1);
});

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = main;
