#!/usr/bin/env node

/**
 * 配置验证脚本
 * 验证JSON配置文件的完整性和正确性
 *
 * 功能：
 * - 检查JSON格式是否正确
 * - 验证数据结构完整性
 * - 检查字段类型一致性
 * - 验证必需字段存在性
 */

const path = require('path');
const chalk = require('../../node_modules/chalk');
const ora = require('../../node_modules/ora');

// 设置工作目录为项目根目录
process.chdir(path.resolve(__dirname, '../..'));

const ConfigValidator = require('../config-tools/config-validator');

async function main() {
  console.log(chalk.blue.bold('🚀 配置验证工具'));
  console.log(chalk.gray('验证配置文件格式和数据完整性\n'));

  const spinner = ora('正在验证配置...').start();

  try {
    const validator = new ConfigValidator();
    
    // 执行验证
    const result = await validator.validateAll();
    
    if (result.success) {
      if (result.report.totalWarnings === 0) {
        spinner.succeed('配置验证完成，所有文件都通过验证！');
      } else {
        spinner.warn('配置验证完成，但存在警告信息');
      }
      
      console.log(chalk.green(`\n✅ 验证通过: ${result.report.validFiles}/${result.report.totalFiles} 个文件`));
      console.log(chalk.cyan(`📊 总记录数: ${result.report.totalRecords} 条`));
      
      if (result.report.totalWarnings > 0) {
        console.log(chalk.yellow(`⚠️  警告数量: ${result.report.totalWarnings} 个`));
      }
      
      // 显示验证详情
      if (result.report.validationResults.length > 0) {
        console.log(chalk.blue('\n📋 验证详情:'));
        result.report.validationResults.forEach(result => {
          const status = result.isValid ? 
            (result.warnings.length > 0 ? chalk.yellow('⚠️ ') : chalk.green('✅')) : 
            chalk.red('❌');
          
          console.log(`  ${status} ${result.tableName}: ${result.recordCount} 条记录`);
          
          if (result.errors.length > 0) {
            result.errors.slice(0, 3).forEach(error => {
              console.log(`    ${chalk.red('错误:')} ${error}`);
            });
            if (result.errors.length > 3) {
              console.log(`    ${chalk.gray(`... 还有 ${result.errors.length - 3} 个错误`)}`);
            }
          }
          
          if (result.warnings.length > 0) {
            result.warnings.slice(0, 2).forEach(warning => {
              console.log(`    ${chalk.yellow('警告:')} ${warning}`);
            });
            if (result.warnings.length > 2) {
              console.log(`    ${chalk.gray(`... 还有 ${result.warnings.length - 2} 个警告`)}`);
            }
          }
        });
      }
      
    } else {
      spinner.fail('配置验证失败');
      console.log(chalk.red(`❌ 错误数量: ${result.report.totalErrors} 个`));
      
      // 显示失败的文件
      if (result.report.invalidFiles.length > 0) {
        console.log(chalk.red('\n❌ 验证失败的文件:'));
        result.report.invalidFiles.forEach(failed => {
          if (failed.error) {
            console.log(`  ${chalk.red(failed.file)}: ${failed.error}`);
          } else {
            console.log(`  ${chalk.red(failed.file)}: ${failed.errors} 个错误, ${failed.warnings} 个警告`);
          }
        });
      }
      
      process.exit(1);
    }
    
  } catch (error) {
    spinner.fail('验证过程中发生错误');
    console.error(chalk.red('❌ 错误详情:'), error.message);
    console.error(chalk.gray(error.stack));
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的Promise拒绝:'), reason);
  process.exit(1);
});

// 执行主函数
if (require.main === module) {
  main();
}

module.exports = main;
