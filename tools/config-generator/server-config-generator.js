#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { Command } = require('commander');

/**
 * 区服配置生成器
 * 为新区服生成标准化的配置文件
 * 
 * 使用方式：
 * node tools/config-generator/server-config-generator.js --server=server001 --service=character
 */

class ServerConfigGenerator {
  constructor() {
    this.serviceConfigs = {
      character: {
        name: 'character',
        port: 3002,
        instances: 2,
        database: 'character_db',
        description: '角色管理服务',
      },
      hero: {
        name: 'hero',
        port: 3003,
        instances: 2,
        database: 'hero_db',
        description: '英雄管理服务',
      },
      economy: {
        name: 'economy',
        port: 3004,
        instances: 1,
        database: 'economy_db',
        description: '经济系统服务',
      },
      social: {
        name: 'social',
        port: 3005,
        instances: 1,
        database: 'social_db',
        description: '社交系统服务',
      },
      activity: {
        name: 'activity',
        port: 3006,
        instances: 1,
        database: 'activity_db',
        description: '活动系统服务',
      },
      match: {
        name: 'match',
        port: 3007,
        instances: 2,
        database: 'match_db',
        description: '比赛系统服务',
      },
    };
  }

  /**
   * 生成区服环境配置文件
   */
  generateServerEnvFile(serverId, serviceName) {
    const serviceConfig = this.serviceConfigs[serviceName];
    if (!serviceConfig) {
      throw new Error(`Unknown service: ${serviceName}`);
    }

    const template = `# ${serviceConfig.description} - ${serverId} 配置
# 自动生成于: ${new Date().toISOString()}

# 基础配置
NODE_ENV=production
SERVICE_NAME=${serviceName}
SERVER_ID=${serverId}
PORT=${serviceConfig.port}

# 数据库配置
${serviceName.toUpperCase()}_MONGODB_URI=mongodb://${serviceName}-admin:password@***************:27017/${serviceConfig.database}_${serverId}

# Redis配置（保持现有前缀机制）
REDIS_HOST=***************
REDIS_PORT=6379
# Redis前缀通过RedisModule自动处理，格式：{环境}:{项目}:server{serverId}:{服务}:

# 微服务配置
MICROSERVICE_CLIENT_ID=${serviceName}-${serverId}
MICROSERVICE_CLIENT_SECRET=\${MICROSERVICE_CLIENT_SECRET}

# 网关配置
GATEWAY_URL=http://gateway:3000

# 监控配置
METRICS_ENABLED=true
METRICS_PORT=${9100 + serviceConfig.port - 3000}

# 健康检查配置
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
`;
    return template.trim();
  }

  /**
   * 生成Docker Compose服务配置
   */
  generateDockerComposeService(serverId, serviceName, instanceIndex) {
    const serviceConfig = this.serviceConfigs[serviceName];
    if (!serviceConfig) {
      throw new Error(`Unknown service: ${serviceName}`);
    }

    const instanceName = `${serviceName}-${serverId}-${instanceIndex}`;
    const port = serviceConfig.port + (instanceIndex - 1) * 10;
    const metricsPort = 9100 + serviceConfig.port - 3000 + (instanceIndex - 1) * 10;

    return `
  ${instanceName}:
    build:
      context: .
      dockerfile: apps/${serviceName}/Dockerfile
    container_name: ${instanceName}
    env_file:
      - config/${serverId}/.env.${serviceName}
    environment:
      - INSTANCE_INDEX=${instanceIndex}
      - PORT=${port}
      - METRICS_PORT=${metricsPort}
    ports:
      - "${port}:${port}"
      - "${metricsPort}:${metricsPort}"
    depends_on:
      - mongodb
      - redis
    networks:
      - microservices
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${port}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "service=${serviceName}"
      - "server=${serverId}"
      - "instance=${instanceIndex}"
`;
  }

  /**
   * 生成完整的区服配置
   */
  generateServerConfig(serverId) {
    const databases = {};
    const services = {};

    Object.entries(this.serviceConfigs).forEach(([name, config]) => {
      databases[name] = `mongodb://${name}-admin:password@***************:27017/${config.database}_${serverId}`;
      services[name] = { ...config };
    });

    return {
      serverId,
      services,
      databases,
      redis: {
        host: '***************',
        port: 6379,
        keyPrefix: `production:fm:server${serverId}:`,
      },
    };
  }

  /**
   * 生成数据库初始化脚本
   */
  generateDatabaseInitScript(serverId) {
    const services = Object.keys(this.serviceConfigs);
    
    const script = `#!/bin/bash
# 数据库初始化脚本 - ${serverId}
# 自动生成于: ${new Date().toISOString()}

set -e

echo "🗄️ 初始化区服 ${serverId} 数据库..."

MONGO_HOST=\${MONGO_HOST:-***************}
MONGO_PORT=\${MONGO_PORT:-27017}

${services.map(service => {
  const config = this.serviceConfigs[service];
  return `
# 创建 ${service} 数据库
echo "创建 ${config.database}_${serverId} 数据库..."
mongo --host \$MONGO_HOST:\$MONGO_PORT --eval "
  db = db.getSiblingDB('${config.database}_${serverId}');
  db.createUser({
    user: '${service}-admin',
    pwd: 'password',
    roles: [
      { role: 'readWrite', db: '${config.database}_${serverId}' },
      { role: 'dbAdmin', db: '${config.database}_${serverId}' }
    ]
  });
  print('✅ ${config.database}_${serverId} 数据库创建完成');
" || echo "⚠️ ${config.database}_${serverId} 数据库可能已存在"
`;
}).join('')}

echo "✅ 区服 ${serverId} 数据库初始化完成！"
`;
    return script;
  }

  /**
   * 生成区服部署脚本
   */
  generateDeploymentScript(serverId, services = Object.keys(this.serviceConfigs)) {
    const script = `#!/bin/bash
# 区服 ${serverId} 部署脚本
# 自动生成于: ${new Date().toISOString()}

set -e

SERVER_ID="${serverId}"
SERVICES=(${services.map(s => `"${s}"`).join(' ')})

echo "🚀 开始部署区服: \$SERVER_ID"

# 1. 创建配置目录
echo "📁 创建配置目录..."
mkdir -p config/\$SERVER_ID

# 2. 生成配置文件
echo "⚙️ 生成配置文件..."
for service in "\${SERVICES[@]}"; do
  echo "生成 \$service 配置..."
  node tools/config-generator/server-config-generator.js env \\
    --server=\$SERVER_ID \\
    --service=\$service \\
    --output=config/\$SERVER_ID/.env.\$service
done

# 3. 初始化数据库
echo "🗄️ 初始化数据库..."
bash scripts/init-\$SERVER_ID-databases.sh

# 4. 生成Docker Compose配置
echo "🐳 生成Docker Compose配置..."
node tools/config-generator/server-config-generator.js docker-compose-full \\
  --server=\$SERVER_ID \\
  --services=\${SERVICES[*]} \\
  --output=docker-compose.\$SERVER_ID.yml

# 5. 部署服务实例
echo "🚀 部署服务实例..."
docker-compose -f docker-compose.\$SERVER_ID.yml up -d

# 6. 等待服务就绪
echo "⏳ 等待服务就绪..."
bash scripts/wait-for-server-services.sh \$SERVER_ID

# 7. 注册到服务发现
echo "📋 注册服务到Gateway..."
bash scripts/register-server-services.sh \$SERVER_ID

echo "🎉 区服 \$SERVER_ID 部署完成！"
echo "📊 查看状态: docker-compose -f docker-compose.\$SERVER_ID.yml ps"
echo "📋 查看日志: docker-compose -f docker-compose.\$SERVER_ID.yml logs -f"
`;
    return script;
  }

  /**
   * 生成完整的Docker Compose文件
   */
  generateFullDockerCompose(serverId, services = Object.keys(this.serviceConfigs)) {
    let compose = `version: '3.8'

networks:
  microservices:
    external: true

services:`;

    services.forEach(serviceName => {
      const serviceConfig = this.serviceConfigs[serviceName];
      const instanceCount = serviceConfig.instances;
      
      for (let i = 1; i <= instanceCount; i++) {
        compose += this.generateDockerComposeService(serverId, serviceName, i);
      }
    });

    return compose;
  }

  /**
   * 保存文件到指定路径
   */
  saveFile(filePath, content) {
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 文件已生成: ${filePath}`);
  }

  /**
   * 设置文件执行权限
   */
  makeExecutable(filePath) {
    try {
      fs.chmodSync(filePath, '755');
      console.log(`🔧 设置执行权限: ${filePath}`);
    } catch (error) {
      console.warn(`⚠️ 无法设置执行权限: ${filePath}`, error.message);
    }
  }
}

// CLI 接口
const program = new Command();

program
  .name('server-config-generator')
  .description('区服配置生成器')
  .version('1.0.0');

program
  .command('env')
  .description('生成环境配置文件')
  .requiredOption('--server <serverId>', '区服ID')
  .requiredOption('--service <serviceName>', '服务名称')
  .option('--output <path>', '输出文件路径')
  .action((options) => {
    const generator = new ServerConfigGenerator();
    const content = generator.generateServerEnvFile(options.server, options.service);
    
    if (options.output) {
      generator.saveFile(options.output, content);
    } else {
      console.log(content);
    }
  });

program
  .command('docker-compose')
  .description('生成Docker Compose服务配置')
  .requiredOption('--server <serverId>', '区服ID')
  .requiredOption('--service <serviceName>', '服务名称')
  .requiredOption('--instance <instanceIndex>', '实例索引')
  .option('--output <path>', '输出文件路径')
  .action((options) => {
    const generator = new ServerConfigGenerator();
    const content = generator.generateDockerComposeService(
      options.server,
      options.service,
      parseInt(options.instance)
    );
    
    if (options.output) {
      generator.saveFile(options.output, content);
    } else {
      console.log(content);
    }
  });

program
  .command('docker-compose-full')
  .description('生成完整的Docker Compose文件')
  .requiredOption('--server <serverId>', '区服ID')
  .option('--services <services...>', '服务列表')
  .option('--output <path>', '输出文件路径')
  .action((options) => {
    const generator = new ServerConfigGenerator();
    const content = generator.generateFullDockerCompose(options.server, options.services);
    
    if (options.output) {
      generator.saveFile(options.output, content);
    } else {
      console.log(content);
    }
  });

program
  .command('database-init')
  .description('生成数据库初始化脚本')
  .requiredOption('--server <serverId>', '区服ID')
  .option('--output <path>', '输出文件路径')
  .action((options) => {
    const generator = new ServerConfigGenerator();
    const content = generator.generateDatabaseInitScript(options.server);
    
    const outputPath = options.output || `scripts/init-${options.server}-databases.sh`;
    generator.saveFile(outputPath, content);
    generator.makeExecutable(outputPath);
  });

program
  .command('deploy-script')
  .description('生成部署脚本')
  .requiredOption('--server <serverId>', '区服ID')
  .option('--services <services...>', '服务列表')
  .option('--output <path>', '输出文件路径')
  .action((options) => {
    const generator = new ServerConfigGenerator();
    const content = generator.generateDeploymentScript(options.server, options.services);
    
    const outputPath = options.output || `scripts/deploy-${options.server}.sh`;
    generator.saveFile(outputPath, content);
    generator.makeExecutable(outputPath);
  });

program
  .command('full-server')
  .description('为新区服生成完整配置')
  .requiredOption('--server <serverId>', '区服ID')
  .option('--services <services...>', '服务列表')
  .action((options) => {
    const generator = new ServerConfigGenerator();
    const services = options.services || Object.keys(generator.serviceConfigs);
    
    console.log(`🚀 为区服 ${options.server} 生成完整配置...`);
    
    // 1. 生成环境配置文件
    services.forEach(service => {
      const envContent = generator.generateServerEnvFile(options.server, service);
      const envPath = `config/${options.server}/.env.${service}`;
      generator.saveFile(envPath, envContent);
    });
    
    // 2. 生成数据库初始化脚本
    const dbScript = generator.generateDatabaseInitScript(options.server);
    const dbScriptPath = `scripts/init-${options.server}-databases.sh`;
    generator.saveFile(dbScriptPath, dbScript);
    generator.makeExecutable(dbScriptPath);
    
    // 3. 生成部署脚本
    const deployScript = generator.generateDeploymentScript(options.server, services);
    const deployScriptPath = `scripts/deploy-${options.server}.sh`;
    generator.saveFile(deployScriptPath, deployScript);
    generator.makeExecutable(deployScriptPath);
    
    // 4. 生成Docker Compose文件
    const composeContent = generator.generateFullDockerCompose(options.server, services);
    const composePath = `docker-compose.${options.server}.yml`;
    generator.saveFile(composePath, composeContent);
    
    console.log(`✅ 区服 ${options.server} 配置生成完成！`);
    console.log(`📁 配置文件: config/${options.server}/`);
    console.log(`🗄️ 数据库脚本: ${dbScriptPath}`);
    console.log(`🚀 部署脚本: ${deployScriptPath}`);
    console.log(`🐳 Docker Compose: ${composePath}`);
  });

// 如果直接运行此文件
if (require.main === module) {
  program.parse();
}

module.exports = { ServerConfigGenerator };
