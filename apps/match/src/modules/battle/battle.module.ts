import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BattleController } from './battle.controller';
import { BattleService } from './battle.service';
import { BattleRepository } from '../../common/repositories/battle.repository';
import { BattleRoom, BattleRoomSchema } from '../../common/schemas/battle.schema';

/**
 * 战斗计算引擎模块
 * 基于old项目battleService.js和room.js的功能实现
 *
 * 核心功能：
 * - PVE战斗计算
 * - PVP战斗计算
 * - 战斗结果处理
 * - 战斗回放生成
 * - 战斗房间管理
 *
 * 注意：
 * - GameConfigModule已在app.module.ts中全局注册，无需重复导入
 * - MicroserviceKitModule已在app.module.ts中注册，可直接使用MicroserviceClientService
 * - MongooseModule.forFeature()用于注册特定Schema，需要在使用的模块中注册
 */
@Module({
  imports: [
    // 注册BattleRoom Schema（BattleRepository需要）
    MongooseModule.forFeature([
      { name: BattleRoom.name, schema: BattleRoomSchema },
    ]),
  ],
  controllers: [BattleController],
  providers: [
    BattleService,
    BattleRepository, // 战斗数据访问层
  ],
  exports: [BattleService, BattleRepository],
})
export class BattleModule {}
