import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { GlobalRanking, CharacterRanking, RankingEntry, RankingReward } from '../schemas/ranking.schema';

/**
 * 排名数据访问层
 * 基于Repository模式，提供排名数据的CRUD操作
 */
@Injectable()
export class RankingRepository {
  private readonly logger = new Logger(RankingRepository.name);

  constructor(
    @InjectModel(GlobalRanking.name) private readonly globalRankingModel: Model<GlobalRanking>,
    @InjectModel(CharacterRanking.name) private readonly characterRankingModel: Model<CharacterRanking>,
  ) {}

  // ==================== 全球排名相关 ====================

  /**
   * 根据排名类型查找全球排名
   */
  async findGlobalRanking(rankType: string, season?: number): Promise<GlobalRanking | null> {
    try {
      const query: any = { rankType };
      if (season !== undefined) {
        query.season = season;
      }
      return await this.globalRankingModel.findOne(query).exec();
    } catch (error) {
      this.logger.error(`查找全球排名失败: ${rankType}`, error);
      throw error;
    }
  }

  /**
   * 创建或更新全球排名
   */
  async upsertGlobalRanking(rankType: string, rankings: RankingEntry[], season: number = 0): Promise<GlobalRanking> {
    try {
      const now = new Date();
      const nextUpdate = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24小时后

      return await this.globalRankingModel.findOneAndUpdate(
        { rankType, season },
        {
          rankings,
          lastUpdateTime: now,
          nextUpdateTime: nextUpdate,
        },
        { new: true, upsert: true }
      ).exec();
    } catch (error) {
      this.logger.error(`更新全球排名失败: ${rankType}`, error);
      throw error;
    }
  }

  /**
   * 获取全球排名列表（分页）
   */
  async getGlobalRankingList(rankType: string, limit: number = 100, offset: number = 0, season?: number): Promise<RankingEntry[]> {
    try {
      const query: any = { rankType };
      if (season !== undefined) {
        query.season = season;
      }

      const globalRanking = await this.globalRankingModel.findOne(query).exec();
      if (!globalRanking) {
        return [];
      }

      return globalRanking.rankings.slice(offset, offset + limit);
    } catch (error) {
      this.logger.error(`获取全球排名列表失败: ${rankType}`, error);
      throw error;
    }
  }

  /**
   * 获取玩家在全球排名中的位置
   */
  async getCharacterGlobalRank(rankType: string, characterId: string, season?: number): Promise<number> {
    try {
      const query: any = { rankType };
      if (season !== undefined) {
        query.season = season;
      }

      const globalRanking = await this.globalRankingModel.findOne(query).exec();
      if (!globalRanking) {
        return 0;
      }

      const characterEntry = globalRanking.rankings.find(entry => entry.characterId === characterId);
      return characterEntry ? characterEntry.rank : 0;
    } catch (error) {
      this.logger.error(`获取玩家全球排名失败: ${rankType}, ${characterId}`, error);
      return 0;
    }
  }

  // ==================== 玩家排名相关 ====================

  /**
   * 根据玩家UID查找排名数据
   */
  async findCharacterRanking(uid: string): Promise<CharacterRanking | null> {
    try {
      return await this.characterRankingModel.findOne({ uid }).exec();
    } catch (error) {
      this.logger.error(`查找玩家排名数据失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 创建或更新玩家排名数据
   */
  async upsertCharacterRanking(uid: string, characterRankingData: Partial<CharacterRanking>): Promise<CharacterRanking> {
    try {
      return await this.characterRankingModel.findOneAndUpdate(
        { uid },
        { ...characterRankingData, lastUpdateTime: new Date() },
        { new: true, upsert: true }
      ).exec();
    } catch (error) {
      this.logger.error(`更新玩家排名数据失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 添加排名奖励记录
   */
  async addRankingReward(uid: string, reward: RankingReward): Promise<CharacterRanking | null> {
    try {
      const characterRanking = await this.findCharacterRanking(uid);
      if (!characterRanking) {
        // 创建新的玩家排名数据
        return await this.upsertCharacterRanking(uid, {
          uid,
          rewardHistory: [reward],
          currentRanks: {},
          bestRanks: {},
        });
      }

      // 限制奖励历史记录数量为100条
      if (characterRanking.rewardHistory.length >= 100) {
        const removeCount = characterRanking.rewardHistory.length - 100 + 1;
        characterRanking.rewardHistory.splice(0, removeCount);
      }

      // 添加新奖励记录
      characterRanking.rewardHistory.push(reward);
      
      // 按时间排序（最新的在前）
      characterRanking.rewardHistory.sort((a, b) => {
        return new Date(b.rewardTime).getTime() - new Date(a.rewardTime).getTime();
      });

      return await this.upsertCharacterRanking(uid, { rewardHistory: characterRanking.rewardHistory });
    } catch (error) {
      this.logger.error(`添加排名奖励记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 更新玩家当前排名
   */
  async updateCharacterCurrentRanks(uid: string, ranks: any): Promise<CharacterRanking | null> {
    try {
      const characterRanking = await this.findCharacterRanking(uid);
      const currentRanks = characterRanking ? { ...characterRanking.currentRanks, ...ranks } : ranks;
      const bestRanks = characterRanking ? { ...characterRanking.bestRanks } : {};

      // 更新历史最佳排名
      for (const [rankType, rank] of Object.entries(ranks)) {
        if (typeof rank === 'number' && rank > 0) {
          if (!bestRanks[rankType] || rank < bestRanks[rankType]) {
            bestRanks[rankType] = rank;
          }
        }
      }

      return await this.upsertCharacterRanking(uid, { currentRanks, bestRanks });
    } catch (error) {
      this.logger.error(`更新玩家当前排名失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 获取未领取的排名奖励
   */
  async getUnclaimedRewards(uid: string): Promise<RankingReward[]> {
    try {
      const characterRanking = await this.findCharacterRanking(uid);
      if (!characterRanking) {
        return [];
      }

      return characterRanking.rewardHistory.filter(reward => reward.status === 0);
    } catch (error) {
      this.logger.error(`获取未领取排名奖励失败: ${uid}`, error);
      return [];
    }
  }

  /**
   * 标记奖励为已领取
   */
  async markRewardAsClaimed(uid: string, rankType: string, season: number): Promise<boolean> {
    try {
      const characterRanking = await this.findCharacterRanking(uid);
      if (!characterRanking) {
        return false;
      }

      const reward = characterRanking.rewardHistory.find(
        r => r.rankType === rankType && r.season === season && r.status === 0
      );

      if (!reward) {
        return false;
      }

      reward.status = 1;
      await this.upsertCharacterRanking(uid, { rewardHistory: characterRanking.rewardHistory });
      return true;
    } catch (error) {
      this.logger.error(`标记奖励为已领取失败: ${uid}`, error);
      return false;
    }
  }

  // ==================== 统计和管理 ====================

  /**
   * 获取排名统计信息
   */
  async getStatistics(): Promise<any> {
    try {
      const totalGlobalRankings = await this.globalRankingModel.countDocuments().exec();
      const totalCharacterRankings = await this.characterRankingModel.countDocuments().exec();
      
      const activeToday = await this.characterRankingModel
        .countDocuments({
          lastUpdateTime: {
            $gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        })
        .exec();

      return {
        totalGlobalRankings,
        totalCharacterRankings,
        activeToday,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error('获取排名统计信息失败', error);
      throw error;
    }
  }

  /**
   * 删除过期的排名数据
   */
  async cleanExpiredRankings(daysToKeep: number = 30): Promise<number> {
    try {
      const expireTime = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
      
      const result = await this.globalRankingModel
        .deleteMany({
          lastUpdateTime: { $lt: expireTime }
        })
        .exec();

      this.logger.log(`清理过期排名数据: ${result.deletedCount}个`);
      return result.deletedCount;
    } catch (error) {
      this.logger.error('清理过期排名数据失败', error);
      throw error;
    }
  }

  /**
   * 批量查询玩家排名数据
   */
  async findCharacterRankingsByIds(ids: string[]): Promise<CharacterRanking[]> {
    try {
      return await this.characterRankingModel.find({ id: { $in: ids } }).exec();
    } catch (error) {
      this.logger.error('批量查询玩家排名数据失败', error);
      throw error;
    }
  }
}
