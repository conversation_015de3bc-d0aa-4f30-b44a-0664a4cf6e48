import { IsString, IsNumber, IsOptional, IsArray, ValidateNested, IsObject, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { HeroPosition } from '@app/game-constants';

/**
 * 战斗系统相关DTO
 * 基于old项目battleService.js的接口定义
 */

/**
 * 队伍战斗数据DTO
 */
export class BattleTeamDataDto {
  @IsString()
  characterId: string; // 玩家ID或AI标识

  @IsString()
  teamName: string; // 队伍名称

  @IsNumber()
  formation: number; // 阵型ID

  @IsNumber()
  tactic: number; // 战术ID

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BattleHeroInfoDto)
  heroes: BattleHeroInfoDto[]; // 英雄列表

  @IsNumber()
  totalAttack: number; // 总防御力

  @IsNumber()
  totalDefend: number; // 总防御力

  @IsNumber()
  morale: number; // 士气值

  @IsNumber()
  score: number; // 比分
}

/**
 * PVE战斗请求DTO
 */
export class PveBattleDto {
  @IsString()
  characterId: string;

  @IsObject()
  characterBattleData: BattleTeamDataDto;                       // 玩家战斗数据

  @IsObject()
  enemyBattleData: BattleTeamDataDto;                      // 敌方配置数据

  @IsString()
  battleType: string;                    // 战斗类型 (league/trophy/tournament等)

  @IsOptional()
  @IsNumber()
  leagueId?: number;                     // 联赛ID

  @IsOptional()
  @IsNumber()
  teamCopyId?: number;                   // 副本ID

  @IsOptional()
  @IsString()
  serverId?: string;
}

/**
 * PVP战斗请求DTO
 */
export class PvpBattleDto {
  @IsString()
  homeCharacterId: string;                  // 主队玩家ID

  @IsString()
  awayCharacterId: string;                  // 客队玩家ID

  @IsObject()
  homeBattleData: BattleTeamDataDto;                   // 主队战斗数据

  @IsObject()
  awayBattleData: BattleTeamDataDto;                   // 客队战斗数据

  @IsString()
  battleType: string;                    // 战斗类型

  @IsOptional()
  @IsString()
  serverId?: string;
}

/**
 * 球员战斗信息DTO
 * 修复：使用项目统一的类型定义，确保架构一致性
 */
export class BattleHeroInfoDto {
  @IsString()
  heroId: string;                        // 修复：使用统一的heroId命名

  @IsNumber()
  attack: number;

  @IsNumber()
  defend: number;

  @IsNumber()
  speed: number;

  @IsNumber()
  power: number;

  @IsNumber()
  technique: number;

  @IsEnum(HeroPosition)
  position: HeroPosition;                // 修复：使用统一的HeroPosition枚举

  @IsNumber()
  level: number;

  @IsArray()
  @IsNumber({}, { each: true })
  skills: number[];
}

/**
 * 战斗结果响应DTO
 */
export class BattleResultResponseDto {
  @IsNumber()
  code: number;

  @IsString()
  message: string;

  @IsOptional()
  @IsString()
  roomUid?: string;

  @IsOptional()
  @IsNumber()
  homeScore?: number;

  @IsOptional()
  @IsNumber()
  awayScore?: number;

  @IsOptional()
  @IsNumber()
  winner?: number;                       // 0平局 1主队胜 2客队胜

  @IsOptional()
  @IsObject()
  battleRecord?: any;                    // 战斗回放数据

  @IsOptional()
  @IsObject()
  statistics?: any;                      // 战斗统计数据
}

/**
 * 获取战斗回放请求DTO
 */
export class GetBattleReplayDto {
  @IsString()
  roomUid: string;

  @IsOptional()
  @IsString()
  characterId?: string;
}

/**
 * 获取战斗回放响应DTO
 */
export class GetBattleReplayResponseDto {
  @IsNumber()
  code: number;

  @IsString()
  message: string;

  @IsOptional()
  @IsObject()
  battleRecord?: any;

  @IsOptional()
  @IsObject()
  teamAData?: any;

  @IsOptional()
  @IsObject()
  teamBData?: any;
}
