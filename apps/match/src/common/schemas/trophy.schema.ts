import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

/**
 * 杯赛副本数据
 * 基于old项目trophyCopy.js的copyData结构
 */
@Schema({ _id: false })
export class TrophyCopyData {
  @Prop({ required: true })
  teamId: number;                        // 球队副本ID 配置的球队副本ID

  @Prop({ default: 0 })
  num: number;                           // 当前挑战次数

  @Prop({ default: 0 })
  alreadyPurchaseNum: number;            // 当前已购买次数
}

/**
 * 单个杯赛数据结构
 * 基于old项目trophyCopy.js的oneTrophyObj结构
 */
@Schema({ _id: false })
export class OneTrophy {
  @Prop({ required: true })
  id: number;                            // 杯赛副本ID，不需要自己生成,直接使用配置的ID

  @Prop({ default: 0 })
  num: number;                           // 当前挑战次数

  @Prop({ default: 0 })
  alreadyPurchaseNum: number;            // 当前已购买次数

  @Prop({ type: [TrophyCopyData], default: [] })
  copyData: TrophyCopyData[];            // 副本数据
}

/**
 * 杯赛奖励信息
 * 基于old项目trophyCopy.js的奖励处理逻辑
 */
@Schema({ _id: false })
export class TrophyRewardInfo {
  @Prop({ type: [Object], default: [] })
  itemUidList: any[];                    // 物品奖励列表

  @Prop({ type: [Object], default: [] })
  heroUidList: any[];                    // 球员奖励列表

  @Prop({ default: 0 })
  code: number;                          // 奖励处理结果代码
}

/**
 * 杯赛战斗结果
 * 基于old项目trophyCopy.js的战斗结果结构
 */
@Schema({ _id: false })
export class TrophyBattleResult {
  @Prop({ required: true })
  trophyId: number;                      // 杯赛ID

  @Prop({ required: true })
  teamId: number;                        // 队伍ID

  @Prop({ default: 0 })
  result: number;                        // 战斗结果 (0失败 1胜利)

  @Prop({ default: 0 })
  selfScore: number;                     // 己方比分

  @Prop({ default: 0 })
  enemyScore: number;                    // 敌方比分

  @Prop({ default: Date.now })
  battleTime: Date;                      // 战斗时间

  @Prop({ type: TrophyRewardInfo })
  rewardInfo: TrophyRewardInfo;          // 奖励信息
}

/**
 * 杯赛主文档
 * 基于old项目trophyCopy.js的主要数据结构
 */
@Schema({
  collection: 'trophy_copies',
  timestamps: true,
  versionKey: false,
})
export class TrophyCopy extends Document {
  @Prop({ required: true, unique: true })
  uid: string;                           // 玩家UID

  @Prop({ type: [OneTrophy], default: [] })
  allTrophyCopys: OneTrophy[];           // 所有杯赛数据 trophyId => oneTrophyObj

  @Prop({ default: 0 })
  lastUpdateTimes: number;               // 最后更新时间戳

  @Prop({ type: [TrophyBattleResult], default: [] })
  battleHistory: TrophyBattleResult[];   // 战斗历史记录

  @Prop({ default: Date.now })
  lastUpdateTime: Date;                  // 最后更新时间
}

export const TrophyCopySchema = SchemaFactory.createForClass(TrophyCopy);

// 创建索引
TrophyCopySchema.index({ uid: 1 });
TrophyCopySchema.index({ 'allTrophyCopys.id': 1 });
TrophyCopySchema.index({ lastUpdateTimes: 1 });
TrophyCopySchema.index({ lastUpdateTime: 1 });
