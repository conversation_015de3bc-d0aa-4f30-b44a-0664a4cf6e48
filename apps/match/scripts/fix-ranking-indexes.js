/**
 * 修复排名系统索引冲突问题
 * 删除旧的rankType_1索引，重建正确的复合索引
 */

const { MongoClient } = require('mongodb');
require('dotenv').config();

async function fixRankingIndexes() {
  const mongoUrl = process.env.MONGODB_URI || '****************************************************************';
  const dbName = process.env.MONGODB_DATABASE || 'match_db';
  
  console.log('🔧 开始修复排名系统索引...');
  console.log(`连接到: ${mongoUrl}`);
  console.log(`数据库: ${dbName}`);

  const client = new MongoClient(mongoUrl);

  try {
    // 连接到MongoDB
    await client.connect();
    console.log('✅ MongoDB连接成功');

    const db = client.db(dbName);
    const collection = db.collection('global_rankings');

    // 1. 查看当前索引
    console.log('\n📋 当前索引列表:');
    const indexes = await collection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
      if (index.unique) {
        console.log(`   - 唯一索引: ${index.unique}`);
      }
    });

    // 2. 检查是否存在问题索引
    const problemIndex = indexes.find(index => index.name === 'rankType_1');
    if (problemIndex) {
      console.log('\n🚨 发现问题索引: rankType_1');
      console.log('   这个索引阻止了相同rankType的不同season记录');
      
      // 删除问题索引
      console.log('\n🗑️ 删除旧的rankType_1索引...');
      await collection.dropIndex('rankType_1');
      console.log('✅ 旧索引删除成功');
    } else {
      console.log('\n✅ 没有发现问题索引rankType_1');
    }

    // 3. 检查是否存在正确的复合索引
    const correctIndex = indexes.find(index => 
      index.name.includes('rankType_1_season_1') || 
      (JSON.stringify(index.key) === '{"rankType":1,"season":1}' && index.unique)
    );

    if (!correctIndex) {
      console.log('\n🔨 创建正确的复合唯一索引...');
      await collection.createIndex(
        { rankType: 1, season: 1 }, 
        { unique: true, name: 'rankType_1_season_1' }
      );
      console.log('✅ 复合索引创建成功');
    } else {
      console.log('\n✅ 正确的复合索引已存在');
    }

    // 4. 清理可能的重复数据
    console.log('\n🧹 检查并清理重复数据...');
    const duplicates = await collection.aggregate([
      {
        $group: {
          _id: { rankType: '$rankType', season: '$season' },
          count: { $sum: 1 },
          docs: { $push: '$_id' }
        }
      },
      {
        $match: { count: { $gt: 1 } }
      }
    ]).toArray();

    if (duplicates.length > 0) {
      console.log(`发现 ${duplicates.length} 组重复数据`);
      
      for (const duplicate of duplicates) {
        console.log(`处理重复: rankType=${duplicate._id.rankType}, season=${duplicate._id.season}`);
        
        // 保留第一个，删除其他的
        const docsToDelete = duplicate.docs.slice(1);
        if (docsToDelete.length > 0) {
          await collection.deleteMany({ _id: { $in: docsToDelete } });
          console.log(`  删除了 ${docsToDelete.length} 个重复记录`);
        }
      }
    } else {
      console.log('✅ 没有发现重复数据');
    }

    // 5. 验证修复结果
    console.log('\n🔍 验证修复结果...');
    const finalIndexes = await collection.indexes();
    console.log('最终索引列表:');
    finalIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
      if (index.unique) {
        console.log(`   - 唯一索引: ${index.unique}`);
      }
    });

    // 6. 测试插入数据
    console.log('\n🧪 测试数据插入...');
    const testData = [
      { rankType: 'fans', season: 1, rankings: [], lastUpdateTime: new Date() },
      { rankType: 'strength', season: 1, rankings: [], lastUpdateTime: new Date() }
    ];

    for (const data of testData) {
      try {
        await collection.replaceOne(
          { rankType: data.rankType, season: data.season },
          data,
          { upsert: true }
        );
        console.log(`✅ 测试插入成功: ${data.rankType}, season: ${data.season}`);
      } catch (error) {
        console.log(`❌ 测试插入失败: ${data.rankType}, season: ${data.season}`);
        console.log(`   错误: ${error.message}`);
      }
    }

    console.log('\n🎉 排名系统索引修复完成！');

  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error);
    throw error;
  } finally {
    await client.close();
    console.log('📝 MongoDB连接已关闭');
  }
}

// 执行修复
if (require.main === module) {
  fixRankingIndexes()
    .then(() => {
      console.log('\n✅ 索引修复成功完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 索引修复失败:', error);
      process.exit(1);
    });
}

module.exports = { fixRankingIndexes };
