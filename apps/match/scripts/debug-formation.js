const MicroserviceWebSocketClient = require('../../../scripts/common/websocket-client');

async function debugFormationData() {
  const client = new MicroserviceWebSocketClient();

  try {
    // 获取认证令牌
    const token = await client.getAuthToken('quicktest');
    console.log('✅ 认证令牌获取成功');

    // 连接WebSocket
    await client.connectWebSocket();
    console.log('✅ WebSocket连接成功');

    // 先获取球员数据，检查adaptability字段
    const heroResult = await client.sendMessage('hero.hero.getList', {
      characterId: 'char_1752804011322_36i2zagdx',
      serverId: 'server_001'
    });

    console.log('\n=== 球员数据详情 ===');
    if (heroResult.success && heroResult.data && heroResult.data.data) {
      const heroes = heroResult.data.data;
      console.log(`球员数量: ${heroes.length}`);
      if (heroes.length > 0) {
        console.log('第一个球员数据:');
        console.log(JSON.stringify(heroes[0], null, 2));
        console.log('\n球员适应性字段检查:');
        heroes.forEach((hero, index) => {
          console.log(`球员${index + 1}: ${hero.name} - adaptability存在: ${!!hero.adaptability}`);
          if (hero.adaptability) {
            console.log(`  GK适应性: ${hero.adaptability.GK}`);
          }
        });
      }
    }

    // 获取阵容数据
    const formationResult = await client.sendMessage('character.formation.getFormations', {
      characterId: 'char_1752804011322_36i2zagdx',
      serverId: 'server_001'
    });

    console.log('\n=== 阵容数据详情 ===');
    console.log(JSON.stringify(formationResult, null, 2));

    if (formationResult.success && formationResult.data && formationResult.data.data) {
      const formations = formationResult.data.data.teamFormations;
      if (formations && formations.length > 0) {
        const currentFormation = formations[0];
        console.log('\n=== 当前阵容的positionToHerosObject ===');
        console.log(JSON.stringify(currentFormation.positionToHerosObject, null, 2));
        
        // 检查每个位置的球员数据 - 严格基于old项目位置字段
        const positions = ['GK', 'DL', 'DC', 'DR', 'ML', 'MC', 'MR', 'WL', 'ST', 'WR', 'AM', 'DM'];
        console.log('\n=== 各位置球员分布 ===');
        positions.forEach(pos => {
          const players = currentFormation.positionToHerosObject[pos];
          console.log(`${pos}: ${Array.isArray(players) ? players.length : 'NOT_ARRAY'} - ${JSON.stringify(players)}`);
        });
      }
    }

  } catch (error) {
    console.error('❌ 调试失败:', error);
  } finally {
    client.disconnect();
  }
}

debugFormationData();
