# 方案2详细实现：基于元数据标记的API访问控制

## 📋 概述

本文档详细介绍方案2的三种具体实现方式，充分利用NestJS的原生功能来实现API访问控制。

---

## 🎯 方案2A：利用MessagePattern的extras参数

### 核心原理

基于您提供的 `@MessagePattern` 装饰器签名，利用 `extras` 参数传递访问控制信息。

```typescript
// MessagePattern装饰器支持的签名
export declare const MessagePattern: {
    <T = PatternMetadata | string>(metadata?: T, extras?: Record<string, any>): MethodDecorator;
    <T = PatternMetadata | string>(metadata?: T, transport?: Transport | symbol, extras?: Record<string, any>): MethodDecorator;
};
```

### 完整实现

#### 创建访问控制装饰器

```typescript
// libs/common/src/decorators/extras-access-control.decorator.ts
import { MessagePattern } from '@nestjs/microservices';

export enum ApiAccess {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  ADMIN = 'admin'
}

// 内部API注册表
const INTERNAL_APIS = new Set<string>();
const ADMIN_APIS = new Set<string>();

/**
 * 使用extras参数的安全MessagePattern
 */
export const SecureMessagePattern = (
  pattern: string,
  access: ApiAccess = ApiAccess.PUBLIC,
  transport?: any
) => {
  // 注册到相应的API列表
  switch (access) {
    case ApiAccess.INTERNAL:
      INTERNAL_APIS.add(pattern);
      console.log(`🔒 注册内部API: ${pattern}`);
      break;
    case ApiAccess.ADMIN:
      ADMIN_APIS.add(pattern);
      console.log(`👑 注册管理员API: ${pattern}`);
      break;
    case ApiAccess.PUBLIC:
      console.log(`🌍 注册公开API: ${pattern}`);
      break;
  }

  // 构建extras对象
  const extras = {
    access,
    timestamp: Date.now(),
    version: '1.0'
  };

  // 根据是否有transport参数选择合适的重载
  return transport
    ? MessagePattern(pattern, transport, extras)
    : MessagePattern(pattern, extras);
};

/**
 * 便捷的公开API装饰器
 */
export const PublicAPI = (pattern: string, transport?: any) =>
  SecureMessagePattern(pattern, ApiAccess.PUBLIC, transport);

/**
 * 便捷的内部API装饰器
 */
export const InternalAPI = (pattern: string, transport?: any) =>
  SecureMessagePattern(pattern, ApiAccess.INTERNAL, transport);

/**
 * 便捷的管理员API装饰器
 */
export const AdminAPI = (pattern: string, transport?: any) =>
  SecureMessagePattern(pattern, ApiAccess.ADMIN, transport);

/**
 * 检查函数
 */
export const isInternalApi = (pattern: string): boolean => INTERNAL_APIS.has(pattern);
export const isAdminApi = (pattern: string): boolean => ADMIN_APIS.has(pattern);
export const isPublicApi = (pattern: string): boolean => 
  !INTERNAL_APIS.has(pattern) && !ADMIN_APIS.has(pattern);

/**
 * 获取API列表
 */
export const getApisByAccess = () => ({
  public: Array.from(new Set([...getAllPatterns()].filter(isPublicApi))),
  internal: Array.from(INTERNAL_APIS),
  admin: Array.from(ADMIN_APIS)
});

// 辅助函数：获取所有已注册的API模式
function getAllPatterns(): string[] {
  return [...INTERNAL_APIS, ...ADMIN_APIS];
}
```

#### 增强的网关守卫

```typescript
// apps/gateway/src/guards/enhanced-api-access.guard.ts
import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { isInternalApi, isAdminApi } from '@common/decorators/extras-access-control.decorator';

@Injectable()
export class EnhancedApiAccessGuard implements CanActivate {
  private readonly logger = new Logger(EnhancedApiAccessGuard.name);

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToWs().getData();
    const pattern = `${request.service}.${request.action}`;
    const userId = request.userId;
    const userRoles = request.userRoles || [];

    // 检查管理员API
    if (isAdminApi(pattern)) {
      if (!this.hasAdminRole(userRoles)) {
        this.logger.warn(`👑 拦截管理员API调用: ${pattern}, 用户: ${userId}, 角色: ${userRoles.join(',')}`);
        throw new ForbiddenException(`管理员API ${pattern} 需要管理员权限`);
      }
      this.logger.log(`👑 允许管理员API调用: ${pattern}, 用户: ${userId}`);
      return true;
    }

    // 检查内部API
    if (isInternalApi(pattern)) {
      // 检查是否是服务间调用
      if (request.serviceAuth && this.validateServiceAuth(request.serviceAuth)) {
        this.logger.log(`🔧 允许服务间调用: ${pattern}, 服务: ${request.serviceAuth.serviceName}`);
        return true;
      }

      this.logger.warn(`🚨 拦截内部API调用: ${pattern}, 用户: ${userId}`);
      throw new ForbiddenException(`内部API ${pattern} 禁止客户端访问`);
    }

    // 公开API，允许访问
    this.logger.debug(`✅ 允许公开API调用: ${pattern}, 用户: ${userId}`);
    return true;
  }

  private hasAdminRole(roles: string[]): boolean {
    const adminRoles = ['admin', 'super_admin', 'system_admin'];
    return roles.some(role => adminRoles.includes(role));
  }

  private validateServiceAuth(serviceAuth: any): boolean {
    // 实现服务间认证逻辑
    // 这里可以验证JWT、HMAC签名等
    return serviceAuth && serviceAuth.serviceName && serviceAuth.signature;
  }
}
```

### 使用示例

```typescript
// apps/character/src/modules/character/character.controller.ts
import { Controller, Logger } from '@nestjs/common';
import { Payload } from '@nestjs/microservices';
import { 
  SecureMessagePattern, 
  PublicAPI, 
  InternalAPI, 
  AdminAPI, 
  ApiAccess 
} from '@common/decorators/extras-access-control.decorator';

@Controller()
export class CharacterController {
  private readonly logger = new Logger(CharacterController.name);

  // 方式1：使用SecureMessagePattern
  @SecureMessagePattern('character.getInfo', ApiAccess.PUBLIC)
  async getCharacterInfo(@Payload() payload: any) {
    const characterId = payload.serverContext?.characterId || payload.characterId;
    return this.characterService.getCharacterInfo(characterId);
  }

  // 方式2：使用便捷装饰器
  @PublicAPI('character.getList')
  async getCharacterList(@Payload() query: any) {
    return this.characterService.getCharacterList(query);
  }

  @PublicAPI('character.create')
  async createCharacter(@Payload() createDto: any) {
    return this.characterService.createCharacter(createDto);
  }

  // 内部API
  @InternalAPI('character.currency.add')
  async addCurrency(@Payload() payload: any) {
    const characterId = payload.serverContext?.characterId || payload.characterId;
    return this.characterService.addCurrency(characterId, payload.currencyDto);
  }

  @InternalAPI('character.currency.subtract')
  async subtractCurrency(@Payload() payload: any) {
    const characterId = payload.serverContext?.characterId || payload.characterId;
    return this.characterService.subtractCurrency(characterId, payload.currencyDto);
  }

  // 管理员API
  @AdminAPI('character.admin.resetAll')
  async adminResetAllCharacters(@Payload() payload: any) {
    this.logger.warn(`管理员重置所有角色: ${payload.userId}`);
    return this.characterService.adminResetAll(payload.reason);
  }

  @AdminAPI('character.admin.grantCurrency')
  async adminGrantCurrency(@Payload() payload: any) {
    this.logger.warn(`管理员发放货币: ${payload.userId} -> ${payload.targetCharacterId}`);
    return this.characterService.addCurrency(payload.targetCharacterId, payload.currencyDto);
  }
}
```

---

## 🏷️ 方案2B：独立元数据装饰器

### 核心原理

使用独立的装饰器来设置元数据，与 `@MessagePattern` 配合使用。

### 完整实现

#### 创建元数据装饰器

```typescript
// libs/common/src/decorators/metadata-access-control.decorator.ts
import { SetMetadata } from '@nestjs/common';

export const API_ACCESS_KEY = 'api-access';
export const API_CONFIG_KEY = 'api-config';

export enum ApiAccess {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  ADMIN = 'admin'
}

export interface ApiConfig {
  access: ApiAccess;
  roles?: string[];
  permissions?: string[];
  rateLimit?: {
    windowMs: number;
    max: number;
  };
  description?: string;
}

/**
 * API访问控制装饰器
 */
export const ApiAccessControl = (access: ApiAccess) => SetMetadata(API_ACCESS_KEY, access);

/**
 * 高级API配置装饰器
 */
export const ApiConfig = (config: ApiConfig) => SetMetadata(API_CONFIG_KEY, config);

/**
 * 便捷装饰器
 */
export const PublicApi = () => ApiAccessControl(ApiAccess.PUBLIC);
export const InternalApi = () => ApiAccessControl(ApiAccess.INTERNAL);
export const AdminApi = () => ApiAccessControl(ApiAccess.ADMIN);

/**
 * 组合装饰器：需要特定角色的API
 */
export const RequireRoles = (...roles: string[]) => 
  ApiConfig({ access: ApiAccess.PUBLIC, roles });

/**
 * 组合装饰器：需要特定权限的API
 */
export const RequirePermissions = (...permissions: string[]) => 
  ApiConfig({ access: ApiAccess.PUBLIC, permissions });

/**
 * 组合装饰器：有速率限制的API
 */
export const RateLimited = (windowMs: number, max: number) => 
  ApiConfig({ access: ApiAccess.PUBLIC, rateLimit: { windowMs, max } });
```

#### 基于反射的网关守卫

```typescript
// apps/gateway/src/guards/metadata-api-access.guard.ts
import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { 
  API_ACCESS_KEY, 
  API_CONFIG_KEY, 
  ApiAccess, 
  ApiConfig 
} from '@common/decorators/metadata-access-control.decorator';

@Injectable()
export class MetadataApiAccessGuard implements CanActivate {
  private readonly logger = new Logger(MetadataApiAccessGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToWs().getData();
    const pattern = `${request.service}.${request.action}`;
    
    // 注意：这里需要解决如何从WebSocket请求映射到具体的处理方法
    // 这是方案2B的主要挑战
    const handler = this.findHandlerByPattern(pattern);
    if (!handler) {
      this.logger.warn(`未找到处理器: ${pattern}`);
      return false;
    }

    // 获取API访问级别
    const access = this.reflector.get<ApiAccess>(API_ACCESS_KEY, handler);
    const config = this.reflector.get<ApiConfig>(API_CONFIG_KEY, handler);

    // 使用配置中的访问级别，如果没有则使用简单的访问级别
    const effectiveAccess = config?.access || access || ApiAccess.PUBLIC;

    return this.validateAccess(effectiveAccess, config, request, pattern);
  }

  private validateAccess(
    access: ApiAccess, 
    config: ApiConfig | undefined, 
    request: any, 
    pattern: string
  ): boolean {
    const userId = request.userId;
    const userRoles = request.userRoles || [];
    const userPermissions = request.userPermissions || [];

    switch (access) {
      case ApiAccess.ADMIN:
        if (!this.hasAdminRole(userRoles)) {
          this.logger.warn(`👑 拦截管理员API: ${pattern}, 用户: ${userId}`);
          throw new ForbiddenException('需要管理员权限');
        }
        break;

      case ApiAccess.INTERNAL:
        if (!this.isServiceCall(request)) {
          this.logger.warn(`🚨 拦截内部API: ${pattern}, 用户: ${userId}`);
          throw new ForbiddenException('内部API禁止客户端访问');
        }
        break;

      case ApiAccess.PUBLIC:
        // 检查角色要求
        if (config?.roles && !this.hasAnyRole(userRoles, config.roles)) {
          this.logger.warn(`🔐 角色验证失败: ${pattern}, 需要: ${config.roles.join(',')}, 拥有: ${userRoles.join(',')}`);
          throw new ForbiddenException(`需要角色: ${config.roles.join(', ')}`);
        }

        // 检查权限要求
        if (config?.permissions && !this.hasAnyPermission(userPermissions, config.permissions)) {
          this.logger.warn(`🔐 权限验证失败: ${pattern}, 需要: ${config.permissions.join(',')}`);
          throw new ForbiddenException(`需要权限: ${config.permissions.join(', ')}`);
        }
        break;
    }

    // 检查速率限制
    if (config?.rateLimit) {
      if (!this.checkRateLimit(userId, pattern, config.rateLimit)) {
        this.logger.warn(`⏱️ 速率限制: ${pattern}, 用户: ${userId}`);
        throw new ForbiddenException('请求过于频繁');
      }
    }

    return true;
  }

  private findHandlerByPattern(pattern: string): Function | null {
    // 这里需要实现从pattern到handler的映射
    // 这是方案2B的技术难点，需要维护一个映射表
    // 或者通过其他方式来解决
    
    // 临时实现：返回null表示无法找到
    // 实际项目中需要根据具体的路由机制来实现
    return null;
  }

  private hasAdminRole(roles: string[]): boolean {
    return roles.includes('admin') || roles.includes('super_admin');
  }

  private hasAnyRole(userRoles: string[], requiredRoles: string[]): boolean {
    return requiredRoles.some(role => userRoles.includes(role));
  }

  private hasAnyPermission(userPermissions: string[], requiredPermissions: string[]): boolean {
    return requiredPermissions.some(permission => userPermissions.includes(permission));
  }

  private isServiceCall(request: any): boolean {
    return request.serviceAuth && this.validateServiceAuth(request.serviceAuth);
  }

  private validateServiceAuth(serviceAuth: any): boolean {
    return serviceAuth.serviceName && serviceAuth.signature;
  }

  private checkRateLimit(userId: string, pattern: string, limit: { windowMs: number; max: number }): boolean {
    // 实现速率限制逻辑
    // 可以使用Redis或内存存储
    return true; // 临时返回true
  }
}
```

### 使用示例

```typescript
// apps/character/src/modules/character/character.controller.ts
import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { 
  PublicApi, 
  InternalApi, 
  AdminApi, 
  RequireRoles, 
  RequirePermissions,
  RateLimited,
  ApiConfig,
  ApiAccess
} from '@common/decorators/metadata-access-control.decorator';

@Controller()
export class CharacterController {

  // 公开API
  @MessagePattern('character.getInfo')
  @PublicApi()
  async getCharacterInfo(@Payload() payload: any) {
    return this.characterService.getCharacterInfo(payload.characterId);
  }

  // 需要特定角色的API
  @MessagePattern('character.vip.getRewards')
  @RequireRoles('vip', 'premium')
  async getVipRewards(@Payload() payload: any) {
    return this.characterService.getVipRewards(payload.characterId);
  }

  // 需要特定权限的API
  @MessagePattern('character.trade.create')
  @RequirePermissions('trade:create', 'market:access')
  async createTrade(@Payload() payload: any) {
    return this.characterService.createTrade(payload);
  }

  // 有速率限制的API
  @MessagePattern('character.message.send')
  @RateLimited(60000, 10) // 每分钟最多10次
  async sendMessage(@Payload() payload: any) {
    return this.characterService.sendMessage(payload);
  }

  // 复杂配置的API
  @MessagePattern('character.premium.upgrade')
  @ApiConfig({
    access: ApiAccess.PUBLIC,
    roles: ['user'],
    permissions: ['premium:upgrade'],
    rateLimit: { windowMs: 300000, max: 1 }, // 5分钟内只能调用1次
    description: '升级为高级用户'
  })
  async upgradeToPremium(@Payload() payload: any) {
    return this.characterService.upgradeToPremium(payload.characterId);
  }

  // 内部API
  @MessagePattern('character.currency.add')
  @InternalApi()
  async addCurrency(@Payload() payload: any) {
    return this.characterService.addCurrency(payload.characterId, payload.currencyDto);
  }

  // 管理员API
  @MessagePattern('character.admin.ban')
  @AdminApi()
  async banCharacter(@Payload() payload: any) {
    return this.characterService.banCharacter(payload.characterId, payload.reason);
  }
}
```

---

## 📊 方案2A vs 方案2B 对比

| 维度 | 方案2A (extras参数) | 方案2B (元数据装饰器) |
|------|-------------------|---------------------|
| **实现复杂度** | ⭐⭐⭐ | ⭐⭐ |
| **功能丰富度** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **性能影响** | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **调试难度** | ⭐⭐⭐⭐ | ⭐⭐ |
| **扩展性** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **框架兼容性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🎯 推荐使用场景

### 方案2A适用于：
- 需要简单快速实现的项目
- 对功能要求不复杂的场景
- 希望充分利用NestJS原生功能的团队

### 方案2B适用于：
- 需要复杂权限控制的企业级项目
- 需要细粒度访问控制的场景
- 有经验的NestJS开发团队

---

## 📝 总结

方案2提供了两种基于NestJS原生功能的实现方式：

1. **方案2A**：利用MessagePattern的extras参数，实现简单直接
2. **方案2B**：使用独立的元数据装饰器，功能更加丰富

两种方案都能有效解决API访问控制问题，选择哪种取决于项目的具体需求和团队的技术偏好。
