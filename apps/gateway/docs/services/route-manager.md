# 路由管理服务 (RouteManagerService)

路由管理服务是网关的核心组件，负责动态路由的管理、验证和热更新。

## 🎯 功能概述

### 核心功能
- **动态路由管理**: 运行时增删改查路由配置
- **路由验证**: 自动验证路由配置的正确性
- **冲突检测**: 检测和防止路由配置冲突
- **热更新**: 无需重启即可更新路由配置
- **版本管理**: 支持路由配置的版本控制
- **缓存集成**: Redis 缓存提高性能

## 📋 API 接口

### 基础操作

#### 获取所有路由
```typescript
getAllRoutes(): Route[]
```

#### 获取启用的路由
```typescript
getEnabledRoutes(): Route[]
```

#### 根据ID获取路由
```typescript
getRoute(routeId: string): Route | undefined
```

### 路由管理

#### 添加路由
```typescript
async addRoute(route: Route): Promise<void>
```

**示例**:
```typescript
const newRoute: Route = {
  id: 'user-profile',
  name: '用户资料',
  path: '/api/users/me',
  method: RequestMethod.GET,
  target: {
    service: 'user-service',
    path: '/users/profile',
    protocol: 'http',
    loadBalancer: 'round-robin'
  },
  config: {
    enabled: true,
    version: '1.0.0',
    auth: { required: true },
    rateLimit: { 
      enabled: true,
      windowMs: 60000,
      max: 100
    },
    cache: { 
      enabled: true,
      ttl: 300
    },
    proxy: {
      timeout: 5000,
      retries: 3,
      retryDelay: 1000
    },
    validation: { enabled: false },
    transform: {
      request: { enabled: false },
      response: { enabled: false }
    },
    monitoring: {
      enabled: true,
      metrics: true
    }
  },
  middleware: ['auth'],
  enabled: true,
  priority: 100,
  createdAt: new Date(),
  updatedAt: new Date()
};

await routeManager.addRoute(newRoute);
```

#### 更新路由
```typescript
async updateRoute(routeId: string, updates: Partial<Route>): Promise<void>
```

**示例**:
```typescript
// 更新路由配置
await routeManager.updateRoute('user-profile', {
  config: {
    ...existingConfig,
    rateLimit: {
      enabled: true,
      windowMs: 60000,
      max: 200  // 增加限流阈值
    }
  }
});
```

#### 删除路由
```typescript
async removeRoute(routeId: string): Promise<void>
```

### 验证和检测

#### 验证路由配置
```typescript
async validateRoute(route: Route): Promise<RouteValidationResult>
```

**返回结果**:
```typescript
interface RouteValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}
```

#### 检查路由冲突
```typescript
checkRouteConflicts(newRoute: Route, existingRoutes: Route[]): RouteConflict[]
```

## 🔧 使用示例

### 基础使用

```typescript
import { RouteManagerService } from './core/router/route-manager.service';

@Injectable()
export class MyService {
  constructor(
    private readonly routeManager: RouteManagerService
  ) {}

  async setupGameRoutes() {
    // 添加比赛相关路由
    await this.routeManager.addRoute({
      id: 'match-live',
      name: '实时比赛数据',
      path: '/api/matches/:id/live',
      method: RequestMethod.GET,
      target: {
        service: 'match-service',
        path: '/matches/:id/live',
        protocol: 'http',
        loadBalancer: 'round-robin'
      },
      config: {
        enabled: true,
        version: '1.0.0',
        auth: { required: true },
        rateLimit: { 
          enabled: true,
          windowMs: 60000,
          max: 100
        },
        cache: { 
          enabled: false,  // 实时数据不缓存
          ttl: 0
        },
        proxy: {
          timeout: 3000,
          retries: 2,
          retryDelay: 500
        },
        validation: { enabled: true },
        transform: {
          request: { enabled: false },
          response: { enabled: true }
        },
        monitoring: {
          enabled: true,
          metrics: true,
          tracing: true
        }
      },
      middleware: ['auth', 'rate-limit'],
      enabled: true,
      priority: 200,
      createdAt: new Date(),
      updatedAt: new Date()
    });
  }
}
```

### 事件监听

```typescript
import { OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class RouteEventHandler {
  @OnEvent('route.added')
  handleRouteAdded(route: Route) {
    console.log(`新路由已添加: ${route.id} - ${route.path}`);
  }

  @OnEvent('route.updated')
  handleRouteUpdated(route: Route) {
    console.log(`路由已更新: ${route.id}`);
  }

  @OnEvent('route.removed')
  handleRouteRemoved(route: Route) {
    console.log(`路由已删除: ${route.id}`);
  }
}
```

## ⚠️ 注意事项

### 路由配置要求
1. **路由ID**: 必须唯一，建议使用描述性名称
2. **路径格式**: 必须以 `/` 开头
3. **目标服务**: 必须指定有效的服务名和协议
4. **负载均衡**: 必须配置负载均衡策略

### 性能考虑
1. **缓存策略**: 路由配置会缓存到 Redis，减少查询开销
2. **批量操作**: 大量路由更新时建议分批处理
3. **事件处理**: 路由变更事件是异步的，不会阻塞主流程

### 错误处理
```typescript
try {
  await routeManager.addRoute(newRoute);
} catch (error) {
  if (error.message.includes('conflicts detected')) {
    // 处理路由冲突
    console.error('路由冲突:', error.message);
  } else if (error.message.includes('Invalid route')) {
    // 处理配置错误
    console.error('路由配置无效:', error.message);
  }
}
```

## 🔍 调试和监控

### 日志输出
路由管理服务会输出详细的日志信息：
```
[RouteManagerService] Loaded 15 routes
[RouteManagerService] Route added: user-profile - /api/users/me
[RouteManagerService] Route updated: match-live
[RouteManagerService] Route removed: deprecated-api
```

### 监控指标
- 路由总数
- 启用路由数
- 路由变更频率
- 验证失败次数
- 冲突检测次数

这个路由管理服务为网关提供了强大的动态路由能力，支持复杂的游戏场景和高并发访问需求。
