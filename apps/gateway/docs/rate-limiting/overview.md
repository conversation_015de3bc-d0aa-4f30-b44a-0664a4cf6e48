# Rate Limiting Overview

The Football Manager Gateway implements sophisticated rate limiting to protect against abuse, ensure fair resource usage, and maintain service quality. This document provides a comprehensive overview of the rate limiting system.

## What is Rate Limiting?

Rate limiting is a technique used to control the rate at which clients can make requests to an API. It helps:

- **Prevent abuse**: Protect against malicious attacks and spam
- **Ensure fairness**: <PERSON><PERSON>rant<PERSON> equal access to resources for all users
- **Maintain performance**: Prevent system overload and maintain response times
- **Control costs**: Manage resource consumption and operational costs

## Rate Limiting Strategies

The gateway supports multiple rate limiting algorithms, each with different characteristics:

### 1. Sliding Window

**How it works**: Maintains a rolling window of requests over time.

**Advantages**:
- Smooth rate limiting without bursts
- More accurate than fixed windows
- Better user experience

**Use cases**: General API rate limiting, user-facing endpoints

```typescript
const config = {
  strategy: 'sliding-window',
  windowMs: 60000,  // 1 minute
  max: 100,         // 100 requests per minute
};
```

### 2. Fixed Window

**How it works**: Divides time into fixed intervals and counts requests per interval.

**Advantages**:
- Simple to implement and understand
- Low memory usage
- Predictable reset times

**Use cases**: Simple rate limiting, batch processing APIs

```typescript
const config = {
  strategy: 'fixed-window',
  windowMs: 60000,  // 1 minute
  max: 100,         // 100 requests per minute
};
```

### 3. Token Bucket

**How it works**: Maintains a bucket of tokens that are consumed by requests and refilled at a constant rate.

**Advantages**:
- Allows controlled bursts
- Flexible rate control
- Good for variable workloads

**Use cases**: APIs with bursty traffic, file uploads

```typescript
const config = {
  strategy: 'token-bucket',
  capacity: 100,        // Bucket capacity
  refillRate: 10,       // Tokens per refill period
  refillPeriod: 1000,   // Refill every 1 second
};
```

### 4. Leaky Bucket

**How it works**: Requests are added to a bucket that "leaks" at a constant rate.

**Advantages**:
- Smooth output rate
- Handles bursts by queuing
- Predictable resource usage

**Use cases**: Streaming APIs, real-time data processing

```typescript
const config = {
  strategy: 'leaky-bucket',
  capacity: 100,        // Bucket capacity
  leakRate: 10,         // Requests per leak period
  leakPeriod: 1000,     // Leak every 1 second
};
```

## Multi-dimensional Rate Limiting

The gateway implements rate limiting across multiple dimensions:

### 1. User-based Limiting

Rate limits applied per authenticated user:

```typescript
// Generate user-specific key
const key = rateLimitService.generateUserKey(userId, endpoint);

// Different limits based on user level
const getUserLimit = (userLevel: number) => {
  const baseLimit = 100;
  const multiplier = Math.min(userLevel / 2 + 1, 5); // Max 5x
  return Math.floor(baseLimit * multiplier);
};
```

### 2. IP-based Limiting

Rate limits applied per client IP address:

```typescript
// Generate IP-specific key
const key = rateLimitService.generateIpKey(ipAddress, endpoint);

// Stricter limits for unauthenticated requests
const config = {
  windowMs: 60000,
  max: 50, // Lower limit for IP-based limiting
  strategy: 'sliding-window',
};
```

### 3. API Endpoint Limiting

Rate limits applied per API endpoint:

```typescript
// Generate endpoint-specific key
const key = rateLimitService.generateApiKey(endpoint);

// Different limits for different endpoints
const endpointLimits = {
  '/api/auth/login': { max: 5, windowMs: 300000 },    // 5 per 5 minutes
  '/api/users': { max: 100, windowMs: 60000 },        // 100 per minute
  '/api/matches': { max: 200, windowMs: 60000 },      // 200 per minute
};
```

### 4. Global Limiting

Overall system-wide rate limits:

```typescript
// Generate global key
const key = rateLimitService.generateGlobalKey();

// System-wide protection
const config = {
  windowMs: 60000,
  max: 10000, // 10,000 requests per minute globally
  strategy: 'sliding-window',
};
```

## Dynamic Rate Limiting

### User Level-based Limits

```typescript
const calculateUserLimit = (user: User) => {
  const baseLimits = {
    basic: 100,
    premium: 500,
    vip: 1000,
  };
  
  const userType = getUserType(user.level);
  const baseLimit = baseLimits[userType];
  
  // Apply additional multipliers
  const levelMultiplier = Math.min(user.level / 10 + 1, 3);
  const subscriptionMultiplier = user.subscription?.active ? 2 : 1;
  
  return Math.floor(baseLimit * levelMultiplier * subscriptionMultiplier);
};
```

### Time-based Adjustments

```typescript
const getTimeBasedLimit = (baseLimit: number) => {
  const hour = new Date().getHours();
  
  // Reduce limits during peak hours (18:00 - 22:00)
  if (hour >= 18 && hour <= 22) {
    return Math.floor(baseLimit * 0.8);
  }
  
  // Increase limits during off-peak hours (02:00 - 06:00)
  if (hour >= 2 && hour <= 6) {
    return Math.floor(baseLimit * 1.5);
  }
  
  return baseLimit;
};
```

## Rate Limit Headers

The gateway includes standard rate limit headers in responses:

```http
HTTP/1.1 200 OK
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995260
X-RateLimit-Reset-After: 45
```

When rate limit is exceeded:

```http
HTTP/1.1 429 Too Many Requests
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 1640995260
Retry-After: 45

{
  "statusCode": 429,
  "message": "Rate limit exceeded",
  "error": "Too Many Requests",
  "retryAfter": 45,
  "limit": 100,
  "remaining": 0,
  "resetTime": "2023-12-01T10:01:00.000Z"
}
```

## Configuration Examples

### Basic Configuration

```typescript
// apps/gateway/src/config/rate-limit.config.ts
export default () => ({
  rateLimit: {
    // Global defaults
    default: {
      windowMs: 60000,
      max: 100,
      strategy: 'sliding-window',
    },
    
    // Endpoint-specific limits
    endpoints: {
      '/api/auth/login': {
        windowMs: 300000, // 5 minutes
        max: 5,
        strategy: 'fixed-window',
      },
      '/api/users/register': {
        windowMs: 3600000, // 1 hour
        max: 3,
        strategy: 'fixed-window',
      },
      '/api/matches/live': {
        windowMs: 60000,
        max: 200,
        strategy: 'token-bucket',
        capacity: 200,
        refillRate: 50,
        refillPeriod: 15000,
      },
    },
    
    // User type limits
    userTypes: {
      basic: { multiplier: 1 },
      premium: { multiplier: 3 },
      vip: { multiplier: 5 },
    },
  },
});
```

### Advanced Configuration

```typescript
export default () => ({
  rateLimit: {
    // Redis configuration
    redis: {
      keyPrefix: 'rate_limit:',
      ttl: 3600, // 1 hour
    },
    
    // Skip rate limiting for certain conditions
    skip: {
      ips: ['127.0.0.1', '::1'], // Localhost
      userAgents: ['HealthCheck/1.0'],
      paths: ['/health', '/metrics'],
    },
    
    // Custom error responses
    errorResponse: {
      statusCode: 429,
      message: 'Too many requests, please try again later',
      includeHeaders: true,
    },
    
    // Monitoring
    monitoring: {
      enabled: true,
      logBlocked: true,
      alertThreshold: 1000, // Alert when 1000+ requests blocked per minute
    },
  },
});
```

## Integration with Other Systems

### Authentication Integration

```typescript
// Apply different limits based on authentication status
const getRateLimitConfig = (req: Request) => {
  if (req.user) {
    // Authenticated user - higher limits
    return {
      windowMs: 60000,
      max: calculateUserLimit(req.user),
      strategy: 'sliding-window',
    };
  } else {
    // Unauthenticated - lower limits
    return {
      windowMs: 60000,
      max: 20,
      strategy: 'fixed-window',
    };
  }
};
```

### Circuit Breaker Integration

```typescript
// Reduce rate limits when circuit breaker is open
const adjustForCircuitBreaker = (config: RateLimitConfig, serviceName: string) => {
  const circuitState = circuitBreakerService.getCircuitState(serviceName);
  
  if (circuitState === 'open') {
    return {
      ...config,
      max: Math.floor(config.max * 0.1), // Reduce to 10%
    };
  }
  
  return config;
};
```

## Monitoring and Alerting

### Metrics Collection

```typescript
// Rate limiting metrics
const metrics = {
  'rate_limit_requests_total': 'Total rate limit checks',
  'rate_limit_blocked_total': 'Total blocked requests',
  'rate_limit_errors_total': 'Total rate limit errors',
  'rate_limit_duration_seconds': 'Rate limit check duration',
};

// Record metrics
metricsService.recordRateLimitEvent('allowed', 'user', 'sliding-window');
metricsService.recordRateLimitEvent('blocked', 'ip', 'fixed-window');
```

### Alerting Rules

```yaml
# Prometheus alerting rules
groups:
  - name: rate_limiting
    rules:
      - alert: HighRateLimitBlocking
        expr: rate(rate_limit_blocked_total[5m]) > 100
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High rate of blocked requests"
          description: "Rate limiting is blocking {{ $value }} requests per second"
      
      - alert: RateLimitServiceDown
        expr: up{job="gateway"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Rate limit service is down"
```

## Best Practices

### 1. Choose the Right Strategy

- **Sliding Window**: Best for most use cases
- **Fixed Window**: Simple scenarios with predictable traffic
- **Token Bucket**: APIs with bursty traffic patterns
- **Leaky Bucket**: Streaming or real-time applications

### 2. Set Appropriate Limits

- Start conservative and adjust based on monitoring
- Consider user experience vs. system protection
- Different limits for different user types
- Account for legitimate use patterns

### 3. Provide Clear Feedback

- Include rate limit headers in responses
- Provide meaningful error messages
- Suggest retry times
- Document rate limits in API documentation

### 4. Monitor and Adjust

- Track rate limit effectiveness
- Monitor false positives
- Adjust limits based on usage patterns
- Set up alerting for unusual patterns

### 5. Handle Edge Cases

- Graceful degradation when Redis is unavailable
- Bypass rate limiting for health checks
- Consider time zone differences for global applications
- Handle clock skew in distributed systems

## Testing Rate Limits

### Unit Tests

```typescript
describe('RateLimitService', () => {
  it('should allow requests within limit', async () => {
    const config = { windowMs: 60000, max: 10, strategy: 'sliding-window' };
    const result = await rateLimitService.checkRateLimit('test-key', config);
    
    expect(result.allowed).toBe(true);
    expect(result.remaining).toBe(9);
  });
  
  it('should block requests exceeding limit', async () => {
    // ... test implementation
  });
});
```

### Load Testing

```bash
# Test rate limiting with Apache Bench
ab -n 1000 -c 10 -H "Authorization: Bearer token" http://localhost:3000/api/users

# Test with different user types
ab -n 500 -c 5 -H "X-User-Type: premium" http://localhost:3000/api/matches
```

This comprehensive rate limiting system ensures your Football Manager Gateway can handle high traffic while protecting against abuse and maintaining fair access for all users.
