# 微服务通信问题排查与修复指南

## 概述

本文档记录了在测试 `test-character-proxy.js` 脚本过程中遇到的关键问题及其修复方案。这些问题涉及微服务架构的核心组件，包括传输层配置、健康检查机制、WebSocket通信格式等。

## 问题分类与修复方案

### 1. 传输层配置不一致问题

#### 问题描述
- **现象**：微服务调用出现 `Connection closed` 错误
- **根本原因**：不同组件使用了不同的传输层配置（TCP vs Redis）
- **影响范围**：Gateway、ConnectionPoolService、MicroserviceClientService

#### 修复方案
```typescript
// 1. 统一Gateway传输层配置
// apps/gateway/src/main.ts
const microserviceOptions = {
  transport: Transport.REDIS,
  options: {
    host: process.env.REDIS_HOST || '***************',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || '123456',
    db: parseInt(process.env.REDIS_DB) || 0,
  },
};

// 2. 修复ConnectionPoolService传输配置
// libs/common/src/microservice-kit/client/connection-pool.service.ts
const client = ClientProxyFactory.create({
  transport: serviceConfig.transport,
  options: serviceConfig.options,
});
```

#### 关键要点
- 所有微服务组件必须使用统一的传输层配置
- 避免硬编码传输类型，从配置中动态获取
- 确保连接池服务能够访问微服务配置

### 2. 健康检查机制缺陷

#### 问题描述
- **现象**：服务实例注册后立即被移除，提示"没有可用的健康实例"
- **根本原因**：健康检查只检查超时时间，不执行实际的健康检查
- **影响范围**：ServerAwareRegistryService

#### 修复方案
```typescript
// libs/service-registry/src/server-aware-registry.service.ts
private async performHealthCheck(): Promise<void> {
  // 原有逻辑：只检查超时
  // if (timeSinceLastCheck > this.instanceTimeoutMs) { ... }
  
  // 新增逻辑：执行实际健康检查
  const isHealthy = await this.checkInstanceHealth(instance);
  instance.healthy = isHealthy;
  instance.lastHealthCheck = now;
}

private async checkInstanceHealth(instance: ServerAwareServiceInstance): Promise<boolean> {
  try {
    const healthUrl = `http://${instance.host}:${instance.port}/health`;
    const response = await fetch(healthUrl, {
      method: 'GET',
      signal: controller.signal,
      headers: { 'Content-Type': 'application/json' },
    });
    
    if (response.ok) {
      const healthData = await response.json();
      return healthData.status === 'ok';
    }
    return false;
  } catch (error) {
    return false;
  }
}
```

#### 关键要点
- 健康检查必须实际调用服务的 `/health` 端点
- 需要处理网络超时和错误情况
- 更新实例的健康状态和最后检查时间

### 3. WebSocket微服务调用格式错误

#### 问题描述
- **现象**：WebSocket微服务调用超时
- **根本原因**：使用了错误的命令格式
- **影响范围**：Gateway WebSocket微服务调用

#### 修复方案
```javascript
// 错误格式
const command = 'character.getInfo';

// 正确格式（遵循微服务通信规范）
const command = 'character.character.getInfo'; // service.module.method
```

#### 关键要点
- WebSocket微服务调用格式：`服务名.模块名.方法名`
- 必须严格遵循微服务通信规范
- HTTP调用和WebSocket调用使用不同的格式

### 4. IPv4/IPv6连接问题

#### 问题描述
- **现象**：连接尝试IPv6地址失败
- **根本原因**：系统默认尝试IPv6连接
- **影响范围**：所有网络连接

#### 修复方案
```typescript
// 使用明确的IPv4地址
host: '127.0.0.1' // 而不是 'localhost'
```

## 测试验证结果

### 最终测试通过率：100% (8/8)

1. ✅ 账号注册
2. ✅ 账号登录  
3. ✅ 获取角色列表
4. ✅ 创建角色
5. ✅ 角色登录
6. ✅ 角色Token验证
7. ✅ WebSocket角色连接
8. ✅ WebSocket微服务调用

### 关键性能指标
- 健康检查响应时间：9ms
- 微服务调用成功率：100%
- 连接建立成功率：100%

## 最佳实践建议

### 1. 传输层配置管理
- 使用统一的配置管理机制
- 避免在代码中硬编码传输类型
- 确保所有组件使用相同的传输配置

### 2. 健康检查设计
- 实现真正的健康检查，而不仅仅是超时检查
- 设置合理的超时时间（建议5秒）
- 记录健康检查的响应时间用于性能监控

### 3. 微服务通信规范
- 严格遵循命名约定：`service.module.method`
- 区分HTTP调用和WebSocket调用的格式差异
- 建立统一的错误处理机制

### 4. 网络连接优化
- 优先使用IPv4地址避免连接问题
- 实现连接池复用提高性能
- 设置合理的连接超时时间

## 故障排查流程

### 1. 连接问题排查
1. 检查传输层配置是否一致
2. 验证网络连接性（ping、telnet）
3. 检查防火墙和端口配置
4. 查看服务日志中的连接错误

### 2. 健康检查问题排查
1. 手动访问 `/health` 端点验证服务状态
2. 检查健康检查间隔和超时配置
3. 查看服务注册中心的实例状态
4. 验证Redis中的实例数据

### 3. 微服务调用问题排查
1. 验证命令格式是否正确
2. 检查服务是否正确注册
3. 验证负载均衡器选择的实例
4. 查看微服务调用的详细日志

## 监控和告警建议

### 1. 关键指标监控
- 微服务调用成功率
- 健康检查响应时间
- 服务实例可用性
- 连接池使用率

### 2. 告警规则
- 微服务调用成功率 < 95%
- 健康检查响应时间 > 1000ms
- 可用实例数量 = 0
- 连接池使用率 > 80%

## 总结

通过系统性地解决传输层配置、健康检查机制、WebSocket通信格式等关键问题，我们成功构建了一个稳定可靠的区服感知微服务架构。这些修复不仅解决了当前的测试问题，更为整个系统的长期稳定运行奠定了坚实基础。
