import { IsString, IsEnum, IsOptional, IsBoolean, IsNumber, IsArray, IsDateString, ValidateNested, IsIn } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { MessagePriority } from '../interfaces/global-message.interface';

/**
 * 操作按钮DTO
 */
export class ActionButtonDto {
  @ApiProperty({ description: '按钮文本' })
  @IsString()
  text: string;

  @ApiProperty({ description: '按钮操作' })
  @IsString()
  action: string;

  @ApiPropertyOptional({ description: '跳转URL' })
  @IsOptional()
  @IsString()
  url?: string;
}

/**
 * 创建公告DTO
 * 严格按照设计文档定义的SystemAnnouncement接口
 */
export class CreateAnnouncementDto {
  @ApiProperty({ description: '公告标题' })
  @IsString()
  title: string;

  @ApiProperty({ description: '公告内容' })
  @IsString()
  content: string;

  @ApiProperty({ 
    description: '公告类型',
    enum: ['system', 'maintenance', 'update', 'event']
  })
  @IsEnum(['system', 'maintenance', 'update', 'event'])
  announcementType: 'system' | 'maintenance' | 'update' | 'event';

  @ApiProperty({ 
    description: '显示类型',
    enum: ['popup', 'banner', 'notification']
  })
  @IsEnum(['popup', 'banner', 'notification'])
  displayType: 'popup' | 'banner' | 'notification';

  @ApiProperty({ 
    description: '消息优先级',
    enum: MessagePriority,
    default: MessagePriority.MEDIUM
  })
  @IsOptional()
  @IsEnum(MessagePriority)
  priority?: MessagePriority;

  @ApiProperty({ 
    description: '目标区服',
    oneOf: [
      { type: 'string', enum: ['all'] },
      { type: 'array', items: { type: 'string' } }
    ]
  })
  @IsIn(['all'])
  targetServers: string[] | 'all';

  @ApiProperty({ description: '公告开始时间' })
  @IsOptional()
  @IsDateString()
  startTime?: Date;

  @ApiProperty({ description: '公告结束时间' })
  @IsDateString()
  endTime: Date;

  @ApiPropertyOptional({ description: '发布时间，默认为立即发布' })
  @IsOptional()
  @IsDateString()
  publishAt?: Date;

  @ApiPropertyOptional({ description: '过期时间，默认与结束时间相同' })
  @IsOptional()
  @IsDateString()
  expireAt?: Date;

  @ApiPropertyOptional({ description: '是否自动关闭', default: false })
  @IsOptional()
  @IsBoolean()
  autoClose?: boolean;

  @ApiPropertyOptional({ description: '自动关闭延迟（秒）' })
  @IsOptional()
  @IsNumber()
  closeDelay?: number;

  @ApiPropertyOptional({ description: '操作按钮' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ActionButtonDto)
  actionButton?: ActionButtonDto;
}

/**
 * 更新公告DTO
 * 所有字段都是可选的，只更新提供的字段
 */
export class UpdateAnnouncementDto {
  @ApiPropertyOptional({ description: '公告标题' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: '公告内容' })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiPropertyOptional({ 
    description: '公告类型',
    enum: ['system', 'maintenance', 'update', 'event']
  })
  @IsOptional()
  @IsEnum(['system', 'maintenance', 'update', 'event'])
  announcementType?: 'system' | 'maintenance' | 'update' | 'event';

  @ApiPropertyOptional({ 
    description: '显示类型',
    enum: ['popup', 'banner', 'notification']
  })
  @IsOptional()
  @IsEnum(['popup', 'banner', 'notification'])
  displayType?: 'popup' | 'banner' | 'notification';

  @ApiPropertyOptional({ 
    description: '消息优先级',
    enum: MessagePriority
  })
  @IsOptional()
  @IsEnum(MessagePriority)
  priority?: MessagePriority;

  @ApiPropertyOptional({ 
    description: '目标区服',
    oneOf: [
      { type: 'string', enum: ['all'] },
      { type: 'array', items: { type: 'string' } }
    ]
  })
  @IsOptional()
  targetServers?: string[] | 'all';

  @ApiPropertyOptional({ description: '公告开始时间' })
  @IsOptional()
  @IsDateString()
  startTime?: Date;

  @ApiPropertyOptional({ description: '公告结束时间' })
  @IsOptional()
  @IsDateString()
  endTime?: Date;

  @ApiPropertyOptional({ description: '发布时间' })
  @IsOptional()
  @IsDateString()
  publishAt?: Date;

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsDateString()
  expireAt?: Date;

  @ApiPropertyOptional({ description: '是否自动关闭' })
  @IsOptional()
  @IsBoolean()
  autoClose?: boolean;

  @ApiPropertyOptional({ description: '自动关闭延迟（秒）' })
  @IsOptional()
  @IsNumber()
  closeDelay?: number;

  @ApiPropertyOptional({ description: '操作按钮' })
  @IsOptional()
  @ValidateNested()
  @Type(() => ActionButtonDto)
  actionButton?: ActionButtonDto;
}

/**
 * 公告查询DTO
 */
export class QueryAnnouncementDto {
  @ApiPropertyOptional({ 
    description: '公告类型',
    enum: ['system', 'maintenance', 'update', 'event']
  })
  @IsOptional()
  @IsEnum(['system', 'maintenance', 'update', 'event'])
  announcementType?: 'system' | 'maintenance' | 'update' | 'event';

  @ApiPropertyOptional({ 
    description: '显示类型',
    enum: ['popup', 'banner', 'notification']
  })
  @IsOptional()
  @IsEnum(['popup', 'banner', 'notification'])
  displayType?: 'popup' | 'banner' | 'notification';

  @ApiPropertyOptional({ description: '是否只查询活跃的公告' })
  @IsOptional()
  @IsBoolean()
  active?: boolean;

  @ApiPropertyOptional({ description: '查询数量限制', default: 50 })
  @IsOptional()
  @IsNumber()
  limit?: number;
}

/**
 * 公告响应DTO
 */
export class AnnouncementResponseDto {
  @ApiProperty({ description: '公告ID' })
  id: string;

  @ApiProperty({ description: '公告标题' })
  title: string;

  @ApiProperty({ description: '公告内容' })
  content: string;

  @ApiProperty({ description: '公告类型' })
  announcementType: 'system' | 'maintenance' | 'update' | 'event';

  @ApiProperty({ description: '显示类型' })
  displayType: 'popup' | 'banner' | 'notification';

  @ApiProperty({ description: '消息优先级' })
  priority: MessagePriority;

  @ApiProperty({ description: '目标区服' })
  targetServers: string[] | 'all';

  @ApiProperty({ description: '公告开始时间' })
  startTime: Date;

  @ApiProperty({ description: '公告结束时间' })
  endTime: Date;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '发布时间' })
  publishAt: Date;

  @ApiPropertyOptional({ description: '过期时间' })
  expireAt?: Date;

  @ApiPropertyOptional({ description: '是否自动关闭' })
  autoClose?: boolean;

  @ApiPropertyOptional({ description: '自动关闭延迟（秒）' })
  closeDelay?: number;

  @ApiPropertyOptional({ description: '操作按钮' })
  actionButton?: ActionButtonDto;

  @ApiProperty({ description: '是否活跃' })
  isActive: boolean;

  @ApiProperty({ description: '状态' })
  status: string;
}
