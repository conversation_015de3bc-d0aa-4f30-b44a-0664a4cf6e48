import { ApiProperty } from '@nestjs/swagger';

/**
 * 区服基础信息DTO
 */
export class ServerInfoDto {
  @ApiProperty({ description: '区服ID', example: 'server_001' })
  id: string;

  @ApiProperty({ description: '区服名称', example: '新手村' })
  name: string;

  @ApiProperty({ description: '区服状态', enum: ['active', 'maintenance', 'closed'] })
  status: 'active' | 'maintenance' | 'closed';

  @ApiProperty({ description: '开服时间', example: '2024-01-01T00:00:00.000Z' })
  openTime: Date;

  @ApiProperty({ description: '最大玩家数', example: 10000 })
  maxPlayers: number;

  @ApiProperty({ description: '当前玩家数', example: 5000 })
  currentPlayers?: number;

  @ApiProperty({ description: '区域', example: 'asia' })
  region?: string;

  @ApiProperty({ description: '标签', example: ['新手友好', '活跃'] })
  tags?: string[];

  @ApiProperty({ description: '描述', example: '适合新手玩家的区服' })
  description?: string;
}

/**
 * 增强区服信息DTO
 */
export class EnhancedServerInfoDto extends ServerInfoDto {
  @ApiProperty({ description: '是否有角色', example: true })
  hasCharacter: boolean;

  @ApiProperty({ description: '最后游戏时间', example: '2024-01-15T10:30:00.000Z' })
  lastPlayTime?: Date;

  @ApiProperty({ description: '总游戏时长(秒)', example: 86400 })
  totalPlayTime: number;

  @ApiProperty({ description: '角色数量', example: 2 })
  characterCount: number;

  @ApiProperty({ description: '推荐分数', example: 85 })
  recommendationScore: number;

  @ApiProperty({ description: '负载比率', example: 0.5 })
  loadRatio: number;

  @ApiProperty({ description: '是否为新服', example: false })
  isNew: boolean;
}

/**
 * 推荐信息DTO
 */
export class RecommendationInfoDto {
  @ApiProperty({ description: '主推荐区服ID', example: 'server_001' })
  primary: string;

  @ApiProperty({ description: '推荐原因', example: ['您在此区服有角色', '人数适中'] })
  reasons: string[];
}

/**
 * 详细推荐信息DTO
 */
export class DetailedRecommendationDto {
  @ApiProperty({ description: '主推荐' })
  primary: any;

  @ApiProperty({ description: '备选推荐' })
  alternatives: any[];

  @ApiProperty({ description: '推荐解释', example: '我们为您推荐此区服，因为...' })
  explanation: string;

  @ApiProperty({ description: '推荐时间', example: '2024-01-15T10:30:00.000Z' })
  timestamp: Date;
}

/**
 * 区服列表数据DTO
 */
export class ServerListDataDto {
  @ApiProperty({ description: '区服列表', type: [EnhancedServerInfoDto] })
  servers: EnhancedServerInfoDto[];

  @ApiProperty({ description: '最后游戏的区服ID', example: 'server_001' })
  lastServerId?: string;

  @ApiProperty({ description: '总区服数量', example: 5 })
  totalServers: number;

  @ApiProperty({ description: '数据时间戳', example: '2024-01-15T10:30:00.000Z' })
  timestamp: Date;

  @ApiProperty({ description: '基础推荐信息' })
  recommendations?: RecommendationInfoDto;

  @ApiProperty({ description: '详细推荐信息' })
  detailedRecommendations?: DetailedRecommendationDto;
}

/**
 * 区服列表响应DTO
 */
export class ServerListResponseDto {
  @ApiProperty({ description: '请求是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '获取区服列表成功' })
  message: string;

  @ApiProperty({ description: '区服列表数据', type: ServerListDataDto })
  data: ServerListDataDto;
}
