import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';

// 核心服务
import { RedisService } from '@common/redis';
import { MICROSERVICE_NAMES } from '@shared/constants';

// 类型定义
interface ServerStatus {
  serverId: string;
  status: 'online' | 'offline' | 'maintenance';
  currentPlayers: number;
  maxPlayers: number;
  cpuUsage: number;
  memoryUsage: number;
  responseTime: number;
  lastUpdate: Date;
  version?: string;
  uptime?: number;
}

interface ServerHealthCheck {
  serverId: string;
  isHealthy: boolean;
  checks: {
    database: boolean;
    redis: boolean;
    api: boolean;
    websocket: boolean;
  };
  lastCheck: Date;
  errorCount: number;
}

/**
 * 区服状态服务
 * 负责区服状态监控、健康检查、性能指标收集等功能
 * 
 * 核心职责：
 * 1. 实时状态监控：定期检查区服的运行状态
 * 2. 健康检查：多维度健康状态评估
 * 3. 性能指标：收集和分析区服性能数据
 * 4. 告警机制：异常状态的及时通知
 * 5. 历史数据：状态变化的历史记录和趋势分析
 */
@Injectable()
export class ServerStatusService {
  private readonly logger = new Logger(ServerStatusService.name);
  private readonly statusCache = new Map<string, ServerStatus>();
  private readonly healthCache = new Map<string, ServerHealthCheck>();

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * 获取区服状态
   */
  async getServerStatus(serverId: string): Promise<ServerStatus | null> {
    try {
      // 1. 先从内存缓存获取
      const cached = this.statusCache.get(serverId);
      if (cached && this.isCacheValid(cached.lastUpdate, 30)) { // 30秒内有效
        return cached;
      }

      // 2. 从Redis获取
      const redisKey = `gateway:server:${serverId}:status`;
      const redisData = await this.redisService.get(redisKey, 'global');
      if (redisData) {
        const status = JSON.parse(redisData as string);
        this.statusCache.set(serverId, status);
        return status;
      }

      // 3. 实时检查
      const status = await this.checkServerStatus(serverId);
      if (status) {
        // 更新缓存
        this.statusCache.set(serverId, status);
        await this.redisService.set(redisKey, JSON.stringify(status), 60, 'global');
      }

      return status;

    } catch (error) {
      this.logger.error(`获取区服状态失败: ${serverId}`, error);
      return null;
    }
  }

  /**
   * 获取所有区服状态
   */
  async getAllServerStatuses(): Promise<Map<string, ServerStatus>> {
    const statusMap = new Map<string, ServerStatus>();

    try {
      // 获取所有区服ID
      const serverIds = await this.getAllServerIds();
      
      // 并发获取状态
      const statusPromises = serverIds.map(async (serverId) => {
        const status = await this.getServerStatus(serverId);
        return { serverId, status };
      });

      const results = await Promise.allSettled(statusPromises);
      
      results.forEach((result) => {
        if (result.status === 'fulfilled' && result.value.status) {
          statusMap.set(result.value.serverId, result.value.status);
        }
      });

    } catch (error) {
      this.logger.error('获取所有区服状态失败', error);
    }

    return statusMap;
  }

  /**
   * 检查区服健康状态
   */
  async checkServerHealth(serverId: string): Promise<ServerHealthCheck> {
    this.logger.debug(`检查区服健康状态: ${serverId}`);

    try {
      const checks = {
        database: await this.checkDatabaseHealth(serverId),
        redis: await this.checkRedisHealth(serverId),
        api: await this.checkApiHealth(serverId),
        websocket: await this.checkWebSocketHealth(serverId),
      };

      const isHealthy = Object.values(checks).every(check => check);
      const errorCount = Object.values(checks).filter(check => !check).length;

      const healthCheck: ServerHealthCheck = {
        serverId,
        isHealthy,
        checks,
        lastCheck: new Date(),
        errorCount,
      };

      // 缓存健康检查结果
      this.healthCache.set(serverId, healthCheck);
      
      // 存储到Redis
      const redisKey = `gateway:server:${serverId}:health`;
      await this.redisService.set(redisKey, JSON.stringify(healthCheck), 300, 'global');

      return healthCheck;

    } catch (error) {
      this.logger.error(`区服健康检查失败: ${serverId}`, error);
      
      return {
        serverId,
        isHealthy: false,
        checks: {
          database: false,
          redis: false,
          api: false,
          websocket: false,
        },
        lastCheck: new Date(),
        errorCount: 4,
      };
    }
  }

  /**
   * 获取区服性能指标
   */
  async getServerMetrics(serverId: string): Promise<any> {
    try {
      const metricsKey = `gateway:server:${serverId}:metrics`;
      const cached = await this.redisService.get(metricsKey, 'global');
      
      if (cached) {
        return JSON.parse(cached as string);
      }

      // 收集性能指标
      const metrics = await this.collectServerMetrics(serverId);
      
      // 缓存5分钟
      await this.redisService.set(metricsKey, JSON.stringify(metrics), 300, 'global');
      
      return metrics;

    } catch (error) {
      this.logger.error(`获取区服性能指标失败: ${serverId}`, error);
      return null;
    }
  }

  /**
   * 定时状态检查任务
   * 每分钟检查一次所有区服状态
   */
  @Cron('0 * * * * *') // 每分钟执行
  async scheduledStatusCheck(): Promise<void> {
    this.logger.debug('开始定时状态检查');

    try {
      const serverIds = await this.getAllServerIds();
      
      // 分批处理，避免同时检查太多区服
      const batchSize = 5;
      for (let i = 0; i < serverIds.length; i += batchSize) {
        const batch = serverIds.slice(i, i + batchSize);
        
        const checkPromises = batch.map(serverId => 
          this.checkServerStatus(serverId).catch(error => {
            this.logger.warn(`定时检查区服状态失败: ${serverId}`, error);
            return null;
          })
        );

        await Promise.allSettled(checkPromises);
        
        // 批次间稍作延迟
        if (i + batchSize < serverIds.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      this.logger.debug('定时状态检查完成');

    } catch (error) {
      this.logger.error('定时状态检查失败', error);
    }
  }

  /**
   * 定时健康检查任务
   * 每5分钟进行一次全面健康检查
   */
  @Cron('0 */5 * * * *') // 每5分钟执行
  async scheduledHealthCheck(): Promise<void> {
    this.logger.debug('开始定时健康检查');

    try {
      const serverIds = await this.getAllServerIds();
      
      for (const serverId of serverIds) {
        try {
          await this.checkServerHealth(serverId);
        } catch (error) {
          this.logger.warn(`定时健康检查失败: ${serverId}`, error);
        }
      }

      this.logger.debug('定时健康检查完成');

    } catch (error) {
      this.logger.error('定时健康检查失败', error);
    }
  }

  /**
   * 私有方法：检查区服状态
   */
  private async checkServerStatus(serverId: string): Promise<ServerStatus | null> {
    try {
      // 这里应该调用具体的区服健康检查接口
      // 暂时使用模拟数据
      const status: ServerStatus = {
        serverId,
        status: 'online',
        currentPlayers: Math.floor(Math.random() * 8000),
        maxPlayers: 10000,
        cpuUsage: Math.random() * 100,
        memoryUsage: Math.random() * 100,
        responseTime: Math.random() * 100 + 50,
        lastUpdate: new Date(),
        version: '1.0.0',
        uptime: Math.floor(Math.random() * 86400),
      };

      return status;

    } catch (error) {
      this.logger.error(`检查区服状态失败: ${serverId}`, error);
      return null;
    }
  }

  /**
   * 私有方法：检查数据库健康状态
   */
  private async checkDatabaseHealth(serverId: string): Promise<boolean> {
    try {
      // 这里应该检查对应区服的数据库连接
      // 暂时返回随机结果
      return Math.random() > 0.1; // 90%概率健康
    } catch (error) {
      return false;
    }
  }

  /**
   * 私有方法：检查Redis健康状态
   */
  private async checkRedisHealth(serverId: string): Promise<boolean> {
    try {
      // 检查Redis连接
      const testKey = `health_check:${serverId}:${Date.now()}`;
      await this.redisService.set(testKey, 'test', 10, 'global');
      const result = await this.redisService.get(testKey, 'global');
      await this.redisService.del(testKey, 'global');
      
      return result === 'test';
    } catch (error) {
      return false;
    }
  }

  /**
   * 私有方法：检查API健康状态
   */
  private async checkApiHealth(serverId: string): Promise<boolean> {
    try {
      // 这里应该调用区服的健康检查API
      // 暂时返回随机结果
      return Math.random() > 0.05; // 95%概率健康
    } catch (error) {
      return false;
    }
  }

  /**
   * 私有方法：检查WebSocket健康状态
   */
  private async checkWebSocketHealth(serverId: string): Promise<boolean> {
    try {
      // 这里应该检查WebSocket连接状态
      // 暂时返回随机结果
      return Math.random() > 0.05; // 95%概率健康
    } catch (error) {
      return false;
    }
  }

  /**
   * 私有方法：收集区服性能指标
   */
  private async collectServerMetrics(serverId: string): Promise<any> {
    return {
      serverId,
      timestamp: new Date(),
      performance: {
        avgResponseTime: Math.random() * 100 + 50,
        requestsPerSecond: Math.random() * 1000 + 100,
        errorRate: Math.random() * 0.05,
        throughput: Math.random() * 1000 + 500,
      },
      resources: {
        cpuUsage: Math.random() * 100,
        memoryUsage: Math.random() * 100,
        diskUsage: Math.random() * 100,
        networkIO: Math.random() * 1000,
      },
      connections: {
        activeConnections: Math.floor(Math.random() * 5000),
        websocketConnections: Math.floor(Math.random() * 3000),
        databaseConnections: Math.floor(Math.random() * 100),
      },
    };
  }

  /**
   * 私有方法：获取所有区服ID
   */
  private async getAllServerIds(): Promise<string[]> {
    try {
      const serverConfigs = this.configService.get('gateway.servers') || [];
      return serverConfigs.map(config => config.id);
    } catch (error) {
      this.logger.error('获取区服ID列表失败', error);
      return ['server_001', 'server_002']; // 默认区服
    }
  }

  /**
   * 私有方法：检查缓存是否有效
   */
  private isCacheValid(lastUpdate: Date, maxAgeSeconds: number): boolean {
    const ageSeconds = (Date.now() - lastUpdate.getTime()) / 1000;
    return ageSeconds < maxAgeSeconds;
  }
}
