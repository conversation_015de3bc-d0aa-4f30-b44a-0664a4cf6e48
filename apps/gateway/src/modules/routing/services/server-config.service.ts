import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * 统一区服配置服务
 * 
 * 负责管理Gateway中的区服配置，提供统一的区服ID获取和验证功能
 * 解决硬编码SERVER_ID的问题，实现配置驱动的区服管理
 */
@Injectable()
export class ServerConfigService {
  private readonly logger = new Logger(ServerConfigService.name);
  private readonly servers: Array<{
    id: string;
    name: string;
    status: 'active' | 'maintenance' | 'closed';
    openTime: string;
    maxPlayers: number;
    region: string;
    tags: string[];
    description: string;
    endpoint: string;
  }>;

  constructor(private readonly configService: ConfigService) {
    // 🔧 修复：从配置中加载区服信息，避免硬编码
    this.servers = this.configService.get('gateway.servers', []);
    this.logger.log(`📋 已加载 ${this.servers.length} 个区服配置`);
  }

  /**
   * 获取默认区服ID
   * 优先级：配置文件 > 第一个活跃区服 > 环境变量 > 硬编码兜底
   */
  getDefaultServerId(): string {
    // 1. 从配置中获取默认区服ID
    const configuredDefault = this.configService.get<string>('gateway.defaultServerId');
    if (configuredDefault && this.isValidServerId(configuredDefault)) {
      this.logger.debug(`✅ 使用配置的默认区服: ${configuredDefault}`);
      return configuredDefault;
    }

    // 2. 选择第一个活跃的区服
    const activeServer = this.servers.find(server => server.status === 'active');
    if (activeServer) {
      this.logger.debug(`✅ 使用第一个活跃区服: ${activeServer.id}`);
      return activeServer.id;
    }

    // 3. 从环境变量获取（兼容性）
    const envServerId = this.configService.get<string>('SERVER_ID');
    if (envServerId && this.isValidServerId(envServerId)) {
      this.logger.debug(`✅ 使用环境变量区服: ${envServerId}`);
      return envServerId;
    }

    // 4. 硬编码兜底（仅用于开发环境）
    const fallbackServerId = 'server_001';
    this.logger.warn(`⚠️ 使用兜底区服ID: ${fallbackServerId}，建议配置正确的区服信息`);
    return fallbackServerId;
  }

  /**
   * 验证区服ID是否有效
   */
  isValidServerId(serverId: string): boolean {
    return this.servers.some(server => server.id === serverId);
  }

  /**
   * 获取区服信息
   */
  getServerInfo(serverId: string) {
    return this.servers.find(server => server.id === serverId);
  }

  /**
   * 获取所有活跃区服
   */
  getActiveServers() {
    return this.servers.filter(server => server.status === 'active');
  }

  /**
   * 智能选择区服ID
   * 根据上下文信息智能选择最合适的区服
   */
  selectServerId(context?: {
    requestedServerId?: string;
    userId?: string;
    region?: string;
    preferredTags?: string[];
  }): string {
    // 1. 如果明确指定了区服ID且有效，直接使用
    if (context?.requestedServerId && this.isValidServerId(context.requestedServerId)) {
      const serverInfo = this.getServerInfo(context.requestedServerId);
      if (serverInfo?.status === 'active') {
        this.logger.debug(`✅ 使用指定区服: ${context.requestedServerId}`);
        return context.requestedServerId;
      } else {
        this.logger.warn(`⚠️ 指定区服不可用: ${context.requestedServerId}, 状态: ${serverInfo?.status}`);
      }
    }

    // 2. 基于用户偏好选择区服（未来扩展）
    if (context?.userId) {
      // TODO: 从用户服务获取用户的区服偏好
      // const userPreference = await this.getUserServerPreference(context.userId);
    }

    // 3. 基于地理位置选择区服
    if (context?.region) {
      const regionServer = this.servers.find(
        server => server.status === 'active' && server.region === context.region
      );
      if (regionServer) {
        this.logger.debug(`✅ 基于地区选择区服: ${regionServer.id} (${context.region})`);
        return regionServer.id;
      }
    }

    // 4. 基于标签选择区服
    if (context?.preferredTags?.length) {
      const tagMatchServer = this.servers.find(server => 
        server.status === 'active' && 
        context.preferredTags.some(tag => server.tags.includes(tag))
      );
      if (tagMatchServer) {
        this.logger.debug(`✅ 基于标签选择区服: ${tagMatchServer.id}`);
        return tagMatchServer.id;
      }
    }

    // 5. 负载均衡选择（选择人数最少的活跃区服）
    const activeServers = this.getActiveServers();
    if (activeServers.length > 1) {
      // TODO: 实现基于实时负载的区服选择
      // 目前简单返回第一个活跃区服
      const selectedServer = activeServers[0];
      this.logger.debug(`✅ 负载均衡选择区服: ${selectedServer.id}`);
      return selectedServer.id;
    }

    // 6. 回退到默认区服
    return this.getDefaultServerId();
  }

  /**
   * 获取区服状态摘要
   */
  getServerStatusSummary() {
    const summary = {
      total: this.servers.length,
      active: this.servers.filter(s => s.status === 'active').length,
      maintenance: this.servers.filter(s => s.status === 'maintenance').length,
      closed: this.servers.filter(s => s.status === 'closed').length,
      defaultServerId: this.getDefaultServerId(),
    };

    this.logger.debug(`📊 区服状态摘要:`, summary);
    return summary;
  }
}
