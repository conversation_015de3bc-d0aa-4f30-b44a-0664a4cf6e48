import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  register, 
  Counter, 
  Histogram, 
  Gauge, 
  collectDefaultMetrics,
  Registry 
} from 'prom-client';

export interface GatewayMetrics {
  // 请求指标
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  
  // 响应时间指标
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  
  // 连接指标
  activeConnections: number;
  totalConnections: number;
  websocketConnections: number;
  
  // 错误指标
  errorRate: number;
  timeoutCount: number;
  circuitBreakerTrips: number;
  
  // 资源指标
  memoryUsage: number;
  cpuUsage: number;
  
  // 业务指标
  authenticatedUsers: number;
  rateLimitHits: number;
  cacheHitRate: number;
  
  timestamp: Date;
}

@Injectable()
export class MetricsService {
  private readonly logger = new Logger(MetricsService.name);
  private readonly registry: Registry;
  
  // 请求指标
  private requestCounter: Counter<string>;
  private requestDuration: Histogram<string>;
  private activeConnectionsGauge: Gauge<string>;

  // 错误指标
  private errorCounter: Counter<string>;
  private timeoutCounter: Counter<string>;
  private circuitBreakerCounter: Counter<string>;

  // 认证指标
  private authCounter: Counter<string>;
  private rateLimitCounter: Counter<string>;

  // 缓存指标
  private cacheCounter: Counter<string>;

  // WebSocket 指标
  private websocketGauge: Gauge<string>;
  private websocketMessageCounter: Counter<string>;

  // 系统路由指标
  private systemRouteSkipCounter: Counter<string>;

  // 业务指标
  private businessMetricsGauge: Gauge<string>;

  constructor(private readonly configService: ConfigService) {
    this.registry = new Registry();
    
    // 启用默认指标收集
    collectDefaultMetrics({ register: this.registry });
    
    // 初始化自定义指标
    this.initializeMetrics();
  }

  async initialize(): Promise<void> {
    this.logger.log('Metrics service initialized');
    
    // 启动指标收集任务
    this.startMetricsCollection();
  }

  /**
   * 记录 HTTP 请求
   */
  recordHttpRequest(
    method: string,
    path: string,
    statusCode: number,
    duration: number,
    userId?: string,
  ): void {
    const labels = {
      method,
      path: this.normalizePath(path),
      status_code: statusCode.toString(),
      user_type: userId ? 'authenticated' : 'anonymous',
    };
    
    this.requestCounter.inc(labels);
    this.requestDuration.observe(labels, duration / 1000); // 转换为秒
    
    if (statusCode >= 400) {
      this.errorCounter.inc({
        method,
        path: this.normalizePath(path),
        status_code: statusCode.toString(),
        error_type: statusCode >= 500 ? 'server_error' : 'client_error',
        user_type: userId ? 'authenticated' : 'anonymous',
      });
    }
  }

  /**
   * 记录 WebSocket 连接
   */
  recordWebSocketConnection(action: 'connect' | 'disconnect', userId?: string): void {
    const labels = {
      action,
      user_type: userId ? 'authenticated' : 'anonymous',
    };
    
    if (action === 'connect') {
      this.websocketGauge.inc(labels);
    } else {
      this.websocketGauge.dec(labels);
    }
  }

  /**
   * 记录 WebSocket 消息
   */
  recordWebSocketMessage(event: string, direction: 'inbound' | 'outbound'): void {
    this.websocketMessageCounter.inc({
      event,
      direction,
    });
  }

  /**
   * 记录认证事件
   */
  recordAuthEvent(
    event: 'login' | 'logout' | 'token_refresh' | 'auth_failure',
    method: 'jwt' | 'session' | 'apikey' | 'oauth',
  ): void {
    this.authCounter.inc({
      event,
      method,
    });
  }

  /**
   * 记录限流事件
   */
  recordRateLimitEvent(
    action: 'allowed' | 'blocked',
    type: 'user' | 'ip' | 'api' | 'global',
    strategy: string,
  ): void {
    this.rateLimitCounter.inc({
      action,
      type,
      strategy,
    });
  }

  /**
   * 记录缓存事件
   */
  recordCacheEvent(
    action: 'hit' | 'miss' | 'set' | 'delete',
    repository: string,
  ): void {
    this.cacheCounter.inc({
      action,
      repository,
    });
  }

  /**
   * 记录超时事件
   */
  recordTimeout(service: string, operation: string): void {
    this.timeoutCounter.inc({
      service,
      operation,
    });
  }

  /**
   * 记录熔断器事件
   */
  recordCircuitBreakerEvent(
    service: string,
    state: 'open' | 'closed' | 'half_open',
  ): void {
    this.circuitBreakerCounter.inc({
      service,
      state,
    });
  }

  /**
   * 更新活跃连接数
   */
  updateActiveConnections(count: number): void {
    this.activeConnectionsGauge.set(count);
  }

  /**
   * 更新业务指标
   */
  updateBusinessMetrics(metrics: Partial<GatewayMetrics>): void {
    if (metrics.authenticatedUsers !== undefined) {
      this.businessMetricsGauge.set(
        { metric: 'authenticated_users' },
        metrics.authenticatedUsers,
      );
    }
    
    if (metrics.cacheHitRate !== undefined) {
      this.businessMetricsGauge.set(
        { metric: 'cache_hit_rate' },
        metrics.cacheHitRate,
      );
    }
    
    if (metrics.memoryUsage !== undefined) {
      this.businessMetricsGauge.set(
        { metric: 'memory_usage_mb' },
        metrics.memoryUsage,
      );
    }
    
    if (metrics.cpuUsage !== undefined) {
      this.businessMetricsGauge.set(
        { metric: 'cpu_usage_percent' },
        metrics.cpuUsage,
      );
    }
  }

  /**
   * 获取所有指标
   */
  async getMetrics(): Promise<string> {
    return this.registry.metrics();
  }

  /**
   * 获取网关统计信息
   */
  async getGatewayStats(): Promise<GatewayMetrics> {
    const metrics = await this.registry.getMetricsAsJSON();
    
    // 从 Prometheus 指标中提取统计信息
    const stats: GatewayMetrics = {
      totalRequests: this.getMetricValue(metrics, 'gateway_http_requests_total'),
      successfulRequests: this.getMetricValue(metrics, 'gateway_http_requests_total', { status_code: '2xx' }),
      failedRequests: this.getMetricValue(metrics, 'gateway_http_requests_total', { status_code: '4xx,5xx' }),
      
      averageResponseTime: this.getMetricValue(metrics, 'gateway_http_request_duration_seconds_sum') / 
                          this.getMetricValue(metrics, 'gateway_http_request_duration_seconds_count'),
      p95ResponseTime: this.getMetricValue(metrics, 'gateway_http_request_duration_seconds', { quantile: '0.95' }),
      p99ResponseTime: this.getMetricValue(metrics, 'gateway_http_request_duration_seconds', { quantile: '0.99' }),
      
      activeConnections: this.getMetricValue(metrics, 'gateway_active_connections'),
      totalConnections: this.getMetricValue(metrics, 'gateway_total_connections'),
      websocketConnections: this.getMetricValue(metrics, 'gateway_websocket_connections'),
      
      errorRate: this.calculateErrorRate(metrics),
      timeoutCount: this.getMetricValue(metrics, 'gateway_timeouts_total'),
      circuitBreakerTrips: this.getMetricValue(metrics, 'gateway_circuit_breaker_total'),
      
      memoryUsage: this.getMetricValue(metrics, 'gateway_business_metrics', { metric: 'memory_usage_mb' }),
      cpuUsage: this.getMetricValue(metrics, 'gateway_business_metrics', { metric: 'cpu_usage_percent' }),
      
      authenticatedUsers: this.getMetricValue(metrics, 'gateway_business_metrics', { metric: 'authenticated_users' }),
      rateLimitHits: this.getMetricValue(metrics, 'gateway_rate_limit_total', { action: 'blocked' }),
      cacheHitRate: this.getMetricValue(metrics, 'gateway_business_metrics', { metric: 'cache_hit_rate' }),
      
      timestamp: new Date(),
    };
    
    return stats;
  }

  /**
   * 重置指标
   */
  resetMetrics(): void {
    this.registry.resetMetrics();
    this.logger.log('Metrics reset');
  }

  /**
   * 记录系统路由跳过
   */
  recordSystemRouteSkip(path: string, routeType: 'prefix' | 'static' | 'direct' | 'fallback'): void {
    this.systemRouteSkipCounter.inc({
      path: this.normalizePath(path),
      route_type: routeType,
    });
  }

  // ==================== 私有方法 ====================

  private initializeMetrics(): void {
    // HTTP 请求指标
    this.requestCounter = new Counter({
      name: 'gateway_http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'path', 'status_code', 'user_type'],
      registers: [this.registry],
    });

    this.requestDuration = new Histogram({
      name: 'gateway_http_request_duration_seconds',
      help: 'HTTP request duration in seconds',
      labelNames: ['method', 'path', 'status_code', 'user_type'],
      buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1, 5, 10],
      registers: [this.registry],
    });

    this.activeConnectionsGauge = new Gauge({
      name: 'gateway_active_connections',
      help: 'Number of active connections',
      registers: [this.registry],
    });

    // 错误指标
    this.errorCounter = new Counter({
      name: 'gateway_errors_total',
      help: 'Total number of errors',
      labelNames: ['method', 'path', 'status_code', 'error_type', 'user_type'],
      registers: [this.registry],
    });

    this.timeoutCounter = new Counter({
      name: 'gateway_timeouts_total',
      help: 'Total number of timeouts',
      labelNames: ['service', 'operation'],
      registers: [this.registry],
    });

    this.circuitBreakerCounter = new Counter({
      name: 'gateway_circuit_breaker_total',
      help: 'Total number of circuit breaker events',
      labelNames: ['service', 'state'],
      registers: [this.registry],
    });

    // 认证指标
    this.authCounter = new Counter({
      name: 'gateway_auth_events_total',
      help: 'Total number of authentication events',
      labelNames: ['event', 'method'],
      registers: [this.registry],
    });

    this.rateLimitCounter = new Counter({
      name: 'gateway_rate_limit_total',
      help: 'Total number of rate limit events',
      labelNames: ['action', 'type', 'strategy'],
      registers: [this.registry],
    });

    // 缓存指标
    this.cacheCounter = new Counter({
      name: 'gateway_cache_operations_total',
      help: 'Total number of cache operations',
      labelNames: ['action', 'repository'],
      registers: [this.registry],
    });

    // WebSocket 指标
    this.websocketGauge = new Gauge({
      name: 'gateway_websocket_connections',
      help: 'Number of WebSocket connections',
      labelNames: ['action', 'user_type'],
      registers: [this.registry],
    });

    this.websocketMessageCounter = new Counter({
      name: 'gateway_websocket_messages_total',
      help: 'Total number of WebSocket messages',
      labelNames: ['event', 'direction'],
      registers: [this.registry],
    });

    // 业务指标
    this.businessMetricsGauge = new Gauge({
      name: 'gateway_business_metrics',
      help: 'Business metrics',
      labelNames: ['metric'],
      registers: [this.registry],
    });

    // 系统路由跳过指标
    this.systemRouteSkipCounter = new Counter({
      name: 'gateway_system_route_skips_total',
      help: 'Total number of system route skips in proxy processing',
      labelNames: ['path', 'route_type'],
      registers: [this.registry],
    });
  }

  private startMetricsCollection(): void {
    // 每30秒收集一次系统指标
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);
  }

  private async collectSystemMetrics(): Promise<void> {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      this.updateBusinessMetrics({
        memoryUsage: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        cpuUsage: Math.round((cpuUsage.user + cpuUsage.system) / 1000000), // 百分比
      });
    } catch (error) {
      this.logger.error('Failed to collect system metrics:', error);
    }
  }

  private normalizePath(path: string): string {
    // 将动态路径参数标准化
    return path
      .replace(/\/\d+/g, '/:id')
      .replace(/\/[a-f0-9-]{36}/g, '/:uuid')
      .replace(/\/[a-zA-Z0-9_-]+/g, '/:param');
  }

  private getMetricValue(metrics: any[], name: string, labels?: Record<string, string>): number {
    const metric = metrics.find(m => m.name === name);
    if (!metric) return 0;
    
    if (!labels) {
      return metric.values?.[0]?.value || 0;
    }
    
    const value = metric.values?.find((v: any) => {
      return Object.entries(labels).every(([key, value]) => v.labels[key] === value);
    });
    
    return value?.value || 0;
  }

  private calculateErrorRate(metrics: any[]): number {
    const totalRequests = this.getMetricValue(metrics, 'gateway_http_requests_total');
    const errorRequests = this.getMetricValue(metrics, 'gateway_errors_total');

    if (totalRequests === 0) return 0;
    return Math.round((errorRequests / totalRequests) * 100 * 100) / 100; // 保留两位小数
  }

  /**
   * 获取 Prometheus 注册表
   */
  getRegister(): Registry {
    return this.registry;
  }

  /**
   * 获取总请求数
   */
  async getTotalRequests(): Promise<number> {
    const metrics = await this.registry.getMetricsAsJSON();
    return this.getMetricValue(metrics, 'gateway_http_requests_total');
  }

  /**
   * 获取平均响应时间
   */
  async getAverageResponseTime(): Promise<number> {
    const metrics = await this.registry.getMetricsAsJSON();
    const totalTime = this.getMetricValue(metrics, 'gateway_http_request_duration_seconds', { quantile: 'sum' });
    const totalRequests = this.getMetricValue(metrics, 'gateway_http_requests_total');

    if (totalRequests === 0) return 0;
    return Math.round((totalTime / totalRequests) * 1000); // 转换为毫秒
  }

  /**
   * 获取错误率
   */
  async getErrorRate(): Promise<number> {
    const metrics = await this.registry.getMetricsAsJSON();
    return this.calculateErrorRate(metrics);
  }

  /**
   * 获取活跃连接数
   */
  async getActiveConnections(): Promise<number> {
    // 从内存中获取当前活跃连接数
    const metric = await this.activeConnectionsGauge.get();
    return metric.values[0]?.value || 0;
  }

  /**
   * 获取系统指标
   */
  getSystemMetrics(): { memoryUsage: number; cpuUsage: number } {
    const memoryUsage = process.memoryUsage();
    return {
      memoryUsage: memoryUsage.heapUsed / memoryUsage.heapTotal,
      cpuUsage: 0, // 需要实现CPU监控
    };
  }

  /**
   * 记录代理请求开始
   */
  recordProxyRequest(method: string, path: string): void {
    this.requestCounter.inc({
      method,
      path: this.normalizePath(path),
      status_code: 'pending',
      user_type: 'unknown',
    });
  }

  /**
   * 记录代理响应
   */
  recordProxyResponse(method: string, path: string, statusCode: number, duration: number): void {
    // 记录响应时间
    this.requestDuration.observe(
      {
        method,
        path: this.normalizePath(path),
        status_code: statusCode.toString(),
        user_type: 'unknown',
      },
      duration / 1000
    );

    // 记录请求计数
    this.requestCounter.inc({
      method,
      path: this.normalizePath(path),
      status_code: statusCode.toString(),
      user_type: 'unknown',
    });
  }

  /**
   * 记录代理错误
   */
  recordProxyError(method: string, path: string, statusCode: number, duration: number): void {
    // 记录错误响应时间
    this.requestDuration.observe(
      {
        method,
        path: this.normalizePath(path),
        status_code: statusCode.toString(),
        user_type: 'unknown',
      },
      duration / 1000
    );

    // 记录错误计数
    this.errorCounter.inc({
      method,
      path: this.normalizePath(path),
      status_code: statusCode.toString(),
      error_type: statusCode >= 500 ? 'server_error' : 'client_error',
      user_type: 'unknown',
    });
  }

}
