# 双Token验证机制详解

## 🎯 架构设计

新的`ws-auth.guard.ts`实现了完整的双Token验证机制，根据Token的scope字段自动选择对应的验证服务：

- **账号Token** (`scope: 'account'`) → 调用 `AuthService.validateToken()`
- **角色Token** (`scope: 'character'`) → 调用 `CharacterAuthService.validateCharacterToken()`

## 🔄 验证流程

### 步骤1：Token解析
```typescript
// 解析Token获取scope信息（不验证签名）
const decoded = this.jwtService.decode(token);
console.log(decoded.scope); // 'account' 或 'character'
```

### 步骤2：选择验证服务
```typescript
switch (decoded.scope) {
  case 'account':
    // 调用AuthService验证账号Token
    return await this.validateAccountTokenViaService(token);
  case 'character':
    // 调用CharacterAuthService验证角色Token
    return await this.validateCharacterTokenViaService(token);
}
```

### 步骤3：微服务验证
```typescript
// 账号Token验证
const result = await this.authService.validateToken(token);
// 内部调用: auth.verifyToken

// 角色Token验证
const result = await this.characterAuthService.validateCharacterToken(token);
// 内部调用: character-auth.verifyToken
```

### 步骤4：上下文注入
```typescript
// 账号Token - 只注入用户信息
client.data.user = result.user;
client.data.user.tokenScope = 'account';

// 角色Token - 注入用户信息 + 角色上下文
client.data.user = result.user;
client.data.user.tokenScope = 'character';
client.data.character = {
  characterId: result.character.characterId,
  serverId: result.character.serverId,
};
```

## 🎮 使用示例

### 账号级WebSocket事件
```typescript
@SubscribeMessage('auth.getServerList')
@AccountToken()  // 要求账号Token
@UseGuards(WsAuthGuard)
async getServerList(@ConnectedSocket() client: Socket) {
  // client.data.user 包含用户基础信息
  // client.data.character 不存在
  const userId = client.data.user.id;
  return await this.getAvailableServers(userId);
}
```

### 角色级WebSocket事件
```typescript
@SubscribeMessage('match.startMatch')
@CharacterToken()  // 要求角色Token
@UseGuards(WsAuthGuard)
async startMatch(
  @ConnectedSocket() client: Socket,
  @MessageBody() data: { matchId: string }
) {
  // client.data.user 包含用户信息
  // client.data.character 包含角色上下文
  const { characterId, serverId } = client.data.character;
  
  // 确保比赛属于当前角色和区服
  await this.validateMatchOwnership(data.matchId, characterId, serverId);
  await this.startMatch(data.matchId);
}
```

## 🔐 安全验证

### Token作用域验证
```typescript
// 如果方法要求character Token，但客户端发送account Token
@CharacterToken()
async someMethod() {} 

// 客户端发送: { scope: 'account', ... }
// 结果: WsException('Token作用域不匹配，期望: character, 实际: account')
```

### 角色上下文验证
```typescript
// 角色Token必须包含角色上下文
if (!result.character?.characterId || !result.character?.serverId) {
  throw new WsException('Character token must contain character and server information');
}
```

## 📊 验证结果格式

### 账号Token验证结果
```typescript
{
  user: {
    id: "user123",
    username: "coach_zhang",
    email: "<EMAIL>",
    roles: ["player"],
    permissions: ["game:play"],
    tokenScope: "account"
  }
  // 无character字段
}
```

### 角色Token验证结果
```typescript
{
  user: {
    id: "user123",
    username: "coach_zhang", 
    email: "<EMAIL>",
    roles: ["player"],
    permissions: ["game:play"],
    tokenScope: "character"
  },
  character: {
    characterId: "char_456",
    serverId: "server_001"
  }
}
```

## 🎯 架构优势

### 1. **职责分离**
- `AuthService` 专注账号级认证
- `CharacterAuthService` 专注角色级认证
- `WsAuthGuard` 只负责路由和验证

### 2. **统一接口**
- 两种Token使用相同的守卫
- 相同的装饰器语法
- 一致的错误处理

### 3. **微服务架构**
- 所有验证逻辑在Auth服务中
- Gateway只做代理和权限检查
- 支持独立的密钥管理

### 4. **类型安全**
- 明确的Token作用域验证
- 角色上下文的强制检查
- 完整的错误处理机制

这种设计既保持了架构的清晰性，又实现了功能的完整性，完美支持了分区分服游戏的双Token需求。
