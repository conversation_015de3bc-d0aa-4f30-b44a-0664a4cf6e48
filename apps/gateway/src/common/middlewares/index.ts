/**
 * Gateway中间件统一导出
 *
 * 提供网关中间件组件，包括日志、安全等功能
 * 这些都是纯函数式中间件，不需要模块包装
 *
 * 命名规范：使用复数形式 middlewares/ 与其他common子目录保持一致
 * (constants/, decorators/, filters/, interceptors/, interfaces/, utils/)
 */

// 日志中间件
export * from './logging.middleware';

// 安全中间件
export * from './security.middleware';

// 便捷的重新导出（最常用的中间件）
export {
  LoggingMiddleware,
  RequestWithTiming,
} from './logging.middleware';

export {
  SecurityMiddleware,
} from './security.middleware';
