# 简化的认证服务 Dockerfile - 使用预构建代码

FROM node:18-alpine AS development

# 安装运行时依赖
RUN apk add --no-cache dumb-init curl

# 设置工作目录
WORKDIR /app

# 复制package文件和锁文件
COPY package*.json package-lock.json ./

# 只安装生产依赖
RUN npm install --omit=dev && npm cache clean --force

# 复制预构建的代码
COPY dist/ ./dist/

# 设置环境变量
ENV NODE_ENV=development
ENV PORT=3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# 暴露端口
EXPOSE 3000

# 启动命令
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/apps/auth/main.js"]

# 第三阶段：生产阶段
FROM node:18-alpine AS production

# 安装运行时依赖
RUN apk add --no-cache \
    dumb-init \
    curl \
    tzdata

# 创建非 root 用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S auth -u 1001 -G nodejs

# 设置工作目录
WORKDIR /app

# 创建必要的目录
RUN mkdir -p logs && \
    chown -R auth:nodejs logs

# 复制构建产物
COPY --from=builder --chown=auth:nodejs /app/dist ./dist
COPY --from=builder --chown=auth:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=auth:nodejs /app/package*.json ./

# 复制配置文件
COPY --chown=auth:nodejs apps/auth/.env.example ./.env

# 设置环境变量
ENV NODE_ENV=production
ENV TZ=UTC
ENV PORT=3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/api/health || exit 1

# 暴露端口
EXPOSE 3001

# 切换到非 root 用户
USER auth

# 使用 dumb-init 作为 PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动命令
CMD ["node", "dist/apps/auth/main.js"]

# 元数据标签
LABEL maintainer="Football Manager Team" \
      version="1.0.0" \
      description="足球经理认证服务" \
      service="auth-service"
