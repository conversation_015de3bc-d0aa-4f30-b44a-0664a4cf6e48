# Auth服务架构图解

## 📊 当前架构 vs 优化架构对比

### 🔴 **当前架构问题图**

```mermaid
graph TB
    subgraph "当前混乱架构"
        A[AppModule] --> B[AuthModule]
        A --> C[UsersModule] 
        A --> D[RolesModule]
        A --> E[PermissionsModule]
        A --> F[SessionModule]
        A --> G[SecurityModule]
        A --> H[CoreModule]
        A --> I[CharacterAuthModule] 
        A --> J[UserHistoryModule]
        A --> K[AdminModule]
        A --> L[HealthModule]
        
        %% 复杂依赖关系
        B --> C
        B --> F
        B --> G
        B --> I
        B --> J
        
        I --> C
        I --> J
        J --> C
        
        C --> H
        F --> H
        G --> H
        H --> G
        G --> F
        
        %% 标记问题
        I -.->|❌ 越权功能| CHAR[Character Service]
        J -.->|❌ 越权功能| PROF[Profile Service]
        
        style I fill:#ffcccc
        style J fill:#ffcccc
        style H fill:#ffffcc
    end
```

### 🟢 **优化后清洁架构**

```mermaid
graph TB
    subgraph "优化后清洁架构"
        APP[AppModule] --> AUTH[AuthenticationModule]
        APP --> AUTHZ[AuthorizationModule]
        APP --> USER[UserManagementModule]
        APP --> SESS[SessionManagementModule]
        APP --> SEC[SecurityModule]
        APP --> ADMIN[AdminModule]
        APP --> INFRA[InfrastructureModule]
        APP --> SHARED[SharedModule]
        
        %% 单向依赖
        AUTH --> USER
        AUTH --> SESS
        AUTH --> SEC
        AUTH --> SHARED
        
        AUTHZ --> SHARED
        USER --> SHARED
        SESS --> SHARED
        SEC --> SHARED
        ADMIN --> SHARED
        
        %% 基础设施依赖
        INFRA --> SHARED
        
        style AUTH fill:#ccffcc
        style AUTHZ fill:#ccffcc
        style USER fill:#ccffcc
        style SESS fill:#ccffcc
        style SEC fill:#ccffcc
        style ADMIN fill:#ccffcc
        style SHARED fill:#e6f3ff
        style INFRA fill:#f0f0f0
    end
```

## 🏗️ 功能模块详细架构

### **Authentication模块内部结构**

```mermaid
graph LR
    subgraph "AuthenticationModule"
        AC[AuthController] --> AS[AuthService]
        MC[MfaController] --> MS[MfaService]
        
        AS --> JS[JwtService]
        AS --> PS[PasswordService]
        AS --> MS
        
        AS --> JWT[JwtStrategy]
        AS --> LOCAL[LocalStrategy]
        
        subgraph "Dependencies"
            UM[UserManagementModule]
            SM[SessionManagementModule]
            SEC[SecurityModule]
        end
        
        AS -.-> UM
        AS -.-> SM
        AS -.-> SEC
    end
```

### **Authorization模块内部结构**

```mermaid
graph LR
    subgraph "AuthorizationModule"
        RC[RolesController] --> RS[RolesService]
        PC[PermissionsController] --> PS[PermissionsService]
        
        RS --> RR[RoleRepository]
        PS --> PR[PermissionRepository]
        
        RG[RolesGuard] --> RS
        PG[PermissionsGuard] --> PS
        
        subgraph "Entities"
            RE[Role Entity]
            PE[Permission Entity]
        end
        
        RR --> RE
        PR --> PE
    end
```

### **Security模块内部结构**

```mermaid
graph LR
    subgraph "SecurityModule"
        subgraph "Services"
            SS[SecurityService]
            CS[CryptoService]
            AS[AuditService]
        end
        
        subgraph "Guards"
            JG[JwtAuthGuard]
            TG[ThrottlerGuard]
        end
        
        subgraph "Interceptors"
            SI[SecurityInterceptor]
            PI[PerformanceInterceptor]
        end
        
        subgraph "Pipes & Filters"
            VP[ValidationPipe]
            EF[ExceptionFilter]
        end
        
        SS --> CS
        SS --> AS
        JG --> SS
        SI --> AS
    end
```

## 📁 目录结构对比

### 🔴 **当前目录结构**

```
apps/auth/src/
├── app/                    # 应用层 (混乱)
├── common/                 # 通用组件 (分散)
├── config/                 # 配置 (分散在5个文件)
├── core/                   # 核心层 (职责不清)
├── domain/                 # 领域层 (包含越权功能)
│   ├── auth/              ✅
│   ├── character-auth/    ❌ 越权
│   ├── permissions/       ✅
│   ├── roles/            ✅
│   ├── user-history/     ❌ 越权
│   └── users/            ✅
├── infrastructure/        # 基础设施 (重复定义)
└── main.ts
```

### 🟢 **优化后目录结构**

```
apps/auth/src/
├── modules/               # 功能模块 (清晰职责)
│   ├── authentication/   # 认证模块
│   ├── authorization/    # 授权模块
│   ├── user/  # 用户管理
│   ├── session/ # 会话管理
│   ├── security/         # 安全模块
│   └── administration/   # 管理模块
├── shared/               # 共享资源 (统一管理)
│   ├── config/          # 统一配置
│   ├── constants/       # 常量定义
│   ├── interfaces/      # 接口定义
│   ├── dto/            # 共享DTO
│   ├── decorators/     # 装饰器
│   └── utils/          # 工具函数
├── infrastructure/      # 基础设施 (清理整合)
│   ├── database/       # 数据库配置
│   ├── microservices/  # 微服务通信
│   └── health/         # 健康检查
├── app.module.ts       # 简化的主模块
└── main.ts
```

## 🔄 数据流架构

### **认证流程数据流**

```mermaid
sequenceDiagram
    participant C as Client
    participant AC as AuthController
    participant AS as AuthService
    participant UR as UserRepository
    participant SS as SessionService
    participant JS as JwtService
    participant SEC as SecurityService
    
    C->>AC: POST /auth/login
    AC->>AS: login(loginDto)
    AS->>UR: findByIdentifier(email)
    UR-->>AS: user
    AS->>AS: validatePassword()
    AS->>SS: createSession()
    SS-->>AS: session
    AS->>JS: generateTokenPair()
    JS-->>AS: tokens
    AS->>SEC: logSecurityEvent()
    AS-->>AC: AuthResult
    AC-->>C: 200 OK + tokens
```

### **权限检查数据流**

```mermaid
sequenceDiagram
    participant C as Client
    participant G as Guard
    participant AS as AuthService
    participant PS as PermissionService
    participant CACHE as Redis Cache
    
    C->>G: Request with JWT
    G->>AS: validateToken(token)
    AS-->>G: tokenPayload
    G->>CACHE: getPermissions(userId)
    alt Cache Hit
        CACHE-->>G: permissions
    else Cache Miss
        G->>PS: getUserPermissions(userId)
        PS-->>G: permissions
        G->>CACHE: setPermissions(userId, permissions)
    end
    G->>G: checkPermission(resource, action)
    G-->>C: Allow/Deny
```

## 📊 性能优化架构

### **缓存策略架构**

```mermaid
graph TB
    subgraph "缓存层次架构"
        L1[L1: 内存缓存<br/>用户会话信息]
        L2[L2: Redis缓存<br/>权限信息、Token验证]
        L3[L3: 数据库<br/>持久化数据]
        
        APP[应用层] --> L1
        L1 --> L2
        L2 --> L3
        
        subgraph "缓存策略"
            CT1[Token验证: 5分钟]
            CT2[用户权限: 10分钟]
            CT3[角色信息: 30分钟]
        end
        
        L2 --> CT1
        L2 --> CT2
        L2 --> CT3
    end
```

### **微服务通信架构**

```mermaid
graph LR
    subgraph "Auth Service"
        AUTH[Authentication Module]
        AUTHZ[Authorization Module]
    end
    
    subgraph "Gateway"
        GW[API Gateway]
        CACHE[Token Cache]
    end
    
    subgraph "Other Services"
        CHAR[Character Service]
        PROF[Profile Service]
        GAME[Game Service]
    end
    
    GW <-->|Token Validation| AUTH
    GW -->|Cache Results| CACHE
    
    CHAR -.->|User Info Request| AUTH
    PROF -.->|Permission Check| AUTHZ
    GAME -.->|Role Validation| AUTHZ
    
    style AUTH fill:#ccffcc
    style AUTHZ fill:#ccffcc
    style GW fill:#e6f3ff
    style CACHE fill:#fff2cc
```

## 🎯 架构优化目标

### **优化前后对比指标**

| 指标 | 优化前 | 优化后 | 改善 |
|------|--------|--------|------|
| 模块数量 | 12个 | 8个 | ⬇️ 33% |
| 循环依赖 | 3处 | 0处 | ✅ 消除 |
| 配置文件 | 5个 | 1个 | ⬇️ 80% |
| 启动时间 | 8-12秒 | 4-6秒 | ⬆️ 50% |
| 内存使用 | 180-220MB | 120-150MB | ⬇️ 30% |
| 代码重复 | 高 | 低 | ✅ 显著改善 |

### **架构质量提升**

```mermaid
radar
    title 架构质量对比
    options
        scale: 0-10
    data
        labels: [可维护性, 可扩展性, 性能, 安全性, 可测试性, 代码质量]
        datasets:
            label: 优化前
            data: [4, 3, 5, 7, 4, 4]
            backgroundColor: rgba(255, 99, 132, 0.2)
            borderColor: rgba(255, 99, 132, 1)
        datasets:
            label: 优化后
            data: [8, 9, 8, 8, 9, 8]
            backgroundColor: rgba(54, 162, 235, 0.2)
            borderColor: rgba(54, 162, 235, 1)
```

---

*本架构图解文档提供了auth服务优化方案的可视化展示，帮助理解架构改进的具体效果和实施方向。*
