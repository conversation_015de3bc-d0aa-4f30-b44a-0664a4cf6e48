# 认证服务 API 文档

## 📋 概述

本文档详细描述了认证服务提供的所有API接口，包括请求格式、响应格式、错误代码等。

## 🔗 基础信息

- **Base URL**: `http://localhost:3001/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: <PERSON><PERSON> (JWT)

## 📊 标准响应格式

### 成功响应

```json
{
  "success": true,
  "data": {}, // 响应数据
  "message": "操作成功",
  "timestamp": "2023-12-01T10:00:00.000Z"
}
```

### 错误响应

```json
{
  "success": false,
  "data": null,
  "message": "错误描述",
  "timestamp": "2023-12-01T10:00:00.000Z",
  "errorCode": "ERROR_CODE",
  "errors": {} // 详细错误信息
}
```

### 分页响应

```json
{
  "success": true,
  "data": {
    "items": [], // 数据项
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10,
      "hasNext": true,
      "hasPrev": false,
      "nextPage": 2,
      "prevPage": null
    },
    "meta": {
      "sortBy": "createdAt",
      "sortOrder": "desc",
      "search": "keyword"
    }
  },
  "message": "查询成功",
  "timestamp": "2023-12-01T10:00:00.000Z"
}
```

## 🔐 认证相关接口

### 用户注册

**POST** `/auth/register`

#### 请求体

```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!",
  "profile": {
    "firstName": "John",
    "lastName": "Doe",
    "language": "zh-CN"
  }
}
```

#### 响应

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "507f1f77bcf86cd799439011",
      "username": "johndoe",
      "email": "<EMAIL>",
      "profile": {
        "firstName": "John",
        "lastName": "Doe"
      },
      "status": "active",
      "emailVerified": false,
      "createdAt": "2023-12-01T10:00:00.000Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 3600,
      "tokenType": "Bearer"
    }
  },
  "message": "注册成功"
}
```

#### 错误响应

```json
{
  "success": false,
  "data": null,
  "message": "数据验证失败",
  "errorCode": "VALID_4001",
  "errors": {
    "errors": [
      {
        "field": "email",
        "value": "invalid-email",
        "constraints": {
          "isEmail": "邮箱格式无效"
        }
      }
    ],
    "totalErrors": 1,
    "fields": ["email"]
  }
}
```

### 用户登录

**POST** `/auth/login`

#### 限流规则
- 15分钟内最多5次尝试
- 超出限制返回429状态码

#### 请求体

```json
{
  "identifier": "<EMAIL>", // 邮箱或用户名
  "password": "SecurePass123!",
  "rememberMe": true,
  "deviceInfo": {
    "deviceId": "device-uuid",
    "deviceName": "iPhone 12",
    "platform": "iOS"
  }
}
```

#### 响应

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "507f1f77bcf86cd799439011",
      "username": "johndoe",
      "email": "<EMAIL>",
      "roles": ["user"],
      "permissions": ["user:read"],
      "lastLoginAt": "2023-12-01T10:00:00.000Z"
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 3600,
      "tokenType": "Bearer"
    },
    "session": {
      "sessionId": "session-uuid",
      "deviceFingerprint": "fp-hash",
      "location": {
        "country": "China",
        "city": "Beijing"
      }
    }
  },
  "message": "登录成功"
}
```

### 刷新令牌

**POST** `/auth/refresh`

#### 请求体

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 响应

```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600,
    "tokenType": "Bearer"
  },
  "message": "令牌刷新成功"
}
```

### 用户登出

**POST** `/auth/logout`

#### 请求头

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 请求体

```json
{
  "allDevices": false // 是否登出所有设备
}
```

#### 响应

```json
{
  "success": true,
  "data": null,
  "message": "登出成功"
}
```

## 👤 用户管理接口

### 获取当前用户信息

**GET** `/users/me`

#### 请求头

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 响应

```json
{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profile": {
      "firstName": "John",
      "lastName": "Doe",
      "avatar": "https://example.com/avatar.jpg",
      "language": "zh-CN"
    },
    "gameProfile": {
      "level": 5,
      "experience": 1250,
      "coins": 5000
    },
    "status": "active",
    "emailVerified": true,
    "roles": ["user"],
    "permissions": ["user:read", "user:update"],
    "createdAt": "2023-12-01T10:00:00.000Z"
  },
  "message": "查询成功"
}
```

### 更新用户资料

**PUT** `/users/me/profile`

#### 请求体

```json
{
  "firstName": "John",
  "lastName": "Smith",
  "avatar": "https://example.com/new-avatar.jpg",
  "bio": "足球经理游戏爱好者",
  "language": "en-US"
}
```

#### 响应

```json
{
  "success": true,
  "data": {
    "profile": {
      "firstName": "John",
      "lastName": "Smith",
      "avatar": "https://example.com/new-avatar.jpg",
      "bio": "足球经理游戏爱好者",
      "language": "en-US"
    }
  },
  "message": "资料更新成功"
}
```

### 修改密码

**PUT** `/users/me/password`

#### 请求体

```json
{
  "currentPassword": "OldPass123!",
  "newPassword": "NewPass456!",
  "confirmPassword": "NewPass456!"
}
```

#### 响应

```json
{
  "success": true,
  "data": null,
  "message": "密码修改成功"
}
```

### 获取用户列表（管理员）

**GET** `/users`

#### 权限要求
- 角色：`admin` 或 `user_admin`
- 权限：`user:read`

#### 查询参数

```
?page=1&limit=10&sortBy=createdAt&sortOrder=desc&search=john&status=active&roles=user
```

#### 响应

```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "507f1f77bcf86cd799439011",
        "username": "johndoe",
        "email": "<EMAIL>",
        "status": "active",
        "roles": ["user"],
        "lastLoginAt": "2023-12-01T10:00:00.000Z",
        "createdAt": "2023-12-01T09:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 1,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  },
  "message": "查询成功"
}
```

## 🔑 多因子认证接口

### 启用MFA

**POST** `/users/me/mfa/enable`

#### 响应

```json
{
  "success": true,
  "data": {
    "secret": "JBSWY3DPEHPK3PXP",
    "qrCode": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "backupCodes": [
      "12345678",
      "87654321",
      "11223344"
    ]
  },
  "message": "MFA设置信息生成成功"
}
```

### 验证并确认MFA

**POST** `/users/me/mfa/verify`

#### 请求体

```json
{
  "token": "123456" // 6位数字验证码
}
```

#### 响应

```json
{
  "success": true,
  "data": {
    "mfaEnabled": true
  },
  "message": "MFA启用成功"
}
```

### 禁用MFA

**POST** `/users/me/mfa/disable`

#### 请求体

```json
{
  "password": "CurrentPass123!",
  "token": "123456" // MFA验证码
}
```

#### 响应

```json
{
  "success": true,
  "data": {
    "mfaEnabled": false
  },
  "message": "MFA禁用成功"
}
```

## 🔄 密码重置接口

### 请求密码重置

**POST** `/auth/reset-password`

#### 限流规则
- 1小时内最多3次请求

#### 请求体

```json
{
  "email": "<EMAIL>"
}
```

#### 响应

```json
{
  "success": true,
  "data": null,
  "message": "密码重置邮件已发送"
}
```

### 确认密码重置

**POST** `/auth/confirm-reset`

#### 请求体

```json
{
  "token": "reset-token-from-email",
  "newPassword": "NewPass123!",
  "confirmPassword": "NewPass123!"
}
```

#### 响应

```json
{
  "success": true,
  "data": null,
  "message": "密码重置成功"
}
```

## 🎮 游戏相关接口

### 获取游戏资料

**GET** `/users/me/game-profile`

#### 响应

```json
{
  "success": true,
  "data": {
    "level": 5,
    "experience": 1250,
    "coins": 5000,
    "premiumUntil": "2024-01-01T00:00:00.000Z",
    "achievements": ["first_login", "first_match"],
    "statistics": {
      "gamesPlayed": 25,
      "wins": 15,
      "losses": 8,
      "draws": 2,
      "winRate": 0.6
    }
  },
  "message": "查询成功"
}
```

### 更新游戏偏好

**PUT** `/users/me/game-preferences`

#### 请求体

```json
{
  "difficulty": "medium",
  "gameSpeed": "normal",
  "notifications": {
    "matchResults": true,
    "transfers": false
  }
}
```

## 📊 错误代码参考

### 认证错误 (1000-1999)

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| AUTH_1001 | 401 | 认证失败 |
| AUTH_1002 | 401 | 令牌已过期 |
| AUTH_1003 | 401 | 无效的令牌 |
| AUTH_1004 | 401 | 缺少认证令牌 |
| AUTH_1005 | 401 | 无效的凭据 |
| AUTH_1006 | 403 | 权限不足 |
| AUTH_1011 | 401 | 需要多因子认证 |
| AUTH_1017 | 429 | 请求过于频繁 |

### 用户错误 (2000-2999)

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| USER_2001 | 404 | 用户不存在 |
| USER_2002 | 409 | 用户已存在 |
| USER_2003 | 400 | 用户名无效 |
| USER_2004 | 400 | 邮箱无效 |
| USER_2005 | 400 | 密码无效 |
| USER_2010 | 400 | 邮箱地址未验证 |
| USER_2011 | 400 | 手机号码未验证 |

### 验证错误 (4000-4999)

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| VALID_4001 | 400 | 数据验证失败 |
| VALID_4002 | 400 | 必填字段缺失 |
| VALID_4003 | 400 | 字段格式无效 |
| VALID_4004 | 400 | 字段长度无效 |

### 系统错误 (5000-5999)

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| SYS_5001 | 500 | 内部服务器错误 |
| SYS_5002 | 500 | 数据库错误 |
| SYS_5003 | 500 | 网络错误 |
| SYS_5004 | 408 | 请求超时 |
| SYS_5005 | 503 | 服务维护中 |

## 📈 响应头说明

### 标准响应头

```
X-Request-ID: req_1234567890_abcdef123
X-Timestamp: 2023-12-01T10:00:00.000Z
X-API-Version: v1
```

### 分页响应头

```
X-Total-Count: 100
X-Page: 1
X-Per-Page: 10
X-Total-Pages: 10
```

### 限流响应头

```
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 5
X-RateLimit-Reset: 2023-12-01T10:01:00.000Z
X-RateLimit-RetryAfter: 60
```

## 🔧 开发工具

### Postman集合

可以导入以下Postman集合来测试API：

```json
{
  "info": {
    "name": "认证服务API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:3001/api/v1"
    },
    {
      "key": "accessToken",
      "value": ""
    }
  ]
}
```

### cURL示例

```bash
# 用户注册
curl -X POST http://localhost:3001/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "johndoe",
    "email": "<EMAIL>",
    "password": "SecurePass123!"
  }'

# 用户登录
curl -X POST http://localhost:3001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "<EMAIL>",
    "password": "SecurePass123!"
  }'

# 获取用户信息
curl -X GET http://localhost:3001/api/v1/users/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 📝 更新日志

### v1.0.0 (2023-12-01)
- 初始版本发布
- 完整的认证和授权功能
- 用户管理功能
- MFA支持
- 密码重置功能
- 游戏资料管理
