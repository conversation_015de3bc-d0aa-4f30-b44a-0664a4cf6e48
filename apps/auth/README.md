# 🏆 足球经理游戏认证服务

一个基于 NestJS 构建的企业级认证和授权服务，专为足球经理游戏设计，提供完整的用户管理、角色权限控制、安全审计等功能。

## ✨ 核心特性

### 🔐 认证功能
- **多种登录方式**: 用户名/邮箱登录、OAuth2.0 社交登录
- **JWT 令牌管理**: 访问令牌 + 刷新令牌，支持令牌轮换
- **会话管理**: 多设备会话控制、设备指纹识别
- **多因子认证**: TOTP、短信、邮箱验证码、备用码
- **密码策略**: 强密码要求、密码历史、定期更换

### 🛡️ 授权系统
- **RBAC 权限模型**: 基于角色的访问控制
- **细粒度权限**: 资源级别的权限控制
- **条件权限**: 基于时间、地理位置、设备的权限限制
- **权限继承**: 角色继承和权限组合
- **动态权限**: 运行时权限检查和更新

### 🔒 安全防护
- **暴力破解防护**: 登录尝试限制、账户锁定、智能限流
- **异常检测**: 地理位置、设备、行为模式异常
- **风险评估**: 多维度风险评分和自动响应
- **数据保护**: AES-256-GCM加密、敏感数据脱敏、密钥管理
- **输入验证**: SQL注入、XSS攻击、路径遍历防护
- **代理后限流**: 真实IP识别、Redis分布式限流
- **安全头配置**: HSTS、CSP、X-Frame-Options等

### 📊 审计监控
- **完整审计日志**: 所有用户操作记录、批量处理优化
- **安全事件监控**: 实时安全威胁检测、智能告警
- **统计报告**: 用户行为分析、安全趋势、性能指标
- **告警系统**: 异常活动自动告警、多渠道通知
- **性能监控**: 响应时间、内存使用、数据库性能
- **健康检查**: 系统组件状态、自动恢复机制

### 👑 管理员功能 🆕
- **管理员仪表板**: 系统概览、关键指标监控、实时数据展示
- **系统管理**: 系统状态监控、健康检查、维护工具、备份管理
- **用户管理**: 用户生命周期管理、角色分配、批量操作、会话控制
- **统计分析**: 用户增长分析、会话统计、安全事件分析、性能指标
- **安全管理**: 安全警告、威胁检测、IP黑名单、安全扫描
- **审计管理**: 操作审计、事件追踪、合规报告、数据导出

### 🎮 游戏特色
- **游戏角色系统**: 球队经理、教练、球探等专业角色
- **游戏权限**: 球员管理、转会、比赛、财务等游戏功能权限
- **社交功能**: 好友系统、公会管理、聊天权限
- **VIP 会员**: 高级会员特权和功能

## 🏗️ 技术架构

### 技术栈
- **框架**: NestJS + TypeScript
- **数据库**: MongoDB + Mongoose
- **缓存**: Redis (支持集群)
- **认证**: JWT + Passport
- **文档**: Swagger/OpenAPI
- **日志**: Winston + 结构化日志
- **测试**: Jest + 性能测试
- **安全**: bcrypt + AES-256-GCM + 输入验证
- **监控**: 健康检查 + 性能指标 + 告警系统

### 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gateway       │    │   Auth Service  │    │   Game Service  │
│   (网关层)      │────│   (认证服务)    │────│   (游戏服务)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   Redis Cache   │              │
         │              │   (缓存层)      │              │
         │              └─────────────────┘              │
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   MongoDB       │
                    │   (数据层)      │
                    └─────────────────┘
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- MongoDB >= 5.0
- Redis >= 6.0
- npm >= 8.0.0

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd football-manager/apps/auth
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **启动数据库**
```bash
# 启动 MongoDB
mongod --dbpath /path/to/data

# 启动 Redis
redis-server
```

5. **初始化数据**
```bash
# 创建基础数据（权限、角色、管理员用户）
npm run setup
```

6. **启动服务**
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

### 默认账户
- **用户名**: admin
- **邮箱**: <EMAIL>
- **密码**: Admin123!@#

## 📚 API 文档

启动服务后访问 Swagger 文档：
- 开发环境: http://localhost:3001/api/docs
- 生产环境: https://your-domain.com/api/docs

### 主要 API 端点

#### 认证相关
```
POST /auth/register     # 用户注册
POST /auth/login        # 用户登录
POST /auth/refresh      # 刷新令牌
POST /auth/logout       # 用户登出
POST /auth/reset-password    # 密码重置
POST /auth/verify-token # 验证令牌
```

#### 多因子认证
```
GET  /auth/mfa/status   # 获取MFA状态
POST /auth/mfa/setup    # 生成MFA设置
POST /auth/mfa/enable   # 启用MFA
DELETE /auth/mfa/disable # 禁用MFA
POST /auth/mfa/backup-codes/regenerate # 重新生成备用码
POST /auth/mfa/sms/send # 发送短信验证码
POST /auth/mfa/sms/verify # 验证短信验证码
```

#### 用户管理
```
GET  /users/me          # 获取当前用户信息
PUT  /users/me          # 更新用户信息
PUT  /users/me/password # 修改密码
GET  /users             # 用户列表（管理员）
```

#### 角色权限
```
GET  /roles             # 角色列表
POST /roles             # 创建角色
GET  /permissions       # 权限列表
POST /permissions       # 创建权限
```

#### 管理员功能 🆕
```
GET  /admin/dashboard              # 管理员仪表板
GET  /admin/system/status          # 系统状态
GET  /admin/system/health          # 健康检查
GET  /admin/system/metrics         # 系统指标
POST /admin/actions/maintenance    # 系统维护
POST /admin/actions/backup         # 创建备份
GET  /admin/logs/system            # 系统日志
GET  /admin/settings               # 系统设置
PUT  /admin/settings               # 更新设置
GET  /admin/notifications          # 管理员通知

# 用户管理
GET    /admin/users                    # 用户列表
GET    /admin/users/:id                # 用户详情
PUT    /admin/users/:id/status         # 更新用户状态
PUT    /admin/users/:id/roles          # 分配用户角色
POST   /admin/users/:id/reset-password # 重置用户密码
POST   /admin/users/:id/lock           # 锁定用户账户
DELETE /admin/users/:id/lock           # 解锁用户账户
DELETE /admin/users/:id/mfa            # 禁用用户MFA
GET    /admin/users/:id/sessions       # 获取用户会话
DELETE /admin/users/:id/sessions/:sessionId # 终止用户会话
POST   /admin/users/bulk               # 批量操作用户

# 统计分析
GET /admin/statistics/overview    # 统计概览
GET /admin/statistics/users       # 用户统计
GET /admin/statistics/sessions    # 会话统计
GET /admin/statistics/security    # 安全统计
GET /admin/statistics/performance # 性能统计

# 安全管理
GET  /admin/security/alerts        # 安全警告
GET  /admin/security/events        # 安全事件
GET  /admin/security/blocked-ips   # 被阻止的IP
POST /admin/security/scan          # 安全扫描

# 审计管理
GET  /admin/audit/logs             # 审计日志
GET  /admin/audit/events           # 审计事件
POST /admin/audit/export           # 导出审计数据
```

## 🎮 游戏角色系统

### 内置角色

| 角色 | 显示名称 | 描述 | 主要权限 |
|------|----------|------|----------|
| `game_master` | 游戏管理员 | 拥有游戏内所有管理权限 | 用户管理、游戏配置、经济控制 |
| `league_admin` | 联赛管理员 | 负责管理联赛运营 | 联赛管理、比赛安排、内容审核 |
| `team_manager` | 球队经理 | 专业球队经理 | 球队管理、球员交易、比赛战术 |
| `assistant_coach` | 助理教练 | 协助球队管理 | 球员训练、比赛观看、数据查看 |
| `scout` | 球探 | 球员发现和评估 | 球员侦察、转会谈判 |
| `youth_coach` | 青训教练 | 青年球员培养 | 球员训练、设施管理 |
| `financial_director` | 财务总监 | 球队财务管理 | 财务管理、预算控制、赞助合同 |
| `fan_club_leader` | 球迷会会长 | 球迷社区管理 | 社区管理、内容审核 |
| `sports_journalist` | 体育记者 | 媒体报道 | 内容创作、数据访问 |
| `casual_player` | 休闲玩家 | 普通游戏玩家 | 基础游戏功能 |
| `trial_user` | 试用用户 | 试用期用户 | 受限游戏功能 |
| `vip_member` | VIP会员 | 高级会员 | 高级游戏功能、特殊权限 |

### 权限分类

- **game_basic**: 基础游戏权限
- **team_management**: 球队管理
- **player_management**: 球员管理
- **match_management**: 比赛管理
- **transfer_market**: 转会市场
- **finance_management**: 财务管理
- **facility_management**: 设施管理
- **league_management**: 联赛管理
- **statistics**: 统计报告
- **social**: 社交功能
- **administration**: 系统管理
- **content_management**: 内容管理
- **api_access**: API访问

## 🔧 配置说明

### 主要配置项

```env
# 应用配置
APP_NAME=football-manager-auth
AUTH_PORT=3001
NODE_ENV=development
API_VERSION=v1

# 数据库
MONGODB_URI=mongodb://localhost:27017/football-manager-auth
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters
JWT_ACCESS_TOKEN_TTL=15m
JWT_REFRESH_TOKEN_TTL=7d

# 加密配置
ENCRYPTION_MASTER_KEY=your-32-byte-master-encryption-key
ENCRYPTION_ALGORITHM=aes-256-gcm

# 安全配置
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL_CHARS=true
ACCOUNT_PROTECTION_MAX_ATTEMPTS=5

# 限流配置
RATE_LIMIT_GLOBAL_LIMIT=100
THROTTLE_TTL=60
THROTTLE_LIMIT=10

# 日志配置
LOGGING_ENABLED=true
LOGGING_LEVEL=info
LOGGING_INCLUDE_BODY=false

# 超时配置
TIMEOUT_DEFAULT=30000
TIMEOUT_UPLOAD=300000
TIMEOUT_AUTH=10000

# 安全头配置
SECURITY_HEADERS_ENABLED=true
CORS_ENABLED=true
CORS_ORIGIN=https://yourdomain.com
```

详细配置请参考 `.env.example` 文件。

## 🚀 新功能特性

### 增强的共享模块
认证服务现已包含完整的共享模块，提供企业级的功能组件：

#### 🔧 核心服务
- **CryptoService**: 完整的加密解密、哈希验证、数字签名功能
- **ValidationService**: 增强的数据验证，支持邮箱、手机、用户名等验证
- **UtilsService**: 丰富的工具函数，包括设备指纹、IP解析等

#### 🛡️ 安全组件
- **ThrottlerBehindProxyGuard**: 代理后智能限流，支持真实IP识别
- **SecurityLoggingInterceptor**: 专门的安全日志记录
- **CustomValidationPipe**: 增强验证管道，包含业务规则和安全检查

#### 📊 监控和日志
- **PerformanceInterceptor**: 性能监控和慢请求检测
- **AllExceptionsFilter**: 全局异常处理和格式化
- **MemoryMonitorService**: 内存泄漏检测和告警

#### 🏷️ 装饰器增强
- **@SecureEndpoint**: 组合安全装饰器
- **@RateLimit**: 灵活的限流配置
- **@ApiPaginatedResponse**: 标准化分页响应文档

### 使用示例

```typescript
// 安全端点配置
@Controller('admin')
export class AdminController {
  @Post('users')
  @SecureEndpoint({
    roles: ['admin'],
    permissions: ['user:create'],
    requireMfa: true,
    requireEmailVerified: true,
    rateLimit: { limit: 10, ttl: 3600 }
  })
  async createUser(@Body() dto: CreateUserDto) {
    return this.usersService.create(dto);
  }
}

// 分页查询
@Get('users')
@ApiPaginatedResponse({ itemType: UserDto })
async getUsers(@Query() pagination: PaginationDto) {
  return this.usersService.findAll(pagination);
}

// 加密服务使用
constructor(private readonly cryptoService: CryptoService) {}

async secureData(data: string): Promise<string> {
  const encrypted = this.cryptoService.encrypt(data);
  return encrypted.encryptedData;
}
```

## 📚 文档

### 核心文档
- [共享模块使用指南](./docs/SHARED_MODULE_GUIDE.md) - 详细的功能说明和使用示例
- [API 文档](./docs/API_DOCUMENTATION.md) - 完整的API接口文档
- [安全指南](./docs/SECURITY_GUIDE.md) - 安全特性和最佳实践
- [性能优化指南](./docs/PERFORMANCE_GUIDE.md) - 性能优化策略和监控
- [故障排除指南](./docs/TROUBLESHOOTING.md) - 常见问题诊断和解决

### 技术文档
- [部署指南](./docs/deployment.md) - 生产环境部署
- [开发指南](./docs/development.md) - 开发环境搭建
- [兼容性报告](./docs/FINAL_COMPATIBILITY_REPORT.md) - 组件兼容性验证

## 🧪 测试

```bash
# 单元测试
npm run test:unit

# 集成测试
npm run test:integration

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov

# 监听模式
npm run test:watch

# 性能测试
npm run test:performance

# 安全测试
npm run test:security

# 兼容性验证
npx ts-node apps/auth/scripts/verify-compatibility.ts
```

## 📦 部署

### Docker 部署

```bash
# 构建镜像
docker build -t football-manager-auth .

# 运行容器
docker run -p 3001:3001 football-manager-auth
```

### 生产环境

1. **环境配置**
```bash
NODE_ENV=production
JWT_SECRET=your-production-secret
MONGODB_URI=mongodb://your-production-db
```

2. **构建和启动**
```bash
npm run build
npm run start:prod
```

3. **进程管理**
```bash
# 使用 PM2
npm install -g pm2
pm2 start dist/main.js --name auth-service
```

## 🔍 监控和维护

### 健康检查
```bash
curl http://localhost:3001/health
```

### 日志查看
```bash
# 应用日志
tail -f logs/auth.log

# 错误日志
tail -f logs/error.log

# 安全日志
tail -f logs/security.log
```

### 数据库维护
```bash
# 清理过期会话
npm run cleanup:sessions

# 清理过期审计日志
npm run cleanup:audit-logs

# 数据库备份
mongodump --db football-manager-auth
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
- 提交 [Issue](https://github.com/your-repo/issues)
- 发送邮件至 <EMAIL>
- 查看 [Wiki](https://github.com/your-repo/wiki) 文档

---

**足球经理认证服务** - 为您的足球经理游戏提供企业级的认证和授权解决方案！ ⚽🏆
