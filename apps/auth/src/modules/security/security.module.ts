import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 服务
import { SecurityService } from './services/security.service';
import { AuditService } from './services/audit.service';
import { EncryptionService } from './services/encryption.service';
import { RiskService } from './services/risk.service';
import { CryptoService } from './services/crypto.service';
import { ValidationService } from './services/validation.service';
import { UtilsService } from './services/utils.service';
import { PasswordService } from './services/password.service';

// 守卫
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { ThrottlerBehindProxyGuard } from './guards/throttler-behind-proxy.guard';

// 拦截器
import {
  ResponseInterceptor,
  PaginationResponseInterceptor,
  FileResponseInterceptor,
  CacheResponseInterceptor
} from './interceptors/response.interceptor';
import { LoggingInterceptor, SecurityLoggingInterceptor } from './interceptors/logging.interceptor';
import { TimeoutInterceptor, DynamicTimeoutInterceptor } from './interceptors/timeout.interceptor';

// 管道
import { ValidationPipe, CustomValidationPipe } from './pipes/validation.pipe';
import {
  ParseObjectIdPipe,
  ParseOptionalObjectIdPipe,
  ParseObjectIdArrayPipe,
  ParseQueryObjectIdPipe,
  ParseParamObjectIdPipe,
  ParseBodyObjectIdPipe
} from './pipes/parse-object-id.pipe';

// 过滤器
import { AllExceptionsFilter } from './filters/all-exceptions.filter';
import { HttpExceptionFilter } from './filters/http-exception.filter';
import { ValidationExceptionFilter, MongoValidationExceptionFilter } from './filters/validation-exception.filter';

// 仓储
import { AuditLogRepository, IAuditLogRepository } from './repositories/audit-log.repository';

// 实体
import { AuditLog, AuditLogSchema } from './entities/audit-log.entity';

/**
 * 安全模块
 * 
 * 整合了所有安全相关的组件，包括：
 * - 安全服务（审计、加密、风险评估）
 * - 安全守卫（JWT认证、限流）
 * - 安全拦截器（日志、响应、超时）
 * - 安全管道（验证、解析）
 * - 安全过滤器（异常处理）
 * 
 * 职责范围：
 * - 系统安全策略实施
 * - 审计日志记录和管理
 * - 数据加密和解密
 * - 安全风险评估和监控
 * - 请求响应的安全处理
 * 
 * 设计原则：
 * - 安全优先：所有安全组件集中管理
 * - 统一标准：一致的安全策略
 * - 可配置：支持灵活的安全配置
 * - 高性能：优化的安全检查流程
 */
@Module({
  imports: [
    ConfigModule,
    
    // MongoDB Schema注册 - 审计日志
    MongooseModule.forFeature([
      { name: AuditLog.name, schema: AuditLogSchema },
    ]),
  ],
  providers: [
    // 仓储层
    AuditLogRepository,

    // 核心安全服务
    SecurityService,
    AuditService,
    EncryptionService,
    RiskService,
    CryptoService,
    ValidationService,
    UtilsService,
    PasswordService,
    
    // 守卫
    JwtAuthGuard,
    ThrottlerBehindProxyGuard,
    
    // 拦截器
    ResponseInterceptor,
    PaginationResponseInterceptor,
    FileResponseInterceptor,
    CacheResponseInterceptor,
    LoggingInterceptor,
    SecurityLoggingInterceptor,
    TimeoutInterceptor,
    DynamicTimeoutInterceptor,
    
    // 管道 - 使用工厂模式配置
    {
      provide: ValidationPipe,
      useFactory: (configService: ConfigService) => new ValidationPipe(configService),
      inject: [ConfigService],
    },
    {
      provide: CustomValidationPipe,
      useFactory: (configService: ConfigService) => new CustomValidationPipe(configService),
      inject: [ConfigService],
    },
    {
      provide: ParseObjectIdPipe,
      useFactory: () => new ParseObjectIdPipe(),
    },
    {
      provide: ParseOptionalObjectIdPipe,
      useFactory: () => new ParseOptionalObjectIdPipe(),
    },
    {
      provide: ParseObjectIdArrayPipe,
      useFactory: () => new ParseObjectIdArrayPipe(),
    },
    {
      provide: ParseQueryObjectIdPipe,
      useFactory: () => new ParseQueryObjectIdPipe(),
    },
    {
      provide: ParseParamObjectIdPipe,
      useFactory: () => new ParseParamObjectIdPipe(),
    },
    {
      provide: ParseBodyObjectIdPipe,
      useFactory: () => new ParseBodyObjectIdPipe(),
    },
    
    // 过滤器
    AllExceptionsFilter,
    HttpExceptionFilter,
    ValidationExceptionFilter,
    MongoValidationExceptionFilter,
    
    // 接口实现注册
    {
      provide: 'IAuditLogRepository',
      useClass: AuditLogRepository,
    },
    {
      provide: 'ISecurityService',
      useClass: SecurityService,
    },
    {
      provide: 'IAuditService',
      useClass: AuditService,
    },
  ],
  exports: [
    // 仓储层
    AuditLogRepository,

    // 核心安全服务
    SecurityService,
    AuditService,
    EncryptionService,
    RiskService,
    CryptoService,
    ValidationService,
    UtilsService,
    PasswordService,
    
    // 守卫
    JwtAuthGuard,
    ThrottlerBehindProxyGuard,
    
    // 拦截器
    ResponseInterceptor,
    PaginationResponseInterceptor,
    FileResponseInterceptor,
    CacheResponseInterceptor,
    LoggingInterceptor,
    SecurityLoggingInterceptor,
    TimeoutInterceptor,
    DynamicTimeoutInterceptor,
    
    // 管道
    ValidationPipe,
    CustomValidationPipe,
    ParseObjectIdPipe,
    ParseOptionalObjectIdPipe,
    ParseObjectIdArrayPipe,
    ParseQueryObjectIdPipe,
    ParseParamObjectIdPipe,
    ParseBodyObjectIdPipe,
    
    // 过滤器
    AllExceptionsFilter,
    HttpExceptionFilter,
    ValidationExceptionFilter,
    MongoValidationExceptionFilter,
    
    // 接口导出
    'IAuditLogRepository',
    'ISecurityService',
    'IAuditService',
  ],
})
export class SecurityModule {
  constructor() {
    console.log('✅ 安全模块已初始化 - 包含所有安全组件和服务');
  }
}
