/**
 * ObjectId 解析管道
 * 验证和转换 MongoDB ObjectId
 */

import {
  PipeTransform,
  Injectable,
  ArgumentMetadata,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { Types } from 'mongoose';

// ObjectId 验证选项接口
export interface ParseObjectIdOptions {
  optional?: boolean;           // 是否可选
  errorMessage?: string;        // 自定义错误消息
  transform?: boolean;          // 是否转换为 ObjectId 实例
  allowString?: boolean;        // 是否允许返回字符串格式
}

@Injectable()
export class ParseObjectIdPipe implements PipeTransform<string, string | Types.ObjectId> {
  private readonly logger = new Logger(ParseObjectIdPipe.name);
  private readonly options: ParseObjectIdOptions;

  constructor(options?: ParseObjectIdOptions) {
    this.options = {
      optional: false,
      errorMessage: '无效的ObjectId格式',
      transform: false,
      allowString: true,
      ...options,
    };
  }

  transform(value: string, metadata: ArgumentMetadata): string | Types.ObjectId {
    // 如果值为空且是可选的，返回 undefined
    if (!value && this.options.optional) {
      return undefined;
    }

    // 如果值为空且不是可选的，抛出错误
    if (!value) {
      this.logError(value, metadata, '缺少必需的ObjectId参数');
      throw new BadRequestException(this.options.errorMessage);
    }

    // 验证 ObjectId 格式
    if (!this.isValidObjectId(value)) {
      this.logError(value, metadata, 'ObjectId格式无效');
      throw new BadRequestException(this.options.errorMessage);
    }

    // 根据选项返回相应格式
    if (this.options.transform) {
      return new Types.ObjectId(value);
    }

    return this.options.allowString ? value : new Types.ObjectId(value);
  }

  /**
   * 验证 ObjectId 格式
   */
  private isValidObjectId(value: string): boolean {
    try {
      // 检查长度
      if (value.length !== 24) {
        return false;
      }

      // 检查是否为有效的十六进制字符串
      if (!/^[0-9a-fA-F]{24}$/.test(value)) {
        return false;
      }

      // 使用 mongoose 的验证
      return Types.ObjectId.isValid(value);
    } catch (error) {
      return false;
    }
  }

  /**
   * 记录错误日志
   */
  private logError(value: any, metadata: ArgumentMetadata, reason: string): void {
    const errorDetails = {
      value,
      type: metadata.type,
      data: metadata.data,
      reason,
      timestamp: new Date().toISOString(),
    };

    this.logger.warn('ObjectId解析失败', errorDetails);
  }
}

/**
 * 可选 ObjectId 解析管道
 */
@Injectable()
export class ParseOptionalObjectIdPipe extends ParseObjectIdPipe {
  constructor(options?: Omit<ParseObjectIdOptions, 'optional'>) {
    super({ ...options, optional: true });
  }
}

/**
 * ObjectId 数组解析管道
 */
@Injectable()
export class ParseObjectIdArrayPipe implements PipeTransform<string[], (string | Types.ObjectId)[]> {
  private readonly logger = new Logger(ParseObjectIdArrayPipe.name);
  private readonly options: ParseObjectIdOptions & { maxLength?: number };

  constructor(options?: ParseObjectIdOptions & { maxLength?: number }) {
    this.options = {
      optional: false,
      errorMessage: '无效的ObjectId数组格式',
      transform: false,
      allowString: true,
      maxLength: 100,
      ...options,
    };
  }

  transform(value: string[], metadata: ArgumentMetadata): (string | Types.ObjectId)[] {
    // 如果值为空且是可选的，返回空数组
    if ((!value || value.length === 0) && this.options.optional) {
      return [];
    }

    // 如果值为空且不是可选的，抛出错误
    if (!value || !Array.isArray(value)) {
      this.logError(value, metadata, '缺少必需的ObjectId数组参数');
      throw new BadRequestException(this.options.errorMessage);
    }

    // 检查数组长度限制
    if (value.length > this.options.maxLength) {
      this.logError(value, metadata, `ObjectId数组长度超出限制: ${this.options.maxLength}`);
      throw new BadRequestException(`ObjectId数组长度不能超过${this.options.maxLength}`);
    }

    // 验证每个 ObjectId
    const result: (string | Types.ObjectId)[] = [];
    
    for (let i = 0; i < value.length; i++) {
      const id = value[i];
      
      if (!this.isValidObjectId(id)) {
        this.logError(value, metadata, `数组索引${i}处的ObjectId格式无效: ${id}`);
        throw new BadRequestException(`数组索引${i}处的ObjectId格式无效`);
      }

      if (this.options.transform) {
        result.push(new Types.ObjectId(id));
      } else {
        result.push(this.options.allowString ? id : new Types.ObjectId(id));
      }
    }

    return result;
  }

  /**
   * 验证 ObjectId 格式
   */
  private isValidObjectId(value: string): boolean {
    try {
      if (!value || typeof value !== 'string') {
        return false;
      }

      if (value.length !== 24) {
        return false;
      }

      if (!/^[0-9a-fA-F]{24}$/.test(value)) {
        return false;
      }

      return Types.ObjectId.isValid(value);
    } catch (error) {
      return false;
    }
  }

  /**
   * 记录错误日志
   */
  private logError(value: any, metadata: ArgumentMetadata, reason: string): void {
    const errorDetails = {
      value,
      type: metadata.type,
      data: metadata.data,
      reason,
      timestamp: new Date().toISOString(),
    };

    this.logger.warn('ObjectId数组解析失败', errorDetails);
  }
}

/**
 * 查询参数 ObjectId 解析管道
 * 专门处理查询参数中的 ObjectId
 */
@Injectable()
export class ParseQueryObjectIdPipe implements PipeTransform<any, any> {
  private readonly logger = new Logger(ParseQueryObjectIdPipe.name);
  private readonly objectIdFields: string[];

  constructor(objectIdFields: string[] = ['id', 'userId', 'roleId', 'permissionId']) {
    this.objectIdFields = objectIdFields;
  }

  transform(value: any, metadata: ArgumentMetadata): any {
    if (!value || typeof value !== 'object') {
      return value;
    }

    const result = { ...value };

    // 处理每个可能的 ObjectId 字段
    for (const field of this.objectIdFields) {
      if (result[field]) {
        if (Array.isArray(result[field])) {
          // 处理数组形式的 ObjectId
          result[field] = this.validateObjectIdArray(result[field], field);
        } else {
          // 处理单个 ObjectId
          result[field] = this.validateSingleObjectId(result[field], field);
        }
      }
    }

    return result;
  }

  /**
   * 验证单个 ObjectId
   */
  private validateSingleObjectId(value: string, field: string): string {
    if (!this.isValidObjectId(value)) {
      this.logger.warn(`查询参数${field}的ObjectId格式无效`, { field, value });
      throw new BadRequestException(`查询参数${field}的ObjectId格式无效`);
    }
    return value;
  }

  /**
   * 验证 ObjectId 数组
   */
  private validateObjectIdArray(values: string[], field: string): string[] {
    if (!Array.isArray(values)) {
      throw new BadRequestException(`查询参数${field}必须是数组`);
    }

    if (values.length > 50) {
      throw new BadRequestException(`查询参数${field}数组长度不能超过50`);
    }

    return values.map((value, index) => {
      if (!this.isValidObjectId(value)) {
        this.logger.warn(`查询参数${field}数组索引${index}的ObjectId格式无效`, { field, value, index });
        throw new BadRequestException(`查询参数${field}数组索引${index}的ObjectId格式无效`);
      }
      return value;
    });
  }

  /**
   * 验证 ObjectId 格式
   */
  private isValidObjectId(value: string): boolean {
    try {
      if (!value || typeof value !== 'string') {
        return false;
      }

      if (value.length !== 24) {
        return false;
      }

      if (!/^[0-9a-fA-F]{24}$/.test(value)) {
        return false;
      }

      return Types.ObjectId.isValid(value);
    } catch (error) {
      return false;
    }
  }
}

/**
 * 路径参数 ObjectId 解析管道
 * 专门处理路径参数中的 ObjectId
 */
@Injectable()
export class ParseParamObjectIdPipe extends ParseObjectIdPipe {
  constructor(options?: ParseObjectIdOptions) {
    super({
      errorMessage: '路径参数ObjectId格式无效',
      ...options,
    });
  }

  transform(value: string, metadata: ArgumentMetadata): string | Types.ObjectId {
    if (metadata.type !== 'param') {
      throw new BadRequestException('此管道只能用于路径参数');
    }

    return super.transform(value, metadata);
  }
}

/**
 * 请求体 ObjectId 解析管道
 * 专门处理请求体中的 ObjectId 字段
 */
@Injectable()
export class ParseBodyObjectIdPipe implements PipeTransform<any, any> {
  private readonly logger = new Logger(ParseBodyObjectIdPipe.name);
  private readonly objectIdFields: string[];
  private readonly options: ParseObjectIdOptions;

  constructor(
    objectIdFields: string[] = [],
    options?: ParseObjectIdOptions,
  ) {
    this.objectIdFields = objectIdFields;
    this.options = {
      errorMessage: '请求体ObjectId格式无效',
      transform: false,
      allowString: true,
      ...options,
    };
  }

  transform(value: any, metadata: ArgumentMetadata): any {
    if (metadata.type !== 'body') {
      return value;
    }

    if (!value || typeof value !== 'object') {
      return value;
    }

    const result = { ...value };

    // 处理指定的 ObjectId 字段
    for (const field of this.objectIdFields) {
      if (result[field] !== undefined) {
        result[field] = this.processObjectIdField(result[field], field);
      }
    }

    return result;
  }

  /**
   * 处理 ObjectId 字段
   */
  private processObjectIdField(value: any, field: string): any {
    if (value === null || value === undefined) {
      return value;
    }

    if (Array.isArray(value)) {
      return value.map((item, index) => {
        if (!this.isValidObjectId(item)) {
          this.logger.warn(`请求体${field}数组索引${index}的ObjectId格式无效`, { field, value: item, index });
          throw new BadRequestException(`请求体${field}数组索引${index}的ObjectId格式无效`);
        }
        return this.options.transform ? new Types.ObjectId(item) : item;
      });
    }

    if (!this.isValidObjectId(value)) {
      this.logger.warn(`请求体${field}的ObjectId格式无效`, { field, value });
      throw new BadRequestException(`请求体${field}的ObjectId格式无效`);
    }

    return this.options.transform ? new Types.ObjectId(value) : value;
  }

  /**
   * 验证 ObjectId 格式
   */
  private isValidObjectId(value: any): boolean {
    try {
      if (!value || typeof value !== 'string') {
        return false;
      }

      if (value.length !== 24) {
        return false;
      }

      if (!/^[0-9a-fA-F]{24}$/.test(value)) {
        return false;
      }

      return Types.ObjectId.isValid(value);
    } catch (error) {
      return false;
    }
  }
}
