/**
 * 日志拦截器
 * 记录请求和响应的详细日志
 */

import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';

// 日志级别枚举
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
  VERBOSE = 'verbose',
}

// 日志配置接口
export interface LoggingConfig {
  enabled: boolean;
  level: LogLevel;
  includeBody: boolean;
  includeHeaders: boolean;
  includeQuery: boolean;
  includeUserAgent: boolean;
  includeIp: boolean;
  maxBodyLength: number;
  sensitiveFields: string[];
  excludePaths: string[];
}

// 请求日志接口
export interface RequestLog {
  requestId: string;
  method: string;
  url: string;
  path: string;
  query?: any;
  headers?: any;
  body?: any;
  ip?: string;
  userAgent?: string;
  userId?: string;
  sessionId?: string;
  timestamp: string;
}

// 响应日志接口
export interface ResponseLog {
  requestId: string;
  statusCode: number;
  duration: number;
  responseSize?: number;
  headers?: any;
  body?: any;
  error?: any;
  timestamp: string;
}

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);
  private readonly config: LoggingConfig;

  constructor(private readonly configService: ConfigService) {
    this.config = {
      enabled: this.configService.get('LOGGING_ENABLED', true),
      level: this.configService.get('LOGGING_LEVEL', LogLevel.INFO),
      includeBody: this.configService.get('LOGGING_INCLUDE_BODY', true),
      includeHeaders: this.configService.get('LOGGING_INCLUDE_HEADERS', false),
      includeQuery: this.configService.get('LOGGING_INCLUDE_QUERY', true),
      includeUserAgent: this.configService.get('LOGGING_INCLUDE_USER_AGENT', true),
      includeIp: this.configService.get('LOGGING_INCLUDE_IP', true),
      maxBodyLength: this.configService.get('LOGGING_MAX_BODY_LENGTH', 1000),
      sensitiveFields: this.configService.get('LOGGING_SENSITIVE_FIELDS', [
        'password',
        'token',
        'secret',
        'key',
        'authorization',
        'cookie',
        'x-api-key',
      ]),
      excludePaths: this.configService.get('LOGGING_EXCLUDE_PATHS', [
        '/health',
        '/metrics',
        '/favicon.ico',
      ]),
    };
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (!this.config.enabled) {
      return next.handle();
    }

    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    const response = ctx.getResponse<Response>();

    // 检查是否需要排除此路径
    if (this.shouldExcludePath(request.path)) {
      return next.handle();
    }

    const startTime = Date.now();
    const requestId = this.getRequestId(request);

    // 记录请求日志
    this.logRequest(request, requestId);

    return next.handle().pipe(
      tap((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        this.logResponse(request, response, duration, requestId, data);
      }),
      catchError((error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        this.logError(request, response, duration, requestId, error);
        throw error;
      }),
    );
  }

  /**
   * 记录请求日志
   */
  private logRequest(request: Request, requestId: string): void {
    const requestLog: RequestLog = {
      requestId,
      method: request.method,
      url: request.url,
      path: request.path,
      timestamp: new Date().toISOString(),
    };

    // 添加查询参数
    if (this.config.includeQuery && Object.keys(request.query).length > 0) {
      requestLog.query = this.sanitizeData(request.query);
    }

    // 添加请求头
    if (this.config.includeHeaders) {
      requestLog.headers = this.sanitizeHeaders(request.headers);
    }

    // 添加请求体
    if (this.config.includeBody && request.body) {
      requestLog.body = this.sanitizeBody(request.body);
    }

    // 添加IP地址
    if (this.config.includeIp) {
      requestLog.ip = this.getClientIp(request);
    }

    // 添加User-Agent
    if (this.config.includeUserAgent) {
      requestLog.userAgent = request.get('User-Agent');
    }

    // 添加用户信息（如果存在）
    if ((request as any).user) {
      requestLog.userId = (request as any).user.id;
      requestLog.sessionId = (request as any).user.sessionId;
    }

    this.logger.log(`📥 ${request.method} ${request.path}`, requestLog);
  }

  /**
   * 记录响应日志
   */
  private logResponse(
    request: Request,
    response: Response,
    duration: number,
    requestId: string,
    data?: any,
  ): void {
    const responseLog: ResponseLog = {
      requestId,
      statusCode: response.statusCode,
      duration,
      timestamp: new Date().toISOString(),
    };

    // 添加响应头
    if (this.config.includeHeaders) {
      responseLog.headers = this.sanitizeHeaders(response.getHeaders());
    }

    // 添加响应体
    if (this.config.includeBody && data) {
      responseLog.body = this.sanitizeBody(data);
    }

    // 添加响应大小
    const contentLength = response.get('Content-Length');
    if (contentLength) {
      responseLog.responseSize = parseInt(contentLength, 10);
    }

    const logLevel = this.getResponseLogLevel(response.statusCode, duration);
    const message = `📤 ${request.method} ${request.path} - ${response.statusCode} (${duration}ms)`;

    switch (logLevel) {
      case LogLevel.ERROR:
        this.logger.error(message, responseLog);
        break;
      case LogLevel.WARN:
        this.logger.warn(message, responseLog);
        break;
      case LogLevel.DEBUG:
        this.logger.debug(message, responseLog);
        break;
      case LogLevel.VERBOSE:
        this.logger.verbose(message, responseLog);
        break;
      default:
        this.logger.log(message, responseLog);
    }
  }

  /**
   * 记录错误日志
   */
  private logError(
    request: Request,
    response: Response,
    duration: number,
    requestId: string,
    error: any,
  ): void {
    const responseLog: ResponseLog = {
      requestId,
      statusCode: response.statusCode || 500,
      duration,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      timestamp: new Date().toISOString(),
    };

    const message = `❌ ${request.method} ${request.path} - ${responseLog.statusCode} (${duration}ms)`;
    this.logger.error(message, responseLog);
  }

  /**
   * 获取请求ID
   */
  private getRequestId(request: Request): string {
    return (request.headers['x-request-id'] as string) || 
           `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }

  /**
   * 清理敏感数据
   */
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = { ...data };
    
    for (const field of this.config.sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * 清理请求头
   */
  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    
    for (const field of this.config.sensitiveFields) {
      const lowerField = field.toLowerCase();
      if (sanitized[lowerField]) {
        sanitized[lowerField] = '[REDACTED]';
      }
    }

    return sanitized;
  }

  /**
   * 清理请求体
   */
  private sanitizeBody(body: any): any {
    if (!body) {
      return body;
    }

    let sanitized = this.sanitizeData(body);
    
    // 限制body长度
    const bodyString = JSON.stringify(sanitized);
    if (bodyString.length > this.config.maxBodyLength) {
      sanitized = {
        ...sanitized,
        _truncated: true,
        _originalLength: bodyString.length,
        _maxLength: this.config.maxBodyLength,
      };
      
      const truncatedString = bodyString.substring(0, this.config.maxBodyLength);
      try {
        sanitized = JSON.parse(truncatedString);
      } catch {
        sanitized = truncatedString + '... [TRUNCATED]';
      }
    }

    return sanitized;
  }

  /**
   * 获取响应日志级别
   */
  private getResponseLogLevel(statusCode: number, duration: number): LogLevel {
    if (statusCode >= 500) {
      return LogLevel.ERROR;
    }
    
    if (statusCode >= 400) {
      return LogLevel.WARN;
    }
    
    if (duration > 5000) {
      return LogLevel.WARN;
    }
    
    if (duration > 1000) {
      return LogLevel.DEBUG;
    }
    
    return LogLevel.INFO;
  }

  /**
   * 检查是否应该排除此路径
   */
  private shouldExcludePath(path: string): boolean {
    return this.config.excludePaths.some(excludePath => 
      path.startsWith(excludePath)
    );
  }
}

/**
 * 安全日志拦截器
 * 专门记录安全相关的日志
 */
@Injectable()
export class SecurityLoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger('SecurityLogger');

  constructor(private readonly configService: ConfigService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const ctx = context.switchToHttp();
    const request = ctx.getRequest<Request>();
    
    // 只记录安全相关的端点
    if (!this.isSecurityEndpoint(request.path)) {
      return next.handle();
    }

    const startTime = Date.now();
    const requestId = this.getRequestId(request);
    const clientIp = this.getClientIp(request);
    const userAgent = request.get('User-Agent');

    // 记录安全请求
    this.logSecurityRequest(request, requestId, clientIp, userAgent);

    return next.handle().pipe(
      tap((data) => {
        const duration = Date.now() - startTime;
        this.logSecurityResponse(request, requestId, clientIp, duration, true);
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        this.logSecurityResponse(request, requestId, clientIp, duration, false, error);
        throw error;
      }),
    );
  }

  /**
   * 检查是否为安全端点
   */
  private isSecurityEndpoint(path: string): boolean {
    const securityPaths = [
      '/auth/login',
      '/auth/logout',
      '/auth/register',
      '/auth/refresh',
      '/auth/reset-password',
      '/auth/confirm-reset',
      '/users/me/password',
      '/users/me/mfa',
    ];

    return securityPaths.some(securityPath => path.startsWith(securityPath));
  }

  /**
   * 记录安全请求
   */
  private logSecurityRequest(
    request: Request,
    requestId: string,
    clientIp: string,
    userAgent?: string,
  ): void {
    const securityLog = {
      type: 'SECURITY_REQUEST',
      requestId,
      method: request.method,
      path: request.path,
      clientIp,
      userAgent,
      timestamp: new Date().toISOString(),
    };

    this.logger.log('🔒 Security Request', securityLog);
  }

  /**
   * 记录安全响应
   */
  private logSecurityResponse(
    request: Request,
    requestId: string,
    clientIp: string,
    duration: number,
    success: boolean,
    error?: any,
  ): void {
    const securityLog = {
      type: success ? 'SECURITY_SUCCESS' : 'SECURITY_FAILURE',
      requestId,
      method: request.method,
      path: request.path,
      clientIp,
      duration,
      success,
      error: error ? {
        name: error.name,
        message: error.message,
      } : undefined,
      timestamp: new Date().toISOString(),
    };

    if (success) {
      this.logger.log('🔓 Security Success', securityLog);
    } else {
      this.logger.warn('🚨 Security Failure', securityLog);
    }
  }

  /**
   * 获取请求ID
   */
  private getRequestId(request: Request): string {
    return (request.headers['x-request-id'] as string) || 
           `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取客户端IP
   */
  private getClientIp(request: Request): string {
    return (
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      (request.headers['x-real-ip'] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      'unknown'
    );
  }
}
