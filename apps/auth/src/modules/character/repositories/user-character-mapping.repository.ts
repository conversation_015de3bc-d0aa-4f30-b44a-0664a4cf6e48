import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery } from 'mongoose';
import { UserCharacterMapping, UserCharacterMappingDocument } from '../entities/user-character-mapping.entity';

/**
 * 用户角色映射Repository
 *
 * 提供用户角色关系的CRUD操作和查询方法
 */
@Injectable()
export class UserCharacterMappingRepository {
  constructor(
    @InjectModel(UserCharacterMapping.name)
    private readonly mappingModel: Model<UserCharacterMappingDocument>,
  ) {}

  /**
   * 创建用户角色映射
   */
  async create(mappingData: Partial<UserCharacterMapping>): Promise<UserCharacterMappingDocument> {
    const mapping = new this.mappingModel(mappingData);
    return await mapping.save();
  }

  /**
   * 根据条件查找单个映射
   */
  async findOne(filter: FilterQuery<UserCharacterMappingDocument>): Promise<UserCharacterMappingDocument | null> {
    return await this.mappingModel.findOne(filter).exec();
  }

  /**
   * 根据条件查找多个映射
   */
  async find(filter: FilterQuery<UserCharacterMappingDocument>): Promise<UserCharacterMappingDocument[]> {
    return await this.mappingModel.find(filter).exec();
  }

  /**
   * 根据用户ID和区服ID查找映射
   */
  async findByUserAndServer(userId: string, serverId: string): Promise<UserCharacterMappingDocument | null> {
    return await this.mappingModel.findOne({ userId, serverId }).exec();
  }

  /**
   * 根据角色ID查找映射
   */
  async findByCharacterId(characterId: string): Promise<UserCharacterMappingDocument | null> {
    return await this.mappingModel.findOne({ characterId }).exec();
  }

  /**
   * 根据用户ID查找所有映射
   */
  async findByUserId(userId: string): Promise<UserCharacterMappingDocument[]> {
    return await this.mappingModel
      .find({ userId })
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * 更新映射信息
   */
  async update(id: string, updateData: Partial<UserCharacterMapping>): Promise<UserCharacterMappingDocument> {
    const updated = await this.mappingModel
      .findByIdAndUpdate(id, { $set: updateData }, { new: true })
      .exec();

    if (!updated) {
      throw new Error(`Mapping ${id} not found after update`);
    }
    return updated;
  }

  /**
   * 更新最后访问时间
   */
  async updateLastAccessed(characterId: string): Promise<void> {
    await this.mappingModel
      .updateOne(
        { characterId },
        {
          $set: { lastAccessedAt: new Date() },
          $inc: { accessCount: 1 },
        }
      )
      .exec();
  }

  /**
   * 删除映射
   */
  async delete(id: string): Promise<void> {
    await this.mappingModel.findByIdAndDelete(id).exec();
  }

  /**
   * 根据角色ID删除映射
   */
  async deleteByCharacterId(characterId: string): Promise<void> {
    await this.mappingModel.deleteOne({ characterId }).exec();
  }

  /**
   * 根据用户ID删除所有映射
   */
  async deleteByUserId(userId: string): Promise<void> {
    await this.mappingModel.deleteMany({ userId }).exec();
  }

  /**
   * 检查用户在区服是否已有角色
   */
  async hasCharacterInServer(userId: string, serverId: string): Promise<boolean> {
    const mapping = await this.mappingModel.findOne({ userId, serverId }).exec();
    return !!mapping;
  }

  /**
   * 统计用户的角色数量
   */
  async countUserCharacters(userId: string): Promise<number> {
    return await this.mappingModel.countDocuments({ userId }).exec();
  }

  /**
   * 获取区服内的用户数量
   */
  async countUsersInServer(serverId: string): Promise<number> {
    return await this.mappingModel.countDocuments({ serverId }).exec();
  }

  /**
   * 批量更新权限
   */
  async batchUpdatePermissions(
    characterIds: string[],
    permissions: UserCharacterMapping['permissions'],
  ): Promise<void> {
    await this.mappingModel
      .updateMany(
        { characterId: { $in: characterIds } },
        { $set: { permissions } }
      )
      .exec();
  }

  /**
   * 获取具有特定权限的角色映射
   */
  async findByPermission(permission: string, value: boolean = true): Promise<UserCharacterMappingDocument[]> {
    return await this.mappingModel
      .find({
        [`permissions.${permission}`]: value,
      })
      .exec();
  }
}
