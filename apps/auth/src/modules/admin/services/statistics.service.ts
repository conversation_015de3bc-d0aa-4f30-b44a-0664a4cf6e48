import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';

import { User, UserDocument } from '@auth/modules/user/entities/user.entity';
import { Session, SessionDocument } from '@auth/modules/session/entities/session.entity';
import { AuditLog, AuditLogDocument } from '@auth/modules/security/entities/audit-log.entity';

@Injectable()
export class StatisticsService {
  private readonly logger = new Logger(StatisticsService.name);

  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
    @InjectModel(AuditLog.name) private auditLogModel: Model<AuditLogDocument>,
  ) {}

  /**
   * 获取用户统计数据
   */
  async getUserStatistics(timeRange?: string): Promise<any> {
    this.logger.log(`获取用户统计数据: ${timeRange}`);

    try {
      const now = new Date();
      const timeRanges = this.getTimeRanges(timeRange, now);

      const [
        totalUsers,
        activeUsers,
        newUsers,
        usersByRole,
        usersByStatus,
        userGrowth,
        topCountries,
        ageDistribution,
      ] = await Promise.all([
        this.getTotalUsers(),
        this.getActiveUsers(timeRanges.start),
        this.getNewUsers(timeRanges.start, timeRanges.end),
        this.getUsersByRole(),
        this.getUsersByStatus(),
        this.getUserGrowth(timeRanges.start, timeRanges.end),
        this.getTopCountries(),
        this.getAgeDistribution(),
      ]);

      return {
        overview: {
          total: totalUsers,
          active: activeUsers,
          new: newUsers,
          growthRate: this.calculateGrowthRate(userGrowth),
        },
        demographics: {
          byRole: usersByRole,
          byStatus: usersByStatus,
          byCountry: topCountries,
          byAge: ageDistribution,
        },
        growth: userGrowth,
        timeRange: timeRange || '30d',
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error('获取用户统计数据失败', error);
      throw error;
    }
  }

  /**
   * 获取会话统计数据
   */
  async getSessionStatistics(timeRange?: string): Promise<any> {
    this.logger.log(`获取会话统计数据: ${timeRange}`);

    try {
      const now = new Date();
      const timeRanges = this.getTimeRanges(timeRange, now);

      const [
        totalSessions,
        activeSessions,
        sessionsByDevice,
        sessionsByLocation,
        averageDuration,
        sessionTrends,
        peakHours,
      ] = await Promise.all([
        this.getTotalSessions(timeRanges.start, timeRanges.end),
        this.getActiveSessions(),
        this.getSessionsByDevice(timeRanges.start, timeRanges.end),
        this.getSessionsByLocation(timeRanges.start, timeRanges.end),
        this.getAverageSessionDuration(timeRanges.start, timeRanges.end),
        this.getSessionTrends(timeRanges.start, timeRanges.end),
        this.getPeakHours(timeRanges.start, timeRanges.end),
      ]);

      return {
        overview: {
          total: totalSessions,
          active: activeSessions,
          averageDuration: averageDuration,
        },
        breakdown: {
          byDevice: sessionsByDevice,
          byLocation: sessionsByLocation,
        },
        trends: sessionTrends,
        peakHours: peakHours,
        timeRange: timeRange || '30d',
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error('获取会话统计数据失败', error);
      throw error;
    }
  }

  /**
   * 获取安全统计数据
   */
  async getSecurityStatistics(timeRange?: string): Promise<any> {
    this.logger.log(`获取安全统计数据: ${timeRange}`);

    try {
      const now = new Date();
      const timeRanges = this.getTimeRanges(timeRange, now);

      const [
        securityEvents,
        failedLogins,
        blockedIPs,
        suspiciousActivities,
        mfaAdoption,
        securityTrends,
        topThreats,
      ] = await Promise.all([
        this.getSecurityEvents(timeRanges.start, timeRanges.end),
        this.getFailedLogins(timeRanges.start, timeRanges.end),
        this.getBlockedIPs(timeRanges.start, timeRanges.end),
        this.getSuspiciousActivities(timeRanges.start, timeRanges.end),
        this.getMfaAdoption(),
        this.getSecurityTrends(timeRanges.start, timeRanges.end),
        this.getTopThreats(timeRanges.start, timeRanges.end),
      ]);

      return {
        overview: {
          securityEvents: securityEvents.total,
          failedLogins: failedLogins.total,
          blockedIPs: blockedIPs.length,
          suspiciousActivities: suspiciousActivities.total,
          mfaAdoptionRate: mfaAdoption.rate,
        },
        details: {
          securityEvents: securityEvents.breakdown,
          failedLogins: failedLogins.breakdown,
          blockedIPs: blockedIPs,
          suspiciousActivities: suspiciousActivities.breakdown,
          mfaAdoption: mfaAdoption,
        },
        trends: securityTrends,
        threats: topThreats,
        timeRange: timeRange || '30d',
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error('获取安全统计数据失败', error);
      throw error;
    }
  }

  /**
   * 获取系统性能统计
   */
  async getPerformanceStatistics(timeRange?: string): Promise<any> {
    this.logger.log(`获取系统性能统计: ${timeRange}`);

    try {
      const now = new Date();
      const timeRanges = this.getTimeRanges(timeRange, now);

      // 这里应该从监控系统获取实际数据
      // 暂时返回模拟数据
      return {
        overview: {
          averageResponseTime: 120,
          requestsPerSecond: 50,
          errorRate: 0.5,
          uptime: 99.9,
        },
        metrics: {
          responseTime: this.generateTimeSeriesData(timeRanges.start, timeRanges.end, 100, 200),
          throughput: this.generateTimeSeriesData(timeRanges.start, timeRanges.end, 40, 60),
          errorRate: this.generateTimeSeriesData(timeRanges.start, timeRanges.end, 0, 2),
          cpuUsage: this.generateTimeSeriesData(timeRanges.start, timeRanges.end, 30, 70),
          memoryUsage: this.generateTimeSeriesData(timeRanges.start, timeRanges.end, 50, 80),
        },
        timeRange: timeRange || '30d',
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error('获取系统性能统计失败', error);
      throw error;
    }
  }

  /**
   * 获取业务统计数据
   */
  async getBusinessStatistics(timeRange?: string): Promise<any> {
    this.logger.log(`获取业务统计数据: ${timeRange}`);

    try {
      const now = new Date();
      const timeRanges = this.getTimeRanges(timeRange, now);

      // 这里应该从业务数据库获取实际数据
      // 暂时返回模拟数据
      return {
        overview: {
          totalTeams: 1250,
          activeTeams: 980,
          totalMatches: 5600,
          totalTransfers: 890,
        },
        trends: {
          teamCreation: this.generateTimeSeriesData(timeRanges.start, timeRanges.end, 5, 15),
          matchActivity: this.generateTimeSeriesData(timeRanges.start, timeRanges.end, 20, 50),
          transferActivity: this.generateTimeSeriesData(timeRanges.start, timeRanges.end, 2, 8),
        },
        topLeagues: [
          { name: '英超联赛', teams: 20, matches: 380 },
          { name: '西甲联赛', teams: 20, matches: 380 },
          { name: '德甲联赛', teams: 18, matches: 306 },
        ],
        timeRange: timeRange || '30d',
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error('获取业务统计数据失败', error);
      throw error;
    }
  }

  // 私有方法

  private getTimeRanges(timeRange: string, now: Date): { start: Date; end: Date } {
    const end = now;
    let start: Date;

    switch (timeRange) {
      case '1d':
        start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    return { start, end };
  }

  private async getTotalUsers(): Promise<number> {
    return this.userModel.countDocuments();
  }

  private async getActiveUsers(since: Date): Promise<number> {
    return this.userModel.countDocuments({
      lastLoginAt: { $gte: since },
    });
  }

  private async getNewUsers(start: Date, end: Date): Promise<number> {
    return this.userModel.countDocuments({
      createdAt: { $gte: start, $lte: end },
    });
  }

  private async getUsersByRole(): Promise<any[]> {
    const result = await this.userModel.aggregate([
      { $unwind: '$roles' },
      { $group: { _id: '$roles', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
    ]);

    return result.map(item => ({
      role: item._id,
      count: item.count,
    }));
  }

  private async getUsersByStatus(): Promise<any[]> {
    const result = await this.userModel.aggregate([
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
    ]);

    return result.map(item => ({
      status: item._id,
      count: item.count,
    }));
  }

  private async getUserGrowth(start: Date, end: Date): Promise<any[]> {
    const result = await this.userModel.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } },
    ]);

    return result.map(item => ({
      date: new Date(item._id.year, item._id.month - 1, item._id.day),
      count: item.count,
    }));
  }

  private async getTopCountries(): Promise<any[]> {
    // 这里应该从用户的地理位置信息获取
    // 暂时返回模拟数据
    return [
      { country: 'CN', name: '中国', count: 450 },
      { country: 'US', name: '美国', count: 320 },
      { country: 'GB', name: '英国', count: 180 },
      { country: 'DE', name: '德国', count: 150 },
      { country: 'FR', name: '法国', count: 120 },
    ];
  }

  private async getAgeDistribution(): Promise<any[]> {
    // 这里应该从用户的年龄信息计算
    // 暂时返回模拟数据
    return [
      { ageGroup: '18-25', count: 320 },
      { ageGroup: '26-35', count: 450 },
      { ageGroup: '36-45', count: 280 },
      { ageGroup: '46-55', count: 150 },
      { ageGroup: '55+', count: 80 },
    ];
  }

  private async getTotalSessions(start: Date, end: Date): Promise<number> {
    return this.sessionModel.countDocuments({
      createdAt: { $gte: start, $lte: end },
    });
  }

  private async getActiveSessions(): Promise<number> {
    return this.sessionModel.countDocuments({ active: true });
  }

  private async getSessionsByDevice(start: Date, end: Date): Promise<any[]> {
    const result = await this.sessionModel.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
        },
      },
      {
        $group: {
          _id: '$deviceInfo.type',
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
    ]);

    return result.map(item => ({
      device: item._id || 'unknown',
      count: item.count,
    }));
  }

  private async getSessionsByLocation(start: Date, end: Date): Promise<any[]> {
    // 这里应该从会话的地理位置信息获取
    // 暂时返回模拟数据
    return [
      { country: 'CN', count: 180 },
      { country: 'US', count: 120 },
      { country: 'GB', count: 80 },
      { country: 'DE', count: 60 },
      { country: 'FR', count: 45 },
    ];
  }

  private async getAverageSessionDuration(start: Date, end: Date): Promise<number> {
    const result = await this.sessionModel.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
          endedAt: { $exists: true },
        },
      },
      {
        $project: {
          duration: {
            $subtract: ['$endedAt', '$createdAt'],
          },
        },
      },
      {
        $group: {
          _id: null,
          averageDuration: { $avg: '$duration' },
        },
      },
    ]);

    return result.length > 0 ? Math.round(result[0].averageDuration / 1000) : 0; // 转换为秒
  }

  private async getSessionTrends(start: Date, end: Date): Promise<any[]> {
    const result = await this.sessionModel.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } },
    ]);

    return result.map(item => ({
      date: new Date(item._id.year, item._id.month - 1, item._id.day),
      count: item.count,
    }));
  }

  private async getPeakHours(start: Date, end: Date): Promise<any[]> {
    const result = await this.sessionModel.aggregate([
      {
        $match: {
          createdAt: { $gte: start, $lte: end },
        },
      },
      {
        $group: {
          _id: { $hour: '$createdAt' },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id': 1 } },
    ]);

    return result.map(item => ({
      hour: item._id,
      count: item.count,
    }));
  }

  private async getSecurityEvents(start: Date, end: Date): Promise<any> {
    const total = await this.auditLogModel.countDocuments({
      type: { $in: ['security_alert', 'failed_login', 'suspicious_activity'] },
      createdAt: { $gte: start, $lte: end },
    });

    const breakdown = await this.auditLogModel.aggregate([
      {
        $match: {
          type: { $in: ['security_alert', 'failed_login', 'suspicious_activity'] },
          createdAt: { $gte: start, $lte: end },
        },
      },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
        },
      },
    ]);

    return {
      total,
      breakdown: breakdown.map(item => ({
        type: item._id,
        count: item.count,
      })),
    };
  }

  private async getFailedLogins(start: Date, end: Date): Promise<any> {
    const total = await this.auditLogModel.countDocuments({
      type: 'failed_login',
      createdAt: { $gte: start, $lte: end },
    });

    const breakdown = await this.auditLogModel.aggregate([
      {
        $match: {
          type: 'failed_login',
          createdAt: { $gte: start, $lte: end },
        },
      },
      {
        $group: {
          _id: '$ipAddress',
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
      { $limit: 10 },
    ]);

    return {
      total,
      breakdown: breakdown.map(item => ({
        ip: item._id,
        count: item.count,
      })),
    };
  }

  private async getBlockedIPs(start: Date, end: Date): Promise<string[]> {
    // 这里应该从安全服务获取被阻止的IP列表
    // 暂时返回模拟数据
    return ['*************', '*********', '***********'];
  }

  private async getSuspiciousActivities(start: Date, end: Date): Promise<any> {
    const total = await this.auditLogModel.countDocuments({
      type: 'suspicious_activity',
      createdAt: { $gte: start, $lte: end },
    });

    const breakdown = await this.auditLogModel.aggregate([
      {
        $match: {
          type: 'suspicious_activity',
          createdAt: { $gte: start, $lte: end },
        },
      },
      {
        $group: {
          _id: '$details.activityType',
          count: { $sum: 1 },
        },
      },
      { $sort: { count: -1 } },
    ]);

    return {
      total,
      breakdown: breakdown.map(item => ({
        activityType: item._id || 'unknown',
        count: item.count,
      })),
    };
  }

  private async getMfaAdoption(): Promise<any> {
    const totalUsers = await this.userModel.countDocuments();
    const mfaUsers = await this.userModel.countDocuments({
      'security.mfaEnabled': true,
    });

    return {
      total: totalUsers,
      enabled: mfaUsers,
      rate: totalUsers > 0 ? (mfaUsers / totalUsers) * 100 : 0,
    };
  }

  private async getSecurityTrends(start: Date, end: Date): Promise<any[]> {
    const result = await this.auditLogModel.aggregate([
      {
        $match: {
          type: { $in: ['security_alert', 'failed_login', 'suspicious_activity'] },
          createdAt: { $gte: start, $lte: end },
        },
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' },
          },
          count: { $sum: 1 },
        },
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } },
    ]);

    return result.map(item => ({
      date: new Date(item._id.year, item._id.month - 1, item._id.day),
      count: item.count,
    }));
  }

  private async getTopThreats(start: Date, end: Date): Promise<any[]> {
    // 这里应该从安全分析系统获取威胁信息
    // 暂时返回模拟数据
    return [
      { type: 'brute_force', count: 25, severity: 'high' },
      { type: 'suspicious_login', count: 18, severity: 'medium' },
      { type: 'unusual_activity', count: 12, severity: 'low' },
    ];
  }

  private calculateGrowthRate(growthData: any[]): number {
    if (growthData.length < 2) return 0;

    const recent = growthData.slice(-7).reduce((sum, item) => sum + item.count, 0);
    const previous = growthData.slice(-14, -7).reduce((sum, item) => sum + item.count, 0);

    if (previous === 0) return 0;
    return ((recent - previous) / previous) * 100;
  }

  private generateTimeSeriesData(start: Date, end: Date, min: number, max: number): any[] {
    const data = [];
    const current = new Date(start);
    const oneDay = 24 * 60 * 60 * 1000;

    while (current <= end) {
      data.push({
        date: new Date(current),
        value: Math.floor(Math.random() * (max - min + 1)) + min,
      });
      current.setTime(current.getTime() + oneDay);
    }

    return data;
  }
}
