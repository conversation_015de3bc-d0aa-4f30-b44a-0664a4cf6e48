import { ApiProperty } from '@nestjs/swagger';

/**
 * 会话信息数据DTO
 */
export class SessionInfoDataDto {
  @ApiProperty({ description: '会话ID', example: 'session_123456789' })
  sessionId: string;

  @ApiProperty({ description: '用户ID', example: 'user_123456' })
  userId: string;

  @ApiProperty({ description: '角色ID', example: 'char_123456' })
  characterId: string;

  @ApiProperty({ description: '区服ID', example: 'server_001' })
  serverId: string;

  @ApiProperty({ description: '区服名称', example: '新手村' })
  serverName: string;

  @ApiProperty({ description: '最后活动时间', example: '2024-01-15T10:25:00.000Z' })
  lastActivity: Date;

  @ApiProperty({ description: '会话过期时间', example: '2024-01-15T14:30:00.000Z' })
  expiresAt: Date;
}

/**
 * 会话信息响应DTO
 */
export class SessionInfoResponseDto {
  @ApiProperty({ description: '请求是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '获取会话信息成功' })
  message: string;

  @ApiProperty({ description: '会话信息数据', type: SessionInfoDataDto })
  data: SessionInfoDataDto;
}
