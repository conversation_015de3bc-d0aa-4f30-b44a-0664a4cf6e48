import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Permission, PermissionDocument } from '../entities/permission.entity';

export interface CreatePermissionData {
  name: string;
  displayName: string;
  description?: string;
  resource: string;
  action: string;
  category?: string;
  level?: number;
  enabled?: boolean;
  system?: boolean;
}

export interface UpdatePermissionData {
  displayName?: string;
  description?: string;
  resource?: string;
  action?: string;
  category?: string;
  level?: number;
  enabled?: boolean;
}

export interface PermissionQuery {
  name?: string;
  resource?: string;
  action?: string;
  category?: string;
  enabled?: boolean;
  system?: boolean;
  level?: { min?: number; max?: number };
  limit?: number;
  offset?: number;
}

/**
 * 权限仓储接口
 */
export interface IPermissionRepository {
  create(permissionData: CreatePermissionData): Promise<PermissionDocument>;
  findById(id: string): Promise<PermissionDocument | null>;
  findByName(name: string): Promise<PermissionDocument | null>;
  findAll(query?: PermissionQuery): Promise<PermissionDocument[]>;
  update(id: string, updateData: UpdatePermissionData): Promise<PermissionDocument | null>;
  delete(id: string): Promise<void>;
  findSystemPermissions(): Promise<PermissionDocument[]>;
  findByResource(resource: string): Promise<PermissionDocument[]>;
  findByCategory(category: string): Promise<PermissionDocument[]>;
  findByResourceAndAction(resource: string, action: string): Promise<PermissionDocument | null>;
  count(query?: PermissionQuery): Promise<number>;
}

/**
 * 权限仓储实现
 * 
 * 负责权限数据的持久化操作，包括：
 * - 权限的CRUD操作
 * - 权限查询和分类
 * - 系统权限管理
 * - 权限统计分析
 */
@Injectable()
export class PermissionRepository implements IPermissionRepository {
  private readonly logger = new Logger(PermissionRepository.name);

  constructor(
    @InjectModel(Permission.name) private permissionModel: Model<PermissionDocument>,
  ) {}

  /**
   * 创建权限
   */
  async create(permissionData: CreatePermissionData): Promise<PermissionDocument> {
    const permission = new this.permissionModel({
      ...permissionData,
      name: permissionData.name.toLowerCase(),
      enabled: permissionData.enabled !== undefined ? permissionData.enabled : true,
      system: permissionData.system || false,
      level: permissionData.level || 1,
    });
    return await permission.save();
  }

  /**
   * 根据ID查找权限
   */
  async findById(id: string): Promise<PermissionDocument | null> {
    return await this.permissionModel.findById(id).exec();
  }

  /**
   * 根据名称查找权限
   */
  async findByName(name: string): Promise<PermissionDocument | null> {
    return await this.permissionModel.findOne({ 
      name: name.toLowerCase() 
    }).exec();
  }

  /**
   * 查找所有权限
   */
  async findAll(query: PermissionQuery = {}): Promise<PermissionDocument[]> {
    const filter: any = {};

    if (query.name) filter.name = new RegExp(query.name, 'i');
    if (query.resource) filter.resource = query.resource;
    if (query.action) filter.action = query.action;
    if (query.category) filter.category = query.category;
    if (query.enabled !== undefined) filter.enabled = query.enabled;
    if (query.system !== undefined) filter.system = query.system;
    
    if (query.level) {
      filter.level = {};
      if (query.level.min !== undefined) filter.level.$gte = query.level.min;
      if (query.level.max !== undefined) filter.level.$lte = query.level.max;
    }

    return await this.permissionModel
      .find(filter)
      .sort({ category: 1, level: 1, name: 1 })
      .limit(query.limit || 100)
      .skip(query.offset || 0)
      .exec();
  }

  /**
   * 更新权限
   */
  async update(id: string, updateData: UpdatePermissionData): Promise<PermissionDocument | null> {
    return await this.permissionModel.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true }
    ).exec();
  }

  /**
   * 删除权限
   */
  async delete(id: string): Promise<void> {
    await this.permissionModel.findByIdAndDelete(id).exec();
  }

  /**
   * 查找系统权限
   */
  async findSystemPermissions(): Promise<PermissionDocument[]> {
    return await this.permissionModel.find({ 
      system: true, 
      enabled: true 
    }).sort({ category: 1, level: 1 }).exec();
  }

  /**
   * 根据资源查找权限
   */
  async findByResource(resource: string): Promise<PermissionDocument[]> {
    return await this.permissionModel.find({
      resource,
      enabled: true
    }).sort({ level: 1, action: 1 }).exec();
  }

  /**
   * 根据分类查找权限
   */
  async findByCategory(category: string): Promise<PermissionDocument[]> {
    return await this.permissionModel.find({
      category,
      enabled: true
    }).sort({ level: 1, name: 1 }).exec();
  }

  /**
   * 根据资源和操作查找权限
   */
  async findByResourceAndAction(resource: string, action: string): Promise<PermissionDocument | null> {
    return await this.permissionModel.findOne({
      resource,
      action,
      enabled: true
    }).exec();
  }

  /**
   * 统计权限数量
   */
  async count(query: PermissionQuery = {}): Promise<number> {
    const filter: any = {};

    if (query.name) filter.name = new RegExp(query.name, 'i');
    if (query.resource) filter.resource = query.resource;
    if (query.action) filter.action = query.action;
    if (query.category) filter.category = query.category;
    if (query.enabled !== undefined) filter.enabled = query.enabled;
    if (query.system !== undefined) filter.system = query.system;
    
    if (query.level) {
      filter.level = {};
      if (query.level.min !== undefined) filter.level.$gte = query.level.min;
      if (query.level.max !== undefined) filter.level.$lte = query.level.max;
    }

    return await this.permissionModel.countDocuments(filter);
  }
}
