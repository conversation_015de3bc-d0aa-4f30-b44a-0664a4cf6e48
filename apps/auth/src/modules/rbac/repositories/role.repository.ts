import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Role, RoleDocument } from '../entities/role.entity';

export interface CreateRoleData {
  name: string;
  displayName: string;
  description?: string;
  permissions: string[];
  priority?: number;
  enabled?: boolean;
  system?: boolean;
  constraints?: any;
}

export interface UpdateRoleData {
  displayName?: string;
  description?: string;
  permissions?: string[];
  priority?: number;
  enabled?: boolean;
  constraints?: any;
}

export interface RoleQuery {
  name?: string;
  enabled?: boolean;
  system?: boolean;
  priority?: { min?: number; max?: number };
  limit?: number;
  offset?: number;
}

/**
 * 角色仓储接口
 */
export interface IRoleRepository {
  create(roleData: CreateRoleData): Promise<RoleDocument>;
  findById(id: string): Promise<RoleDocument | null>;
  findByName(name: string): Promise<RoleDocument | null>;
  findAll(query?: RoleQuery): Promise<RoleDocument[]>;
  update(id: string, updateData: UpdateRoleData): Promise<RoleDocument | null>;
  delete(id: string): Promise<void>;
  findSystemRoles(): Promise<RoleDocument[]>;
  findAssignableRoles(): Promise<RoleDocument[]>;
  updateUserCount(roleName: string, increment: number): Promise<void>;
  findByPermission(permission: string): Promise<RoleDocument[]>;
  count(query?: RoleQuery): Promise<number>;
}

/**
 * 角色仓储实现
 * 
 * 负责角色数据的持久化操作，包括：
 * - 角色的CRUD操作
 * - 角色权限关联管理
 * - 角色查询和统计
 * - 系统角色管理
 */
@Injectable()
export class RoleRepository implements IRoleRepository {
  private readonly logger = new Logger(RoleRepository.name);

  constructor(
    @InjectModel(Role.name) private roleModel: Model<RoleDocument>,
  ) {}

  /**
   * 创建角色
   */
  async create(roleData: CreateRoleData): Promise<RoleDocument> {
    const role = new this.roleModel({
      ...roleData,
      name: roleData.name.toLowerCase(),
      enabled: roleData.enabled !== undefined ? roleData.enabled : true,
      system: roleData.system || false,
      priority: roleData.priority || 0,
      userCount: 0,
    });
    return await role.save();
  }

  /**
   * 根据ID查找角色
   */
  async findById(id: string): Promise<RoleDocument | null> {
    return await this.roleModel.findById(id).exec();
  }

  /**
   * 根据名称查找角色
   */
  async findByName(name: string): Promise<RoleDocument | null> {
    return await this.roleModel.findOne({ 
      name: name.toLowerCase() 
    }).exec();
  }

  /**
   * 查找所有角色
   */
  async findAll(query: RoleQuery = {}): Promise<RoleDocument[]> {
    const filter: any = {};

    if (query.name) filter.name = new RegExp(query.name, 'i');
    if (query.enabled !== undefined) filter.enabled = query.enabled;
    if (query.system !== undefined) filter.system = query.system;
    
    if (query.priority) {
      filter.priority = {};
      if (query.priority.min !== undefined) filter.priority.$gte = query.priority.min;
      if (query.priority.max !== undefined) filter.priority.$lte = query.priority.max;
    }

    return await this.roleModel
      .find(filter)
      .sort({ priority: -1, name: 1 })
      .limit(query.limit || 100)
      .skip(query.offset || 0)
      .exec();
  }

  /**
   * 更新角色
   */
  async update(id: string, updateData: UpdateRoleData): Promise<RoleDocument | null> {
    return await this.roleModel.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true }
    ).exec();
  }

  /**
   * 删除角色
   */
  async delete(id: string): Promise<void> {
    await this.roleModel.findByIdAndDelete(id).exec();
  }

  /**
   * 查找系统角色
   */
  async findSystemRoles(): Promise<RoleDocument[]> {
    return await this.roleModel.find({ 
      system: true, 
      enabled: true 
    }).sort({ priority: -1 }).exec();
  }

  /**
   * 查找可分配角色
   */
  async findAssignableRoles(): Promise<RoleDocument[]> {
    return await this.roleModel.find({
      enabled: true,
      $or: [
        { 'constraints.maxUsers': { $exists: false } },
        { $expr: { $lt: ['$userCount', '$constraints.maxUsers'] } }
      ]
    }).sort({ priority: -1 }).exec();
  }

  /**
   * 更新角色用户数量
   */
  async updateUserCount(roleName: string, increment: number): Promise<void> {
    await this.roleModel.updateOne(
      { name: roleName.toLowerCase() },
      { $inc: { userCount: increment } }
    ).exec();
  }

  /**
   * 根据权限查找角色
   */
  async findByPermission(permission: string): Promise<RoleDocument[]> {
    return await this.roleModel.find({
      permissions: permission,
      enabled: true
    }).sort({ priority: -1 }).exec();
  }

  /**
   * 统计角色数量
   */
  async count(query: RoleQuery = {}): Promise<number> {
    const filter: any = {};

    if (query.name) filter.name = new RegExp(query.name, 'i');
    if (query.enabled !== undefined) filter.enabled = query.enabled;
    if (query.system !== undefined) filter.system = query.system;
    
    if (query.priority) {
      filter.priority = {};
      if (query.priority.min !== undefined) filter.priority.$gte = query.priority.min;
      if (query.priority.max !== undefined) filter.priority.$lte = query.priority.max;
    }

    return await this.roleModel.countDocuments(filter);
  }
}
