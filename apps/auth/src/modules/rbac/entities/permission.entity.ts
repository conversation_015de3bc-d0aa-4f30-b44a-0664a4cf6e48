import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

// 权限条件子文档
@Schema({ _id: false })
export class PermissionConditions {
  @ApiProperty({ description: '属性条件', required: false })
  @Prop({ type: Object })
  attributes?: Record<string, any>;

  @ApiProperty({ description: '时间范围', required: false })
  @Prop({
    type: {
      start: String,
      end: String,
    }
  })
  timeRange?: {
    start: string;
    end: string;
  };

  @ApiProperty({ description: 'IP范围', type: [String], required: false })
  @Prop({ type: [String] })
  ipRange?: string[];

  @ApiProperty({ description: '设备类型', type: [String], required: false })
  @Prop({ type: [String] })
  deviceTypes?: string[];

  @ApiProperty({ description: '地理位置限制', required: false })
  @Prop({
    type: {
      countries: [String],
      regions: [String],
    }
  })
  geoRestrictions?: {
    countries: string[];
    regions: string[];
  };
}

// 权限主文档
@Schema({
  timestamps: true,
  collection: 'permissions',
  toJSON: {
    transform: (doc, ret) => {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
})
export class Permission {

  @ApiProperty({ description: '权限名称', uniqueItems: true })
  @Prop({ 
    required: true, 
    unique: true, 
    trim: true, 
    lowercase: true,
    minlength: 3,
    maxlength: 100,
    match: /^[a-zA-Z0-9_:-]+$/
  })
  name: string;

  @ApiProperty({ description: '资源类型' })
  @Prop({ 
    required: true, 
    trim: true,
    lowercase: true,
    minlength: 2,
    maxlength: 50
  })
  resource: string;

  @ApiProperty({ description: '操作类型' })
  @Prop({ 
    required: true, 
    trim: true,
    lowercase: true,
    minlength: 2,
    maxlength: 50
  })
  action: string;

  @ApiProperty({ description: '权限显示名称' })
  @Prop({ 
    required: true, 
    trim: true,
    minlength: 2,
    maxlength: 100
  })
  displayName: string;

  @ApiProperty({ description: '权限描述' })
  @Prop({ 
    trim: true,
    maxlength: 500
  })
  description: string;

  @ApiProperty({ description: '权限条件', type: PermissionConditions })
  @Prop({ type: PermissionConditions })
  conditions?: PermissionConditions;

  @ApiProperty({ description: '权限分类' })
  @Prop({ 
    required: true,
    trim: true,
    default: 'general'
  })
  category: string;

  @ApiProperty({ description: '权限级别', default: 1 })
  @Prop({ 
    default: 1,
    min: 1,
    max: 10
  })
  level: number;

  @ApiProperty({ description: '是否系统权限', default: false })
  @Prop({ default: false })
  system: boolean;

  @ApiProperty({ description: '是否启用', default: true })
  @Prop({ default: true })
  enabled: boolean;

  @ApiProperty({ description: '是否危险权限', default: false })
  @Prop({ default: false })
  dangerous: boolean;

  @ApiProperty({ description: '依赖权限', type: [String] })
  @Prop({ type: [String], default: [] })
  dependencies: string[];

  @ApiProperty({ description: '互斥权限', type: [String] })
  @Prop({ type: [String], default: [] })
  conflicts: string[];

  @ApiProperty({ description: '权限标签', type: [String] })
  @Prop({ type: [String], default: [] })
  tags: string[];

  @ApiProperty({ description: '使用次数', default: 0 })
  @Prop({ default: 0, min: 0 })
  usageCount: number;

  @ApiProperty({ description: '创建时间' })
  createdAt: Date;

  @ApiProperty({ description: '更新时间' })
  updatedAt: Date;

  // 虚拟字段：完整权限名称
  get fullName(): string {
    return `${this.resource}:${this.action}`;
  }

  // 虚拟字段：是否有条件限制
  get hasConditions(): boolean {
    return !!(this.conditions && Object.keys(this.conditions).length > 0);
  }

  // 虚拟字段：风险等级
  get riskLevel(): 'low' | 'medium' | 'high' | 'critical' {
    if (this.dangerous) return 'critical';
    if (this.level >= 8) return 'high';
    if (this.level >= 5) return 'medium';
    return 'low';
  }
}

export const PermissionSchema = SchemaFactory.createForClass(Permission);

// 创建索引
PermissionSchema.index({ name: 1 }, { unique: true });
PermissionSchema.index({ resource: 1 });
PermissionSchema.index({ action: 1 });
PermissionSchema.index({ category: 1 });
PermissionSchema.index({ system: 1 });
PermissionSchema.index({ enabled: 1 });
PermissionSchema.index({ dangerous: 1 });
PermissionSchema.index({ level: 1 });
PermissionSchema.index({ usageCount: -1 });
PermissionSchema.index({ createdAt: 1 });

// 复合索引
PermissionSchema.index({ resource: 1, action: 1 }, { unique: true });
PermissionSchema.index({ category: 1, enabled: 1 });
PermissionSchema.index({ system: 1, enabled: 1 });
PermissionSchema.index({ dangerous: 1, enabled: 1 });

// 文本索引（用于搜索）
PermissionSchema.index({
  name: 'text',
  displayName: 'text',
  description: 'text',
  tags: 'text'
});

// 中间件：保存前处理
PermissionSchema.pre('save', function(next) {
  // 确保权限名称、资源和操作小写
  if (this.isModified('name')) {
    this.name = this.name.toLowerCase();
  }
  
  if (this.isModified('resource')) {
    this.resource = this.resource.toLowerCase();
  }
  
  if (this.isModified('action')) {
    this.action = this.action.toLowerCase();
  }
  
  // 自动生成权限名称（如果未提供）
  if (this.isNew && !this.name) {
    this.name = `${this.resource}:${this.action}`;
  }
  
  // 移除重复的依赖和冲突
  if (this.isModified('dependencies')) {
    this.dependencies = [...new Set(this.dependencies)];
  }
  
  if (this.isModified('conflicts')) {
    this.conflicts = [...new Set(this.conflicts)];
  }
  
  if (this.isModified('tags')) {
    this.tags = [...new Set(this.tags)];
  }
  
  next();
});

// 实例方法：检查是否与其他权限冲突
PermissionSchema.methods.conflictsWith = function(permissionName: string): boolean {
  return this.conflicts.includes(permissionName);
};

// 实例方法：检查是否依赖其他权限
PermissionSchema.methods.dependsOn = function(permissionName: string): boolean {
  return this.dependencies.includes(permissionName);
};

// 实例方法：增加使用次数
PermissionSchema.methods.incrementUsage = function(): Promise<Permission> {
  return this.updateOne({ $inc: { usageCount: 1 } });
};

// 静态方法：按资源获取权限
PermissionSchema.statics.getByResource = function(resource: string) {
  return this.find({ 
    resource: resource.toLowerCase(), 
    enabled: true 
  }).sort({ action: 1 });
};

// 静态方法：按分类获取权限
PermissionSchema.statics.getByCategory = function(category: string) {
  return this.find({ 
    category, 
    enabled: true 
  }).sort({ level: 1, name: 1 });
};

// 静态方法：获取系统权限
PermissionSchema.statics.getSystemPermissions = function() {
  return this.find({ 
    system: true, 
    enabled: true 
  }).sort({ category: 1, level: 1 });
};

// 静态方法：获取危险权限
PermissionSchema.statics.getDangerousPermissions = function() {
  return this.find({ 
    dangerous: true, 
    enabled: true 
  }).sort({ level: -1 });
};

// 静态方法：搜索权限
PermissionSchema.statics.search = function(query: string) {
  return this.find({
    $text: { $search: query },
    enabled: true
  }).sort({ score: { $meta: 'textScore' } });
};

// 权限文档接口，包含实例方法
export interface PermissionDocument extends Permission, Document {
  // 检查是否与其他权限冲突
  conflictsWith(permissionName: string): boolean;

  // 检查是否依赖其他权限
  dependsOn(permissionName: string): boolean;

  // 增加使用次数
  incrementUsage(): Promise<PermissionDocument>;
}
