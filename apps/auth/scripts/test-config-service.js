/**
 * ConfigService调试工具
 * 通过Auth服务的健康检查端点获取实际的配置信息
 */

const axios = require('axios');

const AUTH_BASE_URL = 'http://127.0.0.1:3001';

async function testConfigService() {
  console.log('🔍 ConfigService配置调试');
  console.log('=' .repeat(50));

  try {
    // 1. 测试健康检查端点（可能包含配置信息）
    console.log('\n📊 测试健康检查端点:');
    
    const healthResponse = await axios.get(`${AUTH_BASE_URL}/health/detailed`, {
      validateStatus: () => true,
      timeout: 5000
    });

    console.log(`状态码: ${healthResponse.status}`);
    
    if (healthResponse.status === 200) {
      console.log('健康检查响应:', JSON.stringify(healthResponse.data, null, 2));
    } else {
      console.log('健康检查失败:', healthResponse.data);
    }

    // 2. 测试限流配置端点（如果存在）
    console.log('\n🔧 测试限流配置:');
    
    // 发送一个登录请求并检查响应头
    const loginResponse = await axios.post(`${AUTH_BASE_URL}/auth/login`, {
      identifier: 'test',
      password: 'wrong'
    }, {
      validateStatus: () => true,
      timeout: 5000
    });

    console.log(`登录请求状态码: ${loginResponse.status}`);
    console.log('响应头:');
    
    // 检查所有响应头
    Object.keys(loginResponse.headers).forEach(header => {
      if (header.toLowerCase().includes('rate') || 
          header.toLowerCase().includes('limit') || 
          header.toLowerCase().includes('throttle')) {
        console.log(`  ${header}: ${loginResponse.headers[header]}`);
      }
    });

    // 3. 尝试触发限流
    console.log('\n🚀 尝试触发限流（发送6个快速请求）:');
    
    const promises = [];
    for (let i = 0; i < 6; i++) {
      promises.push(sendLoginRequest(i + 1));
    }

    const results = await Promise.all(promises);
    
    let rateLimitedCount = 0;
    let successCount = 0;
    
    results.forEach(result => {
      if (result.rateLimited) {
        rateLimitedCount++;
      } else if (result.success) {
        successCount++;
      }
    });

    console.log(`\n📊 快速测试结果:`);
    console.log(`正常处理: ${successCount}`);
    console.log(`被限流: ${rateLimitedCount}`);

    if (rateLimitedCount > 0) {
      console.log('✅ 限流功能正常工作！');
      
      // 显示限流响应的详细信息
      const rateLimitedResult = results.find(r => r.rateLimited);
      if (rateLimitedResult && rateLimitedResult.headers) {
        console.log('\n🔍 限流响应头:');
        Object.keys(rateLimitedResult.headers).forEach(header => {
          console.log(`  ${header}: ${rateLimitedResult.headers[header]}`);
        });
      }
    } else {
      console.log('❌ 限流功能未生效');
      
      // 分析可能的原因
      console.log('\n🔧 可能的原因分析:');
      console.log('1. ConfigService中NODE_ENV不是production');
      console.log('2. RATE_LIMIT_ENABLED不是true');
      console.log('3. 限流阈值设置过高');
      console.log('4. ThrottlerBehindProxyGuard未正确应用');
      
      // 建议检查步骤
      console.log('\n🔍 建议检查:');
      console.log('- 检查Auth服务启动日志中的环境信息');
      console.log('- 验证环境变量加载顺序');
      console.log('- 确认ThrottlerBehindProxyGuard的配置逻辑');
    }

  } catch (error) {
    console.error(`❌ 测试失败: ${error.message}`);
  }
}

async function sendLoginRequest(num) {
  try {
    const response = await axios.post(`${AUTH_BASE_URL}/auth/login`, {
      identifier: 'test',
      password: 'wrong'
    }, {
      validateStatus: () => true,
      timeout: 5000
    });
    
    const isRateLimited = response.status === 429;
    const isSuccess = response.status >= 200 && response.status < 500 && !isRateLimited;
    
    const status = isRateLimited ? '❌ 限流' : (isSuccess ? '✅ 正常' : '⚠️ 错误');
    console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ${status}`);
    
    return { 
      rateLimited: isRateLimited, 
      success: isSuccess, 
      status: response.status,
      headers: isRateLimited ? response.headers : null
    };
  } catch (error) {
    console.log(`  请求 ${num.toString().padStart(2, '0')}: 网络错误`);
    return { rateLimited: false, success: false, error: error.message };
  }
}

if (require.main === module) {
  testConfigService().catch(console.error);
}
