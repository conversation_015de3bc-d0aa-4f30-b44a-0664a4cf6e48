/**
 * 权限数据种子脚本
 * 创建足球经理游戏专用权限
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const database = require('./utils/database');
const config = require('./utils/config');
const Logger = require('./utils/logger');

// 加载环境变量
dotenv.config();

const logger = new Logger('SeedPermissions');

// 权限Schema
const permissionSchema = new mongoose.Schema({
  name: { type: String, unique: true, sparse: true },
  resource: { type: String, required: true },
  action: { type: String, required: true },
  displayName: { type: String, required: true },
  description: String,
  category: String,
  dangerous: { type: Boolean, default: false },
  level: { type: Number, default: 1 },
  enabled: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

// 添加复合索引
permissionSchema.index({ resource: 1, action: 1 }, { unique: true });

// 足球经理游戏权限定义
const gamePermissions = [
  // 基础游戏权限
  { resource: 'game', action: 'play', displayName: '参与游戏', description: '允许用户参与足球经理游戏', category: 'game_basic' },
  { resource: 'game', action: 'pause', displayName: '暂停游戏', description: '允许暂停游戏进程', category: 'game_basic' },
  { resource: 'game', action: 'save', displayName: '保存游戏', description: '允许保存游戏进度', category: 'game_basic' },
  { resource: 'game', action: 'load', displayName: '加载游戏', description: '允许加载游戏存档', category: 'game_basic' },

  // 团队管理权限
  { resource: 'team', action: 'create', displayName: '创建团队', description: '允许创建新的足球团队', category: 'team_management' },
  { resource: 'team', action: 'read', displayName: '查看团队', description: '允许查看团队信息', category: 'team_management' },
  { resource: 'team', action: 'update', displayName: '更新团队', description: '允许修改团队信息', category: 'team_management' },
  { resource: 'team', action: 'delete', displayName: '解散团队', description: '允许解散团队', category: 'team_management', dangerous: true },
  { resource: 'team', action: 'manage', displayName: '管理团队', description: '允许全面管理团队', category: 'team_management' },

  // 球员管理权限
  { resource: 'player', action: 'scout', displayName: '球探球员', description: '允许使用球探功能发现球员', category: 'player_management' },
  { resource: 'player', action: 'sign', displayName: '签约球员', description: '允许签约新球员', category: 'player_management' },
  { resource: 'player', action: 'release', displayName: '释放球员', description: '允许释放球员', category: 'player_management' },
  { resource: 'player', action: 'train', displayName: '训练球员', description: '允许安排球员训练', category: 'player_management' },
  { resource: 'player', action: 'trade', displayName: '交易球员', description: '允许进行球员交易', category: 'player_management' },

  // 比赛管理权限
  { resource: 'match', action: 'schedule', displayName: '安排比赛', description: '允许安排比赛日程', category: 'match_management' },
  { resource: 'match', action: 'lineup', displayName: '设置阵容', description: '允许设置比赛阵容', category: 'match_management' },
  { resource: 'match', action: 'tactics', displayName: '制定战术', description: '允许制定比赛战术', category: 'match_management' },
  { resource: 'match', action: 'substitute', displayName: '球员换人', description: '允许在比赛中进行换人', category: 'match_management' },
  { resource: 'match', action: 'watch', displayName: '观看比赛', description: '允许观看比赛直播', category: 'match_management' },

  // 转会市场权限
  { resource: 'transfer', action: 'bid', displayName: '竞价转会', description: '允许在转会市场竞价', category: 'transfer_market' },
  { resource: 'transfer', action: 'list', displayName: '挂牌球员', description: '允许将球员挂牌转会', category: 'transfer_market' },
  { resource: 'transfer', action: 'negotiate', displayName: '转会谈判', description: '允许进行转会谈判', category: 'transfer_market' },
  { resource: 'transfer', action: 'complete', displayName: '完成转会', description: '允许完成转会交易', category: 'transfer_market' },

  // 财务管理权限
  { resource: 'finance', action: 'view', displayName: '查看财务', description: '允许查看团队财务状况', category: 'finance_management' },
  { resource: 'finance', action: 'budget', displayName: '预算管理', description: '允许管理团队预算', category: 'finance_management' },
  { resource: 'finance', action: 'sponsor', displayName: '赞助管理', description: '允许管理赞助合同', category: 'finance_management' },
  { resource: 'finance', action: 'salary', displayName: '薪资管理', description: '允许管理球员薪资', category: 'finance_management' },

  // 设施管理权限
  { resource: 'facility', action: 'upgrade', displayName: '升级设施', description: '允许升级训练设施', category: 'facility_management' },
  { resource: 'facility', action: 'maintain', displayName: '维护设施', description: '允许维护设施', category: 'facility_management' },
  { resource: 'facility', action: 'build', displayName: '建设设施', description: '允许建设新设施', category: 'facility_management' },

  // 联赛管理权限
  { resource: 'league', action: 'join', displayName: '加入联赛', description: '允许加入联赛', category: 'league_management' },
  { resource: 'league', action: 'create', displayName: '创建联赛', description: '允许创建自定义联赛', category: 'league_management' },
  { resource: 'league', action: 'manage', displayName: '管理联赛', description: '允许管理联赛设置', category: 'league_management', level: 8 },

  // 统计和报告权限
  { resource: 'stats', action: 'view', displayName: '查看统计', description: '允许查看游戏统计数据', category: 'statistics' },
  { resource: 'stats', action: 'export', displayName: '导出统计', description: '允许导出统计数据', category: 'statistics' },
  { resource: 'report', action: 'generate', displayName: '生成报告', description: '允许生成各类报告', category: 'statistics' },

  // 社交功能权限
  { resource: 'chat', action: 'send', displayName: '发送消息', description: '允许发送聊天消息', category: 'social' },
  { resource: 'chat', action: 'moderate', displayName: '管理聊天', description: '允许管理聊天内容', category: 'social', level: 6 },
  { resource: 'friend', action: 'add', displayName: '添加好友', description: '允许添加好友', category: 'social' },
  { resource: 'guild', action: 'create', displayName: '创建公会', description: '允许创建游戏公会', category: 'social' },
  { resource: 'guild', action: 'join', displayName: '加入公会', description: '允许加入公会', category: 'social' },

  // 管理员权限
  { resource: 'admin', action: 'user_manage', displayName: '用户管理', description: '允许管理游戏用户', category: 'administration', dangerous: true, level: 9 },
  { resource: 'admin', action: 'game_config', displayName: '游戏配置', description: '允许修改游戏配置', category: 'administration', dangerous: true, level: 10 },
  { resource: 'admin', action: 'economy_control', displayName: '经济控制', description: '允许控制游戏经济', category: 'administration', dangerous: true, level: 10 },
  { resource: 'admin', action: 'ban_user', displayName: '封禁用户', description: '允许封禁用户', category: 'administration', dangerous: true, level: 8 },

  // 内容管理权限
  { resource: 'content', action: 'create', displayName: '创建内容', description: '允许创建游戏内容', category: 'content_management' },
  { resource: 'content', action: 'moderate', displayName: '内容审核', description: '允许审核用户内容', category: 'content_management', level: 7 },
  { resource: 'content', action: 'delete', displayName: '删除内容', description: '允许删除不当内容', category: 'content_management', level: 6 },

  // API访问权限
  { resource: 'api', action: 'read', displayName: 'API读取', description: '允许通过API读取数据', category: 'api_access' },
  { resource: 'api', action: 'write', displayName: 'API写入', description: '允许通过API写入数据', category: 'api_access', level: 5 },
  { resource: 'api', action: 'admin', displayName: 'API管理', description: '允许管理API访问', category: 'api_access', dangerous: true, level: 9 },
];

/**
 * 创建游戏权限
 */
async function createGamePermissions() {
  logger.subtitle('创建足球经理游戏权限...');

  // 检查模型是否已存在
  let Permission;
  try {
    Permission = mongoose.model('Permission');
  } catch (error) {
    Permission = mongoose.model('Permission', permissionSchema);
  }

  let createdCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  for (const permData of gamePermissions) {
    try {
      const existing = await Permission.findOne({
        resource: permData.resource,
        action: permData.action
      });

      if (existing) {
        skippedCount++;
        logger.verbose(`权限已存在: ${permData.resource}:${permData.action}`);
        continue;
      }

      // 添加name字段
      const permissionData = {
        ...permData,
        name: `${permData.resource}:${permData.action}`
      };

      await Permission.create(permissionData);
      createdCount++;
      logger.log(`权限创建成功: ${permData.resource}:${permData.action} - ${permData.displayName}`);
    } catch (error) {
      errorCount++;
      logger.warn(`权限创建失败: ${permData.resource}:${permData.action} - ${error.message}`);
    }
  }

  logger.success(`游戏权限创建完成: 新建 ${createdCount} 个, 跳过 ${skippedCount} 个, 失败 ${errorCount} 个`);

  // 按分类统计
  const categories = gamePermissions.reduce((acc, perm) => {
    acc[perm.category] = (acc[perm.category] || 0) + 1;
    return acc;
  }, {});

  logger.info('权限分类统计:');
  Object.entries(categories).forEach(([category, count]) => {
    logger.log(`  ${category}: ${count} 个权限`);
  });

  return { createdCount, skippedCount, errorCount };
}

/**
 * 主种子函数
 */
async function seedPermissions() {
  try {
    logger.start('开始创建足球经理游戏权限...');
    
    // 连接数据库
    await database.connect();
    
    // 创建游戏权限
    const result = await createGamePermissions();
    
    logger.complete('足球经理游戏权限创建完成！');
    
    // 显示结果统计
    logger.separator();
    logger.info('📊 权限创建统计:');
    logger.log(`   总权限数: ${gamePermissions.length}`);
    logger.log(`   新建权限: ${result.createdCount}`);
    logger.log(`   跳过权限: ${result.skippedCount}`);
    logger.log(`   失败权限: ${result.errorCount}`);
    logger.log(`   成功率: ${((result.createdCount / gamePermissions.length) * 100).toFixed(1)}%`);
    logger.separator();
    
  } catch (error) {
    logger.error('权限创建失败:', error);
    throw error;
  } finally {
    await database.disconnect();
  }
}

// 运行权限种子
if (require.main === module) {
  seedPermissions().catch((error) => {
    console.error('权限种子失败:', error);
    process.exit(1);
  });
}

module.exports = { seedPermissions, gamePermissions };
