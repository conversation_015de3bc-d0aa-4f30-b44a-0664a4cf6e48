/**
 * 数据库连接和操作工具
 * 提供MongoDB和Redis连接管理
 */

const mongoose = require('mongoose');
const Redis = require('ioredis');
const config = require('./config');
const Logger = require('./logger');

class Database {
  constructor() {
    this.logger = new Logger('Database');
    this.mongodb = null;
    this.redis = null;
    this.isConnected = false;
  }

  /**
   * 连接MongoDB
   */
  async connectMongoDB() {
    try {
      this.logger.progress('正在连接MongoDB...');
      
      const uri = config.getDatabaseUrl();
      const options = config.get('database.mongodb.options');

      this.mongodb = await mongoose.connect(uri, options);
      
      // 监听连接事件
      mongoose.connection.on('connected', () => {
        this.logger.success('MongoDB连接成功');
      });

      mongoose.connection.on('error', (error) => {
        this.logger.error('MongoDB连接错误:', error);
      });

      mongoose.connection.on('disconnected', () => {
        this.logger.warn('MongoDB连接断开');
      });

      return this.mongodb;
    } catch (error) {
      this.logger.error('MongoDB连接失败:', error);
      throw error;
    }
  }

  /**
   * 连接Redis
   */
  async connectRedis() {
    try {
      this.logger.progress('正在连接Redis...');
      
      const redisConfig = config.getRedisConfig();
      
      this.redis = new Redis({
        host: redisConfig.host,
        port: redisConfig.port,
        password: redisConfig.password,
        db: redisConfig.db,
        keyPrefix: redisConfig.keyPrefix,
        retryDelayOnFailover: 100,
        enableReadyCheck: true,
        maxRetriesPerRequest: 3,
      });

      // 监听连接事件
      this.redis.on('connect', () => {
        this.logger.success('Redis连接成功');
      });

      this.redis.on('error', (error) => {
        this.logger.error('Redis连接错误:', error);
      });

      this.redis.on('close', () => {
        this.logger.warn('Redis连接关闭');
      });

      // 测试连接
      await this.redis.ping();
      
      return this.redis;
    } catch (error) {
      this.logger.error('Redis连接失败:', error);
      throw error;
    }
  }

  /**
   * 连接所有数据库
   */
  async connect() {
    try {
      this.logger.start('初始化数据库连接...');
      
      await Promise.all([
        this.connectMongoDB(),
        this.connectRedis()
      ]);
      
      this.isConnected = true;
      this.logger.complete('所有数据库连接成功');
      
      return {
        mongodb: this.mongodb,
        redis: this.redis
      };
    } catch (error) {
      this.logger.error('数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 断开所有连接
   */
  async disconnect() {
    try {
      this.logger.progress('正在断开数据库连接...');
      
      const promises = [];
      
      if (this.mongodb) {
        promises.push(mongoose.disconnect());
      }
      
      if (this.redis) {
        promises.push(this.redis.disconnect());
      }
      
      await Promise.all(promises);
      
      this.isConnected = false;
      this.logger.success('所有数据库连接已断开');
    } catch (error) {
      this.logger.error('断开数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 获取MongoDB连接
   */
  getMongoDB() {
    if (!this.mongodb || !this.isConnected) {
      throw new Error('MongoDB未连接');
    }
    return this.mongodb;
  }

  /**
   * 获取Redis连接
   */
  getRedis() {
    if (!this.redis || !this.isConnected) {
      throw new Error('Redis未连接');
    }
    return this.redis;
  }

  /**
   * 检查连接状态
   */
  async checkHealth() {
    const health = {
      mongodb: false,
      redis: false,
      overall: false
    };

    try {
      // 检查MongoDB
      if (this.mongodb && mongoose.connection.readyState === 1) {
        await mongoose.connection.db.admin().ping();
        health.mongodb = true;
      }
    } catch (error) {
      this.logger.warn('MongoDB健康检查失败:', error.message);
    }

    try {
      // 检查Redis
      if (this.redis && this.redis.status === 'ready') {
        await this.redis.ping();
        health.redis = true;
      }
    } catch (error) {
      this.logger.warn('Redis健康检查失败:', error.message);
    }

    health.overall = health.mongodb && health.redis;
    return health;
  }

  /**
   * 清理测试数据
   */
  async cleanupTestData() {
    try {
      this.logger.progress('清理测试数据...');
      
      if (this.mongodb) {
        // 清理测试用户
        await mongoose.connection.db.collection('users').deleteMany({
          $or: [
            { username: /^test/ },
            { username: /^perf/ },
            { email: /test@/ },
            { email: /perftest/ }
          ]
        });

        // 清理测试会话
        await mongoose.connection.db.collection('sessions').deleteMany({
          deviceInfo: { $regex: /test|performance/i }
        });

        // 清理测试审计日志
        await mongoose.connection.db.collection('auditlogs').deleteMany({
          details: { $regex: /test|performance/i }
        });
      }

      if (this.redis) {
        // 清理测试缓存
        const keys = await this.redis.keys('*test*');
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }

      this.logger.success('测试数据清理完成');
    } catch (error) {
      this.logger.error('清理测试数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取数据库统计信息
   */
  async getStats() {
    const stats = {
      mongodb: {},
      redis: {}
    };

    try {
      if (this.mongodb) {
        const db = mongoose.connection.db;
        
        // 获取集合统计
        const collections = await db.listCollections().toArray();
        stats.mongodb.collections = collections.length;
        
        // 获取数据库统计
        const dbStats = await db.stats();
        stats.mongodb.dataSize = dbStats.dataSize;
        stats.mongodb.storageSize = dbStats.storageSize;
        stats.mongodb.indexSize = dbStats.indexSize;
        stats.mongodb.objects = dbStats.objects;
      }
    } catch (error) {
      this.logger.warn('获取MongoDB统计失败:', error.message);
    }

    try {
      if (this.redis) {
        const info = await this.redis.info();
        const lines = info.split('\r\n');
        
        for (const line of lines) {
          if (line.includes('used_memory:')) {
            stats.redis.usedMemory = line.split(':')[1];
          }
          if (line.includes('connected_clients:')) {
            stats.redis.connectedClients = parseInt(line.split(':')[1]);
          }
          if (line.includes('total_commands_processed:')) {
            stats.redis.totalCommands = parseInt(line.split(':')[1]);
          }
        }
      }
    } catch (error) {
      this.logger.warn('获取Redis统计失败:', error.message);
    }

    return stats;
  }

  /**
   * 执行数据库备份
   */
  async backup(backupPath) {
    try {
      this.logger.progress('开始数据库备份...');
      
      // 这里可以实现具体的备份逻辑
      // 例如使用mongodump和redis-cli
      
      this.logger.success(`数据库备份完成: ${backupPath}`);
    } catch (error) {
      this.logger.error('数据库备份失败:', error);
      throw error;
    }
  }

  /**
   * 恢复数据库
   */
  async restore(backupPath) {
    try {
      this.logger.progress('开始数据库恢复...');
      
      // 这里可以实现具体的恢复逻辑
      // 例如使用mongorestore和redis-cli
      
      this.logger.success(`数据库恢复完成: ${backupPath}`);
    } catch (error) {
      this.logger.error('数据库恢复失败:', error);
      throw error;
    }
  }
}

// 导出单例实例
module.exports = new Database();
