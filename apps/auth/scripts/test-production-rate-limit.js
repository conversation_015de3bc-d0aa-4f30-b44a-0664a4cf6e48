/**
 * 生产环境限流测试工具（简化版）
 * 专门测试生产环境下的限流功能
 */

const axios = require('axios');

const AUTH_BASE_URL = 'http://127.0.0.1:3001';

class ProductionRateLimitTester {
  constructor() {
    this.results = {
      total: 0,
      success: 0,
      rateLimited: 0,
      errors: 0
    };
  }

  async testProductionRateLimit() {
    console.log('🏭 生产环境限流测试');
    console.log('🔍 测试生产环境限流是否正确启用');
    console.log('=' .repeat(50));

    // 1. 测试正常请求频率（应该通过）
    console.log('\n📊 测试1: 正常请求频率（间隔2秒）');
    const normalPassed = await this.testNormalRequestRate();

    // 2. 测试超限请求频率（应该被限流）
    console.log('\n🚨 测试2: 超限请求频率（快速并发）');
    const overLimitTriggered = await this.testOverLimitRequests();

    // 3. 总结
    this.printTestSummary(normalPassed, overLimitTriggered);

    return normalPassed && overLimitTriggered;
  }

  async testNormalRequestRate() {
    console.log('发送3个请求，间隔2秒（正常频率）');
    this.resetResults();

    for (let i = 0; i < 3; i++) {
      await this.sendTestRequest(i + 1);
      if (i < 2) {
        console.log('  等待2秒...');
        await this.sleep(2000);
      }
    }

    const passed = this.results.rateLimited === 0;
    console.log(`结果: ${this.results.success}成功, ${this.results.rateLimited}限流, ${this.results.errors}错误`);
    
    if (passed) {
      console.log('✅ 正常频率测试通过');
    } else {
      console.log('❌ 正常频率被误限流');
    }

    return passed;
  }

  async testOverLimitRequests() {
    console.log('快速发送15个请求（超限频率）');
    this.resetResults();

    // 快速发送大量请求
    const promises = [];
    for (let i = 0; i < 15; i++) {
      promises.push(this.sendTestRequest(i + 1));
    }

    await Promise.all(promises);

    const triggered = this.results.rateLimited > 0;
    console.log(`结果: ${this.results.success}成功, ${this.results.rateLimited}限流, ${this.results.errors}错误`);
    
    if (triggered) {
      console.log('✅ 限流正确触发');
      console.log(`限流触发率: ${(this.results.rateLimited / this.results.total * 100).toFixed(1)}%`);
    } else {
      console.log('❌ 限流未触发（可能配置有误）');
    }

    return triggered;
  }

  async sendTestRequest(num) {
    try {
      const response = await axios.post(`${AUTH_BASE_URL}/auth/login`, {
        identifier: 'test',
        password: 'wrong'
      }, {
        validateStatus: () => true,
        timeout: 5000
      });
      
      const isRateLimited = response.status === 429;
      const isSuccess = response.status >= 200 && response.status < 500 && !isRateLimited;
      
      this.results.total++;
      if (isRateLimited) {
        this.results.rateLimited++;
        console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ❌ 被限流`);
      } else if (isSuccess) {
        this.results.success++;
        console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ✅ 正常`);
      } else {
        this.results.errors++;
        console.log(`  请求 ${num.toString().padStart(2, '0')}: ${response.status} ⚠️ 错误`);
      }
      
      return { rateLimited: isRateLimited, status: response.status };
    } catch (error) {
      this.results.total++;
      this.results.errors++;
      console.log(`  请求 ${num.toString().padStart(2, '0')}: 网络错误 - ${error.message}`);
      return { rateLimited: false, error: error.message };
    }
  }

  resetResults() {
    this.results = {
      total: 0,
      success: 0,
      rateLimited: 0,
      errors: 0
    };
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printTestSummary(normalPassed, overLimitTriggered) {
    console.log('\n🎯 生产环境限流测试总结');
    console.log('=' .repeat(50));
    
    console.log(`正常频率测试: ${normalPassed ? '✅ 通过' : '❌ 失败'}`);
    console.log(`超限触发测试: ${overLimitTriggered ? '✅ 通过' : '❌ 失败'}`);
    
    const allPassed = normalPassed && overLimitTriggered;
    
    if (allPassed) {
      console.log('\n🎉 生产环境限流测试通过！');
      console.log('✅ 限流保护正常工作');
      console.log('✅ 正常请求不受影响');
      console.log('✅ 超限请求被正确拦截');
    } else {
      console.log('\n❌ 生产环境限流测试失败！');
      console.log('\n🔧 故障排除建议:');
      
      if (!normalPassed) {
        console.log('- 正常请求被误限流，检查限流阈值配置');
      }
      if (!overLimitTriggered) {
        console.log('- 限流未触发，检查以下配置:');
        console.log('  * NODE_ENV=production');
        console.log('  * RATE_LIMIT_ENABLED=true');
        console.log('  * 限流阈值设置');
      }
    }
  }
}

// 运行测试
async function runProductionTest() {
  console.log('🔍 环境信息:');
  console.log(`当前目录: ${process.cwd()}`);
  console.log(`Node.js版本: ${process.version}`);
  console.log('');
  
  const tester = new ProductionRateLimitTester();
  await tester.testProductionRateLimit();
}

if (require.main === module) {
  runProductionTest().catch(console.error);
}

module.exports = ProductionRateLimitTester;
