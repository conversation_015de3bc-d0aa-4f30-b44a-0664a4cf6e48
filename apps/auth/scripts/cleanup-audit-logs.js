/**
 * 审计日志清理脚本
 * 清理过期的安全审计日志
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const database = require('./utils/database');
const config = require('./utils/config');
const Logger = require('./utils/logger');

// 加载环境变量
dotenv.config();

const logger = new Logger('CleanupAuditLogs');

// 审计日志Schema
const auditLogSchema = new mongoose.Schema({
  eventType: { type: String, required: true },
  userId: String,
  sessionId: String,
  ipAddress: String,
  userAgent: String,
  resource: String,
  action: String,
  result: { type: String, enum: ['success', 'failure', 'warning'] },
  severity: { type: String, enum: ['low', 'medium', 'high', 'critical'], default: 'low' },
  details: mongoose.Schema.Types.Mixed,
  metadata: {
    requestId: String,
    correlationId: String,
    source: String,
    version: String
  },
  location: {
    country: String,
    region: String,
    city: String,
    timezone: String
  },
  deviceInfo: {
    type: String,
    name: String,
    fingerprint: String
  },
  timestamp: { type: Date, default: Date.now },
  expiresAt: Date,
  archived: { type: Boolean, default: false }
});

/**
 * 清理过期审计日志
 */
async function cleanupExpiredLogs() {
  logger.subtitle('清理过期审计日志...');
  
  const AuditLog = mongoose.model('AuditLog', auditLogSchema);
  const retentionDays = config.get('logging.auditRetentionDays', 90);
  const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
  
  try {
    // 查找过期日志
    const expiredLogs = await AuditLog.find({
      $or: [
        { expiresAt: { $lt: new Date() } },
        { timestamp: { $lt: cutoffDate }, archived: false }
      ]
    });
    
    if (expiredLogs.length === 0) {
      logger.info('没有找到过期审计日志');
      return 0;
    }
    
    logger.log(`找到 ${expiredLogs.length} 条过期审计日志`);
    
    // 统计要删除的日志类型
    const stats = expiredLogs.reduce((acc, log) => {
      acc[log.eventType] = (acc[log.eventType] || 0) + 1;
      return acc;
    }, {});
    
    logger.info('过期日志类型统计:');
    Object.entries(stats).forEach(([type, count]) => {
      logger.log(`  ${type}: ${count} 条`);
    });
    
    // 删除过期日志
    const result = await AuditLog.deleteMany({
      _id: { $in: expiredLogs.map(log => log._id) }
    });
    
    logger.success(`清理了 ${result.deletedCount} 条过期审计日志`);
    return result.deletedCount;
  } catch (error) {
    logger.error('清理过期审计日志失败:', error);
    throw error;
  }
}

/**
 * 归档旧审计日志
 */
async function archiveOldLogs() {
  logger.subtitle('归档旧审计日志...');
  
  const AuditLog = mongoose.model('AuditLog', auditLogSchema);
  const archiveDays = config.get('logging.auditRetentionDays', 90) / 2; // 归档期为保留期的一半
  const archiveDate = new Date(Date.now() - archiveDays * 24 * 60 * 60 * 1000);
  
  try {
    // 查找需要归档的日志（非关键日志）
    const logsToArchive = await AuditLog.find({
      timestamp: { $lt: archiveDate },
      archived: false,
      severity: { $in: ['low', 'medium'] } // 只归档低和中等严重性的日志
    });
    
    if (logsToArchive.length === 0) {
      logger.info('没有找到需要归档的日志');
      return 0;
    }
    
    logger.log(`找到 ${logsToArchive.length} 条需要归档的日志`);
    
    // 标记为已归档
    const result = await AuditLog.updateMany(
      { _id: { $in: logsToArchive.map(log => log._id) } },
      { $set: { archived: true } }
    );
    
    logger.success(`归档了 ${result.modifiedCount} 条审计日志`);
    return result.modifiedCount;
  } catch (error) {
    logger.error('归档审计日志失败:', error);
    throw error;
  }
}

/**
 * 清理重复审计日志
 */
async function cleanupDuplicateLogs() {
  logger.subtitle('清理重复审计日志...');
  
  const AuditLog = mongoose.model('AuditLog', auditLogSchema);
  
  try {
    // 查找重复日志（同一用户、同一事件类型、同一时间段内的多条记录）
    const duplicates = await AuditLog.aggregate([
      {
        $match: {
          timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // 只检查最近24小时
        }
      },
      {
        $group: {
          _id: {
            userId: '$userId',
            eventType: '$eventType',
            ipAddress: '$ipAddress',
            timeSlot: {
              $dateToString: {
                format: '%Y-%m-%d-%H-%M',
                date: '$timestamp'
              }
            }
          },
          logs: { $push: '$$ROOT' },
          count: { $sum: 1 }
        }
      },
      {
        $match: { count: { $gt: 5 } } // 同一分钟内超过5条相同记录视为重复
      }
    ]);
    
    if (duplicates.length === 0) {
      logger.info('没有找到重复审计日志');
      return 0;
    }
    
    logger.log(`找到 ${duplicates.length} 组重复日志`);
    
    let cleanedCount = 0;
    
    for (const duplicate of duplicates) {
      const logs = duplicate.logs;
      // 保留最早的2条记录，删除其他的
      logs.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      const logsToDelete = logs.slice(2);
      
      if (logsToDelete.length > 0) {
        await AuditLog.deleteMany({
          _id: { $in: logsToDelete.map(log => log._id) }
        });
        cleanedCount += logsToDelete.length;
        
        logger.log(`事件 ${duplicate._id.eventType}: 保留2条，删除${logsToDelete.length}条`);
      }
    }
    
    logger.success(`清理了 ${cleanedCount} 条重复审计日志`);
    return cleanedCount;
  } catch (error) {
    logger.error('清理重复审计日志失败:', error);
    throw error;
  }
}

/**
 * 清理测试相关日志
 */
async function cleanupTestLogs() {
  logger.subtitle('清理测试相关日志...');
  
  const AuditLog = mongoose.model('AuditLog', auditLogSchema);
  
  try {
    // 查找测试相关的日志
    const testLogs = await AuditLog.find({
      $or: [
        { 'details.test': true },
        { 'details.source': /test|performance|security/i },
        { userAgent: /test|performance|security/i },
        { 'metadata.source': /test|performance|security/i }
      ]
    });
    
    if (testLogs.length === 0) {
      logger.info('没有找到测试相关日志');
      return 0;
    }
    
    logger.log(`找到 ${testLogs.length} 条测试相关日志`);
    
    // 删除测试日志
    const result = await AuditLog.deleteMany({
      _id: { $in: testLogs.map(log => log._id) }
    });
    
    logger.success(`清理了 ${result.deletedCount} 条测试相关日志`);
    return result.deletedCount;
  } catch (error) {
    logger.error('清理测试日志失败:', error);
    throw error;
  }
}

/**
 * 压缩审计日志
 */
async function compressLogs() {
  logger.subtitle('压缩审计日志...');
  
  const AuditLog = mongoose.model('AuditLog', auditLogSchema);
  
  try {
    // 查找可以压缩的日志（移除详细信息，只保留关键字段）
    const logsToCompress = await AuditLog.find({
      timestamp: { $lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }, // 30天前
      archived: false,
      severity: 'low'
    });
    
    if (logsToCompress.length === 0) {
      logger.info('没有找到需要压缩的日志');
      return 0;
    }
    
    logger.log(`找到 ${logsToCompress.length} 条需要压缩的日志`);
    
    let compressedCount = 0;
    
    for (const log of logsToCompress) {
      // 压缩日志：移除详细信息，只保留关键字段
      const compressedDetails = {
        summary: log.details?.summary || log.eventType,
        compressed: true,
        originalSize: JSON.stringify(log.details || {}).length
      };
      
      await AuditLog.updateOne(
        { _id: log._id },
        {
          $set: {
            details: compressedDetails,
            archived: true
          },
          $unset: {
            userAgent: 1,
            'metadata.requestId': 1,
            'deviceInfo.fingerprint': 1
          }
        }
      );
      
      compressedCount++;
    }
    
    logger.success(`压缩了 ${compressedCount} 条审计日志`);
    return compressedCount;
  } catch (error) {
    logger.error('压缩审计日志失败:', error);
    throw error;
  }
}

/**
 * 获取审计日志统计信息
 */
async function getAuditStats() {
  logger.subtitle('获取审计日志统计信息...');
  
  const AuditLog = mongoose.model('AuditLog', auditLogSchema);
  
  try {
    const stats = await AuditLog.aggregate([
      {
        $group: {
          _id: null,
          totalLogs: { $sum: 1 },
          archivedLogs: {
            $sum: { $cond: [{ $eq: ['$archived', true] }, 1, 0] }
          },
          criticalLogs: {
            $sum: { $cond: [{ $eq: ['$severity', 'critical'] }, 1, 0] }
          },
          highSeverityLogs: {
            $sum: { $cond: [{ $eq: ['$severity', 'high'] }, 1, 0] }
          },
          failureLogs: {
            $sum: { $cond: [{ $eq: ['$result', 'failure'] }, 1, 0] }
          }
        }
      }
    ]);
    
    const eventTypeStats = await AuditLog.aggregate([
      {
        $group: {
          _id: '$eventType',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      }
    ]);
    
    const result = stats[0] || {};
    
    logger.info('审计日志统计:');
    logger.log(`  总日志数: ${result.totalLogs || 0}`);
    logger.log(`  已归档日志: ${result.archivedLogs || 0}`);
    logger.log(`  严重日志: ${result.criticalLogs || 0}`);
    logger.log(`  高级日志: ${result.highSeverityLogs || 0}`);
    logger.log(`  失败日志: ${result.failureLogs || 0}`);
    
    if (eventTypeStats.length > 0) {
      logger.info('事件类型分布 (Top 10):');
      eventTypeStats.forEach(stat => {
        logger.log(`  ${stat._id}: ${stat.count}`);
      });
    }
    
    return result;
  } catch (error) {
    logger.error('获取审计日志统计失败:', error);
    return {};
  }
}

/**
 * 主清理函数
 */
async function cleanupAuditLogs(options = {}) {
  const startTime = Date.now();
  
  try {
    logger.start('开始清理过期审计日志...');
    
    // 连接数据库
    await database.connect();
    
    // 获取清理前的统计信息
    logger.subtitle('清理前统计:');
    const beforeStats = await getAuditStats();
    
    let totalCleaned = 0;
    
    // 1. 清理过期日志
    if (options.expired !== false) {
      const expiredCount = await cleanupExpiredLogs();
      totalCleaned += expiredCount;
    }
    
    // 2. 归档旧日志
    if (options.archive !== false) {
      const archivedCount = await archiveOldLogs();
      totalCleaned += archivedCount;
    }
    
    // 3. 清理重复日志
    if (options.duplicates !== false) {
      const duplicateCount = await cleanupDuplicateLogs();
      totalCleaned += duplicateCount;
    }
    
    // 4. 清理测试日志
    if (options.testLogs !== false) {
      const testCount = await cleanupTestLogs();
      totalCleaned += testCount;
    }
    
    // 5. 压缩日志
    if (options.compress !== false) {
      const compressedCount = await compressLogs();
      totalCleaned += compressedCount;
    }
    
    // 获取清理后的统计信息
    logger.subtitle('清理后统计:');
    const afterStats = await getAuditStats();
    
    const duration = Date.now() - startTime;
    logger.complete(`清理完成！总共处理了 ${totalCleaned} 条日志，耗时: ${(duration / 1000).toFixed(2)}秒`);
    
    return {
      totalCleaned,
      beforeStats,
      afterStats,
      duration
    };
    
  } catch (error) {
    logger.error('清理审计日志失败:', error);
    throw error;
  } finally {
    await database.disconnect();
  }
}

// 运行清理
if (require.main === module) {
  const options = {};
  
  // 解析命令行参数
  if (process.argv.includes('--expired-only')) {
    options.archive = false;
    options.duplicates = false;
    options.testLogs = false;
    options.compress = false;
  }
  
  if (process.argv.includes('--no-compress')) {
    options.compress = false;
  }
  
  if (process.argv.includes('--keep-test-logs')) {
    options.testLogs = false;
  }
  
  cleanupAuditLogs(options).catch((error) => {
    console.error('审计日志清理失败:', error);
    process.exit(1);
  });
}

module.exports = { cleanupAuditLogs };
