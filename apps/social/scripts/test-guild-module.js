/**
 * 公会系统测试模块
 * 基于Hero服务球探系统的成功测试模式
 * 
 * 测试内容：
 * 1. 获取公会信息
 * 2. 创建公会功能
 * 3. 搜索公会功能
 * 4. 申请加入公会
 * 5. 处理公会申请（接受/拒绝）
 * 6. 公会成员管理
 * 7. 退出公会功能
 */

const chalk = require('chalk');

class GuildModuleTester {
  constructor(socket, testData) {
    this.socket = socket;
    this.testData = testData;
    this.testResults = [];
    this.guildData = null;
  }

  /**
   * WebSocket调用封装 - 使用Hero服务的成功模式
   */
  async callWebSocket(command, data = {}) {
    return new Promise((resolve, reject) => {
      const messageId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const message = {
        id: messageId,
        command: command,
        payload: {
          characterId: this.testData.characterId,
          serverId: this.testData.serverId || 'server_001',
          token: this.testData.token,
          ...data
        }
      };

      console.log(chalk.gray(`📤 发送消息: ${command}`));

      // 设置响应监听器
      const responseHandler = (response) => {
        if (response.id === messageId) {
          console.log(chalk.cyan(`📨 收到响应: ${command}`));
          this.socket.off('message', responseHandler);
          resolve(response);
        }
      };

      this.socket.on('message', responseHandler);

      // 发送消息
      this.socket.emit('message', message);

      // 设置超时
      setTimeout(() => {
        this.socket.off('message', responseHandler);
        reject(new Error(`消息超时: ${command}`));
      }, 10000);
    });
  }

  /**
   * 测试获取公会信息
   */
  async testGetGuildInfo() {
    console.log(chalk.yellow('🏰 测试获取公会信息...'));

    try {
      // 如果有已创建的公会，使用它；否则使用测试ID
      const guildId = this.guildData?.guildId || 'test-guild-123';

      const response = await this.callWebSocket('social.guild.getInfo', {
        guildId: guildId
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 获取公会信息成功'));
        console.log(chalk.gray(`   公会名称: ${data.guildName || '无公会'}`));
        console.log(chalk.gray(`   公会等级: ${data.guildLevel || 'N/A'}`));

        this.guildData = { ...this.guildData, ...data };
        this.testResults.push({
          test: '获取公会信息',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 获取公会信息失败: ${errorMsg}`));
        this.testResults.push({
          test: '获取公会信息',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取公会信息异常: ${error.message}`));
      this.testResults.push({
        test: '获取公会信息',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试创建公会功能
   */
  async testCreateGuild() {
    console.log(chalk.yellow('🏗️ 测试创建公会功能...'));

    try {
      const guildName = `测试公会_${Date.now()}`;
      const response = await this.callWebSocket('social.guild.create', {
        playerId: this.testData.characterId,
        playerName: `测试玩家${Date.now()}`,
        guildName: guildName,
        faceId: 1,
        strength: 100,
        gid: `gid_${Date.now()}`,
        faceUrl: 'default_face.jpg'
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 创建公会成功'));
        console.log(chalk.gray(`   公会名称: ${guildName}`));
        
        this.testResults.push({
          test: '创建公会',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 创建公会失败: ${errorMsg}`));
        this.testResults.push({
          test: '创建公会',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 创建公会异常: ${error.message}`));
      this.testResults.push({
        test: '创建公会',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试搜索公会功能
   */
  async testSearchGuild() {
    console.log(chalk.yellow('🔍 测试搜索公会功能...'));

    try {
      const searchKeyword = '测试';
      const response = await this.callWebSocket('social.guild.search', {
        keyword: searchKeyword,
        page: 1,
        limit: 10
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 搜索公会成功'));
        console.log(chalk.gray(`   搜索关键词: ${searchKeyword}`));
        console.log(chalk.gray(`   搜索结果数量: ${data.guilds ? data.guilds.length : 0}`));
        
        this.testResults.push({
          test: '搜索公会',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 搜索公会失败: ${errorMsg}`));
        this.testResults.push({
          test: '搜索公会',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 搜索公会异常: ${error.message}`));
      this.testResults.push({
        test: '搜索公会',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试申请加入公会
   */
  async testJoinGuild() {
    console.log(chalk.yellow('📝 测试申请加入公会...'));

    try {
      const targetGuildId = 'test-guild-123';
      const response = await this.callWebSocket('social.guild.apply', {
        guildId: targetGuildId,
        playerId: this.testData.characterId,
        playerName: `测试玩家${Date.now()}`,
        gid: `gid_${Date.now()}`
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 申请加入公会成功'));
        console.log(chalk.gray(`   目标公会ID: ${targetGuildId}`));
        
        this.testResults.push({
          test: '申请加入公会',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 申请加入公会失败: ${errorMsg}`));
        this.testResults.push({
          test: '申请加入公会',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 申请加入公会异常: ${error.message}`));
      this.testResults.push({
        test: '申请加入公会',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试获取公会成员列表
   */
  async testGetGuildMembers() {
    console.log(chalk.yellow('👥 测试获取公会成员列表...'));

    try {
      const response = await this.callWebSocket('social.guild.getApplicationList', {
        playerId: this.testData.characterId,
        guildId: 'test-guild-123'
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success && data) {
        console.log(chalk.green('✅ 获取公会申请列表成功'));
        console.log(chalk.gray(`   申请数量: ${data.applications ? data.applications.length : 0}`));

        this.testResults.push({
          test: '获取公会申请列表',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 获取公会申请列表失败: ${errorMsg}`));
        this.testResults.push({
          test: '获取公会申请列表',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 获取公会申请列表异常: ${error.message}`));
      this.testResults.push({
        test: '获取公会申请列表',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试处理公会申请
   */
  async testHandleGuildApplication() {
    console.log(chalk.yellow('✅ 测试处理公会申请...'));

    try {
      const applicantId = 'test-applicant-123';
      const response = await this.callWebSocket('social.guild.approveApplication', {
        playerId: this.testData.characterId,
        guildId: 'test-guild-123',
        agreeId: applicantId
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 处理公会申请成功'));
        console.log(chalk.gray(`   申请者ID: ${applicantId}`));
        
        this.testResults.push({
          test: '处理公会申请',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 处理公会申请失败: ${errorMsg}`));
        this.testResults.push({
          test: '处理公会申请',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 处理公会申请异常: ${error.message}`));
      this.testResults.push({
        test: '处理公会申请',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 测试退出公会
   */
  async testLeaveGuild() {
    console.log(chalk.yellow('🚪 测试退出公会...'));

    try {
      const response = await this.callWebSocket('social.guild.leave', {
        playerId: this.testData.characterId,
        guildId: 'test-guild-123'
      });

      const success = response?.payload?.data?.code === 0;
      const data = response?.payload?.data?.data;

      if (success) {
        console.log(chalk.green('✅ 退出公会成功'));
        
        this.testResults.push({
          test: '退出公会',
          success: true,
          data: data
        });
      } else {
        const errorMsg = response?.payload?.data?.message || '未知错误';
        console.log(chalk.red(`❌ 退出公会失败: ${errorMsg}`));
        this.testResults.push({
          test: '退出公会',
          success: false,
          error: errorMsg
        });
      }
    } catch (error) {
      console.log(chalk.red(`❌ 退出公会异常: ${error.message}`));
      this.testResults.push({
        test: '退出公会',
        success: false,
        error: error.message
      });
    }
  }

  /**
   * 运行所有测试
   */
  async runTests() {
    console.log(chalk.cyan('🔧 开始公会系统测试'));

    // 按顺序执行测试
    await this.testGetGuildInfo();
    await this.testCreateGuild();
    await this.testSearchGuild();
    await this.testJoinGuild();
    await this.testGetGuildMembers();
    await this.testHandleGuildApplication();
    await this.testLeaveGuild();

    // 返回测试结果
    return this.testResults;
  }
}

module.exports = GuildModuleTester;
