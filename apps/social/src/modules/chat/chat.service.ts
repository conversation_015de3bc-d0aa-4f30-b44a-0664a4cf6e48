import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ChatRepository } from '../../common/repositories/chat.repository';
import { ChatMessageType, ChatMessageStatus } from '../../common/schemas/chat.schema';
import {
  SendChatMessageDto,
  GetChatHistoryDto,
  GetPrivateChatDto,
  ChatMessageResponseDto,
  ChatHistoryResponseDto
} from '../../common/dto/chat.dto';

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  constructor(
    private readonly chatRepository: ChatRepository,
  ) {}

  /**
   * 发送聊天消息
   */
  async sendMessage(messageData: SendChatMessageDto): Promise<ChatMessageResponseDto> {
    this.logger.log(`发送聊天消息: ${messageData.senderId} -> ${messageData.channelId}`);

    try {
      // 验证私聊消息必须有接收者
      if (messageData.messageType === ChatMessageType.PRIVATE) {
        if (!messageData.receiverId || !messageData.receiverName) {
          throw new BadRequestException('私聊消息必须指定接收者');
        }
      }

      // 生成消息ID
      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const sendTime = Date.now();

      // 创建消息数据
      const chatMessage = {
        messageId,
        senderId: messageData.senderId,
        senderName: messageData.senderName,
        channelId: messageData.channelId,
        messageType: messageData.messageType,
        content: messageData.content,
        receiverId: messageData.receiverId,
        receiverName: messageData.receiverName,
        extraData: messageData.extraData,
        status: ChatMessageStatus.SENT,
        sendTime,
        readTime: 0,
        serverId: messageData.serverId,
      };

      // 保存消息到数据库
      const savedMessage = await this.chatRepository.createMessage(chatMessage);

      // 更新频道最后消息信息
      await this.chatRepository.updateChannelLastMessage(
        messageData.channelId,
        messageData.content,
        sendTime
      );

      this.logger.log(`聊天消息发送成功: ${messageId}`);

      return {
        messageId: savedMessage.messageId,
        senderId: savedMessage.senderId,
        senderName: savedMessage.senderName,
        channelId: savedMessage.channelId,
        messageType: savedMessage.messageType,
        content: savedMessage.content,
        receiverId: savedMessage.receiverId,
        receiverName: savedMessage.receiverName,
        extraData: savedMessage.extraData,
        status: savedMessage.status,
        sendTime: savedMessage.sendTime,
        readTime: savedMessage.readTime,
      };

    } catch (error) {
      this.logger.error('发送聊天消息失败', error);
      throw error;
    }
  }

  /**
   * 获取聊天历史
   */
  async getChatHistory(params: GetChatHistoryDto): Promise<ChatHistoryResponseDto> {
    this.logger.log(`获取聊天历史: ${params.channelId}`);

    try {
      const limit = params.limit || 50;
      const messages = await this.chatRepository.getMessagesByChannel(
        params.channelId,
        limit + 1, // 多获取一条来判断是否还有更多
        params.before
      );

      const hasMore = messages.length > limit;
      const actualMessages = hasMore ? messages.slice(0, limit) : messages;

      // 转换为响应格式
      const messageResponses: ChatMessageResponseDto[] = actualMessages.map(msg => ({
        messageId: msg.messageId,
        senderId: msg.senderId,
        senderName: msg.senderName,
        channelId: msg.channelId,
        messageType: msg.messageType,
        content: msg.content,
        receiverId: msg.receiverId,
        receiverName: msg.receiverName,
        extraData: msg.extraData,
        status: msg.status,
        sendTime: msg.sendTime,
        readTime: msg.readTime,
      }));

      return {
        messages: messageResponses.reverse(), // 按时间正序返回
        total: messageResponses.length,
        channelId: params.channelId,
        hasMore,
      };

    } catch (error) {
      this.logger.error('获取聊天历史失败', error);
      throw error;
    }
  }

  /**
   * 获取私聊历史
   */
  async getPrivateChatHistory(params: GetPrivateChatDto): Promise<ChatHistoryResponseDto> {
    this.logger.log(`获取私聊历史: ${params.senderId} <-> ${params.receiverId}`);

    try {
      const limit = params.limit || 50;
      const messages = await this.chatRepository.getPrivateMessages(
        params.senderId,
        params.receiverId,
        limit + 1,
        params.before
      );

      const hasMore = messages.length > limit;
      const actualMessages = hasMore ? messages.slice(0, limit) : messages;

      // 转换为响应格式
      const messageResponses: ChatMessageResponseDto[] = actualMessages.map(msg => ({
        messageId: msg.messageId,
        senderId: msg.senderId,
        senderName: msg.senderName,
        channelId: msg.channelId,
        messageType: msg.messageType,
        content: msg.content,
        receiverId: msg.receiverId,
        receiverName: msg.receiverName,
        extraData: msg.extraData,
        status: msg.status,
        sendTime: msg.sendTime,
        readTime: msg.readTime,
      }));

      return {
        messages: messageResponses.reverse(), // 按时间正序返回
        total: messageResponses.length,
        channelId: `private_${params.senderId}_${params.receiverId}`,
        hasMore,
      };

    } catch (error) {
      this.logger.error('获取私聊历史失败', error);
      throw error;
    }
  }

  /**
   * 加入聊天频道
   */
  async joinChannel(characterId: string, channelId: string, serverId: string): Promise<any> {
    this.logger.log(`加入聊天频道: ${characterId} -> ${channelId}`);

    try {
      // 确保频道存在
      let channel = await this.chatRepository.findChannelById(channelId);

      if (!channel) {
        // 创建频道（根据频道ID判断类型）
        let channelType = ChatMessageType.WORLD;
        let channelName = '世界频道';

        if (channelId.startsWith('guild_')) {
          channelType = ChatMessageType.GUILD;
          channelName = '公会频道';
        } else if (channelId.startsWith('private_')) {
          channelType = ChatMessageType.PRIVATE;
          channelName = '私聊';
        }

        channel = await this.chatRepository.getOrCreateChannel({
          channelId,
          channelName,
          channelType,
          members: [],
          memberCount: 0,
          maxMembers: channelType === ChatMessageType.GUILD ? 100 :
                     channelType === ChatMessageType.PRIVATE ? 2 : 1000,
          isActive: true,
          isPrivate: channelType === ChatMessageType.PRIVATE,
          messageCount: 0,
          lastMessageTime: 0,
          serverId,
        });
      }

      // 加入频道
      const updatedChannel = await this.chatRepository.joinChannel(channelId, characterId);

      return {
        characterId,
        channelId,
        channelName: channel.channelName,
        channelType: channel.channelType,
        memberCount: updatedChannel?.memberCount || channel.memberCount,
        status: 'joined',
        joinedAt: Date.now(),
      };

    } catch (error) {
      this.logger.error('加入聊天频道失败', error);
      throw error;
    }
  }

  /**
   * 离开聊天频道
   */
  async leaveChannel(characterId: string, channelId: string): Promise<any> {
    this.logger.log(`离开聊天频道: ${characterId} -> ${channelId}`);

    try {
      // 离开频道
      const updatedChannel = await this.chatRepository.leaveChannel(channelId, characterId);

      if (!updatedChannel) {
        throw new BadRequestException('频道不存在或用户不在频道中');
      }

      return {
        characterId,
        channelId,
        channelName: updatedChannel.channelName,
        memberCount: updatedChannel.memberCount,
        status: 'left',
        leftAt: Date.now(),
      };

    } catch (error) {
      this.logger.error('离开聊天频道失败', error);
      throw error;
    }
  }
}
