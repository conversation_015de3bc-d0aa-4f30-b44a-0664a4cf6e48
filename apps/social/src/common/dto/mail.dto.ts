/**
 * 邮件系统DTO定义
 * 基于old项目email.js实体迁移
 */

import { IsString, IsNumber, IsArray, IsOptional, IsEnum, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { MailType, MailStatus } from '../schemas/mail.schema';

// 邮件附件DTO
export class MailAttachmentDto {
  @IsNumber()
  itemType: number;     // 物品类型

  @IsNumber()
  resId: number;        // 资源ID

  @IsNumber()
  num: number;          // 数量

  @IsOptional()
  param1?: any;         // 参数1
}

// 创建邮件DTO
export class CreateMailDto {
  @IsString()
  senderUid: string;    // 发送者ID

  @IsString()
  receiverUid: string;  // 接收者ID

  @IsEnum(MailType)
  mailType: MailType;   // 邮件类型

  @IsString()
  title: string;        // 邮件标题

  @IsString()
  content: string;      // 邮件内容

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MailAttachmentDto)
  @IsOptional()
  attachList?: MailAttachmentDto[]; // 附件列表

  @IsOptional()
  specialAttachInfo?: any; // 特殊附件信息

  @IsNumber()
  @IsOptional()
  sendTime?: number;    // 发送时间 (必需字段，但在DTO中设为可选，由服务层处理)

  @IsNumber()
  @IsOptional()
  expireTime?: number;  // 过期时间

  @IsOptional()
  param1?: any;         // 参数1

  @IsOptional()
  param2?: any;         // 参数2

  @IsOptional()
  param3?: any;         // 参数3

  @IsOptional()
  param4?: any;         // 参数4
}

// 发送奖励邮件DTO
export class SendMailRewardDto {
  @IsString()
  receiverUid: string;  // 接收者ID

  @IsString()
  senderUid: string;    // 发送者ID

  @IsNumber()
  mailId: number;       // 邮件模板ID

  @IsEnum(MailType)
  mailType: MailType;   // 邮件类型

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MailAttachmentDto)
  attachList: MailAttachmentDto[]; // 附件列表

  @IsOptional()
  specialAttachInfo?: any; // 特殊附件信息

  @IsOptional()
  param1?: any;         // 参数1

  @IsOptional()
  param2?: any;         // 参数2

  @IsOptional()
  param3?: any;         // 参数3

  @IsOptional()
  param4?: any;         // 参数4
}

// 可编辑邮件DTO
export class CreateEditMailDto {
  @IsString()
  receiverUid: string;  // 接收者ID

  @IsString()
  senderUid: string;    // 发送者ID

  @IsNumber()
  delType: number;      // 删除类型

  @IsString()
  title: string;        // 邮件标题

  @IsString()
  content: string;      // 邮件内容

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MailAttachmentDto)
  @IsOptional()
  attachList?: MailAttachmentDto[]; // 附件列表
}

// 邮件信息DTO（返回给客户端）
export class MailInfoDto {
  @IsString()
  uid: string;          // 邮件唯一ID

  @IsString()
  senderUid: string;    // 发送者ID

  @IsString()
  receiverUid: string;  // 接收者ID

  @IsEnum(MailType)
  mailType: MailType;   // 邮件类型

  @IsString()
  title: string;        // 邮件标题

  @IsString()
  content: string;      // 邮件内容

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MailAttachmentDto)
  attachList: MailAttachmentDto[]; // 附件列表

  @IsEnum(MailStatus)
  status: MailStatus;   // 邮件状态

  @IsNumber()
  sendTime: number;     // 发送时间

  @IsNumber()
  readTime: number;     // 读取时间

  @IsNumber()
  expireTime: number;   // 过期时间

  @IsNumber()
  remainingTime: number; // 剩余时间

  hasAttachment: boolean; // 是否有附件

  canClaimAttachment: boolean; // 是否可以领取附件
}

// 邮件列表DTO
export class MailListDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MailInfoDto)
  mails: MailInfoDto[]; // 邮件列表

  @IsNumber()
  total: number;        // 总数

  @IsNumber()
  unreadCount: number;  // 未读数量

  isHaveNewMail: boolean; // 是否有新邮件
}

// 领取附件DTO
export class ClaimAttachmentDto {
  @IsString()
  characterId: string;     // 玩家ID

  @IsString()
  mailUid: string;      // 邮件UID
}

// 读取邮件DTO
export class ReadMailDto {
  @IsString()
  characterId: string;     // 玩家ID

  @IsString()
  mailUid: string;      // 邮件UID
}

// 删除邮件DTO
export class DeleteMailDto {
  @IsString()
  characterId: string;     // 玩家ID

  @IsString()
  mailUid: string;      // 邮件UID
}

// 获取邮件列表DTO
export class GetMailListDto {
  @IsString()
  characterId: string;     // 玩家ID

  @IsNumber()
  @IsOptional()
  page?: number;        // 页码

  @IsNumber()
  @IsOptional()
  limit?: number;       // 每页数量
}

// 邮件操作结果DTO
export class MailOperationResultDto {
  success: boolean;     // 操作是否成功

  @IsOptional()
  mailUid?: string;     // 邮件UID

  @IsOptional()
  operationTime?: number; // 操作时间

  @IsOptional()
  rewards?: any[];      // 奖励（领取附件时）

  @IsOptional()
  message?: string;     // 操作消息
}
