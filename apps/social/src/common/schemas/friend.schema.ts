/**
 * 好友系统Schema
 * 基于old项目friends.js实体迁移
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 好友状态枚举
export enum FriendStatus {
  PENDING = 0,      // 待确认
  ACCEPTED = 1,     // 已接受
  BLOCKED = 2,      // 已屏蔽
  DELETED = 3,      // 已删除
}

// 好友信息子文档
@Schema({ _id: false })
export class FriendInfo {
  @Prop({ required: true })
  characterId: string;         // 好友玩家ID

  @Prop({ required: true })
  name: string;             // 好友名称

  @Prop({ default: 1 })
  level: number;            // 好友等级

  @Prop({ default: 0 })
  honor: number;            // 段位

  @Prop({ default: 0 })
  longitude: number;        // 经度

  @Prop({ default: 0 })
  latitude: number;         // 纬度

  @Prop({ default: '' })
  faceIcon: string;         // 头像

  @Prop({ default: '' })
  faceUrl: string;          // 头像URL

  @Prop({ default: 0 })
  vip: number;              // VIP等级

  @Prop({ default: 0 })
  trophy: number;           // 奖杯数

  @Prop({ default: Date.now })
  addTime: number;          // 添加时间

  @Prop({ default: Date.now })
  lastOnlineTime: number;   // 最后在线时间

  @Prop({ default: false })
  isOnline: boolean;        // 是否在线

  @Prop({ default: '' })
  serverId: string;         // 区服ID
}

// 申请记录子文档
@Schema({ _id: false })
export class FriendApply {
  @Prop({ required: true })
  characterId: string;         // 申请者玩家ID

  @Prop({ required: true })
  name: string;             // 申请者名称

  @Prop({ default: 1 })
  level: number;            // 申请者等级

  @Prop({ default: 0 })
  honor: number;            // 段位

  @Prop({ default: '' })
  faceIcon: string;         // 头像

  @Prop({ default: '' })
  faceUrl: string;          // 头像URL

  @Prop({ default: 0 })
  vip: number;              // VIP等级

  @Prop({ default: 0 })
  trophy: number;           // 奖杯数

  @Prop({ default: Date.now })
  applyTime: number;        // 申请时间

  @Prop({ default: '' })
  message: string;          // 申请消息

  @Prop({ default: '' })
  serverId: string;         // 区服ID
}

// 主好友系统Schema
@Schema({ 
  collection: 'friends', 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Friend {
  // 基础标识信息
  @Prop({ required: true, unique: true })
  friendId: string;         // 好友记录ID

  @Prop({ required: true, index: true })
  characterId: string;         // 玩家ID

  @Prop({ required: true, index: true })
  serverId: string;         // 区服ID

  // 好友列表（基于old项目friends Map结构）
  @Prop({ type: [FriendInfo], default: [] })
  friends: FriendInfo[];    // 好友列表

  // 申请列表（基于old项目applyList Map结构）
  @Prop({ type: [FriendApply], default: [] })
  applyList: FriendApply[]; // 收到的好友申请

  // 发出的申请列表（基于old项目relationApply Map结构）
  @Prop({ type: [FriendApply], default: [] })
  relationApply: FriendApply[]; // 发出的好友申请

  // 黑名单
  @Prop({ type: [String], default: [] })
  blackList: string[];      // 黑名单玩家ID列表

  // 统计信息
  @Prop({ default: 0 })
  totalFriends: number;     // 总好友数

  @Prop({ default: 0 })
  onlineFriends: number;    // 在线好友数

  @Prop({ default: 0 })
  pendingApplies: number;   // 待处理申请数

  @Prop({ default: Date.now })
  lastUpdateTime: number;   // 最后更新时间

  // 虚拟字段：好友数量限制检查
  get canAddMoreFriends(): boolean {
    return this.friends.length < 100; // 最大好友数限制
  }

  // 虚拟字段：是否有待处理申请
  get hasPendingApplies(): boolean {
    return this.applyList.length > 0;
  }

  // 虚拟字段：在线好友列表
  get onlineFriendsList(): FriendInfo[] {
    return this.friends.filter(friend => friend.isOnline);
  }
}

export const FriendSchema = SchemaFactory.createForClass(Friend);

// 定义方法接口
export interface FriendMethods {
  addFriend(friendInfo: FriendInfo): boolean;
  removeFriend(characterId: string): boolean;
  addApply(applyInfo: FriendApply): boolean;
  removeApply(characterId: string): boolean;
  acceptApply(characterId: string): boolean;
  rejectApply(characterId: string): boolean;
  blockCharacter(characterId: string): boolean;
  unblockCharacter(characterId: string): boolean;
  updateFriendStatus(characterId: string, isOnline: boolean): void;
  getFriendDistance(characterId: string, longitude: number, latitude: number): number;
}

// 定义Document类型
export type FriendDocument = Friend & Document & FriendMethods;

// 创建索引
FriendSchema.index({ friendId: 1 }, { unique: true });
FriendSchema.index({ characterId: 1 });
FriendSchema.index({ serverId: 1 });
FriendSchema.index({ 'friends.characterId': 1 });
FriendSchema.index({ 'applyList.characterId': 1 });

// 添加虚拟字段
FriendSchema.virtual('canAddMoreFriends').get(function() {
  return this.friends.length < 100;
});

FriendSchema.virtual('hasPendingApplies').get(function() {
  return this.applyList.length > 0;
});

FriendSchema.virtual('onlineFriendsList').get(function() {
  return this.friends.filter(friend => friend.isOnline);
});

// 添加实例方法
FriendSchema.methods.addFriend = function(friendInfo: FriendInfo) {
  if (!this.canAddMoreFriends) return false;
  
  const existingFriend = this.friends.find(f => f.characterId === friendInfo.characterId);
  if (existingFriend) return false;
  
  this.friends.push(friendInfo);
  this.totalFriends = this.friends.length;
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.removeFriend = function(characterId: string) {
  const index = this.friends.findIndex(f => f.characterId === characterId);
  if (index === -1) return false;
  
  this.friends.splice(index, 1);
  this.totalFriends = this.friends.length;
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.addApply = function(applyInfo: FriendApply) {
  const existingApply = this.applyList.find(a => a.characterId === applyInfo.characterId);
  if (existingApply) return false;
  
  this.applyList.push(applyInfo);
  this.pendingApplies = this.applyList.length;
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.removeApply = function(characterId: string) {
  const index = this.applyList.findIndex(a => a.characterId === characterId);
  if (index === -1) return false;
  
  this.applyList.splice(index, 1);
  this.pendingApplies = this.applyList.length;
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.acceptApply = function(characterId: string) {
  const applyIndex = this.applyList.findIndex(a => a.characterId === characterId);
  if (applyIndex === -1) return false;
  
  const apply = this.applyList[applyIndex];
  const friendInfo: FriendInfo = {
    characterId: apply.characterId,
    name: apply.name,
    level: apply.level,
    honor: apply.honor,
    longitude: 0,
    latitude: 0,
    faceIcon: apply.faceIcon,
    faceUrl: apply.faceUrl,
    vip: apply.vip,
    trophy: apply.trophy,
    addTime: Date.now(),
    lastOnlineTime: Date.now(),
    isOnline: true,
    serverId: apply.serverId,
  };
  
  if (this.addFriend(friendInfo)) {
    this.removeApply(characterId);
    return true;
  }
  
  return false;
};

FriendSchema.methods.rejectApply = function(characterId: string) {
  return this.removeApply(characterId);
};

FriendSchema.methods.blockCharacter = function(characterId: string) {
  if (this.blackList.includes(characterId)) return false;
  
  this.blackList.push(characterId);
  this.removeFriend(characterId); // 移除好友关系
  this.removeApply(characterId);  // 移除申请
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.unblockCharacter = function(characterId: string) {
  const index = this.blackList.indexOf(characterId);
  if (index === -1) return false;
  
  this.blackList.splice(index, 1);
  this.lastUpdateTime = Date.now();
  
  return true;
};

FriendSchema.methods.updateFriendStatus = function(characterId: string, isOnline: boolean) {
  const friend = this.friends.find(f => f.characterId === characterId);
  if (!friend) return;
  
  friend.isOnline = isOnline;
  if (!isOnline) {
    friend.lastOnlineTime = Date.now();
  }
  
  this.onlineFriends = this.friends.filter(f => f.isOnline).length;
  this.lastUpdateTime = Date.now();
};

// 计算距离（基于old项目的地理位置计算）
FriendSchema.methods.getFriendDistance = function(characterId: string, longitude: number, latitude: number) {
  const friend = this.friends.find(f => f.characterId === characterId);
  if (!friend || !friend.longitude || !friend.latitude) return -1;
  
  const R = 6371393; // 地球半径（米）
  const lat1 = latitude * Math.PI / 180;
  const lat2 = friend.latitude * Math.PI / 180;
  const deltaLat = (friend.latitude - latitude) * Math.PI / 180;
  const deltaLng = (friend.longitude - longitude) * Math.PI / 180;
  
  const a = Math.sin(deltaLat/2) * Math.sin(deltaLat/2) +
            Math.cos(lat1) * Math.cos(lat2) *
            Math.sin(deltaLng/2) * Math.sin(deltaLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  
  return R * c; // 距离（米）
};
