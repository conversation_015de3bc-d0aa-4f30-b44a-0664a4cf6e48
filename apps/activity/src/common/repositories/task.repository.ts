import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery, QueryOptions } from 'mongoose';
import { Task, TaskDocument, TaskType, TaskStatus } from '../schemas/task.schema';
import { GetTaskListDto, TaskStatsDto } from '../dto/task.dto';

@Injectable()
export class TaskRepository {
  private readonly logger = new Logger(TaskRepository.name);

  constructor(
    @InjectModel(Task.name) private taskModel: Model<TaskDocument>,
  ) {}

  /**
   * 创建任务记录
   */
  async createTask(taskData: Partial<Task>): Promise<TaskDocument> {
    try {
      const task = new this.taskModel({
        ...taskData,
        lastUpdateTime: Date.now(),
      });
      return await task.save();
    } catch (error) {
      this.logger.error('创建任务记录失败', error);
      throw error;
    }
  }

  /**
   * 根据玩家ID查找任务记录
   */
  async findTaskByCharacterId(characterId: string): Promise<TaskDocument | null> {
    try {
      return await this.taskModel.findOne({ characterId }).exec();
    } catch (error) {
      this.logger.error(`根据玩家ID查找任务记录失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 获取或创建任务记录
   */
  async getOrCreateTask(characterId: string, serverId: string): Promise<TaskDocument> {
    try {
      let task = await this.findTaskByCharacterId(characterId);
      
      if (!task) {
        const taskRecordId = this.generateTaskRecordId(characterId);
        task = await this.createTask({
          taskRecordId,
          characterId,
          serverId,
        });
        this.logger.log(`创建新任务记录: ${taskRecordId}`);
      }
      
      return task;
    } catch (error) {
      this.logger.error('获取或创建任务记录失败', error);
      throw error;
    }
  }

  /**
   * 更新任务记录
   */
  async updateTask(
    characterId: string, 
    updateData: UpdateQuery<TaskDocument>,
    options?: QueryOptions
  ): Promise<TaskDocument | null> {
    try {
      return await this.taskModel.findOneAndUpdate(
        { characterId },
        { ...updateData, lastUpdateTime: Date.now() },
        { new: true, ...options }
      ).exec();
    } catch (error) {
      this.logger.error(`更新任务记录失败: ${characterId}`, error);
      throw error;
    }
  }

  /**
   * 获取需要刷新的任务
   */
  async findTasksNeedingRefresh(taskType: TaskType): Promise<TaskDocument[]> {
    try {
      const now = Date.now();
      let timeField: string;
      let intervalMs: number;

      switch (taskType) {
        case TaskType.DAILY:
          timeField = 'dailyRefreshTime';
          intervalMs = 24 * 60 * 60 * 1000; // 1天
          break;
        case TaskType.WEEKLY:
          timeField = 'weeklyRefreshTime';
          intervalMs = 7 * 24 * 60 * 60 * 1000; // 7天
          break;
        default:
          return [];
      }

      const filter: FilterQuery<TaskDocument> = {
        [timeField]: { $lte: now - intervalMs }
      };

      return await this.taskModel.find(filter).exec();
    } catch (error) {
      this.logger.error('获取需要刷新的任务失败', error);
      throw error;
    }
  }

  /**
   * 批量刷新任务
   */
  async batchRefreshTasks(characterIds: string[], taskType: TaskType): Promise<any> {
    try {
      const now = Date.now();
      let updateData: any = {};

      switch (taskType) {
        case TaskType.DAILY:
          updateData = {
            dailyTasks: [],
            dailyRefreshTime: now,
            dailyCompletedCount: 0,
          };
          break;
        case TaskType.WEEKLY:
          updateData = {
            weeklyTasks: [],
            weeklyRefreshTime: now,
            weeklyCompletedCount: 0,
          };
          break;
      }

      return await this.taskModel.updateMany(
        { characterId: { $in: characterIds } },
        { $set: { ...updateData, lastUpdateTime: now } }
      );
    } catch (error) {
      this.logger.error('批量刷新任务失败', error);
      throw error;
    }
  }

  /**
   * 获取任务统计
   */
  async getTaskStats(characterId: string, days: number = 30, taskType?: TaskType): Promise<any> {
    try {
      const task = await this.findTaskByCharacterId(characterId);
      if (!task) {
        return {
          totalCompletedTasks: 0,
          totalClaimedRewards: 0,
          dailyCompletedCount: 0,
          weeklyCompletedCount: 0,
          taskTypeDistribution: {},
          completionRate: {
            daily: 0,
            weekly: 0,
            achievement: 0,
            newbie: 0,
            event: 0,
            main: 0,
          },
          rewardStats: {
            totalRewards: 0,
            rewardTypes: {},
          },
        };
      }

      // 计算完成率
      const completionRate = {
        daily: this.calculateCompletionRate(task.dailyTasks),
        weekly: this.calculateCompletionRate(task.weeklyTasks),
        achievement: this.calculateCompletionRate(task.achievementTasks),
        newbie: this.calculateCompletionRate(task.newbieTasks),
        event: this.calculateCompletionRate(task.eventTasks),
        main: this.calculateCompletionRate(task.mainTasks),
      };

      // 计算任务类型分布
      const taskTypeDistribution = {
        daily: task.dailyTasks.length,
        weekly: task.weeklyTasks.length,
        achievement: task.achievementTasks.length,
        newbie: task.newbieTasks.length,
        event: task.eventTasks.length,
        main: task.mainTasks.length,
      };

      // 计算奖励统计
      const allTasks = [
        ...task.dailyTasks,
        ...task.weeklyTasks,
        ...task.achievementTasks,
        ...task.newbieTasks,
        ...task.eventTasks,
        ...task.mainTasks,
      ];

      const rewardTypes = allTasks
        .filter(t => t.status === TaskStatus.CLAIMED)
        .reduce((acc, t) => {
          t.rewards.forEach(reward => {
            acc[reward.type] = (acc[reward.type] || 0) + reward.quantity;
          });
          return acc;
        }, {} as Record<string, number>);

      return {
        totalCompletedTasks: task.totalCompletedTasks,
        totalClaimedRewards: task.totalClaimedRewards,
        dailyCompletedCount: task.dailyCompletedCount,
        weeklyCompletedCount: task.weeklyCompletedCount,
        taskTypeDistribution,
        completionRate,
        rewardStats: {
          totalRewards: task.totalClaimedRewards,
          rewardTypes,
        },
      };
    } catch (error) {
      this.logger.error('获取任务统计失败', error);
      throw error;
    }
  }

  /**
   * 清理过期任务
   */
  async cleanExpiredTasks(): Promise<any> {
    try {
      const now = Date.now();
      
      // 更新过期任务状态
      const result = await this.taskModel.updateMany(
        {
          $or: [
            { 'dailyTasks.expireTime': { $gt: 0, $lt: now } },
            { 'weeklyTasks.expireTime': { $gt: 0, $lt: now } },
            { 'eventTasks.expireTime': { $gt: 0, $lt: now } },
          ]
        },
        {
          $set: {
            'dailyTasks.$[elem1].status': TaskStatus.EXPIRED,
            'weeklyTasks.$[elem2].status': TaskStatus.EXPIRED,
            'eventTasks.$[elem3].status': TaskStatus.EXPIRED,
            lastUpdateTime: now,
          }
        },
        {
          arrayFilters: [
            { 'elem1.expireTime': { $gt: 0, $lt: now } },
            { 'elem2.expireTime': { $gt: 0, $lt: now } },
            { 'elem3.expireTime': { $gt: 0, $lt: now } },
          ]
        }
      );

      this.logger.log(`清理过期任务完成，影响记录数: ${result.modifiedCount}`);
      return result;
    } catch (error) {
      this.logger.error('清理过期任务失败', error);
      throw error;
    }
  }

  /**
   * 获取玩家任务排行榜
   */
  async getTaskLeaderboard(serverId: string, taskType?: TaskType, limit: number = 100): Promise<any[]> {
    try {
      const pipeline: any[] = [
        { $match: { serverId } },
        {
          $project: {
            characterId: 1,
            totalCompletedTasks: 1,
            totalClaimedRewards: 1,
            dailyCompletedCount: 1,
            weeklyCompletedCount: 1,
          }
        },
        { $sort: { totalCompletedTasks: -1 } },
        { $limit: limit },
      ];

      return await this.taskModel.aggregate(pipeline);
    } catch (error) {
      this.logger.error('获取任务排行榜失败', error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 生成任务记录ID
   */
  private generateTaskRecordId(characterId: string): string {
    return `task_${characterId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 计算完成率
   */
  private calculateCompletionRate(tasks: any[]): number {
    if (tasks.length === 0) return 0;
    
    const completedTasks = tasks.filter(task => 
      task.status === TaskStatus.COMPLETED || task.status === TaskStatus.CLAIMED
    ).length;
    
    return Math.floor((completedTasks / tasks.length) * 100);
  }
}
