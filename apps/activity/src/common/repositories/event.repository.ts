/**
 * 活动数据访问层
 * 基于Repository模式实现数据库访问
 */

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery, UpdateQuery } from 'mongoose';
import { Activity, ActivityDocument, ActivityType, FirstChargeStatus } from '../schemas/event.schema';
import { CreateActivityDto, UpdateActivityDto } from '../dto/event.dto';

@Injectable()
export class EventRepository {
  private readonly logger = new Logger(EventRepository.name);

  constructor(
    @InjectModel(Activity.name) private activityModel: Model<ActivityDocument>,
  ) {}

  /**
   * 创建活动记录
   */
  async create(createData: CreateActivityDto & { activityRecordId: string }): Promise<ActivityDocument> {
    try {
      const activity = new this.activityModel(createData);
      const savedActivity = await activity.save();
      this.logger.log(`活动记录创建成功: ${savedActivity.uid}`);
      return savedActivity;
    } catch (error) {
      this.logger.error('创建活动记录失败', error);
      throw error;
    }
  }

  /**
   * 根据玩家ID查找活动记录
   */
  async findByCharacterId(uid: string): Promise<ActivityDocument | null> {
    try {
      return await this.activityModel.findOne({ uid }).exec();
    } catch (error) {
      this.logger.error(`查找活动记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 根据活动记录ID查找
   */
  async findByActivityRecordId(activityRecordId: string): Promise<ActivityDocument | null> {
    try {
      return await this.activityModel.findOne({ activityRecordId }).exec();
    } catch (error) {
      this.logger.error(`根据记录ID查找活动失败: ${activityRecordId}`, error);
      throw error;
    }
  }

  /**
   * 更新活动记录
   */
  async update(
    uid: string, 
    updateData: UpdateQuery<ActivityDocument>
  ): Promise<ActivityDocument | null> {
    try {
      const updatedActivity = await this.activityModel.findOneAndUpdate(
        { uid },
        { ...updateData, lastUpdateTime: Date.now() },
        { new: true }
      ).exec();

      if (updatedActivity) {
        this.logger.log(`活动记录更新成功: ${uid}`);
      }

      return updatedActivity;
    } catch (error) {
      this.logger.error(`更新活动记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 更新活动进度
   */
  async updateActivityProgress(
    uid: string, 
    actId: number, 
    progress: number, 
    data?: any
  ): Promise<ActivityDocument | null> {
    try {
      const activity = await this.findByCharacterId(uid);
      if (!activity) return null;

      const success = activity.updateActivityProgress(actId, progress, data);
      if (success) {
        await activity.save();
        this.logger.log(`活动进度更新成功: ${uid}, 活动: ${actId}, 进度: ${progress}`);
        return activity;
      }

      return null;
    } catch (error) {
      this.logger.error(`更新活动进度失败: ${uid}, 活动: ${actId}`, error);
      throw error;
    }
  }

  /**
   * 领取活动奖励
   */
  async claimActivityReward(
    uid: string, 
    actId: number, 
    rewardId: number
  ): Promise<ActivityDocument | null> {
    try {
      const activity = await this.findByCharacterId(uid);
      if (!activity) return null;

      const success = activity.claimActivityReward(actId, rewardId);
      if (success) {
        await activity.save();
        this.logger.log(`活动奖励领取成功: ${uid}, 活动: ${actId}, 奖励: ${rewardId}`);
        return activity;
      }

      return null;
    } catch (error) {
      this.logger.error(`领取活动奖励失败: ${uid}, 活动: ${actId}`, error);
      throw error;
    }
  }

  /**
   * 激活首充
   */
  async activateFirstCharge(uid: string): Promise<ActivityDocument | null> {
    try {
      const activity = await this.findByCharacterId(uid);
      if (!activity) return null;

      const success = activity.activateFirstCharge();
      if (success) {
        await activity.save();
        this.logger.log(`首充激活成功: ${uid}`);
        return activity;
      }

      return null;
    } catch (error) {
      this.logger.error(`首充激活失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 激活开服礼包
   */
  async activateOpenCharge(uid: string): Promise<ActivityDocument | null> {
    try {
      const activity = await this.findByCharacterId(uid);
      if (!activity) return null;

      const success = activity.activateOpenCharge();
      if (success) {
        await activity.save();
        this.logger.log(`开服礼包激活成功: ${uid}`);
        return activity;
      }

      return null;
    } catch (error) {
      this.logger.error(`开服礼包激活失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 清理过期活动
   */
  async cleanExpiredActivities(uid: string): Promise<number> {
    try {
      const activity = await this.findByCharacterId(uid);
      if (!activity) return 0;

      const removedCount = activity.cleanExpiredActivities();
      if (removedCount > 0) {
        await activity.save();
        this.logger.log(`清理过期活动: ${uid}, 清理数量: ${removedCount}`);
      }

      return removedCount;
    } catch (error) {
      this.logger.error(`清理过期活动失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 查找需要刷新的活动记录
   */
  async findNeedingRefresh(): Promise<ActivityDocument[]> {
    try {
      const oneHourMs = 60 * 60 * 1000;
      const cutoffTime = Date.now() - oneHourMs;

      return await this.activityModel.find({
        lastRefreshTime: { $lt: cutoffTime }
      }).exec();
    } catch (error) {
      this.logger.error('查找需要刷新的活动记录失败', error);
      throw error;
    }
  }

  /**
   * 根据区服查找活动记录
   */
  async findByServerId(serverId: string): Promise<ActivityDocument[]> {
    try {
      return await this.activityModel.find({ serverId }).exec();
    } catch (error) {
      this.logger.error(`根据区服查找活动记录失败: ${serverId}`, error);
      throw error;
    }
  }

  /**
   * 统计活动数据
   */
  async getActivityStatistics(serverId?: string): Promise<any> {
    try {
      const filter: FilterQuery<ActivityDocument> = {};
      if (serverId) {
        filter.serverId = serverId;
      }

      const [
        totalCharacters,
        firstChargeActivations,
        openChargeActivations,
        avgParticipatedActivities
      ] = await Promise.all([
        this.activityModel.countDocuments(filter),
        this.activityModel.countDocuments({
          ...filter,
          firstChargeStatus: FirstChargeStatus.ACTIVE
        }),
        this.activityModel.countDocuments({
          ...filter,
          openChargeStatus: FirstChargeStatus.ACTIVE
        }),
        this.activityModel.aggregate([
          { $match: filter },
          { $project: { activityCount: { $size: '$globalActMgrInfo' } } },
          { $group: { _id: null, avgActivities: { $avg: '$activityCount' } } }
        ])
      ]);

      const averageParticipatedActivities = avgParticipatedActivities[0]?.avgActivities || 0;

      return {
        totalCharacters,
        firstChargeActivations,
        openChargeActivations,
        averageParticipatedActivities: Math.round(averageParticipatedActivities * 100) / 100,
        firstChargeRate: totalCharacters > 0 ? (firstChargeActivations / totalCharacters) * 100 : 0,
        openChargeRate: totalCharacters > 0 ? (openChargeActivations / totalCharacters) * 100 : 0
      };
    } catch (error) {
      this.logger.error('获取活动统计失败', error);
      throw error;
    }
  }

  /**
   * 查找活动参与排行榜
   */
  async getActivityParticipationLeaderboard(limit: number = 50): Promise<any[]> {
    try {
      return await this.activityModel.aggregate([
        {
          $project: {
            uid: 1,
            activityCount: { $size: '$globalActMgrInfo' },
            rewardCount: {
              $sum: {
                $map: {
                  input: '$globalActMgrInfo',
                  as: 'record',
                  in: { $size: '$$record.rewards' }
                }
              }
            }
          }
        },
        { $sort: { activityCount: -1, rewardCount: -1 } },
        { $limit: limit }
      ]).exec();
    } catch (error) {
      this.logger.error('获取活动参与排行榜失败', error);
      throw error;
    }
  }

  /**
   * 批量更新活动状态
   */
  async batchUpdateActivityStatus(
    filter: FilterQuery<ActivityDocument>,
    updateData: UpdateQuery<ActivityDocument>
  ): Promise<number> {
    try {
      const result = await this.activityModel.updateMany(
        filter,
        { ...updateData, lastUpdateTime: Date.now() }
      );

      this.logger.log(`批量更新活动状态: ${result.modifiedCount} 条记录`);
      return result.modifiedCount;
    } catch (error) {
      this.logger.error('批量更新活动状态失败', error);
      throw error;
    }
  }

  /**
   * 删除活动记录
   */
  async delete(uid: string): Promise<boolean> {
    try {
      const result = await this.activityModel.deleteOne({ uid }).exec();
      if (result.deletedCount > 0) {
        this.logger.log(`活动记录删除成功: ${uid}`);
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error(`删除活动记录失败: ${uid}`, error);
      throw error;
    }
  }

  /**
   * 查找特定活动类型的参与者
   */
  async findParticipantsByActivityType(activityType: ActivityType): Promise<ActivityDocument[]> {
    try {
      return await this.activityModel.find({
        'globalActMgrInfo.actType': activityType
      }).exec();
    } catch (error) {
      this.logger.error(`查找活动类型参与者失败: ${activityType}`, error);
      throw error;
    }
  }

  /**
   * 查找已激活首充的玩家
   */
  async findFirstChargeActivatedCharacters(): Promise<ActivityDocument[]> {
    try {
      return await this.activityModel.find({
        firstChargeStatus: FirstChargeStatus.ACTIVE
      }).exec();
    } catch (error) {
      this.logger.error('查找已激活首充玩家失败', error);
      throw error;
    }
  }

  /**
   * 批量清理过期活动
   */
  async batchCleanExpiredActivities(): Promise<number> {
    try {
      let totalCleaned = 0;
      const activities = await this.activityModel.find().exec();

      for (const activity of activities) {
        const cleaned = activity.cleanExpiredActivities();
        if (cleaned > 0) {
          await activity.save();
          totalCleaned += cleaned;
        }
      }

      this.logger.log(`批量清理过期活动完成: 总计清理 ${totalCleaned} 个活动`);
      return totalCleaned;
    } catch (error) {
      this.logger.error('批量清理过期活动失败', error);
      throw error;
    }
  }
}
