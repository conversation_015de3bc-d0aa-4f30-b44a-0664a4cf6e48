/**
 * 签到系统DTO定义
 * 基于old项目everyDaySign.js和sevenDaySign.js实体迁移
 */

import { IsString, IsNumber, IsArray, IsOptional, IsEnum, ValidateNested, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { SignState } from '../schemas/sign.schema';

// 签到信息DTO
export class SignInfoDto {
  @IsEnum(SignState)
  state: SignState;     // 签到状态

  @IsNumber()
  @IsOptional()
  signTime?: number;    // 签到时间

  @IsArray()
  @IsOptional()
  rewards?: any[];      // 奖励信息
}

// 七日签到记录DTO
export class SevenDaySignRecordDto {
  @IsNumber()
  day: number;          // 第几天

  @IsBoolean()
  claimed: boolean;     // 是否已领取

  @IsNumber()
  @IsOptional()
  claimTime?: number;   // 领取时间

  @IsArray()
  rewards: any[];       // 奖励列表
}

// 获取签到信息DTO
export class GetSignInfoDto {
  @IsString()
  characterId: string;     // 玩家ID

  @IsNumber()
  openServerTime: number; // 开服时间
}

// 每日签到DTO
export class DailySignDto {
  @IsString()
  characterId: string;     // 玩家ID

  @IsNumber()
  openServerTime: number; // 开服时间
}

// 补签DTO
export class MakeUpSignDto {
  @IsString()
  characterId: string;     // 玩家ID

  @IsNumber()
  targetDay: number;    // 目标天数
}

// 七日签到奖励DTO
export class SevenDaySignRewardDto {
  @IsString()
  characterId: string;     // 玩家ID

  @IsNumber()
  day: number;          // 第几天
}

// 签到状态DTO（返回给客户端）
export class SignStatusDto {
  @IsNumber()
  currentStep: number;  // 当前签到天数

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SignInfoDto)
  signInfo: SignInfoDto[]; // 签到信息数组

  @IsNumber()
  continuousDays: number; // 连续签到天数

  @IsNumber()
  totalSignDays: number;  // 总签到天数

  @IsNumber()
  month: number;        // 当前月份

  @IsBoolean()
  canSign: boolean;     // 是否可以签到

  @IsBoolean()
  todaySigned: boolean; // 今日是否已签到

  @IsNumber()
  signProgress: number; // 签到进度百分比

  @IsArray()
  missedDays: number[]; // 可补签天数

  @IsOptional()
  sevenDayProgress?: {  // 七日签到进度
    completed: number;
    total: number;
    percent: number;
  };
}

// 签到结果DTO
export class SignResultDto {
  @IsBoolean()
  success: boolean;     // 签到是否成功

  @IsNumber()
  signDay: number;      // 签到天数

  @IsNumber()
  continuousDays: number; // 连续签到天数

  @IsArray()
  rewards: any[];       // 签到奖励

  @IsNumber()
  signedAt: number;     // 签到时间

  @IsArray()
  @IsOptional()
  nextDayRewards?: any[]; // 下一天奖励预览
}

// 补签结果DTO
export class MakeUpSignResultDto {
  @IsBoolean()
  success: boolean;     // 补签是否成功

  @IsNumber()
  targetDay: number;    // 补签天数

  @IsNumber()
  cost: number;         // 补签费用

  @IsArray()
  rewards: any[];       // 补签奖励

  @IsNumber()
  madeUpAt: number;     // 补签时间
}

// 七日签到奖励结果DTO
export class SevenDaySignRewardResultDto {
  @IsBoolean()
  success: boolean;     // 领取是否成功

  @IsNumber()
  day: number;          // 第几天

  @IsArray()
  rewards: any[];       // 奖励列表

  @IsNumber()
  claimedAt: number;    // 领取时间
}

// 创建签到记录DTO
export class CreateSignDto {
  @IsString()
  uid: string;          // 玩家ID

  @IsString()
  serverId: string;     // 区服ID

  @IsNumber()
  @IsOptional()
  month?: number;       // 月份

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SignInfoDto)
  @IsOptional()
  signInfo?: SignInfoDto[]; // 签到信息

  @IsNumber()
  @IsOptional()
  signDay?: number;     // 签到天数

  @IsNumber()
  @IsOptional()
  signTime?: number;    // 签到时间

  @IsNumber()
  @IsOptional()
  continuousDays?: number; // 连续签到天数

  @IsNumber()
  @IsOptional()
  totalSignDays?: number;  // 总签到天数
}

// 更新签到记录DTO
export class UpdateSignDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SignInfoDto)
  @IsOptional()
  signInfo?: SignInfoDto[]; // 签到信息

  @IsNumber()
  @IsOptional()
  signDay?: number;     // 签到天数

  @IsNumber()
  @IsOptional()
  signTime?: number;    // 签到时间

  @IsNumber()
  @IsOptional()
  continuousDays?: number; // 连续签到天数

  @IsNumber()
  @IsOptional()
  totalSignDays?: number;  // 总签到天数

  @IsNumber()
  @IsOptional()
  totalMakeUpCount?: number; // 总补签次数

  @IsNumber()
  @IsOptional()
  monthMakeUpCount?: number; // 本月补签次数

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SevenDaySignRecordDto)
  @IsOptional()
  sevenDayRecords?: SevenDaySignRecordDto[]; // 七日签到记录
}

// 签到统计DTO
export class SignStatisticsDto {
  @IsNumber()
  totalCharacters: number; // 总玩家数

  @IsNumber()
  todaySignedCharacters: number; // 今日签到玩家数

  @IsNumber()
  signRate: number;     // 签到率

  @IsNumber()
  averageContinuousDays: number; // 平均连续签到天数

  @IsNumber()
  totalMakeUpCount: number; // 总补签次数
}

// 签到奖励配置DTO
export class SignRewardConfigDto {
  @IsNumber()
  day: number;          // 签到天数

  @IsArray()
  rewards: {            // 奖励列表
    itemType: number;
    resId: number;
    num: number;
    param1?: any;
  }[];

  @IsBoolean()
  @IsOptional()
  isSpecial?: boolean;  // 是否特殊奖励

  @IsNumber()
  @IsOptional()
  rewardMultiplier?: number; // 奖励倍数
}
