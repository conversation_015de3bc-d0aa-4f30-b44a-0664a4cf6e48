/**
 * 新手引导模块
 * 基于old项目newerGuide.js实体迁移
 */

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { GuideController } from './guide.controller';
import { GuideService } from './guide.service';
import { GuideRepository } from '@activity/common/repositories/guide.repository';
import { Guide, GuideSchema } from '@activity/common/schemas/guide.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Guide.name, schema: GuideSchema },
    ]),
  ],
  controllers: [GuideController],
  providers: [GuideService, GuideRepository],
  exports: [GuideService, GuideRepository],
})
export class GuideModule {}
