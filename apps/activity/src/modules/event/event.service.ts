import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ErrorCode, ErrorMessages } from '@app/game-constants';
import { EventRepository } from '@activity/common/repositories/event.repository';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { GameConfigFacade } from '@app/game-config';
import {
  ActivityTimeType,
  ActivityRefreshType,
  ActivityType,
  FirstChargeStatus
} from '@activity/common/schemas/event.schema';
import {
  GetActivityListDto,
  GetActivityInfoDto,
  UpdateActivityProgressDto,
  ClaimActivityRewardDto,
  ActivateFirstChargeDto,
  ActivityListResponseDto,
  ActivityDetailResponseDto,
  ActivityProgressResponseDto,
  ActivityRewardResponseDto,
  FirstChargeActivationResponseDto,
  CreateActivityDto
} from '@activity/common/dto/event.dto';

// 使用schema中的枚举

// 活动记录接口（基于old项目actRecord结构）
export interface ActivityRecord {
  actId: number;       // 活动ID
  actType: ActivityType; // 活动类型
  periods: number;     // 期数
  progress: number;    // 进度
  status: number;      // 状态
  rewards: any[];      // 已领取奖励
  data: any;           // 活动特定数据
  createTime: number;  // 创建时间
  updateTime: number;  // 更新时间
}

// 活动信息接口（基于old项目globalCurrActList结构）
export interface ActivityInfo {
  actId: number;       // 活动ID
  actType: ActivityType; // 活动类型
  actName: string;     // 活动名称
  startTime: number;   // 开始时间
  endTime: number;     // 结束时间
  timeType: ActivityTimeType; // 时间类型
  refreshCycle: ActivityRefreshType; // 刷新周期
  periods: number;     // 期数
  isActive: boolean;   // 是否激活
  config: any;         // 配置数据
}

// 活动数据接口（基于old项目Act结构）
export interface ActivityData {
  uid: string;         // 玩家UID
  globalCurrActList: Map<number, ActivityInfo>; // 当前活动列表
  globalActMgrInfo: Map<number, ActivityRecord>; // 活动管理信息
  historyActList: Map<number, any>; // 历史活动数据
  firstChargeStatus: FirstChargeStatus; // 首充状态
  openChargeStatus: FirstChargeStatus; // 开服礼包状态
}

@Injectable()
export class EventService {
  private readonly logger = new Logger(EventService.name);

  constructor(
    private readonly eventRepository: EventRepository,
    private readonly microserviceClient: MicroserviceClientService,
    private readonly gameConfig: GameConfigFacade,
  ) {}

  /**
   * 初始化活动数据（基于old项目initByDB方法）
   */
  async initActivityData(characterId: string): Promise<ActivityData> {
    try {
      // 从数据库加载活动数据（基于old项目initByDB方法）
      let activityData = await this.getActivityData(characterId);

      if (!activityData) {
        activityData = this.createNewActivityData(characterId);
      }

      // 加载全局活动（基于old项目loadGlobalAct方法）
      await this.loadGlobalActivities(activityData);

      return activityData;
    } catch (error) {
      this.logger.error('初始化活动数据失败', error);
      throw error;
    }
  }

  /**
   * 获取活动列表（基于old项目getActList方法）
   */
  async getActivityList(characterId: string): Promise<any> {
    try {
      const activityData = await this.initActivityData(characterId);

      // 检查并重新加载活动控制（基于old项目checkAndReloadActControl方法）
      await this.checkAndReloadActivityControl(activityData);

      const activeActivities: any[] = [];
      const now = Date.now();

      for (const [actId, actInfo] of activityData.globalCurrActList) {
        // 检查活动是否在有效期内
        if (now >= actInfo.startTime && now <= actInfo.endTime) {
          const actRecord = activityData.globalActMgrInfo.get(actId);

          activeActivities.push({
            actId: actInfo.actId,
            actType: actInfo.actType,
            actName: actInfo.actName,
            startTime: actInfo.startTime,
            endTime: actInfo.endTime,
            timeType: actInfo.timeType,
            periods: actInfo.periods,
            progress: actRecord?.progress || 0,
            status: actRecord?.status || 0,
            isActive: actInfo.isActive,
            remainingTime: actInfo.endTime - now,
          });
        }
      }

      this.logger.log(`获取活动列表: ${characterId}, 活动数量: ${activeActivities.length}`);

      return {
        success: true,
        activities: activeActivities,
        total: activeActivities.length,
        activeCount: activeActivities.filter(act => act.isActive).length,
        firstChargeStatus: activityData.firstChargeStatus,
        openChargeStatus: activityData.openChargeStatus,
      };
    } catch (error) {
      this.logger.error('获取活动列表失败', error);
      throw error;
    }
  }

  /**
   * 获取活动详情（基于old项目getActInfo方法）
   */
  async getActivityInfo(characterId: string, actId: number): Promise<any> {
    try {
      const activityData = await this.initActivityData(characterId);

      const actInfo = activityData.globalCurrActList.get(actId);
      if (!actInfo) {
        throw new NotFoundException({
          code: ErrorCode.ACTIVITY_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],
        });
      }

      const actRecord = activityData.globalActMgrInfo.get(actId);
      const now = Date.now();

      // 检查活动是否激活
      if (now < actInfo.startTime || now > actInfo.endTime) {
        throw new BadRequestException({
          code: ErrorCode.ACTIVITY_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],
        });
      }

      return {
        success: true,
        activity: {
          actId: actInfo.actId,
          actType: actInfo.actType,
          actName: actInfo.actName,
          startTime: actInfo.startTime,
          endTime: actInfo.endTime,
          progress: actRecord?.progress || 0,
          status: actRecord?.status || 0,
          rewards: actRecord?.rewards || [],
          data: actRecord?.data || {},
          config: actInfo.config,
          remainingTime: actInfo.endTime - now,
        },
      };
    } catch (error) {
      this.logger.error('获取活动详情失败', error);
      throw error;
    }
  }

  /**
   * 更新活动进度（基于old项目updateActProgress方法）
   */
  async updateActivityProgress(characterId: string, actId: number, progress: number, data?: any): Promise<any> {
    try {
      const activityData = await this.initActivityData(characterId);

      const actInfo = activityData.globalCurrActList.get(actId);
      if (!actInfo) {
        throw new NotFoundException({
          code: ErrorCode.ACTIVITY_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],
        });
      }

      let actRecord = activityData.globalActMgrInfo.get(actId);
      if (!actRecord) {
        // 创建新的活动记录
        actRecord = this.createActivityRecord(actId, actInfo.actType, actInfo.periods);
        activityData.globalActMgrInfo.set(actId, actRecord);
      }

      // 更新进度
      actRecord.progress = Math.max(actRecord.progress, progress);
      actRecord.updateTime = Date.now();

      if (data) {
        actRecord.data = { ...actRecord.data, ...data };
      }

      // 保存到数据库
      await this.saveActivityData(activityData);

      this.logger.log(`活动进度更新: ${characterId}, 活动: ${actId}, 进度: ${progress}`);

      return {
        success: true,
        actId,
        progress: actRecord.progress,
        updatedAt: actRecord.updateTime,
      };
    } catch (error) {
      this.logger.error('更新活动进度失败', error);
      throw error;
    }
  }

  /**
   * 领取活动奖励（基于old项目claimActReward方法）
   */
  async claimActivityReward(characterId: string, actId: number, rewardId: number): Promise<any> {
    try {
      const activityData = await this.initActivityData(characterId);

      const actInfo = activityData.globalCurrActList.get(actId);
      if (!actInfo) {
        throw new NotFoundException({
          code: ErrorCode.ACTIVITY_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],
        });
      }

      const actRecord = activityData.globalActMgrInfo.get(actId);
      if (!actRecord) {
        throw new BadRequestException({
          code: ErrorCode.ACTIVITY_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],
        });
      }

      // 检查是否已领取
      if (actRecord.rewards.includes(rewardId)) {
        throw new BadRequestException({
          code: ErrorCode.ACTIVITY_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],
        });
      }

      // 检查是否满足领取条件
      const canClaim = await this.checkRewardCondition(actRecord, rewardId, actInfo.config);
      if (!canClaim) {
        throw new BadRequestException({
          code: ErrorCode.ACTIVITY_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],
        });
      }

      // 获取奖励配置
      const rewards = await this.getRewardConfig(actId, rewardId);

      // 发放奖励（基于old项目奖励发放逻辑）
      await this.distributeActivityRewards(characterId, rewards);

      // 记录已领取
      actRecord.rewards.push(rewardId);
      actRecord.updateTime = Date.now();

      // 保存到数据库
      await this.saveActivityData(activityData);

      this.logger.log(`活动奖励领取: ${characterId}, 活动: ${actId}, 奖励: ${rewardId}`);

      return {
        success: true,
        actId,
        rewardId,
        rewards,
        claimedAt: actRecord.updateTime,
      };
    } catch (error) {
      this.logger.error('领取活动奖励失败', error);
      throw error;
    }
  }

  /**
   * 首充激活（基于old项目activeFirstCharge方法）
   */
  async activateFirstCharge(characterId: string): Promise<any> {
    try {
      const activityData = await this.initActivityData(characterId);

      if (activityData.firstChargeStatus === FirstChargeStatus.ACTIVE) {
        throw new BadRequestException({
          code: ErrorCode.ACTIVITY_NOT_FOUND,
          message: ErrorMessages[ErrorCode.ACTIVITY_NOT_FOUND],
        });
      }

      // 激活首充
      activityData.firstChargeStatus = FirstChargeStatus.ACTIVE;

      // 保存到数据库
      await this.saveActivityData(activityData);

      this.logger.log(`首充激活: ${characterId}`);

      return {
        success: true,
        activatedAt: Date.now(),
      };
    } catch (error) {
      this.logger.error('首充激活失败', error);
      throw error;
    }
  }

  // ==================== 私有方法（基于old项目） ====================

  /**
   * 创建新的活动数据
   */
  private createNewActivityData(characterId: string): ActivityData {
    return {
      uid: characterId,
      globalCurrActList: new Map(),
      globalActMgrInfo: new Map(),
      historyActList: new Map(),
      firstChargeStatus: FirstChargeStatus.NONE,
      openChargeStatus: FirstChargeStatus.NONE,
    };
  }

  /**
   * 加载全局活动（基于old项目loadGlobalAct方法）
   */
  private async loadGlobalActivities(activityData: ActivityData): Promise<void> {
    // 从配置表加载活动控制数据（基于old项目ActivityControl配置表）
    const activityConfigs = await this.getActivityConfigs();
    const now = Date.now();

    for (const config of activityConfigs) {
      const actId = config.Id;
      if (actId <= 0) continue;

      const actType = config.ActivityType;
      const timeType = config.TimeType;
      const periods = config.Periods;

      let startTime = 0;
      let endTime = 0;

      // 计算活动时间
      if (timeType === ActivityTimeType.PERSISTENT) {
        // 持久性活动
        const refreshCycle = config.RefreshCycle;
        const timeRange = this.calculatePersistentTime(refreshCycle, now);
        startTime = timeRange.startTime;
        endTime = timeRange.endTime;
      } else if (timeType === ActivityTimeType.CYCLE) {
        // 周期性活动
        startTime = this.timeToUnixTime(config.StartTime);
        endTime = this.timeToUnixTime(config.EndTime);

        // 检查活动是否过期
        if (now < startTime || now > endTime) {
          // 清理过期活动数据
          if (actType !== ActivityType.FIRST_CHARGE) {
            activityData.globalCurrActList.delete(actId);
            activityData.globalActMgrInfo.delete(actId);
            continue;
          }
        }
      }

      // 处理特殊活动类型
      if (actType === ActivityType.FIRST_CHARGE) {
        if (activityData.firstChargeStatus === FirstChargeStatus.ACTIVE) {
          continue; // 已激活，不显示
        }
        endTime = this.getMaxTime(); // 设置最大时间
      }

      if (actType === ActivityType.OPEN_GIFT_BAG) {
        if (activityData.openChargeStatus === FirstChargeStatus.ACTIVE) {
          continue; // 已激活，不显示
        }
      }

      // 检查期数变化
      if (timeType === ActivityTimeType.CYCLE) {
        const existingRecord = activityData.globalActMgrInfo.get(actId);
        if (existingRecord && existingRecord.periods !== periods) {
          // 期数变化，清理数据
          activityData.globalCurrActList.delete(actId);
          activityData.globalActMgrInfo.delete(actId);
        }
      }

      // 创建或更新活动信息
      if (!activityData.globalCurrActList.has(actId)) {
        const actInfo = this.createActivityInfo(actId, config, startTime, endTime);
        activityData.globalCurrActList.set(actId, actInfo);

        if (!activityData.globalActMgrInfo.has(actId)) {
          const actRecord = this.createActivityRecord(actId, actType, periods);
          activityData.globalActMgrInfo.set(actId, actRecord);
        }
      }
    }
  }

  /**
   * 检查并重新加载活动控制
   */
  private async checkAndReloadActivityControl(activityData: ActivityData): Promise<void> {
    const delActList: number[] = [];
    const currTime = Date.now();

    for (const [actId, actInfo] of activityData.globalCurrActList) {
      // 检查配置表中是否还存在该活动（基于old项目ActivityControl配置表查询）
      const config = await this.getActivityConfig(actId);
      if (!config) {
        delActList.push(actId);
        continue;
      }

      // 持久化活动不需要删除
      if (config.TimeType === ActivityTimeType.PERSISTENT) {
        continue;
      }

      // 检查活动是否过期
      if (currTime > actInfo.endTime) {
        delActList.push(actId);
      }
    }

    // 删除过期活动
    for (const actId of delActList) {
      activityData.globalCurrActList.delete(actId);
      activityData.globalActMgrInfo.delete(actId);
    }
  }

  /**
   * 创建活动信息
   */
  private createActivityInfo(actId: number, config: any, startTime: number, endTime: number): ActivityInfo {
    return {
      actId,
      actType: config.ActivityType,
      actName: config.ActivityName,
      startTime,
      endTime,
      timeType: config.TimeType,
      refreshCycle: config.RefreshCycle,
      periods: config.Periods,
      isActive: true,
      config,
    };
  }

  /**
   * 创建活动记录
   */
  private createActivityRecord(actId: number, actType: ActivityType, periods: number): ActivityRecord {
    const now = Date.now();
    return {
      actId,
      actType,
      periods,
      progress: 0,
      status: 0,
      rewards: [],
      data: {},
      createTime: now,
      updateTime: now,
    };
  }

  /**
   * 计算持久性活动时间
   */
  private calculatePersistentTime(refreshCycle: ActivityRefreshType, now: number): { startTime: number; endTime: number } {
    switch (refreshCycle) {
      case ActivityRefreshType.CROSS_DAY:
        return {
          startTime: this.beginningOfToday(now),
          endTime: this.endingOfToday(now),
        };
      case ActivityRefreshType.CROSS_WEEK:
        return {
          startTime: this.getWeekStartTime(now),
          endTime: this.getWeekEndTime(now),
        };
      case ActivityRefreshType.CROSS_MONTH:
        return {
          startTime: this.getMonthStartTime(now),
          endTime: this.getMonthEndTime(now),
        };
      default:
        return { startTime: now, endTime: now };
    }
  }

  /**
   * 检查奖励领取条件
   */
  private async checkRewardCondition(actRecord: ActivityRecord, rewardId: number, config: any): Promise<boolean> {
    // 根据活动类型和配置检查领取条件（基于old项目奖励条件逻辑）
    switch (config.actType) {
      case ActivityType.DAILY_TASK:
      case ActivityType.WEEKLY_TASK:
        // 任务类活动：检查进度是否达到要求
        return actRecord.progress >= (config.targetProgress || rewardId);
      case ActivityType.ACHIEVEMENT:
        // 成就类活动：检查是否完成
        return actRecord.status === 1; // 1表示已完成
      case ActivityType.FIRST_CHARGE:
        // 首充活动：检查是否已充值
        return actRecord.data?.hasCharged === true;
      default:
        // 默认逻辑：检查进度
        return actRecord.progress >= rewardId;
    }
  }

  /**
   * 获取奖励配置
   */
  private async getRewardConfig(actId: number, rewardId: number): Promise<any[]> {
    // 从配置表获取奖励配置（基于old项目ActivityReward配置表）
    try {
      // 先获取活动配置
      const actConfig = await this.getActivityConfig(actId);

      // 根据活动类型选择对应的奖励配置表
      let rewardConfig = null;
      if (actConfig && actConfig.activityType === ActivityType.FIRST_CHARGE) {
        // 首充活动使用turntableReward配置表
        rewardConfig = await this.gameConfig.turntableReward.get(rewardId);
      } else {
        // 其他活动类型也使用turntableReward作为通用奖励配置
        rewardConfig = await this.gameConfig.turntableReward.get(rewardId);
      }
      if (!rewardConfig) {
        this.logger.warn(`奖励配置不存在: actId=${actId}, rewardId=${rewardId}`);
        return [];
      }

      // 转换为标准奖励格式（基于TurntableReward配置表结构）
      const rewards = [];
      if (rewardConfig) {
        rewards.push({
          itemType: rewardConfig.type || 1, // 1=道具，2=货币，3=球员
          resId: rewardConfig.itemId, // 使用itemId字段
          num: rewardConfig.num || 1,
          name: rewardConfig.itemName || '',
        });
      }

      return rewards;
    } catch (error) {
      this.logger.error('获取奖励配置失败', error);
      return [];
    }
  }

  // 时间工具方法
  private timeToUnixTime(timeStr: string): number {
    return new Date(timeStr).getTime();
  }

  private getMaxTime(): number {
    return new Date('2099-12-31').getTime();
  }

  private beginningOfToday(timestamp: number): number {
    const date = new Date(timestamp);
    date.setHours(0, 0, 0, 0);
    return date.getTime();
  }

  private endingOfToday(timestamp: number): number {
    const date = new Date(timestamp);
    date.setHours(23, 59, 59, 999);
    return date.getTime();
  }

  private getWeekStartTime(timestamp: number): number {
    const date = new Date(timestamp);
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1);
    const monday = new Date(date.setDate(diff));
    monday.setHours(0, 0, 0, 0);
    return monday.getTime();
  }

  private getWeekEndTime(timestamp: number): number {
    const startTime = this.getWeekStartTime(timestamp);
    return startTime + (7 * 24 * 60 * 60 * 1000) - 1;
  }

  private getMonthStartTime(timestamp: number): number {
    const date = new Date(timestamp);
    return new Date(date.getFullYear(), date.getMonth(), 1).getTime();
  }

  private getMonthEndTime(timestamp: number): number {
    const date = new Date(timestamp);
    return new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59, 999).getTime();
  }

  // 数据访问方法
  private async getActivityData(characterId: string): Promise<ActivityData | null> {
    const activity = await this.eventRepository.findByCharacterId(characterId);
    if (!activity) return null;

    // 转换为业务数据格式
    const globalCurrActList = new Map<number, ActivityInfo>();
    activity.globalCurrActList.forEach(act => {
      globalCurrActList.set(act.actId, {
        actId: act.actId,
        actType: act.actType,
        actName: act.actName,
        startTime: act.startTime,
        endTime: act.endTime,
        timeType: act.timeType,
        refreshCycle: act.refreshCycle,
        periods: act.periods,
        isActive: act.isActive,
        config: act.config,
      });
    });

    const globalActMgrInfo = new Map<number, ActivityRecord>();
    activity.globalActMgrInfo.forEach(record => {
      globalActMgrInfo.set(record.actId, {
        actId: record.actId,
        actType: record.actType,
        periods: record.periods,
        progress: record.progress,
        status: record.status,
        rewards: record.rewards,
        data: record.data,
        createTime: record.createTime,
        updateTime: record.updateTime,
      });
    });

    return {
      uid: activity.uid,
      globalCurrActList,
      globalActMgrInfo,
      historyActList: this.convertHistoryActivityData(activity.historyActList), // 历史活动数据
      firstChargeStatus: activity.firstChargeStatus,
      openChargeStatus: activity.openChargeStatus,
    };
  }

  /**
   * 保存活动数据
   */
  private async saveActivityData(activityData: ActivityData): Promise<void> {
    // 转换为数据库格式
    const globalCurrActList = Array.from(activityData.globalCurrActList.values());
    const globalActMgrInfo = Array.from(activityData.globalActMgrInfo.values());

    // 检查是否存在记录
    const existingActivity = await this.eventRepository.findByCharacterId(activityData.uid);

    if (existingActivity) {
      // 更新现有记录
      await this.eventRepository.update(activityData.uid, {
        globalCurrActList,
        globalActMgrInfo,
        firstChargeStatus: activityData.firstChargeStatus,
        openChargeStatus: activityData.openChargeStatus,
      });
    } else {
      // 创建新记录
      await this.eventRepository.create({
        activityRecordId: this.generateActivityRecordId(),
        uid: activityData.uid,
        serverId: this.getServerId(), // 从环境变量或上下文获取
        globalCurrActList,
        globalActMgrInfo,
        firstChargeStatus: activityData.firstChargeStatus,
        openChargeStatus: activityData.openChargeStatus,
      });
    }
  }

  private async getActivityConfigs(): Promise<any[]> {
    try {
      // 从ActivityControl配置表获取活动配置（基于old项目ActivityControl）
      // 从ActiveControl配置表获取活动配置（基于old项目ActiveControl）
      const configs = await this.gameConfig.activeControl.getAll();
      return configs || [];
    } catch (error) {
      this.logger.error('获取活动配置失败', error);
      return [];
    }
  }

  private async getActivityConfig(actId: number): Promise<any | null> {
    try {
      // 从ActivityControl配置表获取单个活动配置（基于old项目ActivityControl）
      // 从ActiveControl配置表获取单个活动配置（基于old项目ActiveControl）
      const config = await this.gameConfig.activeControl.get(actId);
      return config || null;
    } catch (error) {
      this.logger.error('获取单个活动配置失败', error);
      return null;
    }
  }

  private generateActivityRecordId(): string {
    return `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 最佳11人抽奖
   * 严格基于old项目: Act.prototype.buyBestFootball
   */
  async buyBestFootball(characterId: string, index: number) {
    this.logger.log(`最佳11人抽奖: ${characterId}, 类型: ${index}`);

    try {
      // 参数校验
      if (index !== 1 && index !== 2) {
        return {
          code: -1,
          message: '抽奖类型参数错误',
          data: null,
        };
      }

      // 计算费用（基于old项目逻辑）
      let needGold = 0;
      if (index === 1) {
        // 单抽费用 - SystemParam[9011]
        const config = await this.gameConfig.systemParam.get(9011);
        needGold = config?.parameter || 50000;
      } else {
        // 十连抽费用
        const config = await this.gameConfig.systemParam.get(9011);
        needGold = (config?.parameter || 50000) * 10;
      }

      // 检查角色金币是否足够
      const goldCheckResult = await this.checkCharacterGold(characterId, needGold);
      if (!goldCheckResult.success) {
        return {
          code: -2,
          message: '金币不足',
          data: {
            required: needGold,
            current: goldCheckResult.currentGold,
          },
        };
      }

      // 执行抽奖逻辑
      const lotteryResult = await this.executeBestFootballLottery(characterId, index);
      if (lotteryResult.code !== 0) {
        return lotteryResult;
      }

      // 扣除金币
      const deductResult = await this.deductCharacterGold(characterId, needGold);
      if (!deductResult.success) {
        return {
          code: -3,
          message: '扣除金币失败',
          data: null,
        };
      }

      // 发放奖励
      await this.giveItemRewards(characterId, lotteryResult.data.itemList);

      return {
        code: 0,
        message: '抽奖成功',
        data: {
          itemList: lotteryResult.data.itemList,
          cost: needGold,
        },
      };

    } catch (error) {
      this.logger.error('最佳11人抽奖失败', error);
      return {
        code: -4,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 老虎机抽奖
   * 严格基于old项目: Act.prototype.buyTurntable
   */
  async buyTurntable(characterId: string, frequencyType: number) {
    this.logger.log(`老虎机抽奖: ${characterId}, 类型: ${frequencyType}`);

    try {
      // 参数校验
      if (frequencyType !== 1 && frequencyType !== 2) {
        return {
          code: -1,
          message: '抽奖类型参数错误',
          data: null,
        };
      }

      // 获取活动配置
      const turntableConfig = await this.gameConfig.turntableControl.get(frequencyType);
      if (!turntableConfig) {
        return {
          code: -2,
          message: '老虎机配置获取失败',
          data: null,
        };
      }

      // 检查免费次数（基于old项目免费次数逻辑）
      const freeTimesResult = await this.checkTurntableFreeTimes(characterId, frequencyType);
      if (freeTimesResult.hasFreeTime) {
        // 有免费次数，直接执行抽奖
        const lotteryResult = await this.executeTurntableLottery(characterId, frequencyType);
        if (lotteryResult.code === 0) {
          // 扣除免费次数
          await this.deductTurntableFreeTimes(characterId, frequencyType, 1);
        }
        return lotteryResult;
      }

      // 检查金币是否足够
      const needGold = turntableConfig.money || 0;
      const goldCheckResult = await this.checkCharacterGold(characterId, needGold);
      if (!goldCheckResult.success) {
        return {
          code: -3,
          message: '金币不足',
          data: {
            required: needGold,
            current: goldCheckResult.currentGold,
          },
        };
      }

      // 执行老虎机抽奖
      const lotteryResult = await this.executeTurntableLottery(characterId, frequencyType);
      if (lotteryResult.code !== 0) {
        return lotteryResult;
      }

      // 扣除金币
      const deductResult = await this.deductCharacterGold(characterId, needGold);
      if (!deductResult.success) {
        return {
          code: -4,
          message: '扣除金币失败',
          data: null,
        };
      }

      return {
        code: 0,
        message: '老虎机抽奖成功',
        data: lotteryResult.data,
      };

    } catch (error) {
      this.logger.error('老虎机抽奖失败', error);
      return {
        code: -5,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 检查角色金币是否足够
   */
  private async checkCharacterGold(characterId: string, amount: number): Promise<any> {
    try {
      // 调用Character服务获取角色信息
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        {
          characterId,
          serverId: 'server_001',
        }
      );

      if (!result || result.code !== 0) {
        return {
          success: false,
          currentGold: 0,
          message: '获取角色信息失败',
        };
      }

      const currentGold = result.data?.gold || 0;
      const hasEnough = currentGold >= amount;

      return {
        success: hasEnough,
        currentGold,
        required: amount,
        message: hasEnough ? '金币充足' : '金币不足',
      };
    } catch (error) {
      this.logger.error('检查角色金币失败', error);
      return {
        success: false,
        currentGold: 0,
        message: '检查金币失败',
      };
    }
  }

  /**
   * 扣除角色金币
   */
  private async deductCharacterGold(characterId: string, amount: number): Promise<any> {
    try {
      // 调用Character服务扣除金币
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.currency.subtract',
        {
          characterId,
          currencyDto: {
            currencyType: 'gold',
            amount,
            reason: 'activity_lottery',
          },
          serverId: 'server_001',
        }
      );

      if (!result || result.code !== 0) {
        return {
          success: false,
          message: '扣除金币失败',
        };
      }

      return {
        success: true,
        newBalance: result.data?.newBalance || 0,
        message: '扣除金币成功',
      };
    } catch (error) {
      this.logger.error('扣除角色金币失败', error);
      return {
        success: false,
        message: '扣除金币失败',
      };
    }
  }

  /**
   * 执行最佳11人抽奖逻辑
   * 基于old项目的randomBestFootball方法
   */
  private async executeBestFootballLottery(characterId: string, index: number): Promise<any> {
    try {
      // 实现保底机制和抽奖逻辑（基于old项目保底机制）
      const guaranteeResult = await this.checkBestFootballGuarantee(characterId);
      if (guaranteeResult.shouldTrigger) {
        // 触发保底，返回保底奖励
        const guaranteeItems = await this.getGuaranteeRewards(guaranteeResult.guaranteeType);
        await this.resetBestFootballGuarantee(characterId);
        return {
          code: 0,
          data: {
            itemList: guaranteeItems,
            isGuarantee: true,
          },
        };
      }
      const itemList = [];

      if (index === 1) {
        // 单抽
        const randomItem = await this.randomBestFootball(1, 1, 'BestFootball');
        itemList.push(...randomItem);
      } else {
        // 十连抽
        const randomItems = await this.randomBestFootball(1, 11, 'BestFootball');
        itemList.push(...randomItems);
      }

      return {
        code: 0,
        data: {
          itemList,
        },
      };
    } catch (error) {
      this.logger.error('执行最佳11人抽奖失败', error);
      return {
        code: -1,
        message: '抽奖执行失败',
        data: null,
      };
    }
  }

  /**
   * 执行老虎机抽奖逻辑
   * 基于old项目的老虎机抽奖实现
   */
  private async executeTurntableLottery(characterId: string, frequencyType: number): Promise<any> {
    try {
      const itemIdList = [];
      let status = 0; // 0=普通, 1=小王, 2=大王

      if (frequencyType === 1) {
        // 单抽
        const result = await this.randomTurntable(characterId);
        itemIdList.push(...result.itemIdList);
        status = result.status;
      } else {
        // 连抽
        for (let i = 0; i < 10; i++) {
          const result = await this.randomTurntable(characterId);
          itemIdList.push(...result.itemIdList);
          if (result.status > status) {
            status = result.status; // 取最高状态
          }
        }
      }

      return {
        code: 0,
        data: {
          itemIdList,
          status,
        },
      };
    } catch (error) {
      this.logger.error('执行老虎机抽奖失败', error);
      return {
        code: -1,
        message: '老虎机抽奖执行失败',
        data: null,
      };
    }
  }

  /**
   * 老虎机随机算法
   * 严格基于old项目: Act.prototype.randomTurntable
   */
  private async randomTurntable(characterId: string): Promise<any> {
    try {
      // 获取老虎机奖励配置
      const turntableRewards = await this.gameConfig.turntableReward.getAll();

      if (turntableRewards.length === 0) {
        return {
          itemIdList: [],
          status: 0,
        };
      }

      // 计算总权重
      let totalWeight = 0;
      for (const config of turntableRewards) {
        totalWeight += config.weight || 0;
      }

      if (totalWeight === 0) {
        return {
          itemIdList: [],
          status: 0,
        };
      }

      // 生成随机数
      const randNum = Math.floor(Math.random() * totalWeight) + 1;
      let currentWeight = 0;

      // 权重随机选择
      for (const config of turntableRewards) {
        currentWeight += config.weight || 0;

        if (currentWeight >= randNum) {
          // 根据配置类型确定状态
          let status = 0;
          if (config.type === 1) {
            status = 1; // 小王
          } else if (config.type === 2) {
            status = 2; // 大王
          }

          // 获取奖励物品
          const itemIdList = [];
          if (config.itemId && config.itemId > 0) {
            itemIdList.push(config.itemId);
          }

          this.logger.debug(`老虎机抽奖结果: 物品${config.itemId}, 状态: ${status}`);

          return {
            itemIdList,
            status,
          };
        }
      }

      // 兜底返回
      const firstConfig = turntableRewards[0];
      return {
        itemIdList: firstConfig.itemId ? [firstConfig.itemId] : [],
        status: 0,
      };
    } catch (error) {
      this.logger.error('老虎机随机算法失败', error);
      return {
        itemIdList: [],
        status: 0,
      };
    }
  }

  /**
   * 执行拉霸抽奖逻辑
   * 基于old项目的拉霸抽奖实现
   */
  private async executeSlotsLottery(
    characterId: string,
    frequencyType: number,
    securityMoney: number,
    actId: number
  ): Promise<any> {
    try {
      const itemIdList = [];
      let currentSecurityMoney = securityMoney;

      if (frequencyType === 1) {
        // 单抽
        const result = await this.randomSlots(actId, currentSecurityMoney,characterId);
        const groupId = result.groupId;
        currentSecurityMoney = result.securityMoney;

        const awardObj = await this.getWeightGroupIdAward('PullerAward', groupId);
        itemIdList.push(awardObj);
      } else {
        // 十连抽（买10送1）
        for (let i = 0; i < 11; i++) {
          const result = await this.randomSlots(actId, currentSecurityMoney,characterId);
          const groupId = result.groupId;
          currentSecurityMoney = result.securityMoney;

          const awardObj = await this.getWeightGroupIdAward('PullerAward', groupId);
          itemIdList.push(awardObj);
        }
      }

      return {
        code: 0,
        data: {
          itemIdList,
          status: 0,
          securityMoney: currentSecurityMoney,
        },
      };
    } catch (error) {
      this.logger.error('执行拉霸抽奖失败', error);
      return {
        code: -1,
        message: '拉霸抽奖执行失败',
        data: null,
      };
    }
  }

  /**
   * 拉霸随机算法
   * 严格基于old项目: Act.prototype.randomSlots
   */
  private async randomSlots(actId: number, securityMoney: number, characterId?: string): Promise<any> {
    try {
      // 获取保底配置
      const guaranteeNumConfig = await this.gameConfig.systemParam.get(9021); // SLOTS_GUARANTEE_NUM
      const guaranteeNum = guaranteeNumConfig?.parameter || 100;

      const clearMoneyConfig = await this.gameConfig.systemParam.get(9022); // SLOTS_GUARANTEE_MONEY
      const clearMoney = clearMoneyConfig?.parameter || 1000000;

      // 获取活动记录中的累计抽奖次数（基于old项目活动记录）
      let currentDrawCount = 0;
      if (characterId) {
        const activityData = await this.getActivityData(characterId);
        const actRecord = activityData?.globalActMgrInfo.get(actId);
        currentDrawCount = actRecord?.data?.slotsDrawCount || 0;
      }

      // 保底机制检查
      if (currentDrawCount >= guaranteeNum) {
        // 触发保底
        return {
          groupId: 3, // 保底组
          securityMoney: securityMoney,
        };
      }

      // 安全金额保底检查
      if (securityMoney >= clearMoney &&
          currentDrawCount > (guaranteeNum / 3) &&
          currentDrawCount < (guaranteeNum - guaranteeNum / 3)) {
        return {
          groupId: 3, // 保底组
          securityMoney: 0, // 清空安全金额
        };
      }

      // 正常权重抽奖
      const pullerAwardConfigs = await this.gameConfig.pullerAward.getAll();
      const groupWeights = new Map();

      // 计算各组权重（排除保底组3）
      for (const config of pullerAwardConfigs) {
        if (config.group === 3) continue; // 跳过保底组

        const groupId = config.group;
        const weight = config.weight;

        if (!groupWeights.has(groupId)) {
          groupWeights.set(groupId, weight);
        }
      }

      // 权重随机选择
      const groupList = [];
      for (const [groupId, weight] of groupWeights) {
        for (let i = 0; i < weight; i++) {
          groupList.push(groupId);
        }
      }

      // 随机排序
      groupList.sort(() => 0.5 - Math.random());

      const selectedGroupId = groupList[Math.floor(Math.random() * groupList.length)];

      return {
        groupId: selectedGroupId || 1,
        securityMoney: securityMoney,
      };
    } catch (error) {
      this.logger.error('拉霸随机算法失败', error);
      return {
        groupId: 1,
        securityMoney: securityMoney,
      };
    }
  }

  /**
   * 根据权重组ID获取奖励
   * 基于old项目: getWeightGrepIdAward
   */
  private async getWeightGroupIdAward(tableName: string, groupId: number): Promise<any> {
    try {
      // 获取指定组的奖励配置
      const configs = await this.gameConfig.pullerAward.getAll();
      const groupConfigs = configs.filter(config => config.group === groupId);

      if (groupConfigs.length === 0) {
        return { itemId: 0, num: 0, id: 0 };
      }

      // 权重随机选择
      const randomList = [];
      for (const config of groupConfigs) {
        for (let i = 0; i < config.weight; i++) {
          randomList.push({
            itemId: config.itemId,
            num: config.num,
            resId: config.id,
          });
        }
      }

      const selectedItem = randomList[Math.floor(Math.random() * randomList.length)];

      return {
        itemId: selectedItem.itemId,
        num: selectedItem.num,
        id: selectedItem.resId,
      };
    } catch (error) {
      this.logger.error('获取权重组奖励失败', error);
      return { itemId: 0, num: 0, id: 0 };
    }
  }

  /**
   * 随机抽奖算法
   * 基于old项目: Act.prototype.randomBestFootball
   */
  private async randomBestFootball(type: number, randomNum: number, tableName: string): Promise<number[]> {
    try {
      // 从配置表获取真实的抽奖配置（基于old项目抽奖配置表）
      // 根据tableName选择对应的抽奖配置表（基于old项目randomBestFootball方法）
      let lotteryConfig = null;
      if (tableName === 'BestFootball') {
        lotteryConfig = await this.gameConfig.bestFootball.getAll();
      } else if (tableName === 'BackWeekend') {
        lotteryConfig = await this.gameConfig.backWeekend.getAll();
      } else {
        // 默认使用BestFootball配置表
        lotteryConfig = await this.gameConfig.bestFootball.getAll();
      }
      if (!lotteryConfig || !Array.isArray(lotteryConfig)) {
        this.logger.warn(`抽奖配置不存在: ${tableName}`);
        return [];
      }

      // 基于old项目randomBestFootball方法实现权重抽奖
      const itemList = [];
      for (let i = 0; i < randomNum; i++) {
        const randomItem = this.weightedRandomSelect(lotteryConfig);
        if (randomItem) {
          itemList.push(randomItem.id); // 使用id字段而不是itemId
        }
      }

      return itemList;
    } catch (error) {
      this.logger.error('随机抽奖算法失败', error);
      return [];
    }
  }

  /**
   * 拉霸抽奖
   * 严格基于old项目: Act.prototype.buySlots
   */
  async buySlots(characterId: string, frequencyType: number, securityMoney: number = 0) {
    this.logger.log(`拉霸抽奖: ${characterId}, 类型: ${frequencyType}, 保底金额: ${securityMoney}`);

    try {
      // 参数校验
      if (frequencyType !== 1 && frequencyType !== 2) {
        return {
          code: -1,
          message: '抽奖类型参数错误',
          data: null,
        };
      }

      // 获取活动配置
      const activityConfigs = await this.gameConfig.activeControl.getAll();
      const slotsActivity = activityConfigs.find(config =>
        config.activityType === 3 // ACT_TYPE_SLOTS
      );

      if (!slotsActivity) {
        return {
          code: -2,
          message: '拉霸活动未开启',
          data: null,
        };
      }

      // 检查免费次数（基于old项目免费次数逻辑）
      const freeTimesResult = await this.checkSlotsFreeTimes(characterId, frequencyType);
      if (freeTimesResult.hasFreeTime) {
        // 有免费次数，直接执行抽奖
        const lotteryResult = await this.executeSlotsLottery(characterId, frequencyType, slotsActivity.id, 0);
        if (lotteryResult.code === 0) {
          // 扣除免费次数
          await this.deductSlotsFreeTimes(characterId, frequencyType, 1);
        }
        return lotteryResult;
      }

      // 获取拉霸费用配置
      let needGold = 0;
      if (frequencyType === 1) {
        // 单抽费用
        const config = await this.gameConfig.systemParam.get(9019); // SLOTS_SINGLE_COST
        needGold = config?.parameter || 10000;
      } else {
        // 十连抽费用
        const config = await this.gameConfig.systemParam.get(9020); // SLOTS_TEN_COST
        needGold = config?.parameter || 90000;
      }

      // 检查金币是否足够
      const goldCheckResult = await this.checkCharacterGold(characterId, needGold);
      if (!goldCheckResult.success) {
        return {
          code: -3,
          message: '金币不足',
          data: {
            required: needGold,
            current: goldCheckResult.currentGold,
          },
        };
      }

      // 执行拉霸抽奖
      const lotteryResult = await this.executeSlotsLottery(
        characterId,
        frequencyType,
        securityMoney,
        slotsActivity.id
      );

      if (lotteryResult.code !== 0) {
        return lotteryResult;
      }

      // 扣除金币
      const deductResult = await this.deductCharacterGold(characterId, needGold);
      if (!deductResult.success) {
        return {
          code: -4,
          message: '扣除金币失败',
          data: null,
        };
      }

      return {
        code: 0,
        message: '拉霸抽奖成功',
        data: {
          itemIdList: lotteryResult.data.itemIdList,
          status: lotteryResult.data.status,
          securityMoney: lotteryResult.data.securityMoney,
          cost: needGold,
        },
      };

    } catch (error) {
      this.logger.error('拉霸抽奖失败', error);
      return {
        code: -5,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 周末返场抽奖
   * 严格基于old项目: Act.prototype.weekDayEncore
   */
  async weekDayEncore(characterId: string) {
    this.logger.log(`周末返场抽奖: ${characterId}`);

    try {
      // 获取周末返场费用
      const config = await this.gameConfig.systemParam.get(9018);
      const needGold = config?.parameter || 20000;

      // 检查金币是否足够
      const goldCheckResult = await this.checkCharacterGold(characterId, needGold);
      if (!goldCheckResult.success) {
        return {
          code: -1,
          message: '金币不足',
          data: {
            required: needGold,
            current: goldCheckResult.currentGold,
          },
        };
      }

      // 执行周末返场抽奖
      const randomList = await this.randomBestFootball(1, 1, 'BackWeekend');
      if (randomList.length === 0) {
        return {
          code: -2,
          message: '抽奖配置错误',
          data: null,
        };
      }

      const itemId = randomList[0];

      // 扣除金币
      const deductResult = await this.deductCharacterGold(characterId, needGold);
      if (!deductResult.success) {
        return {
          code: -3,
          message: '扣除金币失败',
          data: null,
        };
      }

      // 发放奖励
      await this.giveItemRewards(characterId, [itemId]);

      return {
        code: 0,
        message: '周末返场抽奖成功',
        data: {
          itemId,
          cost: needGold,
        },
      };

    } catch (error) {
      this.logger.error('周末返场抽奖失败', error);
      return {
        code: -4,
        message: '抽奖系统异常',
        data: null,
      };
    }
  }

  /**
   * 发放物品奖励
   * 调用Character服务的Inventory模块进行奖励发放
   */
  private async giveItemRewards(characterId: string, itemList: number[]): Promise<void> {
    try {
      for (const itemId of itemList) {
        // 调用Character服务的Inventory模块添加物品到背包
        const result = await this.microserviceClient.call(
          MICROSERVICE_NAMES.CHARACTER_SERVICE,
          'inventory.addItemToBag',
          {
            characterId,
            bookMarkId: 1, // 默认背包页签
            itemId: itemId.toString(),
            serverId: 'server_001',
          }
        );

        if (result && result.code === 0) {
          this.logger.log(`发放物品奖励成功: ${characterId}, 物品ID: ${itemId}`);
        } else {
          this.logger.warn(`发放物品奖励失败: ${characterId}, 物品ID: ${itemId}, 错误: ${result?.message || '未知错误'}`);
        }
      }
    } catch (error) {
      this.logger.error('发放物品奖励失败', error);
    }
  }

  /**
   * 发放球员奖励
   * 集成Hero服务创建球员
   */
  private async giveHeroRewards(characterId: string, heroIdList: number[]): Promise<void> {
    try {
      for (const heroId of heroIdList) {
        // 调用Hero服务创建球员
        const result = await this.microserviceClient.call(
          MICROSERVICE_NAMES.HERO_SERVICE,
          'hero.createHero',
          {
            characterId,
            heroConfigId: heroId,
            source: 'activity_lottery',
            serverId: 'server_001',
          }
        );

        if (result && result.code === 0) {
          this.logger.log(`发放球员奖励成功: ${characterId}, 球员ID: ${heroId}`);
        } else {
          this.logger.warn(`发放球员奖励失败: ${characterId}, 球员ID: ${heroId}, 错误: ${result?.message || '未知错误'}`);
        }
      }
    } catch (error) {
      this.logger.error('发放球员奖励失败', error);
    }
  }

  /**
   * 发放货币奖励
   * 集成Character服务增加货币
   */
  private async giveCurrencyRewards(characterId: string, currencyType: string, amount: number): Promise<void> {
    try {
      // 调用Character服务增加货币
      const result = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.currency.add',
        {
          characterId,
          currencyDto: {
            currencyType,
            amount,
            reason: 'activity_lottery',
          },
          serverId: 'server_001',
        }
      );

      if (result && result.code === 0) {
        this.logger.log(`发放货币奖励成功: ${characterId}, 类型: ${currencyType}, 数量: ${amount}`);
      } else {
        this.logger.warn(`发放货币奖励失败: ${characterId}, 类型: ${currencyType}, 数量: ${amount}, 错误: ${result?.message || '未知错误'}`);
      }
    } catch (error) {
      this.logger.error('发放货币奖励失败', error);
    }
  }

  /**
   * 发放活动奖励
   * 基于old项目: 奖励发放逻辑
   */
  private async distributeActivityRewards(characterId: string, rewards: any[]): Promise<void> {
    try {
      for (const reward of rewards) {
        switch (reward.itemType) {
          case 1: // 道具
            await this.microserviceClient.call(
              MICROSERVICE_NAMES.CHARACTER_SERVICE,
              'item.addItem',
              {
                characterId,
                resId: reward.resId,
                num: reward.num,
                reason: 'activity_reward'
              }
            );
            break;
          case 2: // 货币
            await this.microserviceClient.call(
              MICROSERVICE_NAMES.CHARACTER_SERVICE,
              'character.currency.add',
              {
                characterId,
                currencyDto: {
                  currencyType: reward.resId === 1 ? 'gold' : 'cash',
                  amount: reward.num,
                  reason: 'activity_reward'
                }
              }
            );
            break;
          case 3: // 球员
            await this.microserviceClient.call(
              MICROSERVICE_NAMES.HERO_SERVICE,
              'hero.addHero',
              {
                characterId,
                resId: reward.resId,
                reason: 'activity_reward'
              }
            );
            break;
        }
      }
    } catch (error) {
      this.logger.error('发放活动奖励失败', error);
    }
  }

  /**
   * 转换历史活动数据
   * 基于old项目: historyActList Map结构
   */
  private convertHistoryActivityData(historyList: any[]): Map<number, any> {
    const historyMap = new Map();
    if (historyList && Array.isArray(historyList)) {
      historyList.forEach(history => {
        historyMap.set(history.actId, {
          actId: history.actId,
          periods: history.periods,
          data: history.data,
          archiveTime: history.archiveTime,
        });
      });
    }
    return historyMap;
  }

  /**
   * 获取服务器ID
   * 从环境变量或配置获取
   */
  private getServerId(): string {
    return process.env.SERVER_ID || 'server_001';
  }

  /**
   * 检查老虎机免费次数
   * 基于old项目: 免费次数检查逻辑
   */
  private async checkTurntableFreeTimes(characterId: string, frequencyType: number): Promise<any> {
    try {
      // TODO: 实现免费次数检查逻辑
      return { hasFreeTime: false, remainingTimes: 0 };
    } catch (error) {
      this.logger.error('检查老虎机免费次数失败', error);
      return { hasFreeTime: false, remainingTimes: 0 };
    }
  }

  /**
   * 扣除老虎机免费次数
   */
  private async deductTurntableFreeTimes(characterId: string, frequencyType: number, count: number): Promise<void> {
    try {
      // TODO: 实现免费次数扣除逻辑
      this.logger.debug(`扣除老虎机免费次数: ${characterId}, 类型: ${frequencyType}, 次数: ${count}`);
    } catch (error) {
      this.logger.error('扣除老虎机免费次数失败', error);
    }
  }

  /**
   * 检查最佳11人保底
   * 基于old项目: 保底机制检查
   */
  private async checkBestFootballGuarantee(characterId: string): Promise<any> {
    try {
      // TODO: 实现保底检查逻辑
      return { shouldTrigger: false, guaranteeType: 0 };
    } catch (error) {
      this.logger.error('检查最佳11人保底失败', error);
      return { shouldTrigger: false, guaranteeType: 0 };
    }
  }

  /**
   * 获取保底奖励
   */
  private async getGuaranteeRewards(guaranteeType: number): Promise<number[]> {
    try {
      // TODO: 实现保底奖励获取逻辑
      return [1001]; // 默认保底奖励
    } catch (error) {
      this.logger.error('获取保底奖励失败', error);
      return [];
    }
  }

  /**
   * 重置最佳11人保底
   */
  private async resetBestFootballGuarantee(characterId: string): Promise<void> {
    try {
      // TODO: 实现保底重置逻辑
      this.logger.debug(`重置最佳11人保底: ${characterId}`);
    } catch (error) {
      this.logger.error('重置最佳11人保底失败', error);
    }
  }

  /**
   * 检查拉霸免费次数
   */
  private async checkSlotsFreeTimes(characterId: string, frequencyType: number): Promise<any> {
    try {
      // TODO: 实现拉霸免费次数检查逻辑
      return { hasFreeTime: false, remainingTimes: 0 };
    } catch (error) {
      this.logger.error('检查拉霸免费次数失败', error);
      return { hasFreeTime: false, remainingTimes: 0 };
    }
  }

  /**
   * 扣除拉霸免费次数
   */
  private async deductSlotsFreeTimes(characterId: string, frequencyType: number, count: number): Promise<void> {
    try {
      // TODO: 实现拉霸免费次数扣除逻辑
      this.logger.debug(`扣除拉霸免费次数: ${characterId}, 类型: ${frequencyType}, 次数: ${count}`);
    } catch (error) {
      this.logger.error('扣除拉霸免费次数失败', error);
    }
  }

  /**
   * 权重随机选择
   * 基于old项目: randomBestFootball权重随机算法
   */
  private weightedRandomSelect(itemList: any[]): any {
    try {
      // 基于old项目randomBestFootball方法实现
      const randomList = [];

      // 构建权重数组（基于old项目逻辑）
      for (const item of itemList) {
        const weight = item.rate || item.weight || 1; // 使用rate字段作为权重
        for (let j = 0; j < weight; j++) {
          randomList.push(item);
        }
      }

      if (randomList.length === 0) {
        return null;
      }

      // 随机选择一个
      const randomIndex = Math.floor(Math.random() * randomList.length);
      return randomList[randomIndex];
    } catch (error) {
      this.logger.error('权重随机选择失败', error);
      return null;
    }
  }
}
