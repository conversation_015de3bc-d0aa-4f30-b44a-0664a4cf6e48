/**
 * Item Schema - 严格基于old项目item.js重新设计
 * 确保与old项目的数据结构100%一致
 */

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// 单个物品 - 严格基于old项目newItem方法
@Schema({ _id: false })
export class ItemInstance {
  @Prop({ required: true })
  uid: string;                    // 物品的uid - 对应old项目的Uid

  @Prop({ required: true })
  resId: number;                  // 物品的资源ID - 对应old项目的ResID

  @Prop({ required: true })
  num: number;                    // 物品个数 - 对应old项目的Num

  @Prop({ default: 0 })
  bind: number;                   // 绑定：0-未绑定，1-已绑定 - 对应old项目的Bind

  @Prop({ required: true })
  type: number;                   // 物品类别，ITEM_TYPE_ - 对应old项目的Type

  @Prop({ default: 0 })
  timeLimit: number;              // 道具限时 0xFFFF 无限制时间 - 对应old项目的TimeLimit

  @Prop({ default: 0 })
  invalid: number;                // 是否失效：0-未失效，1-已失效 - 对应old项目的Invalid
}

// 物品堆叠映射项 - 基于old项目resId2Uid结构
@Schema({ _id: false })
export class ResId2UidItem {
  @Prop({ required: true })
  itemUid: string;                // 物品UID

  @Prop({ required: true })
  resId: number;                  // 物品配置ID
}

// 主Item Schema - 严格基于old项目Item实体
@Schema({
  collection: 'items',
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})
export class Item {
  @Prop({ required: true, index: true })
  uid: string;                    // 玩家UID - 对应old项目的uid

  @Prop({ required: true, index: true })
  characterId: string;            // 角色ID (新增字段，用于微服务架构)

  @Prop({ required: true, index: true })
  serverId: string;               // 服务器ID (新增字段，用于微服务架构)

  // 所有物品 - 对应old项目的item (Map转为Array)
  @Prop({ type: [ItemInstance], default: [] })
  item: ItemInstance[];

  // 物品堆叠映射表 - 对应old项目的resId2Uid (Map转为Object)
  // ItemResId => [ Uid, Uid] 物品堆叠映射表
  @Prop({ type: Object, default: {} })
  resId2Uid: Record<string, ResId2UidItem[]>;
}

export const ItemSchema = SchemaFactory.createForClass(Item);

// 定义Document类型和方法接口
export interface ItemMethods {
  getItem(uid: string): ItemInstance | null;
  hasItemByConfigId(resId: number): boolean;
  getPartialStackItemId(resId: number, limitNum: number): string;
  addToConfigMapping(resId: number, uid: string): void;
  removeFromConfigMapping(resId: number, uid: string): void;
  removeItemInstance(uid: string): void;
  getItemNum(uid: string): number;
  getItemNumByResID(resId: number): number;
  checkItemIsEnough(resId: number, needNum: number): boolean;
  addItemUseCount(resId: number, num: number, config: any): number;
}

export type ItemDocument = Item & Document & ItemMethods;

// 创建索引
ItemSchema.index({ uid: 1 }, { unique: true });
ItemSchema.index({ characterId: 1 });
ItemSchema.index({ serverId: 1 });

// 添加实例方法 - 基于old项目的核心方法

/**
 * 获取物品
 * 基于old项目: getItem方法
 */
ItemSchema.methods.getItem = function(uid: string): ItemInstance | null {
  return this.item.find((item: ItemInstance) => item.uid === uid) || null;
};

/**
 * 检查物品是否存在
 * 基于old项目: _hasItemResID2Uid方法，优化命名为hasItemByConfigId
 */
ItemSchema.methods.hasItemByConfigId = function(resId: number): boolean {
  return !!this.resId2Uid[resId.toString()] && this.resId2Uid[resId.toString()].length > 0;
};

/**
 * 获取指定配置ID的未满堆叠的物品UID
 * 基于old项目: _getItemNotEnoughUidByResID方法，优化命名为getPartialStackItemId
 */
ItemSchema.methods.getPartialStackItemId = function(resId: number, limitNum: number): string {
  const resIdStr = resId.toString();
  if (!this.resId2Uid[resIdStr]) {
    return '';
  }

  for (const uidItem of this.resId2Uid[resIdStr]) {
    const item = this.getItem(uidItem.itemUid);
    if (item && item.num < limitNum) {
      return uidItem.itemUid;
    }
  }

  return '';
};

/**
 * 添加物品到配置ID映射
 * 基于old项目: _addResId2Uid方法，优化命名为addToConfigMapping
 */
ItemSchema.methods.addToConfigMapping = function(resId: number, uid: string): void {
  const resIdStr = resId.toString();
  if (!this.resId2Uid[resIdStr]) {
    this.resId2Uid[resIdStr] = [];
  }

  this.resId2Uid[resIdStr].push({
    itemUid: uid,
    resId: resId
  });
};

/**
 * 从配置ID映射中删除物品
 * 基于old项目: _delResId2Uid方法，优化命名为removeFromConfigMapping
 */
ItemSchema.methods.removeFromConfigMapping = function(resId: number, uid: string): void {
  const resIdStr = resId.toString();
  if (!this.resId2Uid[resIdStr]) {
    return;
  }

  const index = this.resId2Uid[resIdStr].findIndex(item => item.itemUid === uid);
  if (index !== -1) {
    this.resId2Uid[resIdStr].splice(index, 1);

    // 如果数组为空，删除整个键
    if (this.resId2Uid[resIdStr].length === 0) {
      delete this.resId2Uid[resIdStr];
    }
  }
};

/**
 * 删除物品实例
 * 基于old项目: _delItem方法，优化命名为removeItemInstance
 */
ItemSchema.methods.removeItemInstance = function(uid: string): void {
  const index = this.item.findIndex(item => item.uid === uid);
  if (index !== -1) {
    this.item.splice(index, 1);
  }
};

/**
 * 获取物品数量
 * 基于old项目: getItemNum方法
 */
ItemSchema.methods.getItemNum = function(uid: string): number {
  const item = this.getItem(uid);
  return item ? item.num : 0;
};

/**
 * 根据ResID获取物品总数量
 * 基于old项目: getItemNumByResID方法
 */
ItemSchema.methods.getItemNumByResID = function(resId: number): number {
  const resIdStr = resId.toString();
  if (!this.resId2Uid[resIdStr]) {
    return 0;
  }

  let totalNum = 0;
  for (const uidItem of this.resId2Uid[resIdStr]) {
    const item = this.getItem(uidItem.itemUid);
    if (item) {
      totalNum += item.num;
    }
  }

  return totalNum;
};

/**
 * 检查是否有足够的物品
 * 基于old项目: checkItemIsEnough方法
 */
ItemSchema.methods.checkItemIsEnough = function(resId: number, needNum: number): boolean {
  const haveNum = this.getItemNumByResID(resId);
  return haveNum >= needNum;
};

/**
 * 计算添加物品需要的格子数
 * 基于old项目: addItemUseCount方法
 */
ItemSchema.methods.addItemUseCount = function(resId: number, num: number, config: any): number {
  if (!config) {
    return 9999999; // 最大值
  }

  const isSingle = config.isSingle;

  // 不能支持堆叠的物品，物品的数量是多少就占用多少个格子
  if (isSingle === 0) {
    return num;
  }

  const limitNum = config.isSingleParameter;
  if (!num || !limitNum || num <= 0 || limitNum <= 0) {
    return 9999999;
  }

  // 检查现有物品是否有空间
  let remainingNum = num;
  const resIdStr = resId.toString();

  if (this.resId2Uid[resIdStr]) {
    for (const uidItem of this.resId2Uid[resIdStr]) {
      const item = this.getItem(uidItem.itemUid);
      if (item && item.num < limitNum) {
        const canAdd = limitNum - item.num;
        remainingNum -= Math.min(canAdd, remainingNum);
        if (remainingNum <= 0) {
          return 0; // 不需要新格子
        }
      }
    }
  }

  // 计算需要的新格子数
  return Math.ceil(remainingNum / limitNum);
};
