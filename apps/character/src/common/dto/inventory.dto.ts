/**
 * 背包相关的数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsBoolean, IsArray, IsEnum, Min, Max, Length, ValidateNested } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { BookMarkType, InventoryType } from '../schemas/inventory.schema';

// 背包物品DTO
export class InventoryItemDto {
  @ApiProperty({ description: '物品ID' })
  @IsString()
  itemId: string;

  @ApiProperty({ description: '配置表ID' })
  @IsNumber()
  configId: number;

  @ApiProperty({ description: '数量', minimum: 1 })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiProperty({ description: '槽位' })
  @IsNumber()
  @Min(1)
  slot: number;

  @ApiPropertyOptional({ description: '获得时间' })
  @IsOptional()
  @IsNumber()
  obtainTime?: number;

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsNumber()
  expireTime?: number;

  @ApiPropertyOptional({ description: '是否锁定' })
  @IsOptional()
  @IsBoolean()
  locked?: boolean;

  @ApiPropertyOptional({ description: '来源' })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({ description: '当前耐久度' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  durability?: number;

  @ApiPropertyOptional({ description: '最大耐久度' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxDurability?: number;

  @ApiPropertyOptional({ description: '强化等级' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  enhanceLevel?: number;

  @ApiPropertyOptional({ description: '附加属性' })
  @IsOptional()
  attributes?: Record<string, number>;
}

// 添加物品到背包DTO
export class AddItemToInventoryDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '背包类型', enum: InventoryType })
  @IsEnum(InventoryType)
  inventoryType: InventoryType;

  @ApiProperty({ description: '物品配置ID' })
  @IsNumber()
  configId: number;

  @ApiProperty({ description: '数量', minimum: 1 })
  @IsNumber()
  @Min(1)
  quantity: number;

  @ApiPropertyOptional({ description: '指定槽位' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  slot?: number;

  @ApiPropertyOptional({ description: '来源' })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({ description: '过期时间' })
  @IsOptional()
  @IsNumber()
  expireTime?: number;

  @ApiPropertyOptional({ description: '附加属性' })
  @IsOptional()
  attributes?: Record<string, number>;
}

// 移除物品DTO
export class RemoveItemFromInventoryDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '背包类型', enum: InventoryType })
  @IsEnum(InventoryType)
  inventoryType: InventoryType;

  @ApiProperty({ description: '物品ID' })
  @IsString()
  itemId: string;

  @ApiProperty({ description: '数量', minimum: 1 })
  @IsNumber()
  @Min(1)
  quantity: number;
}

// 移动物品DTO
export class MoveItemDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '源背包类型', enum: InventoryType })
  @IsEnum(InventoryType)
  sourceInventoryType: InventoryType;

  @ApiProperty({ description: '目标背包类型', enum: InventoryType })
  @IsEnum(InventoryType)
  targetInventoryType: InventoryType;

  @ApiProperty({ description: '物品ID' })
  @IsString()
  itemId: string;

  @ApiProperty({ description: '源槽位' })
  @IsNumber()
  @Min(1)
  sourceSlot: number;

  @ApiProperty({ description: '目标槽位' })
  @IsNumber()
  @Min(1)
  targetSlot: number;

  @ApiPropertyOptional({ description: '移动数量' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  quantity?: number;
}

// 交换物品位置DTO
export class SwapItemsDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '背包类型', enum: InventoryType })
  @IsEnum(InventoryType)
  inventoryType: InventoryType;

  @ApiProperty({ description: '第一个槽位' })
  @IsNumber()
  @Min(1)
  slot1: number;

  @ApiProperty({ description: '第二个槽位' })
  @IsNumber()
  @Min(1)
  slot2: number;
}

// 扩展背包DTO
export class ExpandInventoryDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '背包类型', enum: InventoryType })
  @IsEnum(InventoryType)
  inventoryType: InventoryType;

  @ApiProperty({ description: '扩展数量', minimum: 1 })
  @IsNumber()
  @Min(1)
  expandCount: number;
}

// 整理背包DTO
export class SortInventoryDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiProperty({ description: '背包类型', enum: InventoryType })
  @IsEnum(InventoryType)
  inventoryType: InventoryType;

  @ApiPropertyOptional({ description: '排序方式' })
  @IsOptional()
  @IsString()
  sortBy?: 'type' | 'quality' | 'name' | 'quantity' | 'obtainTime';

  @ApiPropertyOptional({ description: '排序顺序' })
  @IsOptional()
  @IsString()
  sortOrder?: 'asc' | 'desc';
}

// 清理过期物品DTO
export class CleanExpiredItemsDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '背包类型', enum: InventoryType })
  @IsOptional()
  @IsEnum(InventoryType)
  inventoryType?: InventoryType;
}

// 背包信息响应DTO
export class InventoryInfoDto {
  @ApiProperty({ description: '背包ID' })
  inventoryId: string;

  @ApiProperty({ description: '角色ID' })
  characterId: string;

  @ApiProperty({ description: '区服ID' })
  serverId: string;

  @ApiProperty({ description: '背包类型' })
  type: InventoryType;

  @ApiProperty({ description: '背包容量' })
  capacity: number;

  @ApiProperty({ description: '已使用槽位' })
  usedSlots: number;

  @ApiProperty({ description: '物品列表' })
  items: InventoryItemDto[];

  @ApiProperty({ description: '扩展次数' })
  expandCount: number;

  @ApiProperty({ description: '最大扩展次数' })
  maxExpand: number;

  @ApiProperty({ description: '下次扩展费用' })
  expandCost: number;

  @ApiProperty({ description: '排序方式' })
  sortBy: string;

  @ApiProperty({ description: '排序顺序' })
  sortOrder: string;

  @ApiProperty({ description: '过滤器' })
  filters: string[];

  @ApiProperty({ description: '创建时间' })
  createTime: number;

  @ApiProperty({ description: '最后整理时间' })
  lastSortTime: number;

  @ApiProperty({ description: '最后清理时间' })
  lastCleanTime: number;

  @ApiProperty({ description: '剩余容量' })
  remainingCapacity: number;

  @ApiProperty({ description: '是否已满' })
  isFull: boolean;

  @ApiProperty({ description: '使用率' })
  usageRate: number;

  @ApiProperty({ description: '是否可扩展' })
  canExpand: boolean;

  @ApiProperty({ description: '过期物品数量' })
  expiredItemsCount: number;
}

// 背包列表查询DTO
export class GetInventoryListDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '背包类型过滤' })
  @IsOptional()
  @IsEnum(InventoryType)
  type?: InventoryType;

  @ApiPropertyOptional({ description: '是否包含物品详情' })
  @IsOptional()
  @IsBoolean()
  includeItems?: boolean;
}

// 物品搜索DTO
export class SearchItemsDto {
  @ApiProperty({ description: '角色ID' })
  @IsString()
  characterId: string;

  @ApiPropertyOptional({ description: '背包类型' })
  @IsOptional()
  @IsEnum(InventoryType)
  inventoryType?: InventoryType;

  @ApiPropertyOptional({ description: '物品名称' })
  @IsOptional()
  @IsString()
  itemName?: string;

  @ApiPropertyOptional({ description: '物品类型' })
  @IsOptional()
  @IsNumber()
  itemType?: number;

  @ApiPropertyOptional({ description: '物品品质' })
  @IsOptional()
  @IsNumber()
  itemQuality?: number;

  @ApiPropertyOptional({ description: '最小数量' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  minQuantity?: number;

  @ApiPropertyOptional({ description: '是否包含过期物品' })
  @IsOptional()
  @IsBoolean()
  includeExpired?: boolean;

  @ApiPropertyOptional({ description: '页码', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}

// 背包统计DTO
export class InventoryStatsDto {
  @ApiProperty({ description: '总物品数量' })
  totalItems: number;

  @ApiProperty({ description: '总重量' })
  totalWeight: number;

  @ApiProperty({ description: '总价值' })
  totalValue: number;

  @ApiProperty({ description: '过期物品数量' })
  expiredItems: number;

  @ApiProperty({ description: '各类型物品统计' })
  typeStats: Record<string, number>;

  @ApiProperty({ description: '各品质物品统计' })
  qualityStats: Record<string, number>;

  @ApiProperty({ description: '背包使用率' })
  usageRate: number;
}
