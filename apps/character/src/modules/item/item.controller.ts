/**
 * Item Controller - 严格基于old项目API设计
 * 确保与old项目的API接口100%一致
 */

import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ItemService } from './item.service';
import { Cacheable, CacheEvict } from '@common/redis';

@Controller()
export class ItemController {
  private readonly logger = new Logger(ItemController.name);

  constructor(private readonly itemService: ItemService) {}

  /**
   * 获取角色物品数据
   * 对应old项目: Item实体的toJSONforDB
   */
  @MessagePattern('item.getItems')
  @Cacheable({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 300
  })
  async getCharacterItems(@Payload() payload: { characterId: string; serverId?: string }) {
    this.logger.log(`获取角色物品数据: ${payload.characterId}`);
    
    const items = await this.itemService.getCharacterItems(payload.characterId);
    if (!items) {
      // 如果不存在，初始化物品数据
      const newItems = await this.itemService.initCharacterItems(
        payload.characterId, 
        payload.serverId || 'server_001'
      );
      return { code: 0, data: newItems };
    }

    return { code: 0, data: items };
  }

  /**
   * 添加物品
   * 对应old项目: addItem方法
   */
  @MessagePattern('item.addItem')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async addItem(@Payload() payload: { 
    characterId: string; 
    resId: number; 
    num: number;
    serverId?: string;
  }) {
    this.logger.log(`添加物品: ${payload.characterId}, 配置ID: ${payload.resId}, 数量: ${payload.num}`);
    
    const result = await this.itemService.addItem(
      payload.characterId,
      payload.resId,
      payload.num
    );

    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 移除物品
   * 对应old项目: delItem方法，优化API命名
   */
  @MessagePattern('item.removeItem')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async removeItem(@Payload() payload: {
    characterId: string;
    itemId: string;
    quantity: number;
    serverId?: string;
  }) {
    this.logger.log(`移除物品: ${payload.characterId}, UID: ${payload.itemId}, 数量: ${payload.quantity}`);

    const result = await this.itemService.removeItem(
      payload.characterId,
      payload.itemId,
      payload.quantity
    );

    return { code: result };
  }

  /**
   * 使用物品
   * 对应old项目: useItem方法
   */
  @MessagePattern('item.useItem')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async useItem(@Payload() payload: {
    characterId: string;
    itemId: string;
    quantity?: number;
    serverId?: string;
  }) {
    this.logger.log(`使用物品: ${payload.characterId}, UID: ${payload.itemId}, 数量: ${payload.quantity || 1}`);

    const result = await this.itemService.useItem(
      payload.characterId,
      payload.itemId,
      payload.quantity || 1
    );

    return result; // 直接返回结果，因为已经包含了code
  }

  /**
   * 获取物品数量
   * 对应old项目: getItemNum方法，优化API命名
   */
  @MessagePattern('item.getItemQuantity')
  @Cacheable({
    key: 'character:item:num:#{payload.characterId}:#{payload.uid}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60
  })
  async getItemQuantity(@Payload() payload: {
    characterId: string;
    itemId: string;
    serverId?: string;
  }) {
    this.logger.log(`获取物品数量: ${payload.characterId}, UID: ${payload.itemId}`);

    const quantity = await this.itemService.getItemQuantity(
      payload.characterId,
      payload.itemId
    );

    return { code: 0, quantity };
  }

  /**
   * 根据配置ID获取物品总数量
   * 对应old项目: getItemNumByResID方法，优化API命名
   */
  @MessagePattern('item.getItemQuantityByConfigId')
  @Cacheable({
    key: 'character:item:numByResId:#{payload.characterId}:#{payload.resId}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 60
  })
  async getItemQuantityByConfigId(@Payload() payload: {
    characterId: string;
    configId: number;
    serverId?: string;
  }) {
    this.logger.log(`根据配置ID获取物品总数量: ${payload.characterId}, 配置ID: ${payload.configId}`);

    const quantity = await this.itemService.getItemQuantityByConfigId(
      payload.characterId,
      payload.configId
    );

    return { code: 0, quantity };
  }

  /**
   * 检查物品数量是否足够
   * 对应old项目: checkItemIsEnough方法，优化API命名
   */
  @MessagePattern('item.checkItemSufficient')
  @Cacheable({
    key: 'character:item:check:#{payload.characterId}:#{payload.resId}:#{payload.needNum}',
    dataType: 'server',
    serverId: '#{payload.serverId}',
    ttl: 30
  })
  async checkItemSufficient(@Payload() payload: {
    characterId: string;
    configId: number;
    requiredQuantity: number;
    serverId?: string;
  }) {
    this.logger.log(`检查物品数量是否足够: ${payload.characterId}, 配置ID: ${payload.configId}, 需要数量: ${payload.requiredQuantity}`);

    const isSufficient = await this.itemService.checkItemSufficient(
      payload.characterId,
      payload.configId,
      payload.requiredQuantity
    );

    return { code: 0, isSufficient };
  }

  /**
   * 创建物品实例（内部方法，主要用于测试）
   * 对应old项目: newItem方法，优化API命名
   */
  @MessagePattern('item.createItemInstance')
  async createItemInstance(@Payload() payload: {
    configId: number;
    quantity: number;
  }) {
    this.logger.log(`创建物品实例: 配置ID: ${payload.configId}, 数量: ${payload.quantity}`);

    const item = await this.itemService.createItemInstance(
      payload.configId,
      payload.quantity
    );

    if (item) {
      return { code: 0, item };
    } else {
      return { code: -1, item: null };
    }
  }

  /**
   * 批量操作物品
   * 对应old项目的批量操作逻辑
   */
  @MessagePattern('item.batchAddItems')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchAddItems(@Payload() payload: {
    characterId: string;
    items: Array<{ configId: number; quantity: number }>;
    serverId?: string;
  }) {
    this.logger.log(`批量添加物品: ${payload.characterId}, 物品数量: ${payload.items.length}`);

    const results: any[] = [];

    for (const itemData of payload.items) {
      const result = await this.itemService.addItem(
        payload.characterId,
        itemData.configId,
        itemData.quantity
      );
      results.push({
        configId: itemData.configId,
        quantity: itemData.quantity,
        result
      });
    }

    return { code: 0, results };
  }

  /**
   * 批量移除物品
   * 对应old项目的批量删除逻辑，优化API命名
   */
  @MessagePattern('item.batchRemoveItems')
  @CacheEvict({
    key: 'character:items:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async batchRemoveItems(@Payload() payload: {
    characterId: string;
    items: Array<{ itemId: string; quantity: number }>;
    serverId?: string;
  }) {
    this.logger.log(`批量移除物品: ${payload.characterId}, 物品数量: ${payload.items.length}`);
    
    const results: any[] = [];
    
    for (const itemData of payload.items) {
      const result = await this.itemService.removeItem(
        payload.characterId,
        itemData.itemId,
        itemData.quantity
      );
      results.push({
        itemId: itemData.itemId,
        quantity: itemData.quantity,
        code: result
      });
    }

    return { code: 0, results };
  }
}
