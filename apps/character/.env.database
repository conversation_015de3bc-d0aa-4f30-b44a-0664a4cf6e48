# ===== Character服务数据库配置 V2.0 =====
# 共享密码 + 动态拼接方案：所有区服共享基础配置，通过SERVER_ID动态生成用户名和数据库名

# MongoDB连接基础信息（所有区服共享）
CHARACTER_MONGODB_HOST=***************
CHARACTER_MONGODB_PORT=27017
CHARACTER_MONGODB_PASSWORD=FslcxeE2025

# 命名前缀配置（动态拼接使用）
CHARACTER_MONGODB_USERNAME_PREFIX=character-admin
CHARACTER_MONGODB_DB_PREFIX=character_db

# ===== 连接池配置 =====
# Character服务连接池大小（高并发服务）
CHARACTER_MONGODB_MAX_POOL_SIZE=15

# 连接超时配置（毫秒）
CHARACTER_MONGODB_SERVER_SELECTION_TIMEOUT=5000
CHARACTER_MONGODB_SOCKET_TIMEOUT=45000
CHARACTER_MONGODB_CONNECT_TIMEOUT=10000

# ===== 可靠性配置 =====
# 写操作重试
CHARACTER_MONGODB_RETRY_WRITES=true

# 读操作重试
CHARACTER_MONGODB_RETRY_READS=true

# 命令缓冲（连接断开时缓冲命令）
CHARACTER_MONGODB_BUFFER_COMMANDS=true

# ===== 数据库管理配置 =====
# 自动创建索引
CHARACTER_MONGODB_AUTO_INDEX=true

# 自动创建集合
CHARACTER_MONGODB_AUTO_CREATE=true

# ===== 动态拼接说明 =====
# 1. SERVER_ID环境变量由启动参数或部署脚本设置，用于区分区服
# 2. 实际用户名格式：${CHARACTER_MONGODB_USERNAME_PREFIX}-${SERVER_ID}
#    例如：character-admin-server_001
# 3. 实际数据库名格式：${CHARACTER_MONGODB_DB_PREFIX}_${SERVER_ID}
#    例如：character_db_server_001
# 4. 实际连接URI：**********************************************************************
# 5. 所有区服共享相同密码，通过用户名和数据库名实现隔离
# 6. 新增区服只需设置SERVER_ID，无需修改配置文件
# 7. 密码修改只需更新一处，所有区服自动生效
