import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { ShopDocument, ShopType, RefreshCycle } from '@economy/common/schemas/shop.schema';
import { ShopRepository } from '@economy/common/repositories/shop.repository';
import {
  PurchaseGoodsDto,
  RefreshShopDto,
  ClaimMonthCardDto,
  BuyMonthCardDto,
  ShopInfoDto,
  GetShopListDto,
  GetPurchaseHistoryDto,
  ShopStatsDto
} from '@economy/common/dto/shop.dto';
import { ErrorCode, ErrorMessages } from '@app/game-constants';
import { GameConfigFacade } from '@app/game-config';
import { MicroserviceClientService } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@shared/constants';

@Injectable()
export class ShopService {
  private readonly logger = new Logger(ShopService.name);

  constructor(
    private readonly shopRepository: ShopRepository,
    private readonly gameConfig: GameConfigFacade,
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  /**
   * 获取商店信息
   */
  async getShopInfo(characterId: string, serverId: string, shopType: ShopType): Promise<ShopInfoDto> {
    try {
      const shop = await this.shopRepository.getOrCreateShop(characterId, serverId, shopType);
      return this.toShopInfoDto(shop);
    } catch (error) {
      this.logger.error('获取商店信息失败', error);
      throw error;
    }
  }

  /**
   * 购买商品（基于old项目Store实体的购买逻辑）
   */
  async purchaseGoods(characterId: string, serverId: string, purchaseDto: PurchaseGoodsDto): Promise<any> {
    try {
      const shop = await this.shopRepository.getOrCreateShop(characterId, serverId, purchaseDto.shopType);

      // 1. 检查商品配置是否存在（基于old项目getConfigInfo）
      const goodsConfig = await this.getShopConfig(purchaseDto.goodsId);
      if (!goodsConfig) {
        throw new NotFoundException({
          code: ErrorCode.CONFIG_NOT_FOUND,
          message: `商品配置不存在: ${purchaseDto.goodsId}`,
        });
      }

      // 2. 检查是否在规定时间内（基于old项目checkIsInBuyTime）
      const timeCheckResult = this.checkIsInBuyTime(goodsConfig);
      if (timeCheckResult !== 0) {
        throw new BadRequestException({
          code: ErrorCode.TIME_LIMIT_EXCEEDED,
          message: '商品不在销售时间内',
        });
      }

      // 3. 检查VIP或个人等级是否达标（基于old项目checkBuyLimitType）
      const limitCheckResult = await this.checkBuyLimitType(characterId, goodsConfig);
      if (limitCheckResult !== 0) {
        throw new BadRequestException({
          code: ErrorCode.LEVEL_REQUIREMENT_NOT_MET,
          message: 'VIP等级或玩家等级不足',
        });
      }

      // 4. 检查购买数量是否超过购买限制（基于old项目checkBuyCustomer）
      const customerCheckResult = this.checkBuyCustomer(shop, goodsConfig, purchaseDto.goodsId, purchaseDto.quantity);
      if (customerCheckResult !== 0) {
        throw new BadRequestException({
          code: ErrorCode.PURCHASE_LIMIT_EXCEEDED,
          message: '超过购买限制',
        });
      }

      // 5. 检查玩家货币是否足够并扣钱（基于old项目checkIsHaveMoney）
      const moneyCheckResult = await this.checkIsHaveMoney(characterId, goodsConfig, purchaseDto.quantity);
      if (moneyCheckResult !== 0) {
        const errorMessage = goodsConfig.priceType === 1 ? '现金不足' : '金币不足';
        throw new BadRequestException({
          code: ErrorCode.INSUFFICIENT_CURRENCY,
          message: errorMessage,
        });
      }

      // 6. 计算费用
      const totalCost = this.calculateCost(goodsConfig, purchaseDto.quantity);

      // 7. 添加购买数量（基于old项目addBuyCount）
      this.addBuyCount(shop, goodsConfig, purchaseDto.goodsId, purchaseDto.quantity);

      // 8. 发货（基于old项目sendItem方法）
      const sendResult = await this.sendItem(characterId, goodsConfig, purchaseDto.quantity);
      if (sendResult !== 0) {
        throw new BadRequestException({
          code: ErrorCode.DELIVERY_FAILED,
          message: '发货失败',
        });
      }

      // 9. 添加购买记录
      shop.addPurchaseRecord(
        purchaseDto.goodsId,
        purchaseDto.quantity,
        totalCost,
        goodsConfig.priceType === 1 ? 'cash' : 'gold'
      );

      const updatedShop = await shop.save();

      this.logger.log(`商品购买成功: ${characterId}, 商品: ${purchaseDto.goodsId}, 数量: ${purchaseDto.quantity}, 费用: ${totalCost}`);

      // 10. 触发任务（基于old项目逻辑）
      await this.triggerPurchaseTask(characterId, purchaseDto.quantity);

      return {
        success: true,
        totalCost,
        currency: goodsConfig.priceType === 1 ? 'cash' : 'gold',
        goodsId: purchaseDto.goodsId,
        quantity: purchaseDto.quantity,
        resId: goodsConfig.resId,
        shop: this.toShopInfoDto(updatedShop),
      };
    } catch (error) {
      this.logger.error('购买商品失败', error);
      throw error;
    }
  }

  /**
   * 刷新商店
   */
  async refreshShop(characterId: string, serverId: string, refreshDto: RefreshShopDto): Promise<ShopInfoDto> {
    try {
      const shop = await this.shopRepository.getOrCreateShop(characterId, serverId, refreshDto.shopType);

      if (refreshDto.forceRefresh || this.needsRefresh(shop, refreshDto.cycle || RefreshCycle.DAILY)) {
        shop.refreshLimits(refreshDto.cycle || RefreshCycle.DAILY);
        await shop.save();
        this.logger.log(`商店刷新成功: ${characterId}, 类型: ${refreshDto.shopType}`);
      }

      return this.toShopInfoDto(shop);
    } catch (error) {
      this.logger.error('刷新商店失败', error);
      throw error;
    }
  }

  /**
   * 购买月卡（基于old项目MonthCard实体）
   */
  async buyMonthCard(characterId: string, serverId: string, buyDto: BuyMonthCardDto): Promise<any> {
    try {
      const shop = await this.shopRepository.getOrCreateShop(characterId, serverId, ShopType.NORMAL);

      // 1. 检查支付状态（基于old项目支付验证逻辑）
      const paymentResult = await this.checkPaymentStatus(characterId, buyDto.orderId);
      if (!paymentResult.success) {
        throw new BadRequestException({
          code: ErrorCode.PAYMENT_VERIFICATION_FAILED,
          message: '支付验证失败',
        });
      }

      // 2. 检查月卡配置（基于old项目MonthCard配置表）
      const monthCardConfig = await this.getMonthCardConfig(buyDto.cardType);
      if (!monthCardConfig) {
        throw new NotFoundException({
          code: ErrorCode.CONFIG_NOT_FOUND,
          message: `月卡配置不存在: ${buyDto.cardType}`,
        });
      }

      // 3. 验证购买天数与配置是否匹配
      if (buyDto.days !== monthCardConfig.days) {
        throw new BadRequestException({
          code: ErrorCode.INVALID_PARAMETER,
          message: '购买天数与配置不匹配',
        });
      }

      // 4. 检查是否已购买月卡（防重复购买）
      if (shop.isBuy === 1 && shop.cardTime > 0) {
        // 如果已有月卡，则延长时间
        shop.cardTime += buyDto.days;
        shop.allDay += buyDto.days;
      } else {
        // 首次购买月卡
        shop.isBuy = 1;
        shop.allDay = buyDto.days;
        shop.cardTime = buyDto.days;
        shop.isGetReward = 0; // 重置奖励领取状态
      }

      const now = Date.now();
      shop.buyMonthCardTime = now;
      shop.buyMonthNum += 1;
      shop.refreshTime = now;

      const updatedShop = await shop.save();

      this.logger.log(`月卡购买成功: ${characterId}, 类型: ${buyDto.cardType}, 天数: ${buyDto.days}, 总天数: ${shop.cardTime}`);

      // 5. 触发月卡购买相关任务
      await this.triggerMonthCardTasks(characterId, buyDto.cardType);

      return {
        success: true,
        cardType: buyDto.cardType,
        days: buyDto.days,
        totalDays: shop.cardTime,
        config: monthCardConfig,
        shop: this.toShopInfoDto(updatedShop),
      };
    } catch (error) {
      this.logger.error('购买月卡失败', error);
      throw error;
    }
  }

  /**
   * 领取月卡奖励（基于old项目MonthCard实体）
   */
  async claimMonthCardReward(claimDto: ClaimMonthCardDto): Promise<any> {
    try {
      const shop = await this.shopRepository.findShopByCharacterAndType(claimDto.characterId, ShopType.NORMAL);
      if (!shop) {
        throw new NotFoundException({
          code: ErrorCode.SHOP_NOT_FOUND,
          message: ErrorMessages[ErrorCode.SHOP_NOT_FOUND],
        });
      }

      if (!shop.canClaimMonthCardReward) {
        throw new BadRequestException({
          code: ErrorCode.MONTH_CARD_REWARD_CLAIMED,
          message: ErrorMessages[ErrorCode.MONTH_CARD_REWARD_CLAIMED],
        });
      }

      const reward = shop.claimMonthCardReward();
      shop.cardTime = Math.max(0, shop.cardTime - 1); // 减少剩余天数

      const updatedShop = await shop.save();

      this.logger.log(`月卡奖励领取成功: ${claimDto.characterId}`);
      
      return {
        success: true,
        reward,
        remainingDays: shop.cardTime,
        shop: this.toShopInfoDto(updatedShop),
      };
    } catch (error) {
      this.logger.error('领取月卡奖励失败', error);
      throw error;
    }
  }

  /**
   * 获取商店列表
   */
  async getShopList(query: GetShopListDto): Promise<ShopInfoDto[]> {
    try {
      const shops = await this.shopRepository.findShopsByCharacterId(query);
      return shops.map(shop => this.toShopInfoDto(shop));
    } catch (error) {
      this.logger.error('获取商店列表失败', error);
      throw error;
    }
  }

  /**
   * 获取购买历史
   */
  async getPurchaseHistory(query: GetPurchaseHistoryDto): Promise<any> {
    try {
      return await this.shopRepository.getPurchaseHistory(query);
    } catch (error) {
      this.logger.error('获取购买历史失败', error);
      throw error;
    }
  }

  /**
   * 获取商店统计
   */
  async getShopStats(query: ShopStatsDto): Promise<any> {
    try {
      return await this.shopRepository.getShopStats(query.characterId, query.days, query.shopType);
    } catch (error) {
      this.logger.error('获取商店统计失败', error);
      throw error;
    }
  }

  /**
   * 批量刷新商店（定时任务用）
   */
  async batchRefreshShops(cycle: RefreshCycle): Promise<any> {
    try {
      const shopsNeedingRefresh = await this.shopRepository.findShopsNeedingRefresh(cycle);
      
      if (shopsNeedingRefresh.length === 0) {
        return { refreshed: 0 };
      }

      const shopIds = shopsNeedingRefresh.map(shop => shop.shopId);
      const result = await this.shopRepository.batchRefreshShops(shopIds, cycle);

      this.logger.log(`批量刷新商店完成: ${shopsNeedingRefresh.length}个商店, 周期: ${cycle}`);
      
      return {
        refreshed: shopsNeedingRefresh.length,
        cycle,
        result,
      };
    } catch (error) {
      this.logger.error('批量刷新商店失败', error);
      throw error;
    }
  }

  // ==================== 私有方法 ====================

  /**
   * 计算购买费用
   */
  private calculateCost(goodsId: number, quantity: number, useDiscount?: boolean): number {
    // TODO: 根据商品配置计算实际费用
    // 这里简化处理
    const basePrice = 100; // 基础价格
    let totalCost = basePrice * quantity;
    
    if (useDiscount) {
      totalCost = Math.floor(totalCost * 0.9); // 9折
    }
    
    return totalCost;
  }

  /**
   * 更新限购记录
   */
  private async updateLimitRecord(shop: ShopDocument, goodsId: number, quantity: number, cycle: RefreshCycle): Promise<void> {
    let limitRecords = shop.everyDayBuy; // 默认每日限购
    
    switch (cycle) {
      case RefreshCycle.WEEKLY:
        limitRecords = shop.weeklyBuy;
        break;
      case RefreshCycle.MONTHLY:
        limitRecords = shop.monthlyBuy;
        break;
      case RefreshCycle.SEASON:
        limitRecords = shop.seasonBuy;
        break;
    }
    
    let record = limitRecords.find(r => r.goodsId === goodsId);
    if (!record) {
      // 从配置获取限购数量（基于old项目Shop配置表的Num字段）
      const limitCount = await this.getLimitCountFromConfig(goodsId, cycle);
      record = {
        goodsId,
        purchasedCount: 0,
        limitCount: limitCount,
        resetTime: Date.now(),
      };
      limitRecords.push(record);
    }

    record.purchasedCount += quantity;
  }

  /**
   * 检查是否需要刷新
   * 基于old项目: Store.prototype.getStoreInfo中的时间检查逻辑
   */
  private needsRefresh(shop: ShopDocument, cycle: RefreshCycle): boolean {
    // 基于old项目：使用时间工具检查是否需要刷新
    switch (cycle) {
      case RefreshCycle.DAILY:
        // 基于old项目：!timeUtils.isToday(this.everyDayReTime)
        return !this.isToday(shop.everyDayReTime || 0);

      case RefreshCycle.WEEKLY:
        // 基于old项目：!timeUtils.isSameWeek(this.weeklyReTime)
        return !this.isSameWeek(shop.weeklyReTime || 0);

      case RefreshCycle.MONTHLY:
        // 基于old项目：!timeUtils.isSameMonth(this.monthlyReTime)
        return !this.isSameMonth(shop.monthlyReTime || 0);

      case RefreshCycle.SEASON:
        // 季节刷新：90天
        const seasonInterval = 90 * 24 * 60 * 60 * 1000;
        return (Date.now() - (shop.seasonReTime || 0)) >= seasonInterval;

      default:
        return false;
    }
  }

  /**
   * 转换为商店信息DTO
   */
  private toShopInfoDto(shop: ShopDocument): ShopInfoDto {
    return {
      shopId: shop.shopId,
      characterId: shop.characterId,
      shopType: shop.shopType,
      everyDayReTime: shop.everyDayReTime,
      weeklyReTime: shop.weeklyReTime,
      monthlyReTime: shop.monthlyReTime,
      seasonReTime: shop.seasonReTime,
      everyDayBuy: shop.everyDayBuy,
      weeklyBuy: shop.weeklyBuy,
      monthlyBuy: shop.monthlyBuy,
      seasonBuy: shop.seasonBuy,
      purchaseHistory: shop.purchaseHistory,
      totalSpent: shop.totalSpent,
      totalPurchases: shop.totalPurchases,
      lastPurchaseTime: shop.lastPurchaseTime,
      needsDailyRefresh: shop.needsDailyRefresh,
      needsWeeklyRefresh: shop.needsWeeklyRefresh,
      needsMonthlyRefresh: shop.needsMonthlyRefresh,
      isMonthCardActive: shop.isMonthCardActive,
      canClaimMonthCardReward: shop.canClaimMonthCardReward,
      cardTime: shop.cardTime,
      isGetReward: shop.isGetReward,
    };
  }

  /**
   * 发放购买奖励
   * 基于old项目: Store.prototype.sendItem方法
   *
   * 实现逻辑：
   * 1. 获取商品配置
   * 2. 根据商品类型发放不同奖励
   * 3. 处理背包满的情况（转邮件）
   * 4. 触发相关任务
   */
  private async sendPurchaseRewards(characterId: string, goodsId: number, quantity: number): Promise<any[]> {
    try {
      // 1. 获取商品配置
      const shopConfig = await this.getShopConfig(goodsId);
      if (!shopConfig) {
        this.logger.error(`商品配置不存在: ${goodsId}`);
        return [];
      }

      const rewards = [];

      // 2. 根据商品类型发放奖励（基于old项目逻辑）
      if (shopConfig.itemType !== 1) {
        // 普通物品
        const reward = await this.sendItemReward(characterId, shopConfig.parameters, quantity);
        if (reward) {
          rewards.push(reward);
        }
      } else {
        // 球员类型
        const heroReward = await this.sendHeroReward(characterId, shopConfig.parameters, quantity);
        if (heroReward) {
          rewards.push(heroReward);
        }
      }

      // 3. 触发购买任务（基于old项目：TARGET_TYPE.NINE）
      await this.triggerPurchaseTask(characterId, quantity);

      this.logger.log(`购买奖励发放完成: ${characterId}, 商品: ${goodsId}, 奖励数量: ${rewards.length}`);
      return rewards;
    } catch (error) {
      this.logger.error('发放购买奖励失败', error);
      return [];
    }
  }

  /**
   * 发放物品奖励
   * 基于old项目: player.bag.addItem逻辑
   */
  private async sendItemReward(characterId: string, itemId: number, quantity: number): Promise<any> {
    try {
      // TODO: 调用Character服务添加物品
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.CHARACTER_SERVICE,
      //   'inventory.addItem',
      //   { characterId, itemId, quantity, source: 'shop_purchase' }
      // );

      // if (result.code === 'BAG_FULL') {
      //   // 背包满了，转邮件发送（基于old项目逻辑）
      //   await this.sendRewardByMail(characterId, itemId, quantity, 'shop_purchase');
      //   return { type: 'item', itemId, quantity, deliveryMethod: 'mail' };
      // }

      return { type: 'item', itemId, quantity, deliveryMethod: 'bag' };
    } catch (error) {
      this.logger.error('发放物品奖励失败', error);
      return null;
    }
  }

  /**
   * 发放球员奖励
   * 基于old项目: 球员购买和相同球员检查逻辑
   */
  private async sendHeroReward(characterId: string, heroResId: number, quantity: number): Promise<any> {
    try {
      // TODO: 调用Hero服务检查相同球员并创建
      // const result = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.HERO_SERVICE,
      //   'hero.createFromPurchase',
      //   { characterId, heroResId, quantity, source: 'shop_purchase' }
      // );

      // 基于old项目：如果已有相同球员，会转换为球星卡
      return { type: 'hero', heroResId, quantity, deliveryMethod: 'direct' };
    } catch (error) {
      this.logger.error('发放球员奖励失败', error);
      return null;
    }
  }

  /**
   * 获取商品配置
   * 基于old项目: dataApi.allData.data["Shop"][Id]
   */
  private async getShopConfig(goodsId: number): Promise<any> {
    try {
      // 从Shop配置表获取商品配置（基于old项目Shop.json）
      const config = await this.gameConfig.shop.get(goodsId);
      if (!config) {
        this.logger.warn(`商品配置不存在: ${goodsId}`);
        return null;
      }

      // 转换为old项目兼容的格式（基于old项目Store.prototype.getConfigInfo）
      const shopConfig = {
        id: config.id,                           // 物品序号
        resId: config.parameters,                // 物品id（对应old项目Parameters字段）
        itemType: config.itemType,               // 物品类型 0：物品 1：球员
        priceType: config.priceType,             // 价格类型 1：现金 2：金币
        isDiscount: config.yNDisCount || 0,      // 是否折扣 0不是 1是
        price: config.price,                     // 物品原价
        discountPrice: config.discountPrice || config.price, // 折后价格
        customer: config.customer || 0,          // 是否限购 0不限购 1限购
        customerNum: config.num || 1,            // 限购数量
        purchase: config.purchase || 0,          // 限购类型 0:无 1：个人限购 2：全服限购
        refreshType: config.refreshType || 0,    // 刷新类型 0：不刷新 1：每日刷新 2：每周刷新 3：每月刷新
        isLimitedTime: config.isLimitedTime || 0, // 是否限时 0不限时 1限时
        startTime: config.startTime ? new Date(config.startTime).getTime() : 0,   // 开始时间
        endingTime: config.endingTime ? new Date(config.endingTime).getTime() : 0, // 结束时间
        limitType: config.limitType || 0,        // 限制类型 0：无 1：VIP 2：等级
        limitParameter: config.limitParameter || 0, // 限制类型等级
      };

      this.logger.debug(`获取商品配置成功: ${goodsId}`, shopConfig);
      return shopConfig;
    } catch (error) {
      this.logger.error('获取商品配置失败', error);
      return null;
    }
  }

  /**
   * 触发购买任务
   * 基于old项目: tasks.triggerTask(TARGET_TYPE.NINE)
   */
  private async triggerPurchaseTask(characterId: string, quantity: number): Promise<void> {
    try {
      // TODO: 调用Activity服务触发购买任务
      // for (let i = 0; i < quantity; i++) {
      //   await this.microserviceClient.call(
      //     MICROSERVICE_NAMES.ACTIVITY_SERVICE,
      //     'task.triggerTask',
      //     { characterId, triggerType: 9, arg1: 1 } // TARGET_TYPE.NINE = 9
      //   );
      // }

      this.logger.debug(`触发购买任务: ${characterId}, 数量: ${quantity}`);
    } catch (error) {
      this.logger.error('触发购买任务失败', error);
    }
  }

  /**
   * 检查是否为今天
   * 基于old项目: timeUtils.isToday
   */
  private isToday(timestamp: number): boolean {
    const now = new Date();
    const target = new Date(timestamp);

    return now.getFullYear() === target.getFullYear() &&
           now.getMonth() === target.getMonth() &&
           now.getDate() === target.getDate();
  }

  /**
   * 检查是否为同一周
   * 基于old项目: timeUtils.isSameWeek
   */
  private isSameWeek(timestamp: number): boolean {
    const now = new Date();
    const target = new Date(timestamp);

    // 获取周一作为一周的开始
    const getMonday = (date: Date) => {
      const day = date.getDay();
      const diff = date.getDate() - day + (day === 0 ? -6 : 1);
      return new Date(date.setDate(diff));
    };

    const nowMonday = getMonday(new Date(now));
    const targetMonday = getMonday(new Date(target));

    return nowMonday.getTime() === targetMonday.getTime();
  }

  /**
   * 检查是否为同一月
   * 基于old项目: timeUtils.isSameMonth
   */
  private isSameMonth(timestamp: number): boolean {
    const now = new Date();
    const target = new Date(timestamp);

    return now.getFullYear() === target.getFullYear() &&
           now.getMonth() === target.getMonth();
  }

  /**
   * 检查是否在规定时间内
   * 基于old项目: Store.prototype.checkIsInBuyTime
   */
  private checkIsInBuyTime(config: any): number {
    if (config.isLimitedTime === 1) {
      const time = Date.now(); // 当前时间
      if (config.startTime === 0 || config.endingTime === 0) {
        return -1; // TIME_FAIL
      }
      if (time < config.startTime || time > config.endingTime) {
        return -1; // TIME_FAIL
      }
    }
    return 0; // OK
  }

  /**
   * 检查VIP或个人等级是否达标
   * 基于old项目: Store.prototype.checkBuyLimitType
   */
  private async checkBuyLimitType(characterId: string, config: any): Promise<number> {
    try {
      if (config.limitType === 1) {
        // VIP等级检查
        const characterInfo = await this.microserviceClient.call(
          MICROSERVICE_NAMES.CHARACTER_SERVICE,
          'character.getInfo',
          { characterId }
        );

        if (characterInfo && characterInfo.data) {
          const vipLevel = characterInfo.data.vip || 0;
          if (vipLevel < config.limitParameter) {
            return -1; // VIP_FAIL
          }
        }
      } else if (config.limitType === 2) {
        // 玩家等级检查
        const characterInfo = await this.microserviceClient.call(
          MICROSERVICE_NAMES.CHARACTER_SERVICE,
          'character.getInfo',
          { characterId }
        );

        if (characterInfo && characterInfo.data) {
          const level = characterInfo.data.level || 0;
          if (level < config.limitParameter) {
            return -1; // LEVEL_FAIL
          }
        }
      }

      return 0; // OK
    } catch (error) {
      this.logger.error('检查购买限制失败', error);
      return -1;
    }
  }

  /**
   * 检查购买数量是否超过购买限制
   * 基于old项目: Store.prototype.checkBuyCustomer
   */
  private checkBuyCustomer(shop: ShopDocument, config: any, goodsId: number, num: number): number {
    // 检查是否限购
    if (config.purchase === 1) {
      if (num > config.customerNum) {
        return -1; // RANGE_FAIL
      }

      // 根据刷新类型去检测数量 1每日 2每周 3每月
      if (config.refreshType === 1) {
        const everyDayBuy = shop.everyDayBuy;
        for (const buyRecord of everyDayBuy) {
          if (goodsId === buyRecord.goodsId) {
            const count = num + buyRecord.purchasedCount;
            if (count > config.customerNum) {
              return -1; // RANGE_FAIL
            }
          }
        }
      } else if (config.refreshType === 2) {
        const weeklyBuy = shop.weeklyBuy;
        for (const buyRecord of weeklyBuy) {
          if (goodsId === buyRecord.goodsId) {
            const count = num + buyRecord.purchasedCount;
            if (count > config.customerNum) {
              return -1; // RANGE_FAIL
            }
          }
        }
      } else if (config.refreshType === 3) {
        const monthlyBuy = shop.monthlyBuy;
        for (const buyRecord of monthlyBuy) {
          if (goodsId === buyRecord.goodsId) {
            const count = num + buyRecord.purchasedCount;
            if (count > config.customerNum) {
              return -1; // RANGE_FAIL
            }
          }
        }
      }
    }
    return 0; // OK
  }

  /**
   * 检查是否足够的钱，并扣钱
   * 基于old项目: Store.prototype.checkIsHaveMoney
   */
  private async checkIsHaveMoney(characterId: string, config: any, num: number): Promise<number> {
    try {
      let totalCost: number;
      let currencyType: 'cash' | 'gold' | 'energy' | 'worldCoin' | 'chip' | 'integral';

      // 计算总费用（基于old项目逻辑）
      if (config.isDiscount === 1) {
        totalCost = config.discountPrice * num;
      } else {
        totalCost = config.price * num;
      }

      // 确定货币类型（基于old项目priceType字段）
      if (config.priceType === 1) {
        currencyType = 'cash'; // 现金（欧元）
      } else {
        currencyType = 'gold'; // 金币
      }

      // 1. 先获取角色信息检查余额（基于old项目checkResourceIsEnough）
      const characterInfo = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.getInfo',
        { characterId }
      );

      if (!characterInfo || characterInfo.code !== 0 || !characterInfo.data) {
        this.logger.error(`获取角色信息失败: ${characterId}`);
        return -1; // 获取信息失败
      }

      const currentBalance = characterInfo.data[currencyType] || 0;
      if (currentBalance < totalCost) {
        this.logger.warn(`货币不足: ${characterId}, 需要: ${totalCost}, 当前: ${currentBalance}, 类型: ${currencyType}`);
        return config.priceType === 1 ? -2 : -3; // CASH_FALL or GOLD_FALL
      }

      // 2. 扣除货币（基于old项目deductMoney）
      const deductResult = await this.microserviceClient.call(
        MICROSERVICE_NAMES.CHARACTER_SERVICE,
        'character.currency.subtract',
        {
          characterId,
          currencyDto: {
            currencyType,
            amount: totalCost,
            reason: 'shop_purchase'
          }
        }
      );

      if (!deductResult || deductResult.code !== 0) {
        this.logger.error(`扣除货币失败: ${characterId}, 类型: ${currencyType}, 数量: ${totalCost}`);
        return config.priceType === 1 ? -2 : -3; // CASH_FALL or GOLD_FALL
      }

      this.logger.log(`货币扣除成功: ${characterId}, 类型: ${currencyType}, 数量: ${totalCost}`);
      return 0; // OK
    } catch (error) {
      this.logger.error('检查和扣除货币失败', error);
      return -1;
    }
  }

  /**
   * 添加购买数量
   * 基于old项目: Store.prototype.addBuyCount
   */
  private addBuyCount(shop: ShopDocument, config: any, goodsId: number, num: number): void {
    if (config.purchase === 1) {
      // 根据刷新类型添加购买记录
      if (config.refreshType === 1) {
        // 每日刷新
        let found = false;
        for (const buyRecord of shop.everyDayBuy) {
          if (goodsId === buyRecord.goodsId) {
            buyRecord.purchasedCount += num;
            found = true;
            break;
          }
        }
        if (!found) {
          shop.everyDayBuy.push({
            goodsId,
            purchasedCount: num,
            limitCount: config.customerNum,
            resetTime: Date.now(),
          });
        }
      } else if (config.refreshType === 2) {
        // 每周刷新
        let found = false;
        for (const buyRecord of shop.weeklyBuy) {
          if (goodsId === buyRecord.goodsId) {
            buyRecord.purchasedCount += num;
            found = true;
            break;
          }
        }
        if (!found) {
          shop.weeklyBuy.push({
            goodsId,
            purchasedCount: num,
            limitCount: config.customerNum,
            resetTime: Date.now(),
          });
        }
      } else if (config.refreshType === 3) {
        // 每月刷新
        let found = false;
        for (const buyRecord of shop.monthlyBuy) {
          if (goodsId === buyRecord.goodsId) {
            buyRecord.purchasedCount += num;
            found = true;
            break;
          }
        }
        if (!found) {
          shop.monthlyBuy.push({
            goodsId,
            purchasedCount: num,
            limitCount: config.customerNum,
            resetTime: Date.now(),
          });
        }
      }
    }
  }

  /**
   * 发货
   * 基于old项目: Store.prototype.sendItem
   */
  private async sendItem(characterId: string, config: any, num: number): Promise<number> {
    try {
      if (config.itemType === 0) {
        // 物品类型：调用Character服务添加物品（基于old项目bag.addItem）
        const addItemResult = await this.microserviceClient.call(
          MICROSERVICE_NAMES.CHARACTER_SERVICE,
          'item.addItem',
          {
            characterId,
            resId: config.resId,
            num: num
          }
        );

        if (!addItemResult || addItemResult.code !== 0) {
          this.logger.error(`添加物品失败: ${config.resId}, 数量: ${num}`);
          // 如果背包满了，发送邮件（基于old项目逻辑）
          if (addItemResult && addItemResult.code === -4) { // BAG_FULL
            await this.sendItemByMail(characterId, config.resId, num, 'shop_purchase');
            this.logger.log(`背包已满，物品已发送邮件: ${characterId}, 物品: ${config.resId}, 数量: ${num}`);
            return 0; // 邮件发送成功也算发货成功
          }
          return -1; // SEND_FAIL
        }
      } else if (config.itemType === 1) {
        // 球员类型：调用Hero服务添加球员（基于old项目heros.addHero）
        const addHeroResult = await this.microserviceClient.call(
          MICROSERVICE_NAMES.HERO_SERVICE,
          'hero.create',
          {
            characterId,
            resId: config.resId,
            serverId: 'server_001', // TODO: 从参数获取
            source: 'shop_purchase'
          }
        );

        if (!addHeroResult || addHeroResult.code !== 0) {
          // 检查是否已有相同球员（基于old项目checkHaveSameHero）
          const heroCard = await this.checkHaveSameHero(config.resId);
          if (heroCard !== 0) {
            // 已有相同球员，转换为球星卡（基于old项目逻辑）
            const addCardResult = await this.microserviceClient.call(
              MICROSERVICE_NAMES.CHARACTER_SERVICE,
              'item.addItem',
              {
                characterId,
                resId: heroCard,
                num: num
              }
            );

            if (!addCardResult || addCardResult.code !== 0) {
              this.logger.error(`添加球星卡失败: ${heroCard}, 数量: ${num}`);
              return -1; // SEND_FAIL
            }

            this.logger.log(`球员已存在，转换为球星卡: ${characterId}, 球员: ${config.resId}, 球星卡: ${heroCard}`);
          } else {
            this.logger.error(`添加球员失败: ${config.resId}, 数量: ${num}`);
            return -1; // SEND_FAIL
          }
        }
      }

      this.logger.log(`发货成功: 类型${config.itemType}, ID${config.resId}, 数量${num}`);
      return 0; // OK
    } catch (error) {
      this.logger.error('发货失败', error);
      return -1;
    }
  }

  /**
   * 获取月卡配置
   * 基于old项目: dataApi.allData.data["ActiveParam"] GroupId=5
   */
  private async getMonthCardConfig(cardType: number): Promise<any> {
    try {
      // 从ActiveParam配置表获取月卡配置（基于old项目逻辑）
      const activeParams = await this.gameConfig.activeParam.getAll();
      if (!activeParams || activeParams.length === 0) {
        this.logger.warn('ActiveParam配置表为空');
        return null;
      }

      // 查找GroupId为5的月卡配置（基于old项目MonthCard.prototype.getConfig）
      const monthCardConfig = activeParams.find(config => config.groupId === 5);
      if (!monthCardConfig) {
        this.logger.warn('月卡配置不存在（GroupId=5）');
        return null;
      }

      return {
        id: 5, // GroupId
        days: 30, // 固定30天
        price: monthCardConfig.parameter1 || 0, // 费用
        priceType: 1, // 1-现金
        rewardType1: monthCardConfig.rewardType1,
        reward1: monthCardConfig.reward1,
        num1: monthCardConfig.number1,
        rewardType2: monthCardConfig.rewardType2,
        reward2: monthCardConfig.reward2,
        num2: monthCardConfig.number2,
        description: '月卡',
      };
    } catch (error) {
      this.logger.error('获取月卡配置失败', error);
      return null;
    }
  }

  /**
   * 检查支付状态
   * 基于old项目支付验证逻辑
   */
  private async checkPaymentStatus(characterId: string, orderId: string): Promise<{ success: boolean; message?: string }> {
    try {
      // TODO: 实现真实的支付验证逻辑
      // 这里应该调用支付服务验证订单状态

      // 暂时返回成功（开发阶段）
      this.logger.debug(`检查支付状态: ${characterId}, 订单: ${orderId}`);
      return { success: true };
    } catch (error) {
      this.logger.error('检查支付状态失败', error);
      return { success: false, message: '支付验证失败' };
    }
  }

  /**
   * 触发月卡购买相关任务
   */
  private async triggerMonthCardTasks(characterId: string, cardType: number): Promise<void> {
    try {
      // TODO: 调用Activity服务触发月卡购买任务
      this.logger.debug(`触发月卡购买任务: ${characterId}, 卡类型: ${cardType}`);
    } catch (error) {
      this.logger.error('触发月卡购买任务失败', error);
    }
  }

  /**
   * 从配置获取限购数量
   * 基于old项目Shop配置表的Num字段
   */
  private async getLimitCountFromConfig(goodsId: number, cycle: RefreshCycle): Promise<number> {
    try {
      const config = await this.getShopConfig(goodsId);
      if (!config) {
        return 10; // 默认限购数量
      }

      return config.customerNum || 10;
    } catch (error) {
      this.logger.error('获取限购配置失败', error);
      return 10; // 默认限购数量
    }
  }

  /**
   * 检查是否有相同球员
   * 基于old项目: Store.prototype.checkHaveSameHero
   */
  private async checkHaveSameHero(resId: number): Promise<number> {
    try {
      // 从Hero配置表获取对应的球星卡ID（footballer已重命名为hero）
      const heroConfig = await this.gameConfig.hero.get(resId);
      if (!heroConfig) {
        this.logger.warn(`球员配置不存在: ${resId}`);
        return 0;
      }

      return heroConfig.itemId || 0; // 返回对应的球星卡ID
    } catch (error) {
      this.logger.error('检查相同球员失败', error);
      return 0;
    }
  }

  /**
   * 通过邮件发送物品
   * 基于old项目: 背包满时发送邮件逻辑
   */
  private async sendItemByMail(characterId: string, resId: number, num: number, reason: string): Promise<void> {
    try {
      // TODO: 调用邮件服务发送物品
      // const mailResult = await this.microserviceClient.call(
      //   MICROSERVICE_NAMES.SOCIAL_SERVICE,
      //   'mail.sendSystemMail',
      //   {
      //     characterId,
      //     title: '商店购买奖励',
      //     content: '您购买的物品已通过邮件发送',
      //     attachments: [{
      //       type: 'item',
      //       resId: resId,
      //       quantity: num
      //     }],
      //     reason
      //   }
      // );

      this.logger.log(`邮件发送成功: ${characterId}, 物品: ${resId}, 数量: ${num}`);
    } catch (error) {
      this.logger.error('邮件发送失败', error);
      throw error;
    }
  }

}
