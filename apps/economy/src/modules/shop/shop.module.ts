/**
 * 商店模块
 * 基于old项目的Store、VipShop、MonthCard等实体迁移
 */

import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

// Shop相关组件
import { ShopController } from './shop.controller';
import { ShopService } from './shop.service';
import { Shop, ShopSchema } from '@economy/common/schemas/shop.schema';
import { ShopRepository } from '@economy/common/repositories/shop.repository';

// 共享模块
import { GameConfigModule } from '@app/game-config';

@Module({
  imports: [
    // 注册Shop Schema
    MongooseModule.forFeature([
      { name: Shop.name, schema: ShopSchema },
    ]),
    // 导入配置模块
    GameConfigModule,
  ],
  controllers: [ShopController],
  providers: [ShopService, ShopRepository],
  exports: [ShopService, ShopRepository],
})
export class ShopModule {}
