#!/usr/bin/env node

/**
 * 直接测试缓存拦截器和表达式解析器
 * 使用专门的测试端点验证功能
 */

const axios = require('axios');
const chalk = require('chalk');

class CacheInterceptorDirectTest {
  constructor() {
    this.testResults = [];
    this.baseUrl = 'http://127.0.0.1:3001';
  }

  /**
   * 测试 @Cacheable 装饰器和表达式解析
   */
  async testCacheableExpression() {
    console.log(chalk.blue('\n🧪 测试 @Cacheable 装饰器和 #{testKey} 表达式'));
    
    try {
      const testKey = `test-${Date.now()}`;
      console.log(chalk.gray(`  📋 测试键: ${testKey}`));
      console.log(chalk.gray(`  📋 表达式: health:cache-test:#{testKey}`));
      
      // 第一次调用 - 应该执行方法并缓存结果
      console.log(chalk.gray('  🔍 第一次调用（应该缓存）...'));
      const start1 = Date.now();
      const response1 = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      const time1 = Date.now() - start1;
      
      console.log(chalk.gray(`  📊 第一次响应时间: ${time1}ms`));
      console.log(chalk.gray(`  📊 返回数据: ${JSON.stringify(response1.data).substring(0, 100)}...`));
      
      // 等待一小段时间确保缓存生效
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // 第二次调用 - 应该从缓存获取
      console.log(chalk.gray('  🔍 第二次调用（应该从缓存获取）...'));
      const start2 = Date.now();
      const response2 = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      const time2 = Date.now() - start2;
      
      console.log(chalk.gray(`  📊 第二次响应时间: ${time2}ms`));
      console.log(chalk.gray(`  📊 返回数据: ${JSON.stringify(response2.data).substring(0, 100)}...`));
      
      // 验证缓存效果
      const performanceImproved = time2 < time1;
      const dataConsistent = JSON.stringify(response1.data) === JSON.stringify(response2.data);
      
      // 检查是否有随机值变化（证明是否来自缓存）
      const firstRandomValue = response1.data.randomValue;
      const secondRandomValue = response2.data.randomValue;
      const fromCache = firstRandomValue === secondRandomValue;
      
      console.log(chalk.gray(`  🔍 性能提升: ${performanceImproved} (${time1}ms -> ${time2}ms)`));
      console.log(chalk.gray(`  🔍 数据一致: ${dataConsistent}`));
      console.log(chalk.gray(`  🔍 来自缓存: ${fromCache} (随机值相同)`));
      
      if (performanceImproved && dataConsistent && fromCache) {
        console.log(chalk.green(`    ✅ @Cacheable 装饰器和 #{testKey} 表达式正常工作`));
        console.log(chalk.green(`    ✅ 性能提升: ${((time1 - time2) / time1 * 100).toFixed(1)}%`));
        console.log(chalk.green(`    ✅ 表达式解析为: health:cache-test:${testKey}`));
        
        this.testResults.push({
          name: '@Cacheable + #{testKey} 表达式',
          status: 'PASS',
          details: `性能提升 ${((time1 - time2) / time1 * 100).toFixed(1)}%, 缓存命中`
        });
      } else {
        throw new Error(`性能提升: ${performanceImproved}, 数据一致: ${dataConsistent}, 来自缓存: ${fromCache}`);
      }
      
    } catch (error) {
      console.log(chalk.red(`    ❌ @Cacheable 测试失败: ${error.message}`));
      this.testResults.push({
        name: '@Cacheable + #{testKey} 表达式',
        status: 'FAIL',
        error: error.message
      });
    }
  }

  /**
   * 测试 @CachePut 装饰器和表达式解析
   */
  async testCachePutExpression() {
    console.log(chalk.blue('\n🧪 测试 @CachePut 装饰器和表达式解析'));
    
    try {
      const testKey = `put-test-${Date.now()}`;
      console.log(chalk.gray(`  📋 测试键: ${testKey}`));
      console.log(chalk.gray(`  📋 表达式: health:cache-test:#{testKey}`));
      
      // 先获取初始数据（建立缓存）
      const initialResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      console.log(chalk.gray(`  📊 初始数据: ${JSON.stringify(initialResponse.data).substring(0, 100)}...`));
      
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 使用 @CachePut 更新缓存
      console.log(chalk.gray('  🔍 使用 @CachePut 更新缓存...'));
      const updateData = {
        newField: 'updated-value',
        updateTime: new Date().toISOString()
      };
      
      const putResponse = await axios.put(`${this.baseUrl}/health/cache-expression-test/${testKey}`, updateData);
      console.log(chalk.gray(`  📊 更新响应: ${JSON.stringify(putResponse.data).substring(0, 100)}...`));
      
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 再次获取数据，应该是更新后的数据
      console.log(chalk.gray('  🔍 获取更新后的数据...'));
      const updatedResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      console.log(chalk.gray(`  📊 更新后数据: ${JSON.stringify(updatedResponse.data).substring(0, 100)}...`));
      
      // 验证数据是否更新
      const dataUpdated = JSON.stringify(initialResponse.data) !== JSON.stringify(updatedResponse.data);
      const hasNewField = updatedResponse.data.newField === 'updated-value';
      
      console.log(chalk.gray(`  🔍 数据已更新: ${dataUpdated}`));
      console.log(chalk.gray(`  🔍 包含新字段: ${hasNewField}`));
      
      if (dataUpdated && hasNewField) {
        console.log(chalk.green(`    ✅ @CachePut 装饰器正常工作`));
        console.log(chalk.green(`    ✅ 缓存已更新为新数据`));
        console.log(chalk.green(`    ✅ 表达式解析正确`));
        
        this.testResults.push({
          name: '@CachePut + 表达式解析',
          status: 'PASS',
          details: '缓存正确更新，表达式解析正常'
        });
      } else {
        throw new Error(`数据更新: ${dataUpdated}, 新字段: ${hasNewField}`);
      }
      
    } catch (error) {
      console.log(chalk.red(`    ❌ @CachePut 测试失败: ${error.message}`));
      this.testResults.push({
        name: '@CachePut + 表达式解析',
        status: 'FAIL',
        error: error.message
      });
    }
  }

  /**
   * 测试 @CacheEvict 装饰器和表达式解析
   */
  async testCacheEvictExpression() {
    console.log(chalk.blue('\n🧪 测试 @CacheEvict 装饰器和表达式解析'));
    
    try {
      const testKey = `evict-test-${Date.now()}`;
      console.log(chalk.gray(`  📋 测试键: ${testKey}`));
      console.log(chalk.gray(`  📋 表达式: health:cache-test:#{testKey}`));
      
      // 先建立缓存
      console.log(chalk.gray('  🔍 建立初始缓存...'));
      const initialResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      const firstRandomValue = initialResponse.data.randomValue;
      console.log(chalk.gray(`  📊 初始随机值: ${firstRandomValue}`));
      
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 验证缓存存在（第二次调用应该返回相同数据）
      const cachedResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      const cachedRandomValue = cachedResponse.data.randomValue;
      console.log(chalk.gray(`  📊 缓存随机值: ${cachedRandomValue}`));
      
      const cacheExists = firstRandomValue === cachedRandomValue;
      console.log(chalk.gray(`  🔍 缓存存在: ${cacheExists}`));
      
      if (!cacheExists) {
        throw new Error('缓存未正确建立');
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 使用 @CacheEvict 清除缓存
      console.log(chalk.gray('  🔍 使用 @CacheEvict 清除缓存...'));
      const evictResponse = await axios.delete(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      console.log(chalk.gray(`  📊 清除响应: ${JSON.stringify(evictResponse.data).substring(0, 100)}...`));
      
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 再次获取数据，应该是新的数据（缓存已清除）
      console.log(chalk.gray('  🔍 获取清除后的数据...'));
      const newResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      const newRandomValue = newResponse.data.randomValue;
      console.log(chalk.gray(`  📊 新随机值: ${newRandomValue}`));
      
      // 验证缓存是否被清除
      const cacheCleared = firstRandomValue !== newRandomValue;
      console.log(chalk.gray(`  🔍 缓存已清除: ${cacheCleared}`));
      
      if (cacheCleared) {
        console.log(chalk.green(`    ✅ @CacheEvict 装饰器正常工作`));
        console.log(chalk.green(`    ✅ 缓存已正确清除`));
        console.log(chalk.green(`    ✅ 表达式解析正确`));
        
        this.testResults.push({
          name: '@CacheEvict + 表达式解析',
          status: 'PASS',
          details: '缓存正确清除，表达式解析正常'
        });
      } else {
        throw new Error('缓存未被清除');
      }
      
    } catch (error) {
      console.log(chalk.red(`    ❌ @CacheEvict 测试失败: ${error.message}`));
      this.testResults.push({
        name: '@CacheEvict + 表达式解析',
        status: 'FAIL',
        error: error.message
      });
    }
  }

  /**
   * 测试条件表达式 #{testKey != null}
   */
  async testConditionalExpression() {
    console.log(chalk.blue('\n🧪 测试条件表达式 #{testKey != null}'));
    
    try {
      const testKey = `condition-test-${Date.now()}`;
      console.log(chalk.gray(`  📋 测试键: ${testKey}`));
      console.log(chalk.gray(`  📋 条件表达式: #{testKey != null}`));
      
      // 测试有效键（应该满足条件并缓存）
      console.log(chalk.gray('  🔍 测试有效键（满足条件）...'));
      const validResponse1 = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      const validRandomValue1 = validResponse1.data.randomValue;
      
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const validResponse2 = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
      const validRandomValue2 = validResponse2.data.randomValue;
      
      const conditionWorking = validRandomValue1 === validRandomValue2;
      console.log(chalk.gray(`  🔍 条件缓存工作: ${conditionWorking}`));
      
      if (conditionWorking) {
        console.log(chalk.green(`    ✅ 条件表达式 #{testKey != null} 正常工作`));
        console.log(chalk.green(`    ✅ 有效键满足条件并正确缓存`));
        
        this.testResults.push({
          name: '#{testKey != null} 条件表达式',
          status: 'PASS',
          details: '条件表达式正确评估并缓存'
        });
      } else {
        throw new Error('条件表达式未正确工作');
      }
      
    } catch (error) {
      console.log(chalk.red(`    ❌ 条件表达式测试失败: ${error.message}`));
      this.testResults.push({
        name: '#{testKey != null} 条件表达式',
        status: 'FAIL',
        error: error.message
      });
    }
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log(chalk.cyan('\n📊 缓存拦截器直接测试报告'));
    console.log(chalk.cyan('='.repeat(60)));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.status === 'PASS').length;
    const failedTests = this.testResults.filter(r => r.status === 'FAIL').length;
    
    console.log(chalk.blue(`\n📋 测试统计:`));
    console.log(chalk.green(`  ✅ 总测试数: ${totalTests}`));
    console.log(chalk.green(`  ✅ 通过: ${passedTests}`));
    console.log(chalk.red(`  ❌ 失败: ${failedTests}`));
    console.log(chalk.blue(`  📈 成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`));

    console.log(chalk.cyan(`\n📋 详细结果:`));
    this.testResults.forEach(result => {
      if (result.status === 'PASS') {
        console.log(chalk.green(`  ✅ ${result.name}`));
        if (result.details) {
          console.log(chalk.gray(`     ${result.details}`));
        }
      } else {
        console.log(chalk.red(`  ❌ ${result.name}`));
        console.log(chalk.red(`     错误: ${result.error}`));
      }
    });

    if (passedTests === totalTests) {
      console.log(chalk.green('\n🎉 所有缓存拦截器测试通过！'));
      console.log(chalk.green('   表达式解析器和缓存装饰器工作完美！'));
    } else {
      console.log(chalk.yellow('\n⚠️ 部分测试失败，需要检查缓存拦截器'));
    }

    console.log(chalk.cyan(`\n📝 验证的功能:`));
    console.log(chalk.blue(`  ✅ @Cacheable 装饰器功能`));
    console.log(chalk.blue(`  ✅ @CachePut 装饰器功能`));
    console.log(chalk.blue(`  ✅ @CacheEvict 装饰器功能`));
    console.log(chalk.blue(`  ✅ #{paramName} 表达式解析`));
    console.log(chalk.blue(`  ✅ #{param != null} 条件表达式`));
    console.log(chalk.blue(`  ✅ 缓存键生成和管理`));
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log(chalk.cyan('🚀 启动缓存拦截器直接测试'));
    console.log(chalk.cyan('='.repeat(60)));
    
    try {
      await this.testCacheableExpression();
      await this.testCachePutExpression();
      await this.testCacheEvictExpression();
      await this.testConditionalExpression();
      this.generateReport();
      
    } catch (error) {
      console.error(chalk.red('❌ 测试执行失败:'), error.message);
      if (error.response) {
        console.error(chalk.red('响应状态:'), error.response.status);
        console.error(chalk.red('响应数据:'), error.response.data);
      }
    }
  }
}

// 运行测试
const test = new CacheInterceptorDirectTest();
test.runAllTests();
