#!/bin/bash

# Redis 服务设置脚本
# 用于初始化和管理 Redis 独立服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    log_success "Docker and Docker Compose are available"
}

# 创建必要的目录
create_directories() {
    log_info "Creating necessary directories..."
    
    mkdir -p data/redis/master
    mkdir -p data/redis/slave
    mkdir -p logs/redis
    mkdir -p config/redis
    
    # 设置权限
    chmod 755 data/redis/master
    chmod 755 data/redis/slave
    chmod 755 logs/redis
    
    log_success "Directories created successfully"
}

# 生成 Redis 密码
generate_redis_password() {
    if [ ! -f .env ]; then
        log_info "Generating Redis password..."
        
        # 生成随机密码
        REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
        
        # 创建 .env 文件
        cat > .env << EOF
# Redis Configuration
REDIS_PASSWORD=${REDIS_PASSWORD}
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_CLUSTER=false
REDIS_KEY_PREFIX=fm:

# Environment
NODE_ENV=development
EOF
        
        log_success "Redis password generated and saved to .env file"
    else
        log_info ".env file already exists, skipping password generation"
    fi
}

# 启动 Redis 服务
start_redis() {
    local mode=${1:-"single"}
    
    log_info "Starting Redis in $mode mode..."
    
    case $mode in
        "single")
            docker-compose -f docker-compose.redis.yml up -d redis-master
            ;;
        "replica")
            docker-compose -f docker-compose.redis.yml up -d redis-master redis-slave
            ;;
        "sentinel")
            docker-compose -f docker-compose.redis.yml up -d redis-master redis-slave redis-sentinel-1 redis-sentinel-2 redis-sentinel-3
            ;;
        "full")
            docker-compose -f docker-compose.redis.yml up -d
            ;;
        *)
            log_error "Invalid mode: $mode. Available modes: single, replica, sentinel, full"
            exit 1
            ;;
    esac
    
    log_success "Redis started in $mode mode"
}

# 停止 Redis 服务
stop_redis() {
    log_info "Stopping Redis services..."
    
    docker-compose -f docker-compose.redis.yml down
    
    log_success "Redis services stopped"
}

# 重启 Redis 服务
restart_redis() {
    local mode=${1:-"single"}
    
    log_info "Restarting Redis services..."
    
    stop_redis
    sleep 2
    start_redis $mode
    
    log_success "Redis services restarted"
}

# 检查 Redis 状态
check_redis_status() {
    log_info "Checking Redis status..."
    
    # 检查容器状态
    docker-compose -f docker-compose.redis.yml ps
    
    # 检查 Redis 连接
    if docker exec fm-redis-master redis-cli -a "${REDIS_PASSWORD:-redispassword}" ping > /dev/null 2>&1; then
        log_success "Redis master is responding"
    else
        log_error "Redis master is not responding"
    fi
    
    # 检查从节点（如果存在）
    if docker ps | grep -q fm-redis-slave; then
        if docker exec fm-redis-slave redis-cli -a "${REDIS_PASSWORD:-redispassword}" ping > /dev/null 2>&1; then
            log_success "Redis slave is responding"
        else
            log_error "Redis slave is not responding"
        fi
    fi
}

# 查看 Redis 日志
view_logs() {
    local service=${1:-"redis-master"}
    
    log_info "Viewing logs for $service..."
    
    docker-compose -f docker-compose.redis.yml logs -f $service
}

# 连接到 Redis CLI
redis_cli() {
    local instance=${1:-"master"}
    
    case $instance in
        "master")
            docker exec -it fm-redis-master redis-cli -a "${REDIS_PASSWORD:-redispassword}"
            ;;
        "slave")
            docker exec -it fm-redis-slave redis-cli -a "${REDIS_PASSWORD:-redispassword}"
            ;;
        *)
            log_error "Invalid instance: $instance. Available instances: master, slave"
            exit 1
            ;;
    esac
}

# 备份 Redis 数据
backup_redis() {
    local backup_dir="backups/redis/$(date +%Y%m%d_%H%M%S)"
    
    log_info "Creating Redis backup..."
    
    mkdir -p $backup_dir
    
    # 备份主节点数据
    docker exec fm-redis-master redis-cli -a "${REDIS_PASSWORD:-redispassword}" BGSAVE
    sleep 5
    docker cp fm-redis-master:/data/dump.rdb $backup_dir/master_dump.rdb
    
    # 备份从节点数据（如果存在）
    if docker ps | grep -q fm-redis-slave; then
        docker exec fm-redis-slave redis-cli -a "${REDIS_PASSWORD:-redispassword}" BGSAVE
        sleep 5
        docker cp fm-redis-slave:/data/dump.rdb $backup_dir/slave_dump.rdb
    fi
    
    log_success "Redis backup created at $backup_dir"
}

# 恢复 Redis 数据
restore_redis() {
    local backup_file=$1
    
    if [ -z "$backup_file" ]; then
        log_error "Please specify backup file path"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        log_error "Backup file not found: $backup_file"
        exit 1
    fi
    
    log_info "Restoring Redis from backup: $backup_file"
    
    # 停止 Redis
    stop_redis
    
    # 复制备份文件
    cp $backup_file data/redis/master/dump.rdb
    
    # 启动 Redis
    start_redis
    
    log_success "Redis restored from backup"
}

# 清理 Redis 数据
clean_redis() {
    log_warning "This will delete all Redis data. Are you sure? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning Redis data..."
        
        stop_redis
        
        rm -rf data/redis/master/*
        rm -rf data/redis/slave/*
        rm -rf logs/redis/*
        
        log_success "Redis data cleaned"
    else
        log_info "Operation cancelled"
    fi
}

# 显示帮助信息
show_help() {
    echo "Redis Service Management Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  setup                 - Initial setup (create directories, generate password)"
    echo "  start [MODE]          - Start Redis (modes: single, replica, sentinel, full)"
    echo "  stop                  - Stop Redis services"
    echo "  restart [MODE]        - Restart Redis services"
    echo "  status                - Check Redis status"
    echo "  logs [SERVICE]        - View logs (services: redis-master, redis-slave, etc.)"
    echo "  cli [INSTANCE]        - Connect to Redis CLI (instances: master, slave)"
    echo "  backup                - Create Redis backup"
    echo "  restore [FILE]        - Restore Redis from backup"
    echo "  clean                 - Clean all Redis data"
    echo "  help                  - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 setup"
    echo "  $0 start single"
    echo "  $0 start replica"
    echo "  $0 logs redis-master"
    echo "  $0 cli master"
    echo "  $0 backup"
    echo "  $0 restore backups/redis/20231201_120000/master_dump.rdb"
}

# 主函数
main() {
    case ${1:-help} in
        "setup")
            check_docker
            create_directories
            generate_redis_password
            log_success "Redis setup completed"
            ;;
        "start")
            check_docker
            start_redis ${2:-single}
            ;;
        "stop")
            stop_redis
            ;;
        "restart")
            check_docker
            restart_redis ${2:-single}
            ;;
        "status")
            check_redis_status
            ;;
        "logs")
            view_logs ${2:-redis-master}
            ;;
        "cli")
            redis_cli ${2:-master}
            ;;
        "backup")
            backup_redis
            ;;
        "restore")
            restore_redis $2
            ;;
        "clean")
            clean_redis
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
