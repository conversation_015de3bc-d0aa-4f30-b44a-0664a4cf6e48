#!/bin/bash
# 区服 server001 部署脚本
# 自动生成于: 2025-07-28T07:18:00.337Z

set -e

SERVER_ID="server001"
SERVICES=("character" "hero")

echo "🚀 开始部署区服: $SERVER_ID"

# 1. 创建配置目录
echo "📁 创建配置目录..."
mkdir -p config/$SERVER_ID

# 2. 生成配置文件
echo "⚙️ 生成配置文件..."
for service in "${SERVICES[@]}"; do
  echo "生成 $service 配置..."
  node tools/config-generator/server-config-generator.js env \
    --server=$SERVER_ID \
    --service=$service \
    --output=config/$SERVER_ID/.env.$service
done

# 3. 初始化数据库
echo "🗄️ 初始化数据库..."
bash scripts/init-$SERVER_ID-databases.sh

# 4. 生成Docker Compose配置
echo "🐳 生成Docker Compose配置..."
node tools/config-generator/server-config-generator.js docker-compose-full \
  --server=$SERVER_ID \
  --services=${SERVICES[*]} \
  --output=docker-compose.$SERVER_ID.yml

# 5. 部署服务实例
echo "🚀 部署服务实例..."
docker-compose -f docker-compose.$SERVER_ID.yml up -d

# 6. 等待服务就绪
echo "⏳ 等待服务就绪..."
bash scripts/wait-for-server-services.sh $SERVER_ID

# 7. 注册到服务发现
echo "📋 注册服务到Gateway..."
bash scripts/register-server-services.sh $SERVER_ID

echo "🎉 区服 $SERVER_ID 部署完成！"
echo "📊 查看状态: docker-compose -f docker-compose.$SERVER_ID.yml ps"
echo "📋 查看日志: docker-compose -f docker-compose.$SERVER_ID.yml logs -f"
