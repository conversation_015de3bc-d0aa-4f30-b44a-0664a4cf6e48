#!/usr/bin/env node

/**
 * 压力测试脚本
 * 测试系统在极限负载下的性能表现和稳定性
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

class StressTest {
  constructor() {
    this.config = {
      authBaseUrl: 'http://localhost:3001',
      gatewayBaseUrl: 'http://localhost:3000',
      phases: [
        { name: '预热阶段', duration: 30000, concurrency: 10, rps: 50 },
        { name: '增压阶段', duration: 60000, concurrency: 100, rps: 200 },
        { name: '峰值阶段', duration: 120000, concurrency: 500, rps: 1000 },
        { name: '极限阶段', duration: 60000, concurrency: 1000, rps: 2000 },
        { name: '恢复阶段', duration: 30000, concurrency: 50, rps: 100 }
      ],
      timeout: 10000,
      maxRetries: 3
    };
    
    this.results = {
      phases: [],
      totalRequests: 0,
      totalErrors: 0,
      systemMetrics: [],
      breakingPoint: null
    };
    
    this.testUsers = [];
    this.isRunning = false;
  }

  // 日志输出
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '🔥',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      phase: '📊'
    }[type] || '🔥';
    
    console.log(`[${timestamp}] ${prefix} ${message}`);
  }

  // 创建大量测试用户
  async createTestUsers(count = 200) {
    this.log(`创建 ${count} 个测试用户...`);
    
    const batchSize = 20;
    const batches = Math.ceil(count / batchSize);
    
    for (let batch = 0; batch < batches; batch++) {
      const batchPromises = [];
      const batchStart = batch * batchSize;
      const batchEnd = Math.min(batchStart + batchSize, count);
      
      for (let i = batchStart; i < batchEnd; i++) {
        const userData = {
          username: `stresstest_user_${i}`,
          email: `stresstest${i}@test.com`,
          password: 'StressTest123!@#',
          confirmPassword: 'StressTest123!@#',
          acceptTerms: true,
          profile: {
            firstName: `Stress${i}`,
            lastName: 'Test'
          }
        };
        
        batchPromises.push(this.createOrLoginUser(userData));
      }
      
      await Promise.all(batchPromises);
      this.log(`批次 ${batch + 1}/${batches} 完成`);
    }
    
    this.log(`成功创建/登录 ${this.testUsers.length} 个测试用户`, 'success');
  }

  // 创建或登录单个用户
  async createOrLoginUser(userData) {
    try {
      // 尝试注册
      const response = await axios.post(
        `${this.config.authBaseUrl}/api/auth/register`,
        userData,
        { timeout: this.config.timeout }
      );
      
      if (response.status === 201) {
        const loginResponse = await axios.post(
          `${this.config.authBaseUrl}/api/auth/login`,
          {
            identifier: userData.username,
            password: userData.password
          },
          { timeout: this.config.timeout }
        );
        
        this.testUsers.push({
          ...userData,
          token: loginResponse.data.data.tokens.accessToken
        });
      }
    } catch (error) {
      // 用户可能已存在，尝试登录
      try {
        const loginResponse = await axios.post(
          `${this.config.authBaseUrl}/api/auth/login`,
          {
            identifier: userData.username,
            password: userData.password
          },
          { timeout: this.config.timeout }
        );
        
        this.testUsers.push({
          ...userData,
          token: loginResponse.data.data.tokens.accessToken
        });
      } catch (loginError) {
        // 忽略登录失败，继续测试
      }
    }
  }

  // 获取系统指标
  getSystemMetrics() {
    const memUsage = process.memoryUsage();
    return {
      timestamp: Date.now(),
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      rss: memUsage.rss
    };
  }

  // 执行压力请求
  async executeStressRequest(user, requestType) {
    const startTime = performance.now();
    
    try {
      let response;
      
      switch (requestType) {
        case 'auth_heavy':
          // 执行多个认证操作
          response = await Promise.all([
            axios.post(`${this.config.authBaseUrl}/api/auth/verify-token`,
              { token: user.token }, { timeout: this.config.timeout }),
            axios.get(`${this.config.authBaseUrl}/api/users/me`,
              { headers: { Authorization: `Bearer ${user.token}` }, timeout: this.config.timeout })
          ]);
          break;
          
        case 'gateway_heavy':
          // 执行多个网关操作
          response = await Promise.all([
            axios.get(`${this.config.gatewayBaseUrl}/health`, { timeout: this.config.timeout }),
            axios.get(`${this.config.gatewayBaseUrl}/api/users/me`, 
              { headers: { Authorization: `Bearer ${user.token}` }, timeout: this.config.timeout })
          ]);
          break;
          
        case 'mixed_heavy':
          // 混合负载
          response = await Promise.all([
            axios.post(`${this.config.authBaseUrl}/api/auth/verify-token`,
              { token: user.token }, { timeout: this.config.timeout }),
            axios.get(`${this.config.gatewayBaseUrl}/health`, { timeout: this.config.timeout }),
            axios.get(`${this.config.authBaseUrl}/api/users/me`,
              { headers: { Authorization: `Bearer ${user.token}` }, timeout: this.config.timeout })
          ]);
          break;
          
        default:
          throw new Error(`未知的请求类型: ${requestType}`);
      }
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      return { success: true, responseTime, responses: response };
      
    } catch (error) {
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      return { 
        success: false, 
        responseTime, 
        error: error.message,
        code: error.code || 'UNKNOWN'
      };
    }
  }

  // 运行单个阶段的压力测试
  async runPhase(phase) {
    this.log(`开始 ${phase.name} - 并发: ${phase.concurrency}, RPS: ${phase.rps}`, 'phase');
    
    const phaseResult = {
      name: phase.name,
      startTime: Date.now(),
      endTime: null,
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [],
      errors: [],
      systemMetrics: [],
      avgConcurrency: phase.concurrency,
      targetRPS: phase.rps
    };
    
    const requestTypes = ['auth_heavy', 'gateway_heavy', 'mixed_heavy'];
    const interval = 1000 / phase.rps; // 请求间隔
    const endTime = Date.now() + phase.duration;
    
    const activeRequests = new Set();
    let requestId = 0;
    
    // 系统监控
    const metricsInterval = setInterval(() => {
      phaseResult.systemMetrics.push(this.getSystemMetrics());
    }, 1000);
    
    while (Date.now() < endTime && this.isRunning) {
      // 控制并发数
      if (activeRequests.size < phase.concurrency) {
        const user = this.testUsers[Math.floor(Math.random() * this.testUsers.length)];
        const requestType = requestTypes[Math.floor(Math.random() * requestTypes.length)];
        
        const currentRequestId = requestId++;
        activeRequests.add(currentRequestId);
        
        // 异步执行请求
        this.executeStressRequest(user, requestType).then(result => {
          activeRequests.delete(currentRequestId);
          
          phaseResult.totalRequests++;
          this.results.totalRequests++;
          
          if (result.success) {
            phaseResult.successfulRequests++;
            phaseResult.responseTimes.push(result.responseTime);
          } else {
            phaseResult.failedRequests++;
            this.results.totalErrors++;
            phaseResult.errors.push({
              type: requestType,
              message: result.error,
              code: result.code,
              responseTime: result.responseTime
            });
          }
        }).catch(error => {
          activeRequests.delete(currentRequestId);
          phaseResult.failedRequests++;
          this.results.totalErrors++;
        });
      }
      
      // 控制请求频率
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    // 等待所有活跃请求完成
    while (activeRequests.size > 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    clearInterval(metricsInterval);
    phaseResult.endTime = Date.now();
    
    // 计算阶段统计
    const duration = (phaseResult.endTime - phaseResult.startTime) / 1000;
    const successRate = (phaseResult.successfulRequests / phaseResult.totalRequests * 100).toFixed(2);
    const actualRPS = (phaseResult.totalRequests / duration).toFixed(2);
    
    this.log(`${phase.name} 完成 - 成功率: ${successRate}%, 实际RPS: ${actualRPS}`, 'success');
    
    // 检查是否达到系统极限
    if (successRate < 50) {
      this.results.breakingPoint = {
        phase: phase.name,
        concurrency: phase.concurrency,
        rps: phase.rps,
        successRate: parseFloat(successRate)
      };
      this.log(`检测到系统极限点: ${phase.name}`, 'warning');
    }
    
    this.results.phases.push(phaseResult);
  }

  // 运行完整压力测试
  async runStressTest() {
    this.log('开始压力测试...');
    this.isRunning = true;
    
    for (const phase of this.config.phases) {
      if (!this.isRunning) break;
      
      await this.runPhase(phase);
      
      // 阶段间休息
      if (phase !== this.config.phases[this.config.phases.length - 1]) {
        this.log('阶段间休息 5 秒...');
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
    
    this.isRunning = false;
    this.log('压力测试完成', 'success');
  }

  // 计算总体统计
  calculateOverallStatistics() {
    const allResponseTimes = [];
    let totalDuration = 0;
    
    this.results.phases.forEach(phase => {
      allResponseTimes.push(...phase.responseTimes);
      totalDuration += (phase.endTime - phase.startTime) / 1000;
    });
    
    if (allResponseTimes.length === 0) {
      return {
        totalDuration,
        overallSuccessRate: 0,
        avgResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0
      };
    }
    
    allResponseTimes.sort((a, b) => a - b);
    
    return {
      totalDuration,
      overallSuccessRate: ((this.results.totalRequests - this.results.totalErrors) / this.results.totalRequests * 100).toFixed(2),
      avgResponseTime: (allResponseTimes.reduce((a, b) => a + b, 0) / allResponseTimes.length).toFixed(2),
      p95ResponseTime: allResponseTimes[Math.floor(allResponseTimes.length * 0.95)].toFixed(2),
      p99ResponseTime: allResponseTimes[Math.floor(allResponseTimes.length * 0.99)].toFixed(2)
    };
  }

  // 输出测试结果
  printResults() {
    const stats = this.calculateOverallStatistics();
    
    console.log('\n' + '='.repeat(80));
    console.log('🔥 压力测试结果报告');
    console.log('='.repeat(80));
    
    console.log(`⏱️  总测试时间: ${stats.totalDuration.toFixed(2)}秒`);
    console.log(`📈 总请求数: ${this.results.totalRequests}`);
    console.log(`❌ 总错误数: ${this.results.totalErrors}`);
    console.log(`📊 总体成功率: ${stats.overallSuccessRate}%`);
    console.log(`⚡ 平均响应时间: ${stats.avgResponseTime}ms`);
    console.log(`📊 95%响应时间: ${stats.p95ResponseTime}ms`);
    console.log(`📊 99%响应时间: ${stats.p99ResponseTime}ms`);
    
    if (this.results.breakingPoint) {
      console.log('\n🚨 系统极限点:');
      console.log(`   阶段: ${this.results.breakingPoint.phase}`);
      console.log(`   并发数: ${this.results.breakingPoint.concurrency}`);
      console.log(`   目标RPS: ${this.results.breakingPoint.rps}`);
      console.log(`   成功率: ${this.results.breakingPoint.successRate}%`);
    }
    
    console.log('\n📊 各阶段详细结果:');
    this.results.phases.forEach(phase => {
      const duration = (phase.endTime - phase.startTime) / 1000;
      const successRate = (phase.successfulRequests / phase.totalRequests * 100).toFixed(2);
      const actualRPS = (phase.totalRequests / duration).toFixed(2);
      
      console.log(`\n   ${phase.name}:`);
      console.log(`     请求数: ${phase.totalRequests}`);
      console.log(`     成功率: ${successRate}%`);
      console.log(`     实际RPS: ${actualRPS}`);
      console.log(`     错误数: ${phase.failedRequests}`);
    });
    
    console.log('='.repeat(80));
    
    return { ...stats, phases: this.results.phases, breakingPoint: this.results.breakingPoint };
  }

  // 保存测试结果
  saveResults(stats) {
    const fs = require('fs');
    const path = require('path');
    
    const resultsDir = path.join(__dirname, '../test-results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const resultsFile = path.join(resultsDir, `stress-test-${timestamp}.json`);
    
    const fullResults = {
      config: this.config,
      statistics: stats,
      phases: this.results.phases,
      breakingPoint: this.results.breakingPoint,
      timestamp: new Date().toISOString()
    };
    
    fs.writeFileSync(resultsFile, JSON.stringify(fullResults, null, 2));
    this.log(`测试结果已保存到: ${resultsFile}`, 'success');
  }

  // 运行完整测试
  async run() {
    try {
      await this.createTestUsers(200);
      await this.runStressTest();
      const stats = this.printResults();
      this.saveResults(stats);
      
      // 判断测试是否通过
      const passed = stats.overallSuccessRate >= 80 && !this.results.breakingPoint;
      if (passed) {
        this.log('压力测试通过！', 'success');
        process.exit(0);
      } else {
        this.log('系统在压力测试中表现不佳', 'warning');
        process.exit(1);
      }
      
    } catch (error) {
      this.log(`压力测试失败: ${error.message}`, 'error');
      process.exit(1);
    }
  }
}

// 主函数
async function main() {
  const stressTest = new StressTest();
  
  // 处理中断信号
  process.on('SIGINT', () => {
    stressTest.log('收到中断信号，正在停止测试...', 'warning');
    stressTest.isRunning = false;
  });
  
  await stressTest.run();
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = StressTest;
