#!/usr/bin/env node

/**
 * Redis 完整测试套件执行器
 * 
 * 按照测试指南文档执行所有Redis服务层测试：
 * 1. 综合功能测试
 * 2. 分布式锁专项测试
 * 3. 布隆过滤器专项测试
 * 4. 缓存模式专项测试
 * 5. 生成详细测试报告
 */

const { spawn } = require('child_process');
const fs = require('fs').promises;
const path = require('path');

// 测试套件配置 - 集成所有Redis服务层测试脚本
const testSuite = [
  {
    name: 'Redis服务层基础功能测试',
    script: 'redis-basic-test.js',
    description: '测试RedisService基础功能、健康检查、缓存、数据结构、错误处理',
    category: 'basic',
    priority: 1,
    required: true
  },
  {
    name: 'Redis服务层高级功能测试',
    script: 'redis-advanced-service-test.js',
    description: '测试RedisQueueService、RedisPubSubService、RedisLockService、缓存防护',
    category: 'advanced',
    priority: 2,
    required: true
  },
  {
    name: 'Redis服务层性能测试',
    script: 'redis-performance-service-test.js',
    description: '测试基础性能、并发性能、WebSocket性能、压力负载',
    category: 'performance',
    priority: 3,
    required: true
  },
  {
    name: 'Redis分布式锁专项测试',
    script: 'redis-lock-service-test.js',
    description: '测试分布式锁基础操作、并发竞争、超时机制、可重入锁',
    category: 'lock',
    priority: 4,
    required: false
  },
  {
    name: 'Redis布隆过滤器专项测试',
    script: 'redis-bloom-filter-test.js',
    description: '测试布隆过滤器基础功能、缓存穿透防护、误判率验证',
    category: 'bloom',
    priority: 5,
    required: false
  },
  {
    name: 'Redis缓存模式专项测试',
    script: 'redis-cache-patterns-test.js',
    description: '测试Cache-Aside、Write-Through、Write-Behind模式和缓存装饰器',
    category: 'cache-patterns',
    priority: 6,
    required: false
  },
  {
    name: 'Redis微服务集成测试',
    script: 'redis-microservice-test.js',
    description: '测试异步模块工厂、微服务集成、会话管理、监控功能',
    category: 'integration',
    priority: 7,
    required: false
  }
];

// 颜色输出函数
function log(message, color = 'white') {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class RunRedisTestSuite {
  constructor() {
    this.startTime = null;
    this.endTime = null;
    this.testResults = [];
    this.summary = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0
    };
  }

  async initialize() {
    log('🚀 初始化Redis完整测试套件...', 'cyan');
    this.startTime = new Date();
    
    // 检查测试脚本是否存在
    for (const test of testSuite) {
      const scriptPath = path.join(__dirname, test.script);
      try {
        await fs.access(scriptPath);
        log(`✅ 找到测试脚本: ${test.script}`, 'green');
      } catch (error) {
        log(`❌ 测试脚本不存在: ${test.script}`, 'red');
        if (test.required) {
          throw new Error(`必需的测试脚本不存在: ${test.script}`);
        }
      }
    }

    return true;
  }

  async runTest(test) {
    log(`\n🧪 执行测试套件: ${test.name}`, 'blue');
    log(`   描述: ${test.description}`, 'blue');
    log(`   优先级: ${test.priority} | 类别: ${test.category}`, 'blue');

    const scriptPath = path.join(__dirname, test.script);
    const startTime = Date.now();

    return new Promise((resolve) => {
      const child = spawn('node', [scriptPath], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: __dirname
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        const output = data.toString();
        stdout += output;
        // 实时输出测试进度
        process.stdout.write(output);
      });

      child.stderr.on('data', (data) => {
        const output = data.toString();
        stderr += output;
        process.stderr.write(output);
      });

      const timeout = setTimeout(() => {
        child.kill('SIGTERM');
        log(`\n⏰ 测试超时: ${test.name}`, 'yellow');
      }, 120000); // 2分钟超时

      child.on('close', (code) => {
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        
        const result = {
          name: test.name,
          script: test.script,
          category: test.category,
          priority: test.priority,
          success: code === 0,
          exitCode: code,
          duration,
          stdout,
          stderr,
          timestamp: new Date().toISOString()
        };

        if (code === 0) {
          log(`\n✅ ${test.name} - 测试通过 (${duration}ms)`, 'green');
        } else {
          log(`\n❌ ${test.name} - 测试失败 (退出码: ${code})`, 'red');
        }

        resolve(result);
      });

      child.on('error', (error) => {
        clearTimeout(timeout);
        const duration = Date.now() - startTime;
        
        const result = {
          name: test.name,
          script: test.script,
          category: test.category,
          priority: test.priority,
          success: false,
          exitCode: -1,
          duration,
          stdout,
          stderr: stderr + error.message,
          timestamp: new Date().toISOString(),
          error: error.message
        };

        log(`\n💥 ${test.name} - 执行错误: ${error.message}`, 'red');
        resolve(result);
      });
    });
  }

  async runAllTests() {
    log('\n🎯 开始执行Redis服务层完整测试套件', 'cyan');
    log('='.repeat(80), 'cyan');

    log('\n📋 测试套件概览 (共7个测试套件):', 'cyan');
    log('  🔧 基础功能测试 - 验证Redis服务层核心组件', 'blue');
    log('  🚀 高级功能测试 - 验证队列、发布订阅、分布式锁', 'blue');
    log('  ⚡ 性能测试 - 验证并发性能和压力负载', 'blue');
    log('  🔒 分布式锁专项 - 深度验证锁机制', 'blue');
    log('  🌸 布隆过滤器专项 - 验证缓存防护机制', 'blue');
    log('  💾 缓存模式专项 - 验证多种缓存策略', 'blue');
    log('  🔗 微服务集成 - 验证服务间协作', 'blue');
    log('', 'white');

    log('📝 测试说明:', 'cyan');
    log('  ✅ 所有测试都针对libs/common/src/redis下的Redis服务层', 'yellow');
    log('  ✅ 通过微服务API间接验证Redis功能，而非直接测试基础设施', 'yellow');
    log('  ✅ 避免了重复测试，每个脚本专注于特定功能领域', 'yellow');
    log('', 'white');

    // 按优先级排序执行测试
    const sortedTests = [...testSuite].sort((a, b) => a.priority - b.priority);

    for (const test of sortedTests) {
      try {
        // 检查脚本是否存在
        const scriptPath = path.join(__dirname, test.script);
        await fs.access(scriptPath);
        
        const result = await this.runTest(test);
        this.testResults.push(result);
        
        // 在测试之间添加短暂延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        if (test.required) {
          log(`❌ 必需测试失败: ${test.name}`, 'red');
          const result = {
            name: test.name,
            script: test.script,
            category: test.category,
            priority: test.priority,
            success: false,
            exitCode: -1,
            duration: 0,
            stdout: '',
            stderr: error.message,
            timestamp: new Date().toISOString(),
            error: error.message
          };
          this.testResults.push(result);
        } else {
          log(`⚠️  跳过可选测试: ${test.name} (${error.message})`, 'yellow');
        }
      }
    }

    this.endTime = new Date();
    this.calculateSummary();
  }

  calculateSummary() {
    this.summary.total = this.testResults.length;
    this.summary.passed = this.testResults.filter(r => r.success).length;
    this.summary.failed = this.testResults.filter(r => !r.success).length;
    this.summary.skipped = testSuite.length - this.testResults.length;
    this.summary.duration = this.endTime - this.startTime;
  }

  generateDetailedReport() {
    const report = {
      metadata: {
        testSuite: 'Redis服务层完整测试套件',
        version: '1.0.0',
        startTime: this.startTime.toISOString(),
        endTime: this.endTime.toISOString(),
        duration: this.summary.duration,
        environment: {
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch
        }
      },
      summary: this.summary,
      testResults: this.testResults,
      categoryAnalysis: this.analyzeByCategoryy(),
      recommendations: this.generateRecommendations()
    };

    return report;
  }

  analyzeByCategoryy() {
    const categories = {};

    // 定义类别描述
    const categoryDescriptions = {
      'basic': 'Redis服务层基础功能',
      'advanced': 'Redis服务层高级功能',
      'performance': 'Redis服务层性能测试',
      'lock': 'Redis分布式锁专项',
      'bloom': 'Redis布隆过滤器专项',
      'cache-patterns': 'Redis缓存模式专项',
      'integration': 'Redis微服务集成'
    };

    this.testResults.forEach(result => {
      if (!categories[result.category]) {
        categories[result.category] = {
          name: categoryDescriptions[result.category] || result.category,
          total: 0,
          passed: 0,
          failed: 0,
          avgDuration: 0,
          successRate: 0
        };
      }

      categories[result.category].total++;
      if (result.success) {
        categories[result.category].passed++;
      } else {
        categories[result.category].failed++;
      }
    });

    // 计算平均持续时间和成功率
    Object.keys(categories).forEach(category => {
      const categoryResults = this.testResults.filter(r => r.category === category);
      const totalDuration = categoryResults.reduce((sum, r) => sum + r.duration, 0);
      categories[category].avgDuration = Math.round(totalDuration / categoryResults.length);
      categories[category].successRate = Math.round((categories[category].passed / categories[category].total) * 100);
    });

    return categories;
  }

  generateRecommendations() {
    const recommendations = [];
    const categoryAnalysis = this.analyzeByCategoryy();

    // 基于测试结果生成建议
    if (this.summary.failed === 0) {
      recommendations.push('🎉 所有Redis服务层测试通过！Redis客户端库/SDK已准备好用于生产环境。');
    } else {
      recommendations.push('⚠️ 部分Redis服务层测试失败，建议检查失败的组件。');
    }

    const successRate = (this.summary.passed / this.summary.total) * 100;
    if (successRate >= 90) {
      recommendations.push('✅ Redis服务层测试成功率优秀，代码质量很高。');
    } else if (successRate >= 75) {
      recommendations.push('⚠️ Redis服务层测试成功率良好，但仍有改进空间。');
    } else {
      recommendations.push('❌ Redis服务层测试成功率较低，需要重点关注失败的测试。');
    }

    // 分类建议
    Object.entries(categoryAnalysis).forEach(([category, stats]) => {
      if (stats.successRate === 100) {
        recommendations.push(`✅ ${stats.name}: 完美通过，功能稳定可靠。`);
      } else if (stats.successRate >= 75) {
        recommendations.push(`⚠️ ${stats.name}: 基本正常，建议关注失败的测试。`);
      } else {
        recommendations.push(`❌ ${stats.name}: 存在问题，需要重点修复。`);
      }
    });

    // 性能建议
    const avgDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0) / this.testResults.length;
    if (avgDuration < 30000) {
      recommendations.push('⚡ Redis服务层响应速度良好。');
    } else {
      recommendations.push('🐌 Redis服务层响应较慢，可能需要性能优化。');
    }

    // 架构建议
    const requiredTests = this.testResults.filter(r => testSuite.find(t => t.script === r.script)?.required);
    const requiredSuccessRate = (requiredTests.filter(r => r.success).length / requiredTests.length) * 100;

    if (requiredSuccessRate === 100) {
      recommendations.push('🏗️ Redis服务层核心架构稳定，可以进行生产部署。');
    } else {
      recommendations.push('🏗️ Redis服务层核心架构需要修复后才能生产部署。');
    }

    return recommendations;
  }

  async saveReport(report) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(__dirname, '..', 'test-reports', `redis-full-test-suite-${timestamp}.json`);
    
    try {
      // 确保报告目录存在
      const reportDir = path.dirname(reportPath);
      await fs.mkdir(reportDir, { recursive: true });
      
      // 保存JSON报告
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      log(`📄 详细测试报告已保存: ${reportPath}`, 'green');

      // 生成简化的文本报告
      const textReportPath = reportPath.replace('.json', '.txt');
      const textReport = this.generateTextReport(report);
      await fs.writeFile(textReportPath, textReport);
      log(`📄 文本报告已保存: ${textReportPath}`, 'green');

    } catch (error) {
      log(`❌ 保存报告失败: ${error.message}`, 'red');
    }
  }

  generateTextReport(report) {
    const lines = [];
    
    lines.push('Redis 服务层完整测试套件报告');
    lines.push('='.repeat(60));
    lines.push('');
    
    lines.push('📊 测试概要:');
    lines.push(`  总测试数: ${report.summary.total}`);
    lines.push(`  通过: ${report.summary.passed}`);
    lines.push(`  失败: ${report.summary.failed}`);
    lines.push(`  跳过: ${report.summary.skipped}`);
    lines.push(`  成功率: ${((report.summary.passed / report.summary.total) * 100).toFixed(2)}%`);
    lines.push(`  总耗时: ${Math.round(report.summary.duration / 1000)}秒`);
    lines.push('');
    
    lines.push('📋 分类分析:');
    Object.entries(report.categoryAnalysis).forEach(([category, stats]) => {
      lines.push(`  ${category}:`);
      lines.push(`    通过/总数: ${stats.passed}/${stats.total}`);
      lines.push(`    平均耗时: ${stats.avgDuration}ms`);
    });
    lines.push('');
    
    lines.push('📝 测试详情:');
    report.testResults.forEach((test, index) => {
      lines.push(`  ${index + 1}. ${test.name}`);
      lines.push(`     状态: ${test.success ? '✅ 通过' : '❌ 失败'}`);
      lines.push(`     耗时: ${test.duration}ms`);
      lines.push(`     类别: ${test.category}`);
      if (!test.success && test.error) {
        lines.push(`     错误: ${test.error}`);
      }
      lines.push('');
    });
    
    lines.push('💡 建议:');
    report.recommendations.forEach(rec => {
      lines.push(`  ${rec}`);
    });
    
    return lines.join('\n');
  }

  printSummary() {
    log('\n📊 Redis完整测试套件执行总结', 'cyan');
    log('='.repeat(80), 'cyan');

    log(`总测试数: ${this.summary.total}`, 'blue');
    log(`通过: ${this.summary.passed}`, 'green');
    log(`失败: ${this.summary.failed}`, 'red');
    log(`跳过: ${this.summary.skipped}`, 'yellow');
    
    const successRate = this.summary.total > 0 ? 
      ((this.summary.passed / this.summary.total) * 100).toFixed(2) : 0;
    log(`成功率: ${successRate}%`, successRate >= 80 ? 'green' : 'yellow');
    log(`总耗时: ${Math.round(this.summary.duration / 1000)}秒`, 'blue');

    // 分类统计
    log('\n📋 分类统计:', 'cyan');
    const categoryAnalysis = this.analyzeByCategoryy();
    Object.entries(categoryAnalysis).forEach(([category, stats]) => {
      const categorySuccessRate = ((stats.passed / stats.total) * 100).toFixed(2);
      log(`  ${category}: ${stats.passed}/${stats.total} (${categorySuccessRate}%)`, 
          stats.failed === 0 ? 'green' : 'yellow');
    });

    if (this.summary.failed > 0) {
      log('\n❌ 失败的测试:', 'red');
      this.testResults.filter(r => !r.success).forEach(test => {
        log(`  - ${test.name}: ${test.error || '退出码 ' + test.exitCode}`, 'red');
      });
    }

    // 总结建议
    log('\n💡 总结:', 'cyan');
    const recommendations = this.generateRecommendations();
    recommendations.forEach(rec => {
      log(`  ${rec}`, 'white');
    });
  }
}

// 主执行函数
async function main() {
  const testSuite = new RunRedisTestSuite();
  
  try {
    await testSuite.initialize();
    await testSuite.runAllTests();
    
    const report = testSuite.generateDetailedReport();
    await testSuite.saveReport(report);
    testSuite.printSummary();
    
    // 根据测试结果设置退出码
    const successRate = testSuite.summary.passed / testSuite.summary.total;
    process.exit(successRate >= 0.8 ? 0 : 1);
    
  } catch (error) {
    log(`\n💥 测试套件执行失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 处理命令行参数
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Redis 服务层完整测试套件执行器

用法: node redis-full-test-suite.js [选项]

选项:
  --help, -h     显示帮助信息

测试套件包括 (共7个测试套件):
  1. Redis服务层基础功能测试 - RedisService、健康检查、缓存、数据结构
  2. Redis服务层高级功能测试 - 队列、发布订阅、分布式锁、缓存防护
  3. Redis服务层性能测试 - 基础性能、并发性能、WebSocket性能
  4. Redis分布式锁专项测试 - 锁操作、并发竞争、超时机制
  5. Redis布隆过滤器专项测试 - 基础功能、缓存穿透防护、误判率
  6. Redis缓存模式专项测试 - Cache-Aside、Write-Through、Write-Behind
  7. Redis微服务集成测试 - 异步模块工厂、微服务集成、会话管理

测试特点:
  ✅ 正确测试libs/common/src/redis下的Redis服务层
  ✅ 通过微服务API间接验证Redis功能
  ✅ 避免重复测试，每个脚本专注特定功能
  ✅ 自动生成详细测试报告和性能分析

前置条件:
  - 网关服务运行在 http://127.0.0.1:3000 (必需)
  - 认证服务运行在 http://127.0.0.1:3001 (可选，影响部分测试)
`);
  process.exit(0);
}

// 运行测试套件
if (require.main === module) {
  main().catch(console.error);
}

module.exports = RunRedisTestSuite;
