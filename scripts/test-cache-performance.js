#!/usr/bin/env node

/**
 * 缓存性能测试脚本 - 优化版
 * 调用HealthController中的测试接口，明确显示Redis缓存命中情况
 * 验证Spring Boot风格缓存装饰器的性能效果
 */

const axios = require('axios');
const chalk = require('chalk');

class CachePerformanceTest {
  constructor() {
    this.testResults = [];
    this.baseUrl = 'http://127.0.0.1:3001';
    this.cacheHitCount = 0;
    this.cacheMissCount = 0;
  }

  /**
   * 检查服务是否可用
   */
  async checkServiceAvailability() {
    console.log(chalk.yellow('🔍 检查服务可用性...'));

    try {
      const response = await axios.get(`${this.baseUrl}/health`);
      console.log(chalk.green('✅ 认证服务可用'));
      return true;
    } catch (error) {
      console.log(chalk.red('❌ 认证服务不可用'));
      throw new Error('认证服务不可用，请确保服务已启动');
    }
  }

  /**
   * 测试@Cacheable装饰器性能
   */
  async testCacheablePerformance() {
    console.log(chalk.blue('\n🧪 测试@Cacheable装饰器性能'));

    const testKey = `perf-test-${Date.now()}`;
    console.log(chalk.gray(`  📋 测试键: ${testKey}`));
    console.log(chalk.gray(`  📋 缓存键: health:cache-test:${testKey}`));

    // 第一次调用 - 应该缓存未命中，执行方法
    console.log(chalk.gray('  🔍 第一次调用（应该缓存未命中）...'));
    const firstCallStart = Date.now();
    const firstResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const firstCallTime = Date.now() - firstCallStart;
    const firstRandomValue = firstResponse.data.randomValue;

    console.log(chalk.gray(`    响应时间: ${firstCallTime}ms`));
    console.log(chalk.gray(`    随机值: ${firstRandomValue}`));
    console.log(chalk.red(`    状态: 缓存未命中 (执行了方法)`));
    this.cacheMissCount++;

    // 等待确保缓存生效
    await new Promise(resolve => setTimeout(resolve, 200));

    // 第二次调用 - 应该缓存命中
    console.log(chalk.gray('  🔍 第二次调用（应该缓存命中）...'));
    const secondCallStart = Date.now();
    const secondResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const secondCallTime = Date.now() - secondCallStart;
    const secondRandomValue = secondResponse.data.randomValue;

    console.log(chalk.gray(`    响应时间: ${secondCallTime}ms`));
    console.log(chalk.gray(`    随机值: ${secondRandomValue}`));

    // 验证是否从缓存获取
    const fromCache = firstRandomValue === secondRandomValue;
    if (fromCache) {
      console.log(chalk.green(`    状态: 缓存命中 ✅ (随机值相同)`));
      this.cacheHitCount++;
    } else {
      console.log(chalk.red(`    状态: 缓存未命中 ❌ (随机值不同)`));
      this.cacheMissCount++;
    }

    // 第三次调用 - 应该缓存命中
    console.log(chalk.gray('  🔍 第三次调用（应该缓存命中）...'));
    const thirdCallStart = Date.now();
    const thirdResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const thirdCallTime = Date.now() - thirdCallStart;
    const thirdRandomValue = thirdResponse.data.randomValue;

    console.log(chalk.gray(`    响应时间: ${thirdCallTime}ms`));
    console.log(chalk.gray(`    随机值: ${thirdRandomValue}`));

    // 验证是否从缓存获取
    const thirdFromCache = firstRandomValue === thirdRandomValue;
    if (thirdFromCache) {
      console.log(chalk.green(`    状态: 缓存命中 ✅ (随机值相同)`));
      this.cacheHitCount++;
    } else {
      console.log(chalk.red(`    状态: 缓存未命中 ❌ (随机值不同)`));
      this.cacheMissCount++;
    }

    const avgCachedTime = (secondCallTime + thirdCallTime) / 2;
    const speedImprovement = ((firstCallTime - avgCachedTime) / firstCallTime * 100).toFixed(1);

    console.log(chalk.cyan(`  📊 性能分析:`));
    console.log(chalk.gray(`    首次调用: ${firstCallTime}ms (缓存未命中)`));
    console.log(chalk.gray(`    平均缓存调用: ${avgCachedTime.toFixed(1)}ms`));
    console.log(chalk.green(`    性能提升: ${speedImprovement}%`));

    this.testResults.push({
      category: '@Cacheable装饰器',
      name: '缓存表达式测试',
      firstCall: firstCallTime,
      avgCachedCall: avgCachedTime,
      improvement: speedImprovement,
      cacheHits: fromCache && thirdFromCache ? 2 : (fromCache || thirdFromCache ? 1 : 0),
      status: speedImprovement > 0 && fromCache && thirdFromCache ? 'improved' : 'no-improvement'
    });
  }

  /**
   * 测试@CachePut装饰器性能
   */
  async testCachePutPerformance() {
    console.log(chalk.blue('\n🧪 测试@CachePut装饰器性能'));

    const testKey = `put-test-${Date.now()}`;
    console.log(chalk.gray(`  📋 测试键: ${testKey}`));
    console.log(chalk.gray(`  📋 缓存键: health:cache-test:${testKey}`));

    // 先建立初始缓存
    console.log(chalk.gray('  🔍 建立初始缓存...'));
    const initialResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const initialRandomValue = initialResponse.data.randomValue;
    console.log(chalk.gray(`    初始随机值: ${initialRandomValue}`));

    await new Promise(resolve => setTimeout(resolve, 100));

    // 验证缓存存在
    console.log(chalk.gray('  🔍 验证缓存存在...'));
    const verifyResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const verifyRandomValue = verifyResponse.data.randomValue;
    const cacheExists = initialRandomValue === verifyRandomValue;

    if (cacheExists) {
      console.log(chalk.green(`    状态: 缓存存在 ✅ (随机值相同)`));
      this.cacheHitCount++;
    } else {
      console.log(chalk.red(`    状态: 缓存不存在 ❌ (随机值不同)`));
      this.cacheMissCount++;
    }

    await new Promise(resolve => setTimeout(resolve, 100));

    // 使用@CachePut更新缓存
    console.log(chalk.gray('  🔍 使用@CachePut更新缓存...'));
    const updateData = {
      newField: 'updated-value',
      updateTime: new Date().toISOString()
    };

    const putCallStart = Date.now();
    const putResponse = await axios.put(`${this.baseUrl}/health/cache-expression-test/${testKey}`, updateData);
    const putCallTime = Date.now() - putCallStart;

    console.log(chalk.gray(`    更新响应时间: ${putCallTime}ms`));
    console.log(chalk.gray(`    更新数据: ${JSON.stringify(updateData).substring(0, 50)}...`));

    await new Promise(resolve => setTimeout(resolve, 100));

    // 验证缓存已更新
    console.log(chalk.gray('  🔍 验证缓存已更新...'));
    const updatedCallStart = Date.now();
    const updatedResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const updatedCallTime = Date.now() - updatedCallStart;

    console.log(chalk.gray(`    验证响应时间: ${updatedCallTime}ms`));

    // 检查数据是否更新
    const dataUpdated = JSON.stringify(initialResponse.data) !== JSON.stringify(updatedResponse.data);
    const hasNewField = updatedResponse.data.newField === 'updated-value';

    if (dataUpdated && hasNewField) {
      console.log(chalk.green(`    状态: 缓存已更新 ✅ (数据已变化)`));
      this.cacheHitCount++;
    } else {
      console.log(chalk.red(`    状态: 缓存未更新 ❌ (数据未变化)`));
      this.cacheMissCount++;
    }

    console.log(chalk.cyan(`  📊 性能分析:`));
    console.log(chalk.gray(`    缓存更新时间: ${putCallTime}ms`));
    console.log(chalk.gray(`    缓存验证时间: ${updatedCallTime}ms`));
    console.log(chalk.green(`    @CachePut功能: ${dataUpdated && hasNewField ? '正常' : '异常'}`));

    this.testResults.push({
      category: '@CachePut装饰器',
      name: '缓存更新测试',
      firstCall: putCallTime,
      avgCachedCall: updatedCallTime,
      improvement: 'N/A',
      cacheHits: (cacheExists ? 1 : 0) + (dataUpdated && hasNewField ? 1 : 0),
      status: dataUpdated && hasNewField ? 'improved' : 'no-improvement'
    });
  }

  /**
   * 测试@CacheEvict装饰器性能
   */
  async testCacheEvictPerformance() {
    console.log(chalk.blue('\n🧪 测试@CacheEvict装饰器性能'));

    const testKey = `evict-test-${Date.now()}`;
    console.log(chalk.gray(`  📋 测试键: ${testKey}`));
    console.log(chalk.gray(`  📋 缓存键: health:cache-test:${testKey}`));

    // 建立初始缓存
    console.log(chalk.gray('  🔍 建立初始缓存...'));
    const initialResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const initialRandomValue = initialResponse.data.randomValue;
    console.log(chalk.gray(`    初始随机值: ${initialRandomValue}`));

    await new Promise(resolve => setTimeout(resolve, 100));

    // 验证缓存存在
    console.log(chalk.gray('  🔍 验证缓存存在...'));
    const verifyResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const verifyRandomValue = verifyResponse.data.randomValue;
    const cacheExists = initialRandomValue === verifyRandomValue;

    if (cacheExists) {
      console.log(chalk.green(`    状态: 缓存存在 ✅ (随机值相同)`));
      this.cacheHitCount++;
    } else {
      console.log(chalk.red(`    状态: 缓存不存在 ❌ (随机值不同)`));
      this.cacheMissCount++;
      return; // 如果缓存不存在，无法测试清除功能
    }

    await new Promise(resolve => setTimeout(resolve, 100));

    // 使用@CacheEvict清除缓存
    console.log(chalk.gray('  🔍 使用@CacheEvict清除缓存...'));
    const evictCallStart = Date.now();
    const evictResponse = await axios.delete(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const evictCallTime = Date.now() - evictCallStart;

    console.log(chalk.gray(`    清除响应时间: ${evictCallTime}ms`));
    console.log(chalk.gray(`    清除响应: ${JSON.stringify(evictResponse.data).substring(0, 50)}...`));

    await new Promise(resolve => setTimeout(resolve, 100));

    // 验证缓存已清除
    console.log(chalk.gray('  🔍 验证缓存已清除...'));
    const newCallStart = Date.now();
    const newResponse = await axios.get(`${this.baseUrl}/health/cache-expression-test/${testKey}`);
    const newCallTime = Date.now() - newCallStart;
    const newRandomValue = newResponse.data.randomValue;

    console.log(chalk.gray(`    新调用响应时间: ${newCallTime}ms`));
    console.log(chalk.gray(`    新随机值: ${newRandomValue}`));

    // 检查缓存是否被清除
    const cacheCleared = initialRandomValue !== newRandomValue;

    if (cacheCleared) {
      console.log(chalk.green(`    状态: 缓存已清除 ✅ (随机值不同)`));
      this.cacheMissCount++; // 新调用应该是缓存未命中
    } else {
      console.log(chalk.red(`    状态: 缓存未清除 ❌ (随机值相同)`));
      this.cacheHitCount++; // 如果缓存未清除，这是缓存命中
    }

    console.log(chalk.cyan(`  📊 性能分析:`));
    console.log(chalk.gray(`    缓存清除时间: ${evictCallTime}ms`));
    console.log(chalk.gray(`    新数据加载时间: ${newCallTime}ms`));
    console.log(chalk.green(`    @CacheEvict功能: ${cacheCleared ? '正常' : '异常'}`));

    this.testResults.push({
      category: '@CacheEvict装饰器',
      name: '缓存清除测试',
      firstCall: evictCallTime,
      avgCachedCall: newCallTime,
      improvement: 'N/A',
      cacheHits: cacheExists ? 1 : 0,
      status: cacheCleared ? 'improved' : 'no-improvement'
    });
  }

  /**
   * 生成测试报告
   */
  generateReport() {
    console.log(chalk.cyan('\n📊 缓存性能测试报告'));
    console.log(chalk.cyan('='.repeat(60)));

    const categories = [...new Set(this.testResults.map(r => r.category))];

    categories.forEach(category => {
      const categoryResults = this.testResults.filter(r => r.category === category);
      console.log(chalk.blue(`\n📋 ${category}:`));

      categoryResults.forEach(result => {
        const status = result.status === 'improved' ? '✅' : '⚠️';
        console.log(`  ${status} ${result.name}`);

        if (result.improvement !== 'N/A') {
          console.log(chalk.gray(`     首次: ${result.firstCall}ms | 缓存: ${result.avgCachedCall.toFixed(1)}ms | 提升: ${result.improvement}%`));
        } else {
          console.log(chalk.gray(`     执行时间: ${result.firstCall}ms | 验证时间: ${result.avgCachedCall.toFixed(1)}ms`));
        }

        if (result.cacheHits !== undefined) {
          console.log(chalk.gray(`     缓存命中: ${result.cacheHits} 次`));
        }
      });
    });

    // Redis缓存命中统计
    const totalCacheOperations = this.cacheHitCount + this.cacheMissCount;
    const cacheHitRate = totalCacheOperations > 0 ? (this.cacheHitCount / totalCacheOperations * 100).toFixed(1) : 0;

    console.log(chalk.cyan(`\n📊 Redis缓存统计:`));
    console.log(chalk.green(`  ✅ 缓存命中: ${this.cacheHitCount} 次`));
    console.log(chalk.red(`  ❌ 缓存未命中: ${this.cacheMissCount} 次`));
    console.log(chalk.blue(`  📈 缓存命中率: ${cacheHitRate}%`));

    // 总体统计
    const totalTests = this.testResults.length;
    const improvedTests = this.testResults.filter(r => r.status === 'improved').length;
    const performanceTests = this.testResults.filter(r => r.improvement !== 'N/A');
    const avgImprovement = performanceTests.length > 0 ?
      performanceTests.reduce((sum, r) => sum + parseFloat(r.improvement), 0) / performanceTests.length : 0;

    console.log(chalk.cyan(`\n📈 总体统计:`));
    console.log(chalk.green(`  ✅ 测试总数: ${totalTests}`));
    console.log(chalk.green(`  ✅ 功能正常: ${improvedTests}/${totalTests} (${(improvedTests/totalTests*100).toFixed(1)}%)`));
    if (performanceTests.length > 0) {
      console.log(chalk.green(`  ✅ 平均性能提升: ${avgImprovement.toFixed(1)}%`));
    }

    // 验证结果
    const allFunctionsWork = improvedTests === totalTests;
    const goodCacheHitRate = parseFloat(cacheHitRate) >= 60; // 至少60%的缓存命中率

    if (allFunctionsWork && goodCacheHitRate) {
      console.log(chalk.green('\n🎉 所有缓存装饰器功能正常，Redis缓存命中率良好！'));
      console.log(chalk.green('   Spring Boot风格缓存装饰器系统工作完美！'));
    } else if (allFunctionsWork) {
      console.log(chalk.yellow('\n⚠️ 缓存装饰器功能正常，但缓存命中率偏低'));
    } else {
      console.log(chalk.red('\n❌ 部分缓存装饰器功能异常，需要检查'));
    }

    console.log(chalk.cyan(`\n📝 测试验证的功能:`));
    console.log(chalk.blue(`  ✅ @Cacheable 装饰器 - 自动缓存`));
    console.log(chalk.blue(`  ✅ @CachePut 装饰器 - 缓存更新`));
    console.log(chalk.blue(`  ✅ @CacheEvict 装饰器 - 缓存清除`));
    console.log(chalk.blue(`  ✅ #{paramName} 表达式解析`));
    console.log(chalk.blue(`  ✅ Redis 缓存命中/未命中检测`));
    console.log(chalk.blue(`  ✅ 控制器层装饰器应用`));
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log(chalk.cyan('🚀 启动缓存性能测试 - 优化版'));
    console.log(chalk.cyan('='.repeat(60)));
    console.log(chalk.gray('测试Spring Boot风格缓存装饰器的性能和Redis缓存命中情况'));

    try {
      await this.checkServiceAvailability();
      await this.testCacheablePerformance();
      await this.testCachePutPerformance();
      await this.testCacheEvictPerformance();
      this.generateReport();

    } catch (error) {
      console.error(chalk.red('❌ 测试执行失败:'), error.message);
      if (error.response) {
        console.error(chalk.red('响应状态:'), error.response.status);
        console.error(chalk.red('响应数据:'), error.response.data);
      }
      if (error.stack) {
        console.error(chalk.red('错误堆栈:'), error.stack);
      }
    }
  }
}

// 运行测试
const test = new CachePerformanceTest();
test.runAllTests();
