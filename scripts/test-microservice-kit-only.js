#!/usr/bin/env node

/**
 * 专门测试 microservice-kit 库的脚本
 * 只检查 microservice-kit 相关的文件
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧪 开始测试 microservice-kit 库...\n');

// 创建临时的 tsconfig 文件，只包含 microservice-kit
const tempTsConfig = {
  "extends": "./tsconfig.json",
  "include": [
    "libs/common/src/microservice-kit/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*spec.ts",
    "**/*test.ts"
  ]
};

const tempConfigPath = path.join(__dirname, '../tsconfig.microservice-kit.json');

try {
  // 写入临时配置文件
  fs.writeFileSync(tempConfigPath, JSON.stringify(tempTsConfig, null, 2));
  
  console.log('📋 测试: microservice-kit TypeScript 编译检查');
  console.log('📝 描述: 检查 microservice-kit 库的 TypeScript 编译');
  
  try {
    execSync(`npx tsc --noEmit --project tsconfig.microservice-kit.json`, {
      stdio: 'pipe',
      cwd: path.join(__dirname, '..')
    });
    console.log('✅ microservice-kit 编译检查通过');
    
    // 清理临时文件
    fs.unlinkSync(tempConfigPath);
    
    console.log('\n🎉 microservice-kit 库测试完全通过！');
    console.log('\n📋 下一步操作:');
    console.log('   1. 开始按迁移清单执行服务迁移');
    console.log('   2. 从通知服务开始试点迁移');
    console.log('   3. 逐步迁移其他服务');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ microservice-kit 编译检查失败:');
    console.error(error.stdout?.toString() || error.message);
    
    // 清理临时文件
    if (fs.existsSync(tempConfigPath)) {
      fs.unlinkSync(tempConfigPath);
    }
    
    process.exit(1);
  }
} catch (error) {
  console.error('❌ 测试执行失败:', error.message);
  
  // 清理临时文件
  if (fs.existsSync(tempConfigPath)) {
    fs.unlinkSync(tempConfigPath);
  }
  
  process.exit(1);
}
