# ===== 测试环境配置 =====
# 此文件定义测试环境专用的配置

# 环境标识
NODE_ENV=test

# 测试环境特性
DEBUG=false
LOG_LEVEL=warn
VERBOSE_LOGGING=false

# 限流配置（测试环境禁用）
RATE_LIMIT_ENABLED=false
RATE_LIMIT_GLOBAL_ENABLED=false
RATE_LIMIT_LOGIN_ENABLED=false
RATE_LIMIT_REGISTER_ENABLED=false
RATE_LIMIT_PASSWORD_RESET_ENABLED=false

# 全局限流配置（测试环境宽松）
RATE_LIMIT_GLOBAL_TTL=1
RATE_LIMIT_GLOBAL_LIMIT=999999
RATE_LIMIT_SKIP_SUCCESSFUL=true
RATE_LIMIT_SKIP_FAILED=true

# 登录限流配置（测试环境宽松）
RATE_LIMIT_LOGIN_TTL=1
RATE_LIMIT_LOGIN_LIMIT=999999
RATE_LIMIT_LOGIN_BLOCK_DURATION=1

# 注册限流配置（测试环境宽松）
RATE_LIMIT_REGISTER_TTL=1
RATE_LIMIT_REGISTER_LIMIT=999999

# 密码重置限流配置（测试环境宽松）
RATE_LIMIT_PASSWORD_RESET_TTL=1
RATE_LIMIT_PASSWORD_RESET_LIMIT=999999

# 账户保护配置（测试环境禁用）
ACCOUNT_PROTECTION_LOGIN_ENABLED=false
ACCOUNT_PROTECTION_MAX_ATTEMPTS=999999
ACCOUNT_PROTECTION_LOCKOUT_DURATION=1
ACCOUNT_PROTECTION_PROGRESSIVE_LOCKOUT=false
ACCOUNT_PROTECTION_CAPTCHA_THRESHOLD=999999
ACCOUNT_PROTECTION_IP_BASED_LOCKOUT=false
ACCOUNT_PROTECTION_DEVICE_BASED_LOCKOUT=false

# 安全配置（测试环境禁用）
ENABLE_IP_WHITELIST=false
ENABLE_SERVICE_AUTH=false
ALLOWED_IPS=
TRUSTED_PROXIES=

# JWT配置（测试环境简化）
JWT_SECRET=test-secret-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=1d
JWT_BLACKLIST_ENABLED=false

# 密码策略配置（测试环境简化）
PASSWORD_SALT_ROUNDS=10
PASSWORD_MIN_LENGTH=6
PASSWORD_MAX_LENGTH=128
PASSWORD_REQUIRE_UPPERCASE=false
PASSWORD_REQUIRE_LOWERCASE=false
PASSWORD_REQUIRE_NUMBERS=false
PASSWORD_REQUIRE_SPECIAL_CHARS=false
PASSWORD_HISTORY_COUNT=0
PASSWORD_MAX_AGE=999999

# 会话配置（测试环境宽松）
SESSION_MAX_CONCURRENT=10
SESSION_TIMEOUT=86400
SESSION_ABSOLUTE_TIMEOUT=604800
SESSION_RENEWAL_THRESHOLD=3600

# 测试工具
ENABLE_SWAGGER=false
ENABLE_DEVTOOLS=false
DISABLE_CORS=false
ALLOW_ALL_ORIGINS=true

# 监控和日志配置
SECURITY_LOG_LEVEL=warn
LOG_SUCCESSFUL_ACCESS=false
LOG_FAILED_ACCESS=false
ENABLE_SECURITY_METRICS=false

# 测试数据库配置
MONGODB_URI=mongodb://***************:27017/football_manager_test
REDIS_DB=2
