{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@views/*": ["./src/views/*"], "@stores/*": ["./src/stores/*"], "@services/*": ["./src/services/*"], "@utils/*": ["./src/utils/*"], "@types/*": ["./src/types/*"], "@styles/*": ["./src/styles/*"]}, "types": ["node", "vite/client"], "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "skipLibCheck": true}}