<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    @click="handleClick"
    v-bind="$attrs"
  >
    <!-- 加载图标 -->
    <LoadingOutlined v-if="loading" class="button-icon loading" />
    
    <!-- 前置图标 -->
    <component 
      v-else-if="prefixIcon" 
      :is="prefixIcon" 
      class="button-icon prefix" 
    />
    
    <!-- 按钮文本 -->
    <span class="button-text">
      <slot>{{ text }}</slot>
    </span>
    
    <!-- 后置图标 -->
    <component 
      v-if="suffixIcon && !loading" 
      :is="suffixIcon" 
      class="button-icon suffix" 
    />
    
    <!-- 热键提示 -->
    <span v-if="hotkey" class="button-hotkey">
      [{{ hotkey }}]
    </span>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { LoadingOutlined } from '@ant-design/icons-vue'

// Props
interface Props {
  // 按钮类型
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost'
  // 按钮大小
  size?: 'small' | 'medium' | 'large'
  // 按钮文本
  text?: string
  // 禁用状态
  disabled?: boolean
  // 加载状态
  loading?: boolean
  // 块级按钮
  block?: boolean
  // 前置图标
  prefixIcon?: any
  // 后置图标
  suffixIcon?: any
  // 热键
  hotkey?: string
  // 是否显示边框
  bordered?: boolean
  // 自定义样式类
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'medium',
  text: '',
  disabled: false,
  loading: false,
  block: false,
  bordered: true,
  customClass: ''
})

// Emits
const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

// 计算按钮样式类
const buttonClasses = computed(() => {
  return [
    'game-button',
    `game-button--${props.type}`,
    `game-button--${props.size}`,
    {
      'game-button--disabled': props.disabled,
      'game-button--loading': props.loading,
      'game-button--block': props.block,
      'game-button--borderless': !props.bordered
    },
    props.customClass
  ]
})

// 处理点击事件
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="less" scoped>
.game-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: @padding-sm @padding-base;
  border: 1px solid transparent;
  background-color: transparent;
  color: @text-color;
  font-family: @font-family-mono;
  font-size: @font-size-base;
  font-weight: 500;
  line-height: 1;
  cursor: pointer;
  border-radius: @border-radius-base;
  transition: all @transition-duration;
  text-decoration: none;
  user-select: none;
  white-space: nowrap;
  position: relative;
  
  // 基础悬停效果
  &:hover:not(.game-button--disabled):not(.game-button--loading) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  &:active:not(.game-button--disabled):not(.game-button--loading) {
    transform: translateY(0);
  }
  
  // 禁用状态
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      box-shadow: none;
    }
  }
  
  // 加载状态
  &--loading {
    cursor: default;
    
    .loading {
      animation: spin 1s linear infinite;
    }
  }
  
  // 块级按钮
  &--block {
    width: 100%;
  }
  
  // 无边框
  &--borderless {
    border: none !important;
  }
  
  // 按钮类型样式
  &--primary {
    background-color: @primary-color;
    border-color: @primary-color;
    color: @bg-color;
    
    &:hover:not(.game-button--disabled):not(.game-button--loading) {
      background-color: @primary-color-hover;
      border-color: @primary-color-hover;
    }
    
    &:active:not(.game-button--disabled):not(.game-button--loading) {
      background-color: @primary-color-active;
      border-color: @primary-color-active;
    }
  }
  
  &--secondary {
    border-color: @border-color;
    color: @text-color;
    
    &:hover:not(.game-button--disabled):not(.game-button--loading) {
      border-color: @primary-color;
      color: @primary-color;
    }
  }
  
  &--success {
    background-color: @success-color;
    border-color: @success-color;
    color: @bg-color;
    
    &:hover:not(.game-button--disabled):not(.game-button--loading) {
      background-color: lighten(@success-color, 10%);
      border-color: lighten(@success-color, 10%);
    }
  }
  
  &--warning {
    background-color: @warning-color;
    border-color: @warning-color;
    color: @bg-color;
    
    &:hover:not(.game-button--disabled):not(.game-button--loading) {
      background-color: lighten(@warning-color, 10%);
      border-color: lighten(@warning-color, 10%);
    }
  }
  
  &--danger {
    background-color: @error-color;
    border-color: @error-color;
    color: @bg-color;
    
    &:hover:not(.game-button--disabled):not(.game-button--loading) {
      background-color: lighten(@error-color, 10%);
      border-color: lighten(@error-color, 10%);
    }
  }
  
  &--ghost {
    background-color: transparent;
    border-color: @primary-color;
    color: @primary-color;
    
    &:hover:not(.game-button--disabled):not(.game-button--loading) {
      background-color: @primary-color;
      color: @bg-color;
    }
  }
  
  // 按钮大小
  &--small {
    padding: @padding-xs @padding-sm;
    font-size: @font-size-sm;
    
    .button-hotkey {
      font-size: @font-size-xs;
    }
  }
  
  &--medium {
    padding: @padding-sm @padding-base;
    font-size: @font-size-base;
  }
  
  &--large {
    padding: @padding-base @padding-lg;
    font-size: @font-size-lg;
    
    .button-hotkey {
      font-size: @font-size-sm;
    }
  }
}

.button-icon {
  font-size: 1em;
  
  &.loading {
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }
}

.button-text {
  flex: 1;
  text-align: center;
}

.button-hotkey {
  font-size: @font-size-xs;
  opacity: 0.7;
  margin-left: 4px;
}

// 响应式设计
@media (max-width: 768px) {
  .game-button {
    &--small {
      padding: @padding-xs;
      font-size: @font-size-xs;
    }
    
    &--medium {
      padding: @padding-xs @padding-sm;
      font-size: @font-size-sm;
    }
    
    &--large {
      padding: @padding-sm @padding-base;
      font-size: @font-size-base;
    }
    
    .button-hotkey {
      display: none; // 移动端隐藏热键提示
    }
  }
}
</style>
