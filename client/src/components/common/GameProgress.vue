<template>
  <div :class="['game-progress', `progress-${size}`, customClass]">
    <!-- 进度条标签 -->
    <div v-if="showLabel" class="progress-label">
      <div class="label-left">
        <component v-if="icon" :is="icon" class="label-icon" />
        <span v-if="label" class="label-text">{{ label }}</span>
      </div>
      <div class="label-right">
        <span class="label-value">
          {{ formatValue(current) }} / {{ formatValue(total) }}
        </span>
        <span v-if="showPercent" class="label-percent">
          ({{ Math.round(percentage) }}%)
        </span>
      </div>
    </div>

    <!-- 进度条主体 -->
    <div class="progress-bar" :style="{ height: barHeight }">
      <div 
        class="progress-fill"
        :class="[`fill-${type}`, { 'fill-animated': animated }]"
        :style="{ width: `${percentage}%` }"
      >
        <!-- 进度条内文字 -->
        <span v-if="showInnerText" class="progress-inner-text">
          {{ innerText || `${Math.round(percentage)}%` }}
        </span>
      </div>

      <!-- 分段标记 -->
      <div v-if="segments && segments.length > 0" class="progress-segments">
        <div
          v-for="(segment, index) in segments"
          :key="index"
          class="segment-mark"
          :style="{ left: `${(segment.value / total) * 100}%` }"
          :title="segment.label"
        />
      </div>
    </div>

    <!-- 额外信息 -->
    <div v-if="$slots.extra || extraText" class="progress-extra">
      <slot name="extra">
        <span class="extra-text">{{ extraText }}</span>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface ProgressSegment {
  value: number
  label: string
}

interface Props {
  current: number
  total: number
  label?: string
  icon?: any
  type?: 'default' | 'primary' | 'success' | 'warning' | 'error'
  size?: 'small' | 'default' | 'large'
  showLabel?: boolean
  showPercent?: boolean
  showInnerText?: boolean
  innerText?: string
  animated?: boolean
  customClass?: string
  extraText?: string
  segments?: ProgressSegment[]
  formatter?: (value: number) => string
}

const props = withDefaults(defineProps<Props>(), {
  current: 0,
  total: 100,
  type: 'primary',
  size: 'default',
  showLabel: true,
  showPercent: true,
  showInnerText: false,
  animated: true,
  customClass: ''
})

const percentage = computed(() => {
  if (props.total <= 0) return 0
  return Math.min((props.current / props.total) * 100, 100)
})

const barHeight = computed(() => {
  switch (props.size) {
    case 'small': return '6px'
    case 'large': return '12px'
    default: return '8px'
  }
})

const formatValue = (value: number): string => {
  if (props.formatter) {
    return props.formatter(value)
  }
  
  // 自动格式化大数字
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`
  }
  
  return value.toString()
}
</script>

<style lang="less" scoped>
.game-progress {
  width: 100%;

  &.progress-small {
    .progress-label {
      margin-bottom: @margin-xs;
      font-size: @font-size-sm;
    }
  }

  &.progress-default {
    .progress-label {
      margin-bottom: @margin-xs;
      font-size: @font-size-base;
    }
  }

  &.progress-large {
    .progress-label {
      margin-bottom: @margin-sm;
      font-size: @font-size-lg;
    }
  }
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .label-left {
    display: flex;
    align-items: center;
    gap: @margin-xs;

    .label-icon {
      font-size: @font-size-sm;
      color: @primary-color;
    }

    .label-text {
      color: @text-color;
      font-weight: 500;
    }
  }

  .label-right {
    display: flex;
    align-items: center;
    gap: @margin-xs;

    .label-value {
      color: @text-color;
      font-weight: bold;
    }

    .label-percent {
      color: @text-color-secondary;
      font-size: @font-size-sm;
    }
  }
}

.progress-bar {
  position: relative;
  background-color: @border-color;
  border-radius: 4px;
  overflow: hidden;

  .progress-fill {
    height: 100%;
    border-radius: 4px;
    transition: width @transition-duration ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    &.fill-default {
      background-color: @text-color-secondary;
    }

    &.fill-primary {
      background-color: @primary-color;
    }

    &.fill-success {
      background-color: @success-color;
    }

    &.fill-warning {
      background-color: @warning-color;
    }

    &.fill-error {
      background-color: @error-color;
    }

    &.fill-animated {
      background-image: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
      );
      background-size: 20px 20px;
      animation: progress-stripes 1s linear infinite;
    }

    .progress-inner-text {
      color: white;
      font-size: @font-size-xs;
      font-weight: bold;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
  }

  .progress-segments {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .segment-mark {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 2px;
      background-color: rgba(255, 255, 255, 0.8);
      box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
    }
  }
}

.progress-extra {
  margin-top: @margin-xs;

  .extra-text {
    color: @text-color-secondary;
    font-size: @font-size-sm;
  }
}

@keyframes progress-stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 0;
  }
}

// 特殊用途的进度条样式
.game-progress.exp-bar {
  .progress-fill.fill-primary {
    background: linear-gradient(90deg, #52c41a 0%, #73d13d 50%, #95de64 100%);
  }
}

.game-progress.health-bar {
  .progress-fill.fill-success {
    background: linear-gradient(90deg, #f5222d 0%, #ff4d4f 50%, #ff7875 100%);
  }
}

.game-progress.energy-bar {
  .progress-fill.fill-warning {
    background: linear-gradient(90deg, #1890ff 0%, #40a9ff 50%, #69c0ff 100%);
  }
}

.game-progress.training-bar {
  .progress-fill.fill-primary {
    background: linear-gradient(90deg, #722ed1 0%, #9254de 50%, #b37feb 100%);
  }
}
</style>
