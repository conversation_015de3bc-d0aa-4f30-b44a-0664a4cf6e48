<template>
  <div class="server-selection-view">
    <div class="server-selection-container">
      <div class="selection-header">
        <h1 class="selection-title">选择服务器</h1>
        <p class="selection-subtitle">选择一个服务器开始您的足球游戏之旅</p>
      </div>

      <div class="servers-list" v-loading="isLoading">
        <div
          v-for="server in servers"
          :key="server.serverId"
          class="server-card"
          :class="{
            'selected': selectedServerId === server.serverId,
            'recommended': server.isRecommended,
            'offline': server.status === 'offline',
            'maintenance': server.status === 'maintenance'
          }"
          @click="selectServer(server)"
        >
          <div class="server-header">
            <div class="server-name">{{ server.name }}</div>
            <div class="server-status" :class="server.status">
              {{ getStatusText(server.status) }}
            </div>
          </div>

          <div class="server-info">
            <div class="info-item">
              <span class="info-label">区域:</span>
              <span class="info-value">{{ server.region }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">在线人数:</span>
              <span class="info-value">{{ server.playerCount }}/{{ server.maxPlayers }}</span>
            </div>
            <div v-if="server.openTime" class="info-item">
              <span class="info-label">开服时间:</span>
              <span class="info-value">{{ formatDate(server.openTime) }}</span>
            </div>
          </div>

          <div v-if="server.description" class="server-description">
            {{ server.description }}
          </div>

          <div v-if="server.isRecommended" class="recommended-badge">
            推荐
          </div>

          <div class="server-load">
            <div class="load-bar">
              <div 
                class="load-fill" 
                :style="{ width: getLoadPercentage(server) + '%' }"
                :class="getLoadClass(server)"
              ></div>
            </div>
            <span class="load-text">{{ getLoadText(server) }}</span>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!isLoading && servers.length === 0" class="empty-servers">
          <div class="empty-icon">🌐</div>
          <div class="empty-text">暂无可用服务器</div>
          <a-button @click="refreshServers">刷新</a-button>
        </div>
      </div>

      <div class="selection-actions">
        <a-button @click="refreshServers" :loading="isLoading">
          刷新服务器列表
        </a-button>
        <a-button
          type="primary"
          :disabled="!selectedServerId"
          @click="confirmSelection"
          :loading="isConfirming"
        >
          确认选择
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useGlobalStore } from '@/stores/global'
import type { ServerInfo } from '@/types'

const router = useRouter()
const authStore = useAuthStore()
const globalStore = useGlobalStore()

// 状态
const isLoading = ref(false)
const isConfirming = ref(false)
const servers = ref<ServerInfo[]>([])
const selectedServerId = ref<string>('')

// 方法
const refreshServers = async () => {
  try {
    isLoading.value = true
    // 模拟获取服务器列表
    servers.value = []
    
    // 自动选择推荐服务器
    const recommendedServer = servers.value.find(s => s.isRecommended && s.status === 'online')
    if (recommendedServer && !selectedServerId.value) {
      selectedServerId.value = recommendedServer.serverId
    }
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '获取服务器列表失败',
      message: error.message
    })
  } finally {
    isLoading.value = false
  }
}

const selectServer = (server: ServerInfo) => {
  if (server.status === 'offline' || server.status === 'maintenance') {
    globalStore.addNotification({
      type: 'warning',
      title: '服务器不可用',
      message: `服务器 ${server.name} 当前${server.status === 'offline' ? '离线' : '维护中'}`
    })
    return
  }
  
  selectedServerId.value = server.serverId
}

const confirmSelection = async () => {
  if (!selectedServerId.value) return
  
  try {
    isConfirming.value = true
    
    const selectedServer = servers.value.find(s => s.serverId === selectedServerId.value)
    if (selectedServer) {
      await authStore.selectServer(selectedServer)
      
      globalStore.addNotification({
        type: 'success',
        title: '服务器选择成功',
        message: `已选择服务器: ${selectedServer.name}`
      })
      
      router.push('/character-selection')
    }
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '选择服务器失败',
      message: error.message
    })
  } finally {
    isConfirming.value = false
  }
}

// 工具函数
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    online: '在线',
    offline: '离线',
    maintenance: '维护中'
  }
  return statusMap[status] || status
}

const getLoadPercentage = (server: ServerInfo): number => {
  return Math.round((server.playerCount / server.maxPlayers) * 100)
}

const getLoadClass = (server: ServerInfo): string => {
  const percentage = getLoadPercentage(server)
  if (percentage >= 90) return 'high'
  if (percentage >= 70) return 'medium'
  return 'low'
}

const getLoadText = (server: ServerInfo): string => {
  const percentage = getLoadPercentage(server)
  if (percentage >= 90) return '拥挤'
  if (percentage >= 70) return '繁忙'
  if (percentage >= 30) return '适中'
  return '流畅'
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString()
}

// 生命周期
onMounted(() => {
  refreshServers()
})
</script>

<style lang="less" scoped>
.server-selection-view {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, @bg-color 0%, #002140 100%);
  padding: @padding-lg;
}

.server-selection-container {
  width: 100%;
  max-width: 800px;
  background-color: @card-bg;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @padding-xl;
  box-shadow: @box-shadow-elevated;
}

.selection-header {
  text-align: center;
  margin-bottom: @margin-xl;

  .selection-title {
    font-size: @font-size-xxl;
    color: @text-color;
    margin: 0 0 @margin-sm 0;
    font-family: @font-family-mono;
  }

  .selection-subtitle {
    color: @text-color-secondary;
    margin: 0;
  }
}

.servers-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: @margin-base;
  margin-bottom: @margin-xl;
  min-height: 200px;

  .server-card {
    position: relative;
    background-color: rgba(0, 0, 0, 0.2);
    border: 2px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    cursor: pointer;
    transition: all @transition-duration;

    &:hover {
      border-color: @primary-color;
      transform: translateY(-2px);
    }

    &.selected {
      border-color: @primary-color;
      background-color: rgba(82, 196, 26, 0.1);
    }

    &.recommended {
      border-color: @warning-color;
    }

    &.offline,
    &.maintenance {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        transform: none;
      }
    }

    .server-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: @margin-sm;

      .server-name {
        font-size: @font-size-lg;
        font-weight: bold;
        color: @text-color;
      }

      .server-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: @font-size-xs;
        font-weight: bold;

        &.online {
          background-color: @success-color;
          color: white;
        }

        &.offline {
          background-color: @error-color;
          color: white;
        }

        &.maintenance {
          background-color: @warning-color;
          color: white;
        }
      }
    }

    .server-info {
      margin-bottom: @margin-sm;

      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: @margin-xs;

        .info-label {
          color: @text-color-secondary;
        }

        .info-value {
          color: @text-color;
          font-weight: bold;
        }
      }
    }

    .server-description {
      color: @text-color-secondary;
      font-size: @font-size-sm;
      margin-bottom: @margin-sm;
    }

    .recommended-badge {
      position: absolute;
      top: -1px;
      right: -1px;
      background-color: @warning-color;
      color: white;
      padding: 2px 8px;
      border-radius: 0 @border-radius-base 0 8px;
      font-size: @font-size-xs;
      font-weight: bold;
    }

    .server-load {
      display: flex;
      align-items: center;
      gap: @margin-sm;

      .load-bar {
        flex: 1;
        height: 6px;
        background-color: @border-color;
        border-radius: 3px;
        overflow: hidden;

        .load-fill {
          height: 100%;
          transition: width @transition-duration;

          &.low {
            background-color: @success-color;
          }

          &.medium {
            background-color: @warning-color;
          }

          &.high {
            background-color: @error-color;
          }
        }
      }

      .load-text {
        font-size: @font-size-xs;
        color: @text-color-secondary;
        min-width: 40px;
      }
    }
  }

  .empty-servers {
    grid-column: 1 / -1;
    text-align: center;
    padding: @padding-xl;

    .empty-icon {
      font-size: 3rem;
      margin-bottom: @margin-base;
    }

    .empty-text {
      color: @text-color-secondary;
      margin-bottom: @margin-base;
    }
  }
}

.selection-actions {
  display: flex;
  justify-content: space-between;
  gap: @margin-base;

  .ant-btn {
    flex: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .server-selection-view {
    padding: @padding-base;
  }

  .server-selection-container {
    padding: @padding-lg;
  }

  .servers-list {
    grid-template-columns: 1fr;
  }

  .selection-actions {
    flex-direction: column;
  }
}
</style>
