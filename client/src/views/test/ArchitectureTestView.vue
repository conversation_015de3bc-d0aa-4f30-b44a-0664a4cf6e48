<template>
  <div class="architecture-test-view">
    <div class="page-header">
      <h2 class="page-title">客户端架构测试</h2>
      <p class="page-subtitle">测试与服务端的完整通信流程</p>
    </div>

    <div class="test-sections">
      <!-- 认证流程测试 -->
      <div class="test-section">
        <h3 class="section-title">🔐 认证流程测试</h3>
        <div class="test-controls">
          <a-space>
            <a-button @click="testLogin" :loading="authLoading">
              测试登录
            </a-button>
            <a-button @click="testCharacterAuth" :loading="authLoading">
              测试角色认证
            </a-button>
            <a-button @click="testLogout" :loading="authLoading">
              测试登出
            </a-button>
          </a-space>
        </div>
        <div class="test-results">
          <div class="auth-status">
            <span class="status-label">认证状态:</span>
            <span :class="['status-value', authStatus.type]">
              {{ authStatus.message }}
            </span>
          </div>
          <div v-if="currentUser" class="user-info">
            <span class="info-label">当前用户:</span>
            <span class="info-value">{{ currentUser.username }}</span>
          </div>
          <div v-if="currentCharacter" class="character-info">
            <span class="info-label">当前角色:</span>
            <span class="info-value">{{ currentCharacter.name }}</span>
          </div>
        </div>
      </div>

      <!-- WebSocket连接测试 -->
      <div class="test-section">
        <h3 class="section-title">🔌 WebSocket连接测试</h3>
        <div class="test-controls">
          <a-space>
            <a-button @click="testWebSocketConnect" :loading="wsLoading">
              连接WebSocket
            </a-button>
            <a-button @click="testWebSocketDisconnect" :loading="wsLoading">
              断开连接
            </a-button>
            <a-button @click="testPing" :loading="wsLoading">
              测试Ping
            </a-button>
          </a-space>
        </div>
        <div class="test-results">
          <div class="ws-status">
            <span class="status-label">连接状态:</span>
            <span :class="['status-value', wsStatus.type]">
              {{ wsStatus.message }}
            </span>
          </div>
          <div class="ws-latency">
            <span class="info-label">延迟:</span>
            <span class="info-value">{{ latency }}ms</span>
          </div>
        </div>
      </div>

      <!-- 游戏接口测试 -->
      <div class="test-section">
        <h3 class="section-title">🎮 游戏接口测试</h3>
        <div class="test-controls">
          <a-space wrap>
            <a-button @click="testGetCharacterInfo" :loading="gameLoading">
              获取角色信息
            </a-button>
            <a-button @click="testGetHeroList" :loading="gameLoading">
              获取英雄列表
            </a-button>
            <a-button @click="testGetFormations" :loading="gameLoading">
              获取阵型配置
            </a-button>
            <a-button @click="testGetMatchList" :loading="gameLoading">
              获取比赛列表
            </a-button>
          </a-space>
        </div>
        <div class="test-results">
          <div v-if="gameTestResults.length > 0" class="game-results">
            <div
              v-for="(result, index) in gameTestResults"
              :key="index"
              class="result-item"
              :class="result.success ? 'success' : 'error'"
            >
              <div class="result-header">
                <span class="result-api">{{ result.api }}</span>
                <span class="result-status">{{ result.success ? '✅' : '❌' }}</span>
              </div>
              <div class="result-message">{{ result.message }}</div>
              <div v-if="result.data" class="result-data">
                <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误处理测试 -->
      <div class="test-section">
        <h3 class="section-title">⚠️ 错误处理测试</h3>
        <div class="test-controls">
          <a-space>
            <a-button @click="testInvalidToken" :loading="errorLoading">
              测试无效Token
            </a-button>
            <a-button @click="testNetworkError" :loading="errorLoading">
              测试网络错误
            </a-button>
            <a-button @click="testServerError" :loading="errorLoading">
              测试服务器错误
            </a-button>
          </a-space>
        </div>
        <div class="test-results">
          <div v-if="errorTestResults.length > 0" class="error-results">
            <div
              v-for="(result, index) in errorTestResults"
              :key="index"
              class="result-item"
              :class="result.handled ? 'success' : 'error'"
            >
              <div class="result-header">
                <span class="result-test">{{ result.test }}</span>
                <span class="result-status">{{ result.handled ? '✅' : '❌' }}</span>
              </div>
              <div class="result-message">{{ result.message }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 性能测试 -->
      <div class="test-section">
        <h3 class="section-title">⚡ 性能测试</h3>
        <div class="test-controls">
          <a-space>
            <a-button @click="testConcurrentRequests" :loading="perfLoading">
              并发请求测试
            </a-button>
            <a-button @click="testLargeDataTransfer" :loading="perfLoading">
              大数据传输测试
            </a-button>
            <a-button @click="testReconnection" :loading="perfLoading">
              重连测试
            </a-button>
          </a-space>
        </div>
        <div class="test-results">
          <div v-if="perfTestResults.length > 0" class="perf-results">
            <div
              v-for="(result, index) in perfTestResults"
              :key="index"
              class="result-item"
            >
              <div class="result-header">
                <span class="result-test">{{ result.test }}</span>
                <span class="result-time">{{ result.time }}ms</span>
              </div>
              <div class="result-message">{{ result.message }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时日志 -->
    <div class="log-section">
      <h3 class="section-title">📋 实时日志</h3>
      <div class="log-container">
        <div
          v-for="(log, index) in logs"
          :key="index"
          class="log-item"
          :class="log.level"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <div class="log-controls">
        <a-button @click="clearLogs" size="small">清空日志</a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { characterAuthService } from '@/services/character-auth.service'
import { gameService } from '@/services/game.service'
import { wsService } from '@/services/websocket'

// 状态定义
const authLoading = ref(false)
const wsLoading = ref(false)
const gameLoading = ref(false)
const errorLoading = ref(false)
const perfLoading = ref(false)

const authStatus = ref({ type: 'info', message: '未认证' })
const wsStatus = ref({ type: 'info', message: '未连接' })
const latency = ref(0)

const currentUser = ref<{username: string} | null>(null)
const currentCharacter = ref<{name: string} | null>(null)

const gameTestResults = ref<any[]>([])
const errorTestResults = ref<any[]>([])
const perfTestResults = ref<any[]>([])
const logs = ref<any[]>([])

// 工具方法
const addLog = (level: string, message: string) => {
  logs.value.unshift({
    level,
    message,
    timestamp: Date.now()
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleTimeString()
}

const clearLogs = () => {
  logs.value = []
}

// 认证测试
const testLogin = async () => {
  try {
    authLoading.value = true
    addLog('info', '开始测试登录...')
    
    const response = await characterAuthService.login({
      username: 'test_user',
      password: 'test_password'
    })
    
    if (response.success) {
      authStatus.value = { type: 'success', message: '登录成功' }
      currentUser.value = response.data?.user
      addLog('success', '登录测试成功')
    } else {
      authStatus.value = { type: 'error', message: '登录失败' }
      addLog('error', `登录测试失败: ${response.error?.message}`)
    }
  } catch (error: any) {
    authStatus.value = { type: 'error', message: error.message }
    addLog('error', `登录测试异常: ${error.message}`)
  } finally {
    authLoading.value = false
  }
}

const testCharacterAuth = async () => {
  try {
    authLoading.value = true
    addLog('info', '开始测试角色认证...')
    
    const result = await characterAuthService.selectAndLoginCharacter('test_character_id', 'test_server_id')
    
    currentCharacter.value = result.character as {name: string}
    authStatus.value = { type: 'success', message: '角色认证成功' }
    addLog('success', '角色认证测试成功')
  } catch (error: any) {
    authStatus.value = { type: 'error', message: error.message }
    addLog('error', `角色认证测试失败: ${error.message}`)
  } finally {
    authLoading.value = false
  }
}

const testLogout = async () => {
  try {
    authLoading.value = true
    addLog('info', '开始测试登出...')
    
    await characterAuthService.logout()
    
    authStatus.value = { type: 'info', message: '已登出' }
    currentUser.value = null
    currentCharacter.value = null
    addLog('success', '登出测试成功')
  } catch (error: any) {
    addLog('error', `登出测试失败: ${error.message}`)
  } finally {
    authLoading.value = false
  }
}

// WebSocket测试
const testWebSocketConnect = async () => {
  try {
    wsLoading.value = true
    addLog('info', '开始连接WebSocket...')
    
    await wsService.connect()
    wsStatus.value = { type: 'success', message: '已连接' }
    addLog('success', 'WebSocket连接成功')
  } catch (error: any) {
    wsStatus.value = { type: 'error', message: error.message }
    addLog('error', `WebSocket连接失败: ${error.message}`)
  } finally {
    wsLoading.value = false
  }
}

const testWebSocketDisconnect = () => {
  wsLoading.value = true
  addLog('info', '断开WebSocket连接...')
  
  wsService.disconnect()
  wsStatus.value = { type: 'info', message: '已断开' }
  addLog('info', 'WebSocket已断开')
  wsLoading.value = false
}

const testPing = async () => {
  try {
    wsLoading.value = true
    const startTime = Date.now()
    addLog('info', '发送Ping测试...')
    
    // 这里应该发送一个ping消息
    // const response = await wsService.sendMessage('ping', {})
    
    latency.value = Date.now() - startTime
    addLog('success', `Ping测试完成，延迟: ${latency.value}ms`)
  } catch (error: any) {
    addLog('error', `Ping测试失败: ${error.message}`)
  } finally {
    wsLoading.value = false
  }
}

// 游戏接口测试
const testGetCharacterInfo = async () => {
  await testGameAPI('character.getInfo', '获取角色信息', () => gameService.getCharacterInfo())
}

const testGetHeroList = async () => {
  await testGameAPI('hero.getList', '获取英雄列表', () => gameService.getHeroList())
}

const testGetFormations = async () => {
  await testGameAPI('formation.getFormations', '获取阵型配置', () => gameService.getFormations())
}

const testGetMatchList = async () => {
  await testGameAPI('match.getList', '获取比赛列表', () => gameService.getMatchList())
}

const testGameAPI = async (api: string, description: string, apiCall: () => Promise<any>) => {
  try {
    gameLoading.value = true
    addLog('info', `开始测试 ${description}...`)
    
    const response = await apiCall()
    
    const result = {
      api,
      success: response.code === 0,
      message: response.code === 0 ? `${description}成功` : response.message,
      data: response.data
    }
    
    gameTestResults.value.unshift(result)
    addLog(result.success ? 'success' : 'error', result.message)
  } catch (error: any) {
    const result = {
      api,
      success: false,
      message: `${description}失败: ${error.message}`,
      data: null
    }
    
    gameTestResults.value.unshift(result)
    addLog('error', result.message)
  } finally {
    gameLoading.value = false
  }
}

// 错误处理测试
const testInvalidToken = async () => {
  // 实现无效Token测试
  addLog('info', '测试无效Token处理...')
  // TODO: 实现具体测试逻辑
}

const testNetworkError = async () => {
  // 实现网络错误测试
  addLog('info', '测试网络错误处理...')
  // TODO: 实现具体测试逻辑
}

const testServerError = async () => {
  // 实现服务器错误测试
  addLog('info', '测试服务器错误处理...')
  // TODO: 实现具体测试逻辑
}

// 性能测试
const testConcurrentRequests = async () => {
  // 实现并发请求测试
  addLog('info', '开始并发请求测试...')
  // TODO: 实现具体测试逻辑
}

const testLargeDataTransfer = async () => {
  // 实现大数据传输测试
  addLog('info', '开始大数据传输测试...')
  // TODO: 实现具体测试逻辑
}

const testReconnection = async () => {
  // 实现重连测试
  addLog('info', '开始重连测试...')
  // TODO: 实现具体测试逻辑
}

// 生命周期
onMounted(() => {
  addLog('info', '架构测试页面已加载')
  
  // 检查当前认证状态
  if (characterAuthService.isAuthenticated()) {
    authStatus.value = { type: 'success', message: '已认证' }
    currentUser.value = characterAuthService.getCurrentUser()
    currentCharacter.value = characterAuthService.getCurrentCharacter() as {name: string} | null
  }
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style lang="less" scoped>
.architecture-test-view {
  padding: @padding-lg;
  max-width: 1200px;
  margin: 0 auto;
  
  .page-header {
    text-align: center;
    margin-bottom: @margin-xl;
    
    .page-title {
      font-size: @font-size-xxl;
      color: @text-color;
      margin: 0 0 @margin-sm 0;
    }
    
    .page-subtitle {
      color: @text-color-secondary;
      margin: 0;
    }
  }
  
  .test-sections {
    display: grid;
    gap: @margin-lg;
    margin-bottom: @margin-xl;
  }
  
  .test-section {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-lg;
    
    .section-title {
      color: @text-color;
      margin: 0 0 @margin-base 0;
      font-size: @font-size-lg;
    }
    
    .test-controls {
      margin-bottom: @margin-base;
    }
    
    .test-results {
      .status-value {
        font-weight: bold;
        
        &.success { color: @success-color; }
        &.error { color: @error-color; }
        &.info { color: @info-color; }
      }
      
      .result-item {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: @border-radius-base;
        padding: @padding-sm;
        margin-bottom: @margin-sm;
        
        &.success {
          border-left: 4px solid @success-color;
        }
        
        &.error {
          border-left: 4px solid @error-color;
        }
        
        .result-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: @margin-xs;
          
          .result-api,
          .result-test {
            font-weight: bold;
            color: @text-color;
          }
        }
        
        .result-message {
          color: @text-color-secondary;
          margin-bottom: @margin-xs;
        }
        
        .result-data {
          background-color: rgba(0, 0, 0, 0.3);
          border-radius: @border-radius-base;
          padding: @padding-sm;
          
          pre {
            margin: 0;
            font-size: @font-size-xs;
            color: @text-color-secondary;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }
      }
    }
  }
  
  .log-section {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-lg;
    
    .section-title {
      color: @text-color;
      margin: 0 0 @margin-base 0;
      font-size: @font-size-lg;
    }
    
    .log-container {
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: @border-radius-base;
      padding: @padding-sm;
      height: 300px;
      overflow-y: auto;
      font-family: @font-family-mono;
      
      .log-item {
        display: flex;
        gap: @margin-sm;
        margin-bottom: @margin-xs;
        font-size: @font-size-sm;
        
        .log-time {
          color: @text-color-secondary;
          min-width: 80px;
        }
        
        .log-level {
          min-width: 60px;
          font-weight: bold;
        }
        
        .log-message {
          flex: 1;
          color: @text-color;
        }
        
        &.info .log-level { color: @info-color; }
        &.success .log-level { color: @success-color; }
        &.error .log-level { color: @error-color; }
      }
    }
    
    .log-controls {
      margin-top: @margin-base;
      text-align: right;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .architecture-test-view {
    padding: @padding-base;
    
    .test-controls {
      .ant-space {
        width: 100%;
        
        .ant-btn {
          width: 100%;
        }
      }
    }
  }
}
</style>
