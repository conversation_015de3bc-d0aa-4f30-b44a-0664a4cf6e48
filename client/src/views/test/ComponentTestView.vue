<template>
  <div class="component-test-view">
    <h1>组件测试页面</h1>
    
    <!-- 测试MarketPlace组件 -->
    <div class="test-section">
      <h2>MarketPlace组件测试</h2>
      <MarketPlace />
    </div>
    
    <!-- 测试MatchSimulator组件 -->
    <div class="test-section">
      <h2>MatchSimulator组件测试</h2>
      <MatchSimulator :match="testMatch" />
    </div>
    
    <!-- 测试TrainingCenter组件 -->
    <div class="test-section">
      <h2>TrainingCenter组件测试</h2>
      <TrainingCenter />
    </div>
    
    <!-- 测试FormationField组件 -->
    <div class="test-section">
      <h2>FormationField组件测试</h2>
      <FormationField 
        :formation="testFormation as Formation"
        :heroes="testHeroes"
        :interactive="true"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MarketPlace from '@/components/game/MarketPlace.vue'
import MatchSimulator from '@/components/game/MatchSimulator.vue'
import TrainingCenter from '@/components/game/TrainingCenter.vue'
import FormationField from '@/components/game/FormationField.vue'
import type { Match, Formation, Hero } from '@/types'

// 测试数据
const testMatch = ref<Match>({
  id: 'test-match-1',
  homeTeam: {
    id: 'team-1',
    name: '测试主队',
    rating: 85
  },
  awayTeam: {
    id: 'team-2', 
    name: '测试客队',
    rating: 82
  },
  homeScore: 2,
  awayScore: 1,
  currentTime: 75,
  status: 'playing',
  events: [],
  stats: {
    homeShots: 12,
    awayShots: 8,
    homeShotsOnTarget: 6,
    awayShotsOnTarget: 4,
    homePossession: 58,
    awayPossession: 42,
    homeCorners: 5,
    awayCorners: 3,
    homeFouls: 8,
    awayFouls: 12
  }
})

const testFormation = ref({
  id: 'test-formation-1',
  name: '4-3-3',
  formation: '4-3-3',
  description: '测试阵型',
  positions: [
    { id: 'gk', position: 'GK', x: 10, y: 50, heroId: null, role: 'goalkeeper' },
    { id: 'lb', position: 'DEF', x: 25, y: 20, heroId: null, role: 'defender' },
    { id: 'cb', position: 'DEF', x: 25, y: 50, heroId: null, role: 'defender' },
    { id: 'rb', position: 'DEF', x: 25, y: 80, heroId: null, role: 'defender' }
  ],
  tactics: {
    attacking: 70,
    defending: 60,
    pressing: 65,
    tempo: 70
  },
  isActive: true
})

const testHeroes = ref<Hero[]>([
  {
    heroId: 'hero-1',
    characterId: 'test-character',
    serverId: 'test-server',
    resId: 1001,
    name: '测试球员1',
    position: 'GK',
    quality: 3,
    level: 10,
    exp: 1500,
    experience: 1500,
    attributes: {
      pace: 65,
      shooting: 30,
      passing: 70,
      dribbling: 45,
      defending: 85,
      physical: 80
    }
  }
])
</script>

<style lang="less" scoped>
.component-test-view {
  padding: 20px;
  
  .test-section {
    margin-bottom: 40px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 20px;
    
    h2 {
      color: #333;
      margin-bottom: 20px;
      border-bottom: 1px solid #e8e8e8;
      padding-bottom: 10px;
    }
  }
}
</style>
