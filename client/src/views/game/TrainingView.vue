<template>
  <div class="training-view">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">训练中心</h2>
        <div class="training-info">
          <span class="info-item">
            今日训练次数: <strong>{{ todayTrainingCount }}</strong>
          </span>
          <span class="info-item">
            剩余体力: <strong>{{ currentEnergy }}/{{ maxEnergy }}</strong>
          </span>
        </div>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button @click="refreshTrainingData">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 训练类型选择 -->
    <div class="training-types">
      <h3 class="section-title">训练类型</h3>
      <div class="types-grid">
        <div
          v-for="type in trainingTypes"
          :key="type.id"
          class="training-type-card"
          :class="{ 'selected': selectedTrainingType?.id === type.id }"
          @click="selectTrainingType(type)"
        >
          <div class="type-icon">{{ type.icon }}</div>
          <div class="type-name">{{ type.name }}</div>
          <div class="type-description">{{ type.description }}</div>
          <div class="type-cost">
            <span class="cost-item">
              💰 {{ type.coinCost }}
            </span>
            <span class="cost-item">
              ⚡ {{ type.energyCost }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 球员选择 -->
    <div v-if="selectedTrainingType" class="hero-selection">
      <h3 class="section-title">选择训练球员</h3>
      <div class="heroes-grid">
        <div
          v-for="hero in availableHeroes"
          :key="hero.heroId"
          class="hero-card"
          :class="{ 
            'selected': selectedHeroes.includes(hero.heroId),
            'disabled': !canTrainHero(hero)
          }"
          @click="toggleHeroSelection(hero)"
        >
          <div class="hero-avatar">
            <span class="hero-symbol">{{ getHeroSymbol(hero.position) }}</span>
          </div>
          <div class="hero-info">
            <div class="hero-name">{{ hero.name }}</div>
            <div class="hero-position">{{ hero.position }}</div>
            <div class="hero-level">Lv.{{ hero.level }}</div>
            <div class="hero-overall">{{ hero.overall }}</div>
          </div>
          <div v-if="hero.training?.trainingCooldown" class="cooldown-overlay">
            <div class="cooldown-text">冷却中</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 训练配置 -->
    <div v-if="selectedTrainingType && selectedHeroes.length > 0" class="training-config">
      <h3 class="section-title">训练配置</h3>
      <div class="config-panel">
        <div class="config-item">
          <label>训练次数:</label>
          <a-input-number
            v-model:value="trainingCount"
            :min="1"
            :max="10"
            style="width: 120px"
          />
        </div>
        
        <div class="config-item">
          <label>使用道具:</label>
          <a-select
            v-model:value="selectedItems"
            mode="multiple"
            placeholder="选择训练道具"
            style="width: 300px"
            :options="trainingItems"
          />
        </div>
        
        <div class="config-summary">
          <div class="summary-item">
            <span class="label">总消耗:</span>
            <span class="value">
              💰 {{ totalCoinCost }} | ⚡ {{ totalEnergyCost }}
            </span>
          </div>
          <div class="summary-item">
            <span class="label">预期效果:</span>
            <span class="value">{{ getExpectedEffect() }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 训练按钮 -->
    <div v-if="selectedTrainingType && selectedHeroes.length > 0" class="training-actions">
      <a-button
        type="primary"
        size="large"
        :loading="isTraining"
        :disabled="!canStartTraining"
        @click="startTraining"
      >
        开始训练 ({{ selectedHeroes.length }}名球员)
      </a-button>
    </div>

    <!-- 训练历史 -->
    <div class="training-history">
      <h3 class="section-title">训练记录</h3>
      <a-table
        :columns="historyColumns"
        :data-source="trainingHistory"
        :pagination="{ pageSize: 10 }"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'heroName'">
            <span class="hero-name">{{ record.heroName }}</span>
          </template>
          
          <template v-else-if="column.key === 'trainingType'">
            <a-tag color="blue">{{ record.trainingTypeName }}</a-tag>
          </template>
          
          <template v-else-if="column.key === 'result'">
            <div class="training-result">
              <div v-for="(change, attr) in record.attributeChanges" :key="attr" class="attr-change">
                <span class="attr-name">{{ attr }}:</span>
                <span class="attr-value" :class="{ 'positive': change.change > 0 }">
                  {{ change.oldValue }} → {{ change.newValue }} (+{{ change.change }})
                </span>
              </div>
            </div>
          </template>
          
          <template v-else-if="column.key === 'time'">
            {{ formatTime(record.trainingTime) }}
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ReloadOutlined } from '@ant-design/icons-vue'
import { useHeroStore } from '@/stores/hero'
import { useGlobalStore } from '@/stores/global'
import type { Hero, TrainingType, TrainingResult } from '@/types'

// 状态
const heroStore = useHeroStore()
const globalStore = useGlobalStore()

const isTraining = ref(false)
const selectedTrainingType = ref<TrainingType | null>(null)
const selectedHeroes = ref<string[]>([])
const trainingCount = ref(1)
const selectedItems = ref<string[]>([])
const todayTrainingCount = ref(0)
const currentEnergy = ref(80)
const maxEnergy = ref(100)

// 训练类型数据
const trainingTypes = ref<TrainingType[]>([
  {
    id: 'basic',
    name: '基础训练',
    description: '提升球员基础属性',
    icon: '🏃',
    coinCost: 1000,
    energyCost: 10,
    effects: [
      { attribute: 'pace', value: 2 },
      { attribute: 'physical', value: 1 }
    ],
    duration: 3600000 // 1小时
  },
  {
    id: 'technical',
    name: '技术训练',
    description: '提升球员技术能力',
    icon: '⚽',
    coinCost: 1500,
    energyCost: 15,
    effects: [
      { attribute: 'dribbling', value: 2 },
      { attribute: 'passing', value: 1 }
    ],
    duration: 3600000 // 1小时
  },
  {
    id: 'shooting',
    name: '射门训练',
    description: '提升球员射门能力',
    icon: '🥅',
    coinCost: 2000,
    energyCost: 20,
    effects: [
      { attribute: 'shooting', value: 3 }
    ],
    duration: 3600000 // 1小时
  },
  {
    id: 'defending',
    name: '防守训练',
    description: '提升球员防守能力',
    icon: '🛡️',
    coinCost: 1800,
    energyCost: 18,
    effects: [
      { attribute: 'defending', value: 3 }
    ],
    duration: 3600000 // 1小时
  }
])

// 训练道具
const trainingItems = ref([
  { label: '训练加速器', value: 'speed_booster' },
  { label: '属性强化剂', value: 'attr_enhancer' },
  { label: '经验药水', value: 'exp_potion' }
])

// 训练历史
const trainingHistory = ref<TrainingResult[]>([])

// 计算属性
const availableHeroes = computed(() => heroStore.heroes.filter(h => h.status === 'active'))

const totalCoinCost = computed(() => {
  if (!selectedTrainingType.value) return 0
  return selectedTrainingType.value.coinCost * trainingCount.value * selectedHeroes.value.length
})

const totalEnergyCost = computed(() => {
  if (!selectedTrainingType.value) return 0
  return selectedTrainingType.value.energyCost * trainingCount.value
})

const canStartTraining = computed(() => {
  return selectedTrainingType.value &&
         selectedHeroes.value.length > 0 &&
         currentEnergy.value >= totalEnergyCost.value
})

// 表格列配置
const historyColumns = [
  {
    title: '球员',
    key: 'heroName',
    dataIndex: 'heroName',
    width: 120
  },
  {
    title: '训练类型',
    key: 'trainingType',
    width: 100
  },
  {
    title: '训练结果',
    key: 'result',
    width: 300
  },
  {
    title: '训练时间',
    key: 'time',
    width: 150
  }
]

// 方法
const selectTrainingType = (type: TrainingType) => {
  selectedTrainingType.value = type
  selectedHeroes.value = []
}

const toggleHeroSelection = (hero: any) => {
  if (!canTrainHero(hero)) return
  
  const index = selectedHeroes.value.indexOf(hero.heroId)
  if (index > -1) {
    selectedHeroes.value.splice(index, 1)
  } else {
    selectedHeroes.value.push(hero.heroId)
  }
}

const canTrainHero = (hero: any): boolean => {
  // 检查是否在冷却中
  if (hero.training?.trainingCooldown && hero.training.trainingCooldown > Date.now()) {
    return false
  }
  
  // 检查是否受伤或停赛
  if (hero.status !== 'active') {
    return false
  }
  
  return true
}

const getHeroSymbol = (position: string): string => {
  const symbols: Record<string, string> = {
    GK: '🥅',
    DEF: '🛡️',
    MID: '⚽',
    ATT: '⚡'
  }
  return symbols[position] || '👤'
}

const getExpectedEffect = (): string => {
  if (!selectedTrainingType.value) return ''
  
  const effects = selectedTrainingType.value.effects
  const baseIncrease = trainingCount.value * 2
  
  return `${effects.join(', ')} +${baseIncrease}`
}

const startTraining = async () => {
  if (!selectedTrainingType.value || selectedHeroes.value.length === 0) return
  
  try {
    isTraining.value = true
    
    const trainingPromises = selectedHeroes.value.map(heroId => 
      heroStore.trainHero({
        heroId,
        trainType: parseInt(selectedTrainingType.value!.id),
        trainCount: trainingCount.value,
        useItems: selectedItems.value
      })
    )
    
    const results = await Promise.all(trainingPromises)
    
    // 更新训练历史
    results.forEach((result, index) => {
      if (result) {
        const hero = heroStore.getHeroById(selectedHeroes.value[index])
        trainingHistory.value.unshift({
          heroId: selectedHeroes.value[index],
          heroName: hero?.name || '',
          trainingTypeId: selectedTrainingType.value!.id,
          success: result.success,
          attributeGains: {},
          expGained: result.expGained || 0,
          message: result.message || '训练完成'
        })
      }
    })
    
    // 更新资源
    currentEnergy.value -= totalEnergyCost.value
    todayTrainingCount.value += selectedHeroes.value.length
    
    // 重置选择
    selectedHeroes.value = []
    selectedItems.value = []
    trainingCount.value = 1
    
    globalStore.addNotification({
      type: 'success',
      title: '训练完成',
      message: `${results.filter(r => r).length} 名球员训练完成`
    })
    
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '训练失败',
      message: error.message
    })
  } finally {
    isTraining.value = false
  }
}

const refreshTrainingData = async () => {
  await heroStore.fetchHeroes()
}

const formatTime = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString()
}

// 生命周期
onMounted(() => {
  refreshTrainingData()
})
</script>

<style lang="less" scoped>
.training-view {
  padding: @padding-lg;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-lg;
    
    .header-left {
      .page-title {
        font-size: @font-size-xl;
        color: @text-color;
        margin: 0 0 @margin-sm 0;
      }
      
      .training-info {
        display: flex;
        gap: @margin-base;
        
        .info-item {
          color: @text-color-secondary;
          font-size: @font-size-sm;
          
          strong {
            color: @primary-color;
          }
        }
      }
    }
  }
  
  .section-title {
    color: @text-color;
    margin-bottom: @margin-base;
    font-size: @font-size-lg;
  }
  
  .training-types {
    margin-bottom: @margin-xl;
    
    .types-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: @margin-base;
      
      .training-type-card {
        background-color: @card-bg;
        border: 2px solid @border-color;
        border-radius: @border-radius-base;
        padding: @padding-base;
        cursor: pointer;
        transition: all @transition-duration;
        text-align: center;
        
        &:hover {
          border-color: @primary-color;
          transform: translateY(-2px);
        }
        
        &.selected {
          border-color: @primary-color;
          background-color: rgba(82, 196, 26, 0.1);
        }
        
        .type-icon {
          font-size: 2rem;
          margin-bottom: @margin-sm;
        }
        
        .type-name {
          font-size: @font-size-lg;
          font-weight: bold;
          color: @text-color;
          margin-bottom: @margin-xs;
        }
        
        .type-description {
          color: @text-color-secondary;
          margin-bottom: @margin-sm;
        }
        
        .type-cost {
          display: flex;
          justify-content: center;
          gap: @margin-base;
          
          .cost-item {
            color: @text-color;
            font-weight: bold;
          }
        }
      }
    }
  }
  
  .hero-selection {
    margin-bottom: @margin-xl;
    
    .heroes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: @margin-base;
      
      .hero-card {
        position: relative;
        background-color: @card-bg;
        border: 2px solid @border-color;
        border-radius: @border-radius-base;
        padding: @padding-base;
        cursor: pointer;
        transition: all @transition-duration;
        
        &:hover {
          border-color: @primary-color;
        }
        
        &.selected {
          border-color: @primary-color;
          background-color: rgba(82, 196, 26, 0.1);
        }
        
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          
          &:hover {
            border-color: @border-color;
          }
        }
        
        .hero-avatar {
          text-align: center;
          margin-bottom: @margin-sm;
          
          .hero-symbol {
            font-size: 2rem;
          }
        }
        
        .hero-info {
          text-align: center;
          
          .hero-name {
            font-weight: bold;
            color: @text-color;
            margin-bottom: @margin-xs;
          }
          
          .hero-position {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
          
          .hero-level {
            color: @primary-color;
            font-weight: bold;
          }
          
          .hero-overall {
            color: @text-color;
            font-size: @font-size-lg;
            font-weight: bold;
          }
        }
        
        .cooldown-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.7);
          border-radius: @border-radius-base;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .cooldown-text {
            color: @error-color;
            font-weight: bold;
          }
        }
      }
    }
  }
  
  .training-config {
    margin-bottom: @margin-xl;
    
    .config-panel {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-base;
      
      .config-item {
        display: flex;
        align-items: center;
        gap: @margin-base;
        margin-bottom: @margin-base;
        
        label {
          color: @text-color;
          min-width: 80px;
        }
      }
      
      .config-summary {
        border-top: 1px solid @border-color;
        padding-top: @padding-base;
        
        .summary-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: @margin-xs;
          
          .label {
            color: @text-color-secondary;
          }
          
          .value {
            color: @text-color;
            font-weight: bold;
          }
        }
      }
    }
  }
  
  .training-actions {
    text-align: center;
    margin-bottom: @margin-xl;
  }
  
  .training-history {
    .training-result {
      .attr-change {
        margin-bottom: @margin-xs;
        
        .attr-name {
          color: @text-color-secondary;
          font-size: @font-size-sm;
        }
        
        .attr-value {
          color: @text-color;
          font-size: @font-size-sm;
          
          &.positive {
            color: @success-color;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .training-view {
    padding: @padding-base;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: @margin-base;
    }
    
    .types-grid {
      grid-template-columns: 1fr;
    }
    
    .heroes-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .config-item {
      flex-direction: column;
      align-items: flex-start;
    }
  }
}
</style>
