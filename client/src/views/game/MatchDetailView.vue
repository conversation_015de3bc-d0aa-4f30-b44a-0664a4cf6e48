<template>
  <div class="match-detail-view">
    <div class="page-header">
      <div class="header-left">
        <a-button @click="goBack" type="text">
          <ArrowLeftOutlined />
          返回
        </a-button>
        <h2 v-if="match" class="page-title">比赛详情</h2>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button @click="refreshMatch">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <div v-if="isLoading" class="loading-container">
      <a-spin size="large" />
    </div>

    <div v-else-if="match" class="match-content">
      <!-- 比赛基本信息 -->
      <div class="match-basic-info">
        <div class="match-header">
          <div class="team home-team">
            <div class="team-name">{{ match.homeTeam }}</div>
            <div class="team-formation">{{ match.homeTeam.formation?.teamFormations?.[0]?.name || '4-4-2' }}</div>
          </div>
          
          <div class="match-center">
            <div class="match-score">
              <span class="home-score">{{ match.homeScore }}</span>
              <span class="score-separator">-</span>
              <span class="away-score">{{ match.awayScore }}</span>
            </div>
            <div class="match-status">{{ getStatusText(match.status) }}</div>
            <div v-if="match.currentTime" class="match-time">
              {{ match.currentTime }}'
            </div>
          </div>
          
          <div class="team away-team">
            <div class="team-name">{{ match.awayTeam }}</div>
            <div class="team-formation">{{ match.awayTeam.formation?.teamFormations?.[0]?.name || '4-4-2' }}</div>
          </div>
        </div>
        
        <div class="match-meta">
          <div class="meta-item">
            <span class="label">比赛时间:</span>
            <span class="value">{{ formatTime(match.startTime || '') }}</span>
          </div>
          <div class="meta-item">
            <span class="label">比赛类型:</span>
            <span class="value">联赛</span>
          </div>
        </div>
      </div>

      <!-- 比赛统计 -->
      <div v-if="match.stats" class="match-stats">
        <h4 class="section-title">比赛统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-label">控球率</div>
            <div class="stat-bar">
              <div class="home-value">{{ match.stats.homePossession || 50 }}%</div>
              <div class="stat-progress">
                <div
                  class="home-bar"
                  :style="{ width: (match.stats.homePossession || 50) + '%' }"
                ></div>
                <div
                  class="away-bar"
                  :style="{ width: (match.stats.awayPossession || 50) + '%' }"
                ></div>
              </div>
              <div class="away-value">{{ match.stats.awayPossession || 50 }}%</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">射门次数</div>
            <div class="stat-bar">
              <div class="home-value">{{ match.stats.homeShots || 0 }}</div>
              <div class="stat-progress">
                <div class="stat-comparison">
                  <div 
                    class="home-bar" 
                    :style="{ width: getShotPercentage('home') + '%' }"
                  ></div>
                  <div 
                    class="away-bar" 
                    :style="{ width: getShotPercentage('away') + '%' }"
                  ></div>
                </div>
              </div>
              <div class="away-value">{{ match.stats.awayShots || 0 }}</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">射正次数</div>
            <div class="stat-bar">
              <div class="home-value">{{ match.stats.homeShotsOnTarget || 0 }}</div>
              <div class="stat-progress">
                <div class="stat-comparison">
                  <div 
                    class="home-bar" 
                    :style="{ width: getShotOnTargetPercentage('home') + '%' }"
                  ></div>
                  <div 
                    class="away-bar" 
                    :style="{ width: getShotOnTargetPercentage('away') + '%' }"
                  ></div>
                </div>
              </div>
              <div class="away-value">{{ match.stats.awayShotsOnTarget || 0 }}</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">角球次数</div>
            <div class="stat-bar">
              <div class="home-value">{{ match.stats.homeCorners || 0 }}</div>
              <div class="stat-progress">
                <div class="stat-comparison">
                  <div 
                    class="home-bar" 
                    :style="{ width: getCornerPercentage('home') + '%' }"
                  ></div>
                  <div 
                    class="away-bar" 
                    :style="{ width: getCornerPercentage('away') + '%' }"
                  ></div>
                </div>
              </div>
              <div class="away-value">{{ match.stats.awayCorners || 0 }}</div>
            </div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">犯规次数</div>
            <div class="stat-bar">
              <div class="home-value">{{ match.stats.homeFouls || 0 }}</div>
              <div class="stat-progress">
                <div class="stat-comparison">
                  <div 
                    class="home-bar" 
                    :style="{ width: getFoulPercentage('home') + '%' }"
                  ></div>
                  <div 
                    class="away-bar" 
                    :style="{ width: getFoulPercentage('away') + '%' }"
                  ></div>
                </div>
              </div>
              <div class="away-value">{{ match.stats.awayFouls || 0 }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 比赛事件 -->
      <div class="match-events">
        <h4 class="section-title">比赛事件</h4>
        <div class="events-timeline">
          <div
            v-for="(event, index) in match.events"
            :key="index"
            class="event-item"
            :class="`event-${event.type}`"
          >
            <div class="event-time">{{ event.minute }}'</div>
            <div class="event-content">
              <div class="event-icon">{{ getEventIcon(event.type) }}</div>
              <div class="event-info">
                <div class="event-description">{{ event.description }}</div>
                <div v-if="event.player" class="event-player">{{ event.player }}</div>
              </div>
            </div>
            <div class="event-team" :class="event.team">
              {{ event.team === 'home' ? match.homeTeam : match.awayTeam }}
            </div>
          </div>
          
          <div v-if="match.events.length === 0" class="empty-events">
            <span>暂无比赛事件</span>
          </div>
        </div>
      </div>

      <!-- 比赛结果 -->
      <div v-if="match.result" class="match-result">
        <h4 class="section-title">比赛结果</h4>
        <div class="result-info">
          <div class="result-status">
            <span class="status-text" :class="getResultClass(match.result)">
              {{ getResultText(match.result) }}
            </span>
          </div>
          
          <div class="result-details">
            <div class="detail-item">
              <span class="label">进球数:</span>
              <span class="value">{{ match.result.goalsFor }}</span>
            </div>
            <div class="detail-item">
              <span class="label">失球数:</span>
              <span class="value">{{ match.result.goalsAgainst }}</span>
            </div>
            <div class="detail-item">
              <span class="label">获得经验:</span>
              <span class="value">{{ match.result.experience }}</span>
            </div>
          </div>
          
          <div v-if="match.result.rewards && match.result.rewards.length > 0" class="result-rewards">
            <h5>比赛奖励:</h5>
            <div class="rewards-list">
              <div
                v-for="(reward, index) in match.result.rewards"
                :key="index"
                class="reward-item"
              >
                <span class="reward-icon">{{ getRewardIcon(reward.type) }}</span>
                <span class="reward-amount">{{ reward.amount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="error-container">
      <a-result
        status="404"
        title="比赛不存在"
        sub-title="找不到指定的比赛信息"
      >
        <template #extra>
          <a-button type="primary" @click="goBack">返回比赛列表</a-button>
        </template>
      </a-result>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowLeftOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import { useMatchStore } from '@/stores/match'
import { useGlobalStore } from '@/stores/global'
import type { Match, MatchResult } from '@/types'

// 路由
const route = useRoute()
const router = useRouter()

// 状态
const matchStore = useMatchStore()
const globalStore = useGlobalStore()

const isLoading = ref(false)
const match = ref<Match | null>(null)

// 方法
const loadMatch = async () => {
  const matchId = route.params.id as string
  if (!matchId) return
  
  try {
    isLoading.value = true
    // TODO: 实现获取比赛详情的方法
    // match.value = await matchStore.fetchMatchDetail(matchId)
    
    // 模拟数据
    match.value = {
      id: matchId,
      homeTeam: {
        id: 'home-team',
        name: '我的球队',
        rating: 85
      },
      awayTeam: {
        id: 'away-team',
        name: '对手球队',
        rating: 82
      },
      homeScore: 2,
      awayScore: 1,
      status: 'finished',
      startTime: new Date().toISOString(),
      competition: '友谊赛',
      currentTime: 90,
      events: [
        {
          id: '1',
          matchId: matchId,
          type: 'goal',
          minute: 15,
          player: '梅西',
          team: 'home',
          description: '梅西射门得分'
        },
        {
          id: '2',
          matchId: matchId,
          type: 'goal',
          minute: 67,
          player: '对手球员',
          team: 'away',
          description: '对手扳回一球'
        },
        {
          id: '3',
          matchId: matchId,
          type: 'goal',
          minute: 89,
          player: '内马尔',
          team: 'home',
          description: '内马尔绝杀进球'
        }
      ],
      stats: {
        homePossession: 65,
        awayPossession: 35,
        homeShots: 12,
        awayShots: 8,
        homeShotsOnTarget: 6,
        awayShotsOnTarget: 3,
        homeCorners: 5,
        awayCorners: 2,
        homeFouls: 8,
        awayFouls: 12
      },
      result: {
        matchId: matchId,
        isWin: true,
        isDraw: false,
        goalsFor: 2,
        goalsAgainst: 1,
        experience: 150,
        message: '恭喜获得胜利！',
        rewards: [
          { type: 'coins', amount: 5000 },
          { type: 'exp', amount: 150 }
        ]
      }
    }
  } catch (error: any) {
    await globalStore.addNotification({
      type: 'error',
      title: '加载失败',
      message: error.message
    })
  } finally {
    isLoading.value = false
  }
}

const refreshMatch = () => {
  loadMatch()
}

const goBack = () => {
  router.back()
}

// 工具函数
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    scheduled: '未开始',
    live: '进行中',
    finished: '已结束',
    paused: '暂停',
    halftime: '中场休息'
  }
  return statusMap[status] || status
}

const getEventIcon = (type: string): string => {
  const icons: Record<string, string> = {
    goal: '⚽',
    card: '🟨',
    redcard: '🟥',
    substitution: '🔄',
    penalty: '🥅',
    corner: '📐',
    offside: '🚩',
    foul: '⚠️'
  }
  return icons[type] || '📝'
}

const getShotPercentage = (team: 'home' | 'away'): number => {
  if (!match.value?.stats) return 50
  const homeShots = match.value.stats.homeShots
  const awayShots = match.value.stats.awayShots
  const total = homeShots + awayShots
  if (total === 0) return 50
  return team === 'home' ? (homeShots / total) * 100 : (awayShots / total) * 100
}

const getShotOnTargetPercentage = (team: 'home' | 'away'): number => {
  if (!match.value?.stats) return 50
  const homeShots = match.value.stats.homeShotsOnTarget
  const awayShots = match.value.stats.awayShotsOnTarget
  const total = homeShots + awayShots
  if (total === 0) return 50
  return team === 'home' ? (homeShots / total) * 100 : (awayShots / total) * 100
}

const getCornerPercentage = (team: 'home' | 'away'): number => {
  if (!match.value?.stats) return 50
  const homeCorners = match.value.stats.homeCorners
  const awayCorners = match.value.stats.awayCorners
  const total = homeCorners + awayCorners
  if (total === 0) return 50
  return team === 'home' ? (homeCorners / total) * 100 : (awayCorners / total) * 100
}

const getFoulPercentage = (team: 'home' | 'away'): number => {
  if (!match.value?.stats) return 50
  const homeFouls = match.value.stats.homeFouls
  const awayFouls = match.value.stats.awayFouls
  const total = homeFouls + awayFouls
  if (total === 0) return 50
  return team === 'home' ? (homeFouls / total) * 100 : (awayFouls / total) * 100
}

const getResultClass = (result: MatchResult): string => {
  if (result.isWin) return 'win'
  if (result.isDraw) return 'draw'
  return 'loss'
}

const getResultText = (result: MatchResult): string => {
  if (result.isWin) return '胜利'
  if (result.isDraw) return '平局'
  return '失败'
}

const getRewardIcon = (type: string): string => {
  const icons: Record<string, string> = {
    coins: '💰',
    gems: '💎',
    exp: '⭐',
    item: '🎁'
  }
  return icons[type] || '🎁'
}

const formatTime = (timeString: string): string => {
  return new Date(timeString).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadMatch()
})
</script>

<style lang="less" scoped>
.match-detail-view {
  padding: @padding-lg;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-lg;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: @margin-base;
      
      .page-title {
        font-size: @font-size-xl;
        color: @text-color;
        margin: 0;
      }
    }
  }
  
  .loading-container,
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
  }
  
  .match-content {
    display: grid;
    gap: @margin-lg;
    
    .section-title {
      color: @text-color;
      margin-bottom: @margin-base;
    }
    
    .match-basic-info {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-lg;
      
      .match-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: @margin-lg;
        
        .team {
          flex: 1;
          text-align: center;
          
          .team-name {
            font-size: @font-size-lg;
            font-weight: bold;
            color: @text-color;
            margin-bottom: @margin-xs;
          }
          
          .team-formation {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
        
        .match-center {
          flex: 1;
          text-align: center;
          
          .match-score {
            font-size: 2.5rem;
            font-weight: bold;
            color: @primary-color;
            margin-bottom: @margin-sm;
            
            .score-separator {
              margin: 0 @margin-sm;
              color: @text-color-secondary;
            }
          }
          
          .match-status {
            color: @text-color;
            font-weight: bold;
            margin-bottom: @margin-xs;
          }
          
          .match-time {
            color: @text-color-secondary;
            font-size: @font-size-sm;
          }
        }
      }
      
      .match-meta {
        display: flex;
        justify-content: center;
        gap: @margin-xl;
        
        .meta-item {
          display: flex;
          gap: @margin-sm;
          
          .label {
            color: @text-color-secondary;
          }
          
          .value {
            color: @text-color;
            font-weight: bold;
          }
        }
      }
    }
    
    .match-stats {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-lg;
      
      .stats-grid {
        display: grid;
        gap: @margin-base;
        
        .stat-item {
          .stat-label {
            text-align: center;
            color: @text-color-secondary;
            margin-bottom: @margin-sm;
          }
          
          .stat-bar {
            display: flex;
            align-items: center;
            gap: @margin-sm;
            
            .home-value,
            .away-value {
              width: 60px;
              text-align: center;
              font-weight: bold;
              color: @text-color;
            }
            
            .stat-progress {
              flex: 1;
              height: 20px;
              background-color: @border-color;
              border-radius: 10px;
              overflow: hidden;
              display: flex;
              
              .home-bar {
                background-color: @info-color;
                transition: width 0.3s;
              }
              
              .away-bar {
                background-color: @warning-color;
                transition: width 0.3s;
              }
              
              .stat-comparison {
                display: flex;
                width: 100%;
                height: 100%;
                
                .home-bar {
                  background-color: @info-color;
                  transition: width 0.3s;
                }
                
                .away-bar {
                  background-color: @warning-color;
                  transition: width 0.3s;
                }
              }
            }
          }
        }
      }
    }
    
    .match-events {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-lg;
      
      .events-timeline {
        .event-item {
          display: flex;
          align-items: center;
          gap: @margin-base;
          padding: @padding-sm 0;
          border-bottom: 1px solid rgba(67, 67, 67, 0.3);
          
          &:last-child {
            border-bottom: none;
          }
          
          .event-time {
            width: 50px;
            font-weight: bold;
            color: @highlight-color;
            text-align: center;
          }
          
          .event-content {
            flex: 1;
            display: flex;
            align-items: center;
            gap: @margin-sm;
            
            .event-icon {
              font-size: @font-size-lg;
            }
            
            .event-info {
              .event-description {
                color: @text-color;
                margin-bottom: @margin-xs;
              }
              
              .event-player {
                color: @text-color-secondary;
                font-size: @font-size-sm;
              }
            }
          }
          
          .event-team {
            width: 100px;
            text-align: right;
            font-size: @font-size-sm;
            color: @text-color-secondary;
            
            &.home {
              color: @info-color;
            }
            
            &.away {
              color: @warning-color;
            }
          }
          
          &.event-goal {
            background-color: rgba(82, 196, 26, 0.1);
          }
          
          &.event-card {
            background-color: rgba(250, 173, 20, 0.1);
          }
          
          &.event-redcard {
            background-color: rgba(255, 77, 79, 0.1);
          }
        }
        
        .empty-events {
          text-align: center;
          color: @text-color-secondary;
          padding: @padding-xl;
          font-style: italic;
        }
      }
    }
    
    .match-result {
      background-color: @card-bg;
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-lg;
      
      .result-info {
        .result-status {
          text-align: center;
          margin-bottom: @margin-lg;
          
          .status-text {
            font-size: @font-size-xl;
            font-weight: bold;
            padding: @padding-sm @padding-lg;
            border-radius: @border-radius-base;
            
            &.win {
              background-color: @success-color;
              color: white;
            }
            
            &.draw {
              background-color: @warning-color;
              color: white;
            }
            
            &.loss {
              background-color: @error-color;
              color: white;
            }
          }
        }
        
        .result-details {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: @margin-base;
          margin-bottom: @margin-lg;
          
          .detail-item {
            display: flex;
            justify-content: space-between;
            
            .label {
              color: @text-color-secondary;
            }
            
            .value {
              color: @text-color;
              font-weight: bold;
            }
          }
        }
        
        .result-rewards {
          h5 {
            color: @text-color;
            margin-bottom: @margin-base;
          }
          
          .rewards-list {
            display: flex;
            gap: @margin-base;
            
            .reward-item {
              display: flex;
              align-items: center;
              gap: @margin-xs;
              padding: @padding-sm;
              background-color: rgba(0, 0, 0, 0.2);
              border-radius: @border-radius-base;
              
              .reward-icon {
                font-size: @font-size-lg;
              }
              
              .reward-amount {
                color: @primary-color;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .match-detail-view {
    padding: @padding-base;
    
    .match-header {
      flex-direction: column;
      gap: @margin-base;
      text-align: center;
    }
    
    .match-meta {
      flex-direction: column;
      gap: @margin-base;
    }
    
    .result-details {
      grid-template-columns: 1fr;
    }
    
    .rewards-list {
      flex-wrap: wrap;
    }
  }
}
</style>
