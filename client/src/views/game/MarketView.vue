<template>
  <div class="market-view">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">转会市场</h2>
        <div class="market-stats">
          <span class="stat-item">在售球员: <strong>{{ totalPlayers }}</strong></span>
          <span class="stat-item">我的出售: <strong>{{ myListings }}</strong></span>
        </div>
      </div>
      
      <div class="header-right">
        <a-space>
          <a-button @click="showSellModal = true" type="primary">
            <PlusOutlined />
            出售球员
          </a-button>
          <a-button @click="refreshMarket">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="market-filters">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-input
            v-model:value="searchText"
            placeholder="搜索球员名称"
            allow-clear
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select v-model:value="positionFilter" placeholder="位置" allow-clear>
            <a-select-option value="">全部位置</a-select-option>
            <a-select-option value="GK">门将</a-select-option>
            <a-select-option value="DEF">后卫</a-select-option>
            <a-select-option value="MID">中场</a-select-option>
            <a-select-option value="ATT">前锋</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select v-model:value="priceRange" placeholder="价格区间" allow-clear>
            <a-select-option value="">全部价格</a-select-option>
            <a-select-option value="0-10000">0-1万</a-select-option>
            <a-select-option value="10000-50000">1-5万</a-select-option>
            <a-select-option value="50000-100000">5-10万</a-select-option>
            <a-select-option value="100000+">10万以上</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select v-model:value="sortBy">
            <a-select-option value="price_asc">价格升序</a-select-option>
            <a-select-option value="price_desc">价格降序</a-select-option>
            <a-select-option value="overall_desc">总评降序</a-select-option>
            <a-select-option value="time_desc">最新上架</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6">
          <a-button @click="applyFilters" type="primary">搜索</a-button>
          <a-button @click="resetFilters" style="margin-left: 8px">重置</a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 市场列表 -->
    <div class="market-content">
      <a-table
        :columns="marketColumns"
        :data-source="marketListings"
        :pagination="pagination"
        :loading="isLoading"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'hero'">
            <div class="hero-info">
              <div class="hero-name">{{ record.heroName }}</div>
              <div class="hero-details">
                {{ record.position }} | Lv.{{ record.level }} | {{ record.overall }}总评
              </div>
            </div>
          </template>
          
          <template v-else-if="column.key === 'price'">
            <div class="price-info">
              <div class="current-price">💰 {{ formatNumber(record.price) }}</div>
              <div v-if="record.originalPrice !== record.price" class="original-price">
                原价: {{ formatNumber(record.originalPrice) }}
              </div>
            </div>
          </template>
          
          <template v-else-if="column.key === 'seller'">
            <div class="seller-info">
              <div class="seller-name">{{ record.sellerName }}</div>
              <div class="seller-rating">⭐ {{ record.sellerRating }}</div>
            </div>
          </template>
          
          <template v-else-if="column.key === 'timeLeft'">
            <div class="time-left">
              {{ getTimeLeft(record.endTime) }}
            </div>
          </template>
          
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button
                size="small"
                @click="viewHeroDetail(record)"
              >
                查看详情
              </a-button>
              <a-button
                v-if="!record.isMyListing"
                type="primary"
                size="small"
                @click="buyHero(record)"
                :disabled="!canBuyHero(record)"
              >
                购买
              </a-button>
              <a-button
                v-else
                size="small"
                @click="cancelListing(record)"
                danger
              >
                取消出售
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 出售球员模态框 -->
    <a-modal
      v-model:open="showSellModal"
      title="出售球员"
      @ok="confirmSell"
      @cancel="cancelSell"
      :confirm-loading="isSelling"
    >
      <div class="sell-form">
        <div class="form-item">
          <label>选择球员:</label>
          <a-select
            v-model:value="sellForm.heroId"
            placeholder="选择要出售的球员"
            style="width: 100%"
            @change="onHeroSelect"
          >
            <a-select-option
              v-for="hero in sellableHeroes"
              :key="hero.heroId"
              :value="hero.heroId"
            >
              {{ hero.name }} ({{ hero.position }}, {{ hero.overall }}总评)
            </a-select-option>
          </a-select>
        </div>
        
        <div v-if="selectedHeroForSell" class="hero-preview">
          <div class="hero-card">
            <div class="hero-name">{{ selectedHeroForSell.name }}</div>
            <div class="hero-stats">
              <span>位置: {{ selectedHeroForSell.position }}</span>
              <span>等级: {{ selectedHeroForSell.level }}</span>
              <span>总评: {{ selectedHeroForSell.overall }}</span>
            </div>
            <div class="market-value">
              市场估价: 💰 {{ formatNumber(selectedHeroForSell.marketValue || 0) }}
            </div>
          </div>
        </div>
        
        <div class="form-item">
          <label>出售价格:</label>
          <a-input-number
            v-model:value="sellForm.price"
            :min="1000"
            :max="1000000"
            style="width: 100%"
            :formatter="(value: number) => `💰 ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="(value: string) => value!.replace(/💰\s?|(,*)/g, '')"
          />
        </div>
        
        <div class="form-item">
          <label>出售时长:</label>
          <a-select v-model:value="sellForm.duration" style="width: 100%">
            <a-select-option :value="24">24小时</a-select-option>
            <a-select-option :value="48">48小时</a-select-option>
            <a-select-option :value="72">72小时</a-select-option>
            <a-select-option :value="168">7天</a-select-option>
          </a-select>
        </div>
        
        <div class="fee-info">
          <div class="fee-item">
            <span>上架费用:</span>
            <span>💰 {{ listingFee }}</span>
          </div>
          <div class="fee-item">
            <span>交易税率:</span>
            <span>{{ taxRate }}%</span>
          </div>
          <div class="fee-item total">
            <span>实际收入:</span>
            <span>💰 {{ actualIncome }}</span>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons-vue'
import { useHeroStore } from '@/stores/hero'
import { useGlobalStore } from '@/stores/global'
import type { Hero, MarketListing } from '@/types'

// 状态
const heroStore = useHeroStore()
const globalStore = useGlobalStore()

const isLoading = ref(false)
const isSelling = ref(false)
const showSellModal = ref(false)

// 搜索和筛选
const searchText = ref('')
const positionFilter = ref('')
const priceRange = ref('')
const sortBy = ref('time_desc')

// 市场数据
const marketListings = ref<MarketListing[]>([])
const totalPlayers = ref(0)
const myListings = ref(0)

// 出售表单
const sellForm = reactive({
  heroId: '',
  price: 0,
  duration: 24
})

// 计算属性
const sellableHeroes = computed(() => 
  heroStore.heroes.filter(h => !h.isInFormation && !h.isLocked)
)

const selectedHeroForSell = computed(() => 
  sellableHeroes.value.find(h => h.heroId === sellForm.heroId)
)

const listingFee = computed(() => Math.floor(sellForm.price * 0.05))
const taxRate = computed(() => 10)
const actualIncome = computed(() => 
  sellForm.price - listingFee.value - Math.floor(sellForm.price * taxRate.value / 100)
)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
})

// 表格列配置
const marketColumns = [
  {
    title: '球员信息',
    key: 'hero',
    width: 200
  },
  {
    title: '价格',
    key: 'price',
    width: 120,
    sorter: true
  },
  {
    title: '卖家',
    key: 'seller',
    width: 120
  },
  {
    title: '剩余时间',
    key: 'timeLeft',
    width: 100
  },
  {
    title: '操作',
    key: 'actions',
    width: 150
  }
]

// 方法
const refreshMarket = async () => {
  try {
    isLoading.value = true
    // TODO: 调用获取市场数据的API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    marketListings.value = [
      {
        id: '1',
        heroId: 'hero1',
        heroName: '梅西',
        position: 'ATT',
        level: 35,
        overall: 94,
        price: 500000,
        originalPrice: 600000,
        sellerName: '玩家A',
        sellerRating: 4.8,
        endTime: Date.now() + 86400000,
        isMyListing: false
      }
    ]
    
    totalPlayers.value = 1250
    myListings.value = 3
    pagination.total = 1250
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '刷新失败',
      message: error.message
    })
  } finally {
    isLoading.value = false
  }
}

const applyFilters = () => {
  pagination.current = 1
  refreshMarket()
}

const resetFilters = () => {
  searchText.value = ''
  positionFilter.value = ''
  priceRange.value = ''
  sortBy.value = 'time_desc'
  applyFilters()
}

const handleTableChange = (pag: any, filters: any, sorter: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  refreshMarket()
}

const viewHeroDetail = (listing: MarketListing) => {
  globalStore.addNotification({
    type: 'info',
    title: '球员详情',
    message: `查看 ${listing.heroName} 的详细信息`
  })
}

const buyHero = async (listing: MarketListing) => {
  try {
    // TODO: 调用购买球员的API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    globalStore.addNotification({
      type: 'success',
      title: '购买成功',
      message: `成功购买球员 ${listing.heroName}`
    })
    
    refreshMarket()
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '购买失败',
      message: error.message
    })
  }
}

const canBuyHero = (listing: MarketListing): boolean => {
  return !listing.isMyListing && listing.endTime > Date.now()
}

const cancelListing = async (listing: MarketListing) => {
  try {
    // TODO: 调用取消出售的API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    globalStore.addNotification({
      type: 'success',
      title: '取消成功',
      message: `已取消出售 ${listing.heroName}`
    })
    
    refreshMarket()
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '取消失败',
      message: error.message
    })
  }
}

const onHeroSelect = (heroId: string) => {
  const hero = sellableHeroes.value.find(h => h.heroId === heroId)
  if (hero) {
    sellForm.price = hero.marketValue || 10000
  }
}

const confirmSell = async () => {
  if (!sellForm.heroId || !sellForm.price) {
    globalStore.addNotification({
      type: 'warning',
      title: '信息不完整',
      message: '请选择球员并设置价格'
    })
    return
  }
  
  try {
    isSelling.value = true
    
    // TODO: 调用出售球员的API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    globalStore.addNotification({
      type: 'success',
      title: '上架成功',
      message: `球员 ${selectedHeroForSell.value?.name} 已上架`
    })
    
    showSellModal.value = false
    resetSellForm()
    refreshMarket()
  } catch (error: any) {
    globalStore.addNotification({
      type: 'error',
      title: '上架失败',
      message: error.message
    })
  } finally {
    isSelling.value = false
  }
}

const cancelSell = () => {
  resetSellForm()
}

const resetSellForm = () => {
  sellForm.heroId = ''
  sellForm.price = 0
  sellForm.duration = 24
}

// 工具函数
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

const getTimeLeft = (endTime: number): string => {
  const now = Date.now()
  const diff = endTime - now
  
  if (diff <= 0) return '已结束'
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 24) {
    const days = Math.floor(hours / 24)
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 生命周期
onMounted(() => {
  refreshMarket()
  heroStore.fetchHeroes()
})
</script>

<style lang="less" scoped>
.market-view {
  padding: @padding-lg;
  
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @margin-lg;
    
    .header-left {
      .page-title {
        font-size: @font-size-xl;
        color: @text-color;
        margin: 0 0 @margin-sm 0;
      }
      
      .market-stats {
        display: flex;
        gap: @margin-base;
        
        .stat-item {
          color: @text-color-secondary;
          font-size: @font-size-sm;
          
          strong {
            color: @primary-color;
          }
        }
      }
    }
  }
  
  .market-filters {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    margin-bottom: @margin-lg;
  }
  
  .market-content {
    background-color: @card-bg;
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    
    :deep(.ant-table) {
      background-color: transparent;
      
      .ant-table-thead > tr > th {
        background-color: @card-bg;
        color: @text-color;
        border-bottom: 1px solid @border-color;
      }
      
      .ant-table-tbody > tr > td {
        border-bottom: 1px solid @border-color;
        color: @text-color-secondary;
      }
      
      .ant-table-tbody > tr:hover > td {
        background-color: rgba(82, 196, 26, 0.05);
      }
    }
    
    .hero-info {
      .hero-name {
        color: @text-color;
        font-weight: bold;
        margin-bottom: @margin-xs;
      }
      
      .hero-details {
        color: @text-color-secondary;
        font-size: @font-size-sm;
      }
    }
    
    .price-info {
      .current-price {
        color: @primary-color;
        font-weight: bold;
        margin-bottom: @margin-xs;
      }
      
      .original-price {
        color: @text-color-secondary;
        font-size: @font-size-sm;
        text-decoration: line-through;
      }
    }
    
    .seller-info {
      .seller-name {
        color: @text-color;
        margin-bottom: @margin-xs;
      }
      
      .seller-rating {
        color: @warning-color;
        font-size: @font-size-sm;
      }
    }
    
    .time-left {
      color: @text-color;
      font-weight: bold;
    }
  }
}

// 出售模态框样式
.sell-form {
  .form-item {
    margin-bottom: @margin-base;
    
    label {
      display: block;
      color: @text-color;
      margin-bottom: @margin-xs;
    }
  }
  
  .hero-preview {
    margin: @margin-base 0;
    
    .hero-card {
      background-color: rgba(0, 0, 0, 0.2);
      border: 1px solid @border-color;
      border-radius: @border-radius-base;
      padding: @padding-base;
      
      .hero-name {
        color: @text-color;
        font-weight: bold;
        margin-bottom: @margin-sm;
      }
      
      .hero-stats {
        display: flex;
        gap: @margin-base;
        margin-bottom: @margin-sm;
        
        span {
          color: @text-color-secondary;
          font-size: @font-size-sm;
        }
      }
      
      .market-value {
        color: @primary-color;
        font-weight: bold;
      }
    }
  }
  
  .fee-info {
    background-color: rgba(0, 0, 0, 0.1);
    border: 1px solid @border-color;
    border-radius: @border-radius-base;
    padding: @padding-base;
    margin-top: @margin-base;
    
    .fee-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: @margin-xs;
      
      &.total {
        border-top: 1px solid @border-color;
        padding-top: @margin-xs;
        font-weight: bold;
        color: @primary-color;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .market-view {
    padding: @padding-base;
    
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: @margin-base;
    }
    
    .market-filters {
      :deep(.ant-row) {
        flex-direction: column;
        
        .ant-col {
          width: 100% !important;
          margin-bottom: @margin-sm;
        }
      }
    }
  }
}
</style>
