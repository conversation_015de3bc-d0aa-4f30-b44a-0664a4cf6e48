import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import { useRouter } from 'vue-router'
import type { User, LoginCredentials, RegisterData, ServerInfo, Character } from '@/types'
import { characterAuthService } from '@/services/character-auth.service'
import { useGlobalStore } from './global'
import authService from "@services/auth";

/**
 * 认证状态管理
 * 管理用户登录、注册、服务器选择、角色选择等认证相关状态
 */
export const useAuthStore = defineStore('auth', () => {
  const router = useRouter()
  const globalStore = useGlobalStore()

  // 状态定义
  const user = ref<User | null>(null)
  const accessToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const selectedServer = ref<ServerInfo | null>(null)
  const selectedCharacter = ref<Character | null>(null)
  const availableServers = ref<ServerInfo[]>([])
  const availableCharacters = ref<Character[]>([])

  // 计算属性
  const isAuthenticated = computed(() => !!user.value && !!accessToken.value)
  const hasSelectedServer = computed(() => !!selectedServer.value)
  const hasSelectedCharacter = computed(() => !!selectedCharacter.value)
  const canEnterGame = computed(() => 
    isAuthenticated.value && hasSelectedServer.value && hasSelectedCharacter.value
  )

  // 动作方法

  /**
   * 用户登录
   */
  const login = async (credentials: LoginCredentials) => {
    try {
      globalStore.setLoading(true)

      const response = await characterAuthService.login(credentials)

      if (!response.success || !response.data) {
        throw new Error(response.error?.message || '登录失败')
      }

      // 保存认证信息
      user.value = response.data.user
      accessToken.value = response.data.accessToken
      refreshToken.value = response.data.refreshToken

      await globalStore.addNotification({
        type: 'success',
        title: '登录成功',
        message: `欢迎回来，${response.data.user.username}！`
      })

      // 跳转到服务器选择页面
      router.push('/server-selection')

    } catch (error: any) {
      await globalStore.addNotification({
        type: 'error',
        title: '登录失败',
        message: error.message || '登录过程中发生错误'
      })
      throw error
    } finally {
      globalStore.setLoading(false)
    }
  }

  /**
   * 用户注册
   */
  const register = async (data: RegisterData) => {
    try {
      globalStore.setLoading(true)

      const response = await characterAuthService.register(data)

      if (!response.success) {
        throw new Error(response.error?.message || '注册失败')
      }

      await globalStore.addNotification({
        type: 'success',
        title: '注册成功',
        message: '账号注册成功，请使用新账号登录'
      })

      // 跳转到登录页面
      router.push('/login')

    } catch (error: any) {
      await globalStore.addNotification({
        type: 'error',
        title: '注册失败',
        message: error.message || '注册过程中发生错误'
      })
      throw error
    } finally {
      globalStore.setLoading(false)
    }
  }

  /**
   * 用户登出
   */
  const logout = async () => {
    try {
      // 调用登出API
      if (refreshToken.value) {
        await authService.logout(refreshToken.value)
      }
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      // 清除本地状态
      user.value = null
      accessToken.value = null
      refreshToken.value = null
      selectedServer.value = null
      selectedCharacter.value = null
      availableServers.value = []
      availableCharacters.value = []
      
      // 清除本地存储
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
      localStorage.removeItem('selectedServer')
      localStorage.removeItem('selectedCharacter')
      
      globalStore.addNotification({
        type: 'info',
        title: '已登出',
        message: '您已成功登出系统'
      })
      
      // 跳转到登录页面
      router.push('/login')
    }
  }

  /**
   * 尝试自动登录
   */
  const tryAutoLogin = async () => {
    const savedToken = localStorage.getItem('accessToken')
    const savedUser = localStorage.getItem('user')
    const savedServer = localStorage.getItem('selectedServer')
    const savedCharacter = localStorage.getItem('selectedCharacter')
    
    if (savedToken && savedUser) {
      try {
        // 验证token有效性
        const response = await authService.verifyToken(savedToken)
        
        if (response.success && response.data?.valid) {
          // 恢复用户状态
          accessToken.value = savedToken
          refreshToken.value = localStorage.getItem('refreshToken')
          user.value = JSON.parse(savedUser)
          
          // 恢复服务器和角色选择
          if (savedServer) {
            selectedServer.value = JSON.parse(savedServer)
          }
          if (savedCharacter) {
            selectedCharacter.value = JSON.parse(savedCharacter)
          }
          
          // 根据状态跳转到合适的页面
          if (canEnterGame.value) {
            router.push('/game')
          } else if (hasSelectedServer.value) {
            router.push('/character-selection')
          } else {
            router.push('/server-selection')
          }
        } else {
          // Token无效，清除本地存储
          await logout()
        }
      } catch (error) {
        console.error('自动登录失败:', error)
        await logout()
      }
    }
  }

  /**
   * 获取可用服务器列表
   */
  const fetchServers = async () => {
    try {
      const response = await authService.getServers()
      if (response.success && response.data) {
        availableServers.value = response.data
      } else {
        throw new Error(response.error?.message || '获取服务器列表失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '获取服务器列表失败',
        message: error.message || '无法获取服务器信息'
      })
      throw error
    }
  }

  /**
   * 选择服务器
   */
  const selectServer = async (server: ServerInfo) => {
    try {
      globalStore.setLoading(true)
      
      // 调用API选择服务器
      await authService.selectServer(server.serverId)
      
      selectedServer.value = server
      localStorage.setItem('selectedServer', JSON.stringify(server))
      
      globalStore.addNotification({
        type: 'success',
        title: '服务器选择成功',
        message: `已选择服务器：${server.name}`
      })
      
      // 跳转到角色选择页面
      router.push('/character-selection')
      
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '服务器选择失败',
        message: error.message || '选择服务器时发生错误'
      })
      throw error
    } finally {
      globalStore.setLoading(false)
    }
  }

  /**
   * 获取角色列表
   */
  const fetchCharacters = async () => {
    if (!selectedServer.value) {
      throw new Error('请先选择服务器')
    }
    
    try {
      const response = await authService.getCharacters(selectedServer.value.serverId)
      if (response.success && response.data) {
        availableCharacters.value = response.data
      } else {
        throw new Error(response.error?.message || '获取角色列表失败')
      }
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '获取角色列表失败',
        message: error.message || '无法获取角色信息'
      })
      throw error
    }
  }

  /**
   * 选择角色
   */
  const selectCharacter = async (character: Character) => {
    try {
      globalStore.setLoading(true)
      
      // 调用API选择角色
      await authService.selectCharacter(character.characterId)
      
      selectedCharacter.value = character
      localStorage.setItem('selectedCharacter', JSON.stringify(character))
      
      globalStore.addNotification({
        type: 'success',
        title: '角色选择成功',
        message: `已选择角色：${character.name}`
      })
      
      // 进入游戏
      router.push('/game')
      
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '角色选择失败',
        message: error.message || '选择角色时发生错误'
      })
      throw error
    } finally {
      globalStore.setLoading(false)
    }
  }

  /**
   * 创建新角色
   */
  const createCharacter = async (name: string) => {
    if (!selectedServer.value) {
      throw new Error('请先选择服务器')
    }
    
    try {
      globalStore.setLoading(true)
      
      const response = await authService.createCharacter({
        name,
        serverId: selectedServer.value.serverId
      })

      if (response.success && response.data) {
        availableCharacters.value.push(response.data)

        await globalStore.addNotification({
          type: 'success',
          title: '角色创建成功',
          message: `角色 ${response.data.name} 创建成功！`
        })

        return response.data
      } else {
        throw new Error(response.error?.message || '角色创建失败')
      }
      
    } catch (error: any) {
      globalStore.addNotification({
        type: 'error',
        title: '角色创建失败',
        message: error.message || '创建角色时发生错误'
      })
      throw error
    } finally {
      globalStore.setLoading(false)
    }
  }

  /**
   * 刷新访问令牌
   */
  const refreshAccessToken = async () => {
    if (!refreshToken.value) {
      throw new Error('没有刷新令牌')
    }
    
    try {
      const response = await authService.refreshToken(refreshToken.value)

      if (response.success && response.data) {
        accessToken.value = response.data.accessToken
        localStorage.setItem('accessToken', response.data.accessToken)

        if (response.data.refreshToken) {
          refreshToken.value = response.data.refreshToken
          localStorage.setItem('refreshToken', response.data.refreshToken)
        }

        return response.data.accessToken
      } else {
        throw new Error(response.error?.message || '刷新Token失败')
      }
    } catch (error) {
      // 刷新失败，需要重新登录
      await logout()
      throw error
    }
  }

  return {
    // 状态
    user: readonly(user),
    accessToken: readonly(accessToken),
    refreshToken: readonly(refreshToken),
    selectedServer: readonly(selectedServer),
    selectedCharacter: readonly(selectedCharacter),
    availableServers: readonly(availableServers),
    availableCharacters: readonly(availableCharacters),

    // 计算属性
    isAuthenticated,
    hasSelectedServer,
    hasSelectedCharacter,
    canEnterGame,

    // 方法
    login,
    register,
    logout,
    tryAutoLogin,
    fetchServers,
    selectServer,
    fetchCharacters,
    selectCharacter,
    createCharacter,
    refreshAccessToken
  }
})
